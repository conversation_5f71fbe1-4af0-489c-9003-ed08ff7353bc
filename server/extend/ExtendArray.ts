

if (!Array.prototype.remove) {
    Object.defineProperty(Array.prototype, 'remove', {
        value(key: any, value?: any) {
            let i = value !== undefined ? this.findIndex(m => m[key] === value) : this.indexOf(key);
            return i === -1 ? null : this.splice(i, 1)[0];
        },
        enumerable: false
    });
}

if (!Array.prototype.delete) {
    Object.defineProperty(Array.prototype, 'delete', {
        value(cb: (value: any, index: number) => boolean) {
            const arr = [];
            for (let i = this.length - 1; i >= 0; i--) {
                if (cb(this[i], i)) {
                    arr.push(this.splice(i, 1)[0]);
                }
            }
            return arr;
        },
        enumerable: false
    });
}

if (!Array.prototype.random) {
    Object.defineProperty(Array.prototype, 'random', {
        value() {
            if (this.length === 0) {
                return undefined;
            }
            const i = Math.floor(Math.random() * this.length);
            return this[i];
        },
        enumerable: false
    });
}

if (!Array.prototype.randomRemove) {
    Object.defineProperty(Array.prototype, 'randomRemove', {
        value() {
            if (this.length === 0) {
                return undefined;
            }
            const i = Math.floor(Math.random() * this.length);
            return this.splice(i, 1)[0];
        },
        enumerable: false
    });
}

if (!Array.prototype.has) {
    Object.defineProperty(Array.prototype, 'has', {
        value(key: any, value?: any) {
            return value !== undefined ? this.some(m => m[key] === value) : this.indexOf(key) !== -1;
        },
        enumerable: false
    });
}

if (!Array.prototype.append) {
    Object.defineProperty(Array.prototype, 'append', {
        value(val: any) {
            this.push(val);
            return this;
        },
        enumerable: false
    });
}

if (!Array.prototype.add) {
    Object.defineProperty(Array.prototype, 'add', {
        value(val: any) {
            this.push(val);
            return val;
        },
        enumerable: false
    });
}

if (!Array.prototype.last) {
    Object.defineProperty(Array.prototype, 'last', {
        value() {
            return this[this.length - 1];
        },
        enumerable: false
    });
}

// 拼接数组 对象
if (!Array.prototype.join2) {
    Object.defineProperty(Array.prototype, 'join2', {
        value(cb: (value: any, index: number) => string, separator: string = ',') {
            return this.map((value: any, index: number) => cb(value, index) || '').join(separator)
        },
        enumerable: false
    });
}

if (!Array.prototype.pushArr) {
    Object.defineProperty(Array.prototype, 'pushArr', {
        value(arr: any[]) {
            if (!arr || arr.length === 0) {
                return this.length
            }
            for (let e of arr) {
                this.push(e)
            }
            return arr.length
        },
        enumerable: false
    });
}

if (!Array.prototype.set) {
    Object.defineProperty(Array.prototype, 'set', {
        value(arr: any[]) {
            this.length = 0
            return Array.prototype.push.apply(this, arr)
        },
        enumerable: false
    });
}

if (!Array.prototype.findLastIndex) {
    Object.defineProperty(Array.prototype, 'findLastIndex', {
        value(cb: (value: any, index: number) => boolean) {
            for (let i = this.length - 1; i >= 0; i--) {
                if (cb(this[i], i)) {
                    return i
                }
            }
            return -1
        },
        enumerable: false
    });
}

if (!Array.prototype.findLast) {
    Object.defineProperty(Array.prototype, 'findLast', {
        value(cb: (value: any, index: number) => boolean) {
            for (let i = this.length - 1; i >= 0; i--) {
                const val = this[i]
                if (cb(val, i)) {
                    return val
                }
            }
            return null
        },
        enumerable: false
    });
}

if (!Array.prototype.unique) {
    Object.defineProperty(Array.prototype, 'unique', {
        value(key?: string) {
            let map = {}
            for (let i = 0; i < this.length; i++) {
                let val
                if (key == undefined) {
                    val = this[i]
                }
                else {
                    val = this[i][key]
                }
                map[val] = this[i]
            }
            return Object.values(map)
        },
        enumerable: false
    });
}