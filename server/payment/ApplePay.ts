import { payMod, Order, Stat } from "./PayModel"
const logger = require("../common/log").getLogger("ApplePay");
import { Platform } from "../common/constant"
import got from "got"
import util from "../common/util";
const {payment} = require("../config")

class ApplePay {

    public init() {
    }

    public async check(order) {
        let args = this.fixInfo(order)
        let {transaction_id} = args
        let stat = await payMod.checkOrderStat(order)
        if (stat == Stat.FINISH) {
            return payMod.getErrCode("ok")
        }
        else {
            let [ok, rsp] = await this.req(args)
            if (ok) {
                let errcode = await this.parseResponse(rsp, args)
                if (errcode.code != payMod.getErrCode("ok").code) {
                    await payMod.saveExceptionOrder(Platform.APPLE, transaction_id, errcode)
                }
                if (rsp.isTest) {
                    errcode = Object.assign({isTest: rsp.isTest}, errcode)
                }
                return errcode
            }
            else {
                let errcode = payMod.getErrCode("pay_ibrowse_send_err")
                await payMod.saveExceptionOrder(Platform.APPLE, transaction_id, errcode)
                return errcode
            }
        }
    }

    private fixInfo(info) {
        return {
            recipe_data: info.token,
            transaction_id: info.order_id,
            package: info.package,
            product: info.product,
            uid: info.uid
        }
    }

    private async req(args, isSand?) {
        logger.info("try req iap service args :", isSand, args)
        try {
            let url = isSand ? 'https://sandbox.itunes.apple.com/verifyReceipt' : 'https://buy.itunes.apple.com/verifyReceipt' 
            let body = {"receipt-data": args.recipe_data}
            let res:any = await got.post(url, {
                json: body,
            }).json()
            let {status} = res
            if (status == 21007 && !isSand) { //应该是走沙盒
                return this.req(args, true)
            }
            if (status == 0) {
                res.isTest = isSand
                return [true, res]
            }
            else {
                logger.error("transaction status error args :", args, "res: ", res)
                return [false, res]
            }
        } catch (error) {
            logger.error("apple req err", error)
            return [false, error]       
        }
    }

    private async parseResponse(rsp, args) {
        let {receipt} = rsp
        let recipe_data = args.recipe_data
        let {transaction_id, product, uid} = args
        let bundleId = receipt.bid || receipt.bundle_id

        let trans = this.getTrans(transaction_id, receipt)
        if (!trans) {
            logger.error("game check TransactionId_wrong args :", rsp, args)
            return payMod.getErrCode("receipt_TransactionId_wrong")
        }

        let isValid = bundleId == payment.apple.package
        let reProductId = trans && trans.product_id
       
        if (!isValid || bundleId != args.package) {
            logger.error("game check game_wrong args", bundleId, payment.apple.package, rsp, args)
            return payMod.getErrCode("receipt_game_wrong")
        }

        if (reProductId != product) {
            logger.error("game check product_id_wrong args", reProductId, product, rsp, args)
            return payMod.getErrCode("receipt_product_id_wrong")
        }

        let now = Date.now()
        let purchaseDate = trans.purchase_date_ms
        let orgPurchaseDate = trans.original_purchase_date_ms
        let pdiff = now - Number(purchaseDate)
        let opDiff = now - Number(orgPurchaseDate)
        if (purchaseDate && pdiff >= 24 * util.Time.Hour) {
            logger.warn("iap fuck cheater, timeout Trans", uid, pdiff, opDiff, now, trans)
            return payMod.getErrCode("transaction_timeout")
        }
        else {
            let payInfo = {
                platform: Platform.APPLE,
                order_id: transaction_id,
                package: args.package,
                product,
                token: recipe_data,
                timestamp: now,
                uid,
            }
            await payMod.finishOrder(Platform.APPLE, payInfo.order_id, rsp.isTest)
            return payMod.getErrCode("ok")
        }
    }

    private getTrans(transaction_id, receipt) {
        if (receipt.transaction_id) { //老版本
            if (transaction_id == receipt.transaction_id) return receipt
        }
        else { //IOS 7 之后支持的新版本收据信息
            for (let item of receipt.in_app) {
                if (transaction_id == item.transaction_id) {
                    return item
                }
            }
        }
    }
}

export const applePay = new ApplePay()
