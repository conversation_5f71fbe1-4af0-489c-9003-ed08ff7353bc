const logger = require("../common/log").getLogger("GoogleVerify");
const {payment, appleLogin} = require("../config")
import {google} from "googleapis"

//scopes: ['https://androidpublisher.googleapis.com/androidpublisher/v3/applications'],
//url: `https://androidpublisher.googleapis.com/androidpublisher/v3/applications/${this.packageName}/purchases/voidedpurchases`,
const PURCHASE_SCOPE = 'https://www.googleapis.com/auth/androidpublisher'
const DAY = 24 * 60 * 60 * 1000;

class GoogleVerify {

    public async init() {
        const auth = new google.auth.JWT({
            keyFile: payment.google.secret,
            scopes: [PURCHASE_SCOPE],
            email: payment.google.email,
        });

        google.options({auth});
    }

    public async getPurchase(packageName, productId, token) {
        let res = await google.androidpublisher('v3').purchases.products.get({
            packageName,
            productId,
            token,
        })
        logger.debug("getPurchase", res.data)
        return res.data
    }

    /**
     * google查询退款记录信息
     * @param startTime 最早记录的时间
     * @param endTime 最晚记录的时间，默认当前且不超过当前
     * @param packageName 包名
     * @param maxResults 最大条数
     * @param type 0 退款的购买， 1退款的购买和订阅，默认是0
     */
    public async getRefund(startTime = Date.now() - DAY, endTime = Date.now(), packageName = appleLogin.sub, maxResults = 1000, type = 0) {
        // 国内没有谷歌退款
        if (packageName.indexOf('inland') > -1) return null;
        await this.init();
        return await google.androidpublisher('v3').purchases.voidedpurchases.list({
            startTime: startTime.toString(),
            endTime: endTime.toString(),
            packageName,
            maxResults,
            type
        });
    }
}

export const googleVerify = new GoogleVerify()