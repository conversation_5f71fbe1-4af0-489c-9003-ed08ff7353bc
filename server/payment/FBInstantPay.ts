import hmacSHA256 from "crypto-js/hmac-sha256";
import Base64 from "crypto-js/enc-base64";
//import utf8 from "crypto-js/enc-utf8";
//import JSONbig from "json-bigint";
import { payMod } from "./PayModel";

const AppSecret = '851537f6283ba9ec962b41baf9569fe4';

class FBInstantPay {
    public init() {
    }

    public check({signedRequest}): boolean {
        const firstPart = signedRequest.split(".")[0].replace(/-/g, "+").replace(/_/g, "/");
        const secondPart = signedRequest.split(".")[1];
        const signature = Base64.parse(firstPart).toString();
        const dataHash = hmacSHA256(secondPart, AppSecret).toString();
        const isValid = signature === dataHash;
        //const json = Base64.parse(secondPart).toString(utf8);
        //const data = JSONbig({storeAsString: true}).parse(json);
        if (isValid) {
            return payMod.getErrCode('ok')
        } else {
            return payMod.getErrCode('fb_signResult_err')
        }
    }
}

export const fbInstantPay = new FBInstantPay()
