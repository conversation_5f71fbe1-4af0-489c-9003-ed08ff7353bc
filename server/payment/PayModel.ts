import {googlePay} from "./GooglePay"

// createPayOrderModel
const logger = require("../common/log").getLogger("PayModel")
let db = require("../db/db")
const {payment, payServer} = require("../config")
const global = require("../common/global")
import {Platform} from "../common/constant"
import {applePay} from "./ApplePay"
import {hmsPay} from "./HuaWeiPay";
import { fbInstantPay } from "./FBInstantPay"


export type Order = {
    uid: string,
    order_id: string,
    platform: string,
    package: string,
    product: string,
    finish?: number, //0.未完成 1.完成
    timestamp?: number,
    is_test: boolean, //是测试订单
    stat: number //1.下单，2:回调成功，未发商品， 3:已经发放商品,
    token:string,
}

export type GPArg = {
    order: string,
    product: string,
    token: string,
    package: string,
}

export type AppleArg = {
    recipe_data: string,
    transaction_id: string,
    package: string,
    product: string,
}

export enum Stat {
    PENDING = 0, //开始检测
    FINISH = 100, //正常落地
    USED = 101,  // 已使用
    EXCEPTION = 1000,
    CANCEL = 2000,
}

const ErrorCode = {
    "ok": {code: 0, msg: "成功"},

    //不合法订单
    "unsupport_platform": {code: -10001, msg: "不支持的验证平台"},
    "receipt_game_wrong": {code: -10002, msg: "收据的游戏和我们的游戏对不上-2"},
    "receipt_TransactionId_wrong": {code: -10003, msg: "发来的收据的交易id和他自己发的交易id不一样"},
    "receipt_package_wrong": {code: -10004, msg: "发来的包名和收据对应的包名不相同"},
    "transaction_timeout": {code: -10005, msg: "订单超过了限定的时间，但是订单是可用的"},
    "receipt_product_id_wrong": {code: -10006, msg: "前端发来的消息篡改了产品号"},
    "pay_sid_error_args": {code: -10007, msg: "订单购买者和发货者不同（sid或uid不同）-参数不对"},
    "pay_sid_error_encode": {code: -10008, msg: "订单购买者和发货者不同（sid或uid不同）-编码不对"},
    "pay_sid_error": {code: -10009, msg: "订单购买者和发货者不同（sid或uid不同）"},
    "receipt_format_wrong": {code: -10010, msg: "订单格式不对"},
    "order_not_exist": {code: -10011, msg: "订单不存在"},
    "gp_400": {code: -10012, msg: "google play purchaseTokenDoesNotMatchProductId 或token不对等信息错误"},
    "gp_purchased": {code: -10013, msg: "谷歌订单已经确认，需要去后台看看是不是取消的"},

    //内部错误
    "pay_ibrowse_send_err": {code: -20001, msg: "使用ibrowse发送请求时返回error"},
    "pay_ibrowse_send_other": {code: -20002, msg: "使用ibrowse发送请求时返回类型未知的返回值"},
    "order_finished": {code: -20003, msg: "订单已经完成，需要看一下logic的日志，是否真的发过货了"},
    "pay_http_req_err": {code: -20004, msg: "访问appstore验证订单服务时发生错误"},
    "gp_401": {code: -20005, msg: "google play 验证订单时access_token有问题或者账号权限没有财务权限等账号本身问题"},
    "order_pending": {code: -20006, msg: "订单还在处理中"},

    "appstore_status_err": {code: -20000000, msg: "调用appstore那边校验接口时返回的订单状态错误码起始"},

    "hms_token_err": {code: -30000000, msg: "华为pay验证失败,支付token错误."},
    "hms_product_err": {code: -30000001, msg: "华为pay验证失败,productId错误."},
    "hms_order_err": {code: -30000002, msg: "华为pay验证失败."},

    "fb_signResult_err": {code: -40000000, msg: "fb pay验证失败,signResult验证错误."},

}

class PayModel {

    private payCol: any

    public init() {
        if (!payment) return
        let {google, apple, wx, hms} = payment
        this.payCol = db.createPayOrderModel()
        if (google) {
            googlePay.init()
        }
        if (apple) {
            applePay.init()
        }
        if(hms){
            hmsPay.init()
        } 
    }

    public async check(info) {

        let { platform } = info
        if (platform == Platform.FBINSTANT) { // fb直接走这个判定
            return fbInstantPay.check(info)
        }

        if (!payment) {
            let res = await global.requestPayServer("chekPayOrder", info)
            if (res == false || res.status) {
                return this.getErrCode("unknow")
            }
            return res
        }

        if (platform == Platform.GOOGLE) {
            return googlePay.check(info)
        } else if (platform == Platform.APPLE) {
            return applePay.check(info)
        } else if (platform == Platform.HMS) {
            return hmsPay.check(info)
        } else {
            logger.error("check_order unsupport_platform :", info)
            return this.getErrCode("unsupport_platform")
        }
    }

    //db
    public async checkOrderStat(info): Promise<Stat> {
        let {order_id, uid, product} = info
        let doc = await this.payCol.findOne({order_id})
        if (doc) {
            return doc.stat
        } else {
            logger.debug("数据库里没有，走三方校验", info)
            return this.newOrder(info)
        }
    }

    private async newOrder(info): Promise<Stat> {
        let {order_id} = info
        info.stat = Stat.PENDING
        info.timestamp = Date.now()
        await this.payCol.updateOne({order_id}, info, {upsert: true})
        return Stat.PENDING
    }

    public async saveExceptionOrder(platform, order_id, exception) {
        await this.payCol.findOneAndUpdate({order_id, platform, stat: Stat.PENDING}, {
            stat: Stat.EXCEPTION,
            exception: JSON.stringify(exception),
            errCode: exception.code
        })
    }

    public getExceptionOrders(platform, order_id) {
        return this.payCol.find({order_id})
    }

    //设置订单为完成状态
    public async finishOrder(platform, order_id, isTest) {
        let q = {platform, order_id, stat: {$ne: Stat.FINISH}}
        let u = {stat: Stat.FINISH, is_test: isTest}
        let doc = await this.payCol.findOneAndUpdate(q, u)
        if (doc) {
            logger.info("order finish", platform, order_id)
            return this.getErrCode("ok")
        } else {
            return this.getErrCode("order_finished")
        }
    }

    //设置订单为被取消状态
    public async cancelOrder(order_id) {
        await this.payCol.updateOne({order_id}, {stat: Stat.CANCEL})
    }

    public getErrCode(name) {
        return ErrorCode[name] || {code: -20000000, msg: "充值未定义的错误码"}
    }

    public isInvalidErroCode(error) {
        let code = error.code
        return code > -20000 && code < -10000
    }
}
export const payMod = new PayModel()
