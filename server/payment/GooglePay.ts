import { googleVerify } from "./GoogleVerify"
import { payMod, Order, Stat} from "./PayModel"
const logger = require("../common/log").getLogger("GooglePay");
import { Platform } from "../common/constant"

class GooglePay {
	public init() {
		googleVerify.init()
	}

	public async check(order: Order) {
		let args = this.fixInfo(order)
		let { order_id, product, uid } = args
		if (this.preCheck(order_id)) {
			let stat = await payMod.checkOrderStat(order)
			if (stat == Stat.FINISH) {
				return payMod.getErrCode("ok")
			}
			else {
				let [ok, rsp] = await this.req(args)
				if (ok) {
					let errcode = await this.parseResponse(rsp, args)
					if (errcode.code == payMod.getErrCode("order_pending").code) {
						await payMod.saveExceptionOrder(Platform.GOOGLE, order_id, errcode)
					}
					return errcode
				}
				else {
					let errcode = payMod.getErrCode("pay_ibrowse_send_err")
					await payMod.saveExceptionOrder(Platform.GOOGLE, order_id, errcode)
					return errcode
				}
			}
		}
		else {
			logger.warn("receipt_format_wrong", uid, order_id, product)
			return payMod.getErrCode("receipt_format_wrong")
		}
	}

	private fixInfo(order: Order) {
		return order
	}

	private preCheck(order_id: string) {
		if (order_id && order_id.startsWith("GPA.")) return true
		return false
	}

	private async req(args) {
		let { product, token } = args
		try {
			let rsp = await googleVerify.getPurchase(args.package, product, token)
			return [true, rsp]
		} catch (error) {
			logger.error("google req err", error, args)
			return [false, error]
		}
	}

	private async parseResponse(rsp, args) {
		let { order_id, product, uid } = args
		let { purchaseState } = rsp
		if (purchaseState == 1) {
			logger.error("purchaseState: 1 finished by front args : ", args, rsp)
			return payMod.getErrCode("gp_purchased")
		}
		else if (purchaseState == 0) {
			logger.info("purchaseState ok Args : ", args, "Response: ", rsp)
			let isTest = this.isTest(rsp)
			if (order_id) {
				await payMod.finishOrder(Platform.GOOGLE, order_id, isTest)
			}
			let res = payMod.getErrCode("ok")
			if (isTest) {
				res = Object.assign({isTest}, res) //拷贝一下
			}
			return res
		}
		else {
			logger.error("order is pending", args, rsp)
			return payMod.getErrCode("order_pending")
		}
	}

	isTest(rsp) {
		return rsp && rsp.purchaseType == 0
	}
}

export const googlePay = new GooglePay()
