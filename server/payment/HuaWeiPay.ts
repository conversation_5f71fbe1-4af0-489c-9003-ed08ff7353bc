import {Order, payMod, Stat} from "./PayModel";
import got from "got"

const logger = require("../common/log").getLogger("HuaWeiPay");
const verify = '/applications/purchases/tokens/verify'

const client_secret = '9c626fd9a119388506ace6716b1d121c79d75551580ad20436deb091f264d14c';
const client_id = '107631751';
const grant_type = 'client_credentials';

const db = require("../db/db");
const redisKey = require("../db/redisKey");

/**
 * 华为pay验证
 */
class HuaWeiPay {
    lastGetTokenTime = 0;
    redis: null;

    public async init() {
        this.redis = db.redis
    }

    public async check(order: Order) {
        let {token, product, order_id} = order
        if (!token) return payMod.getErrCode("hms_token_err")
        if (!product) return payMod.getErrCode("hms_product_err")
        if (!order_id) return payMod.getErrCode("hms_order_err")

        // 核查当前订单状态
        let stat = await payMod.checkOrderStat(order)
        if (stat == Stat.FINISH) {
            return payMod.getErrCode("ok")
        }
        // 去华为服务器验证
        return await this.getAppAtAndExecuteRequest(token, product)
    }

    public async remote(json, authorization, retry) {
        try {
            logger.info("try req hms service args :", JSON.stringify(json))
            let url = `https://orders-dra.iap.cloud.huawei.asia${verify}`
            // 设置header
            let {responseCode} = await got.post(url, {
                json,
                headers: {
                    "Authorization": authorization,
                    "Content-Type": "application/json; charset=UTF-8"
                }
            }).json()
            if (responseCode == 0) return payMod.getErrCode("ok")
            // responseCode @see https://developer.huawei.com/consumer/cn/doc/development/HMSCore-References/server-error-code-0000001050166248
            if (retry && responseCode == 401) {
                // token过期，retry 一次
                return await this.getAppAtAndExecuteRequest(json.purchaseToken, json.product, false)
            }
            logger.error(`hms pay check rsp err:${responseCode}`)
            return payMod.getErrCode("hms_order_err")
        } catch (e) {
            logger.error(`hms pay check err:${e}`)
            return payMod.getErrCode("hms_order_err")
        }
    }

    // retry 保证401时必定重试一次
    public async getAppAtAndExecuteRequest(purchaseToken, productId, retry = true) {
        // @ts-ignore
        let accessToken =  await this.redis.get(redisKey.HMS_TOKEN())
        if (!accessToken || !retry) {
            // expires_in 是3600秒
            let headers = {
                'Content-Type': `application/x-www-form-urlencoded;charset=utf-8`,

            }
            let {access_token, expires_in} = await got.post("https://oauth-login.cloud.huawei.com/oauth2/v3/token", {
                headers,
                form: {
                    "grant_type": grant_type,
                    "client_id": client_id,
                    "client_secret": client_secret
                }
            }).json()
            if (!access_token) {
                logger.error(`hms pay get token = null`)
            }
            // 打印本次获取token距上次获取过去的时间
            logger.info(`hms get access_token:${access_token},last token during :${Date.now() - this.lastGetTokenTime}`)
            this.lastGetTokenTime = Date.now()
            accessToken = access_token
            // @ts-ignore
            await this.redis.set(redisKey.HMS_TOKEN(), accessToken)
            // @ts-ignore
            await this.redis.expire(redisKey.HMS_TOKEN(), expires_in - 60)
            // 不可以再重试
            retry = false
        }
        let oriString = 'APPAT:' + accessToken;
        let authorization = 'Basic ' + Buffer.from(oriString).toString('base64');
        return await this.remote({
            purchaseToken, productId
        }, authorization, retry)
    }


}

export const hmsPay = new HuaWeiPay()
