const path = require("path")
const fs = require("fs")

let updateJson = async () => {
    let serverPath = path.join(__dirname, 'client/json/')
    let copy = (clientPath, fileList = []) => {
        let stat = fs.statSync(clientPath)
        if (stat.isDirectory()) {
            let fileNames = fs.readdirSync(clientPath)
            for (let name of fileNames) {
                if (fileList.length > 0 && !fileList.includes(name)) {
                    continue
                }
                if (path.extname(name) === '.json') {
                    fs.copyFileSync(path.join(clientPath, name), path.join(serverPath, name))
                }
            }
        } else {
            fs.copyFileSync(clientPath, path.join(serverPath, clientPath))
        }
    }

    let subPath = "../Hotel/assets/resources/common/json"
    copy(path.join(__dirname, subPath, 'commonJson'))
    copy(path.join(__dirname, subPath, 'internalJson'))
    copy(path.join(__dirname, subPath, 'cnJson'), ["share.json"])

    subPath = "../Hotel/assets/startSubpackage/common/json"
    copy(path.join(__dirname, subPath, 'commonJson'))
    copy(path.join(__dirname, subPath, 'internalJson'))
}

let updateWebJson = async () => {
    let root = __dirname;
    let webPath = path.join(root, '../web/src/assets/json/')
    let copy = (clientPath, fileList = []) => {
        let stat = fs.statSync(clientPath)
        if (stat.isDirectory()) {
            let fileNames = fs.readdirSync(clientPath)
            for (let name of fileNames) {
                if (fileList.length > 0 && !fileList.includes(name)) {
                    continue
                }
                if (path.extname(name) === '.json') {
                    fs.existsSync(clientPath) && fs.copyFileSync(path.join(clientPath, name), path.join(webPath, name))
                }
            }
        } else {
            fs.existsSync(clientPath) && fs.copyFileSync(clientPath, path.join(webPath, clientPath))
        }
    }

    let subPath = "../Hotel/assets/resources/common/json"
    copy(path.join(root, subPath, 'commonJson'), idTableList)
    copy(path.join(root, subPath, 'cnJson'), nameTableList)

    subPath = "../Hotel/assets/startSubpackage/common/json"
    copy(path.join(root, subPath, 'commonJson'), idTableList)
    copy(path.join(root, subPath, 'cnJson'), nameTableList)
}

(async () => {
    try {
        await updateJson()
        await updateWebJson()
    } catch (e) {
        console.error(e)
    }
    console.log("done")
})()

const idTableList = [
    'itemBase.json',
    'roomFurnitureAttr.json',
    'customersBase.json',
    'foodBase.json',
    'wudongBase.json',
    'hotelSkinBase.json',
    'customersBase.json',
    'filmBase.json',
    'roomFurnitureUnlock_1.json',
    'roomFurnitureUnlock_2.json',
    'roomFurnitureUnlock_3.json',
    'roomFurnitureUnlock_4.json',
    'roomFurnitureUnlock_5.json',
    'roomFurnitureUnlock_6.json',
    'roomTypeConfig.json',
    "staffTrainItem.json",
    "souvenirBase.json",
    "adventureCard.json",
    "adventurePostcard.json",
    "adventureDebris.json",
    'facialBase.json',
    'hairstyleBase.json',
    'staffBase.json',
    "plantBase.json",
    "fruitBase.json",
    "flowerContainerBase.json",
    "gardenUnlock.json",
    "homePartySkinBase.json",
    "homePartyThemeBase.json",
    "roleSkinBase.json",
    "roomFurnitureUnlock_10000.json",
    "meditatorBase.json",
    "omenCardBase.json",
    "divinationUnlock.json",
    "clothesBase.json",
    "clothesAttr.json",
    "drinkMaterial.json",
    "drinkBase.json",
];
const nameTableList = [
    'itemName.json',
    'roomFurnitureName.json',
    'customersName.json',
    'foodName.json',
    'wudongBaseDesc.json',
    'hotelSkinBaseDesc.json',
    'customersName.json',
    'filmDesc.json',
    'ui.json',
    "resourceDesc.json",
    "souvenirDesc.json",
    'adventureDesc.json',
    'beautyProductDesc.json',
    'beautyProductDesc.json',
    'staffBaseDesc.json'
    , "plantDesc.json",
    "fruitDesc.json",
    "flowerContainerDesc.json",
    "gardenFurnitureName.json",
    "partyClothesDesc.json",
    "partyThemeDesc.json",
    "roleSkinDesc.json",
    "roomFurnitureDesc.json",
    "meditatorDesc.json",
    "omenCardDesc.json",
    "divinationName.json",
    "clothesName.json",
    "drinkMaterialDesc.json",
    "drinkDesc.json",
];
