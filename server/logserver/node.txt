1 需要收集的数据
    A: 基本数据
        a 看广告的总次数
        b 本次广告的奖励
        c 玩家的基本资产(相对于上面的奖励)
        d 广告大概信息(时长/类型等)
    B: 行为数据
        a 用户为什么触发了该次广告
        b 用户是否进入了本次广告的观看(奖励太低,不看?)
        c 如果用户没进入本次广告的观看，那么他是直接没进入广告界面/关闭/跳过了广告(观看不成功)
          或者是广告在加载(没获取到广告)?
        d 使用广告跳过道具

利用A.a可以分析用户是否属于爱看广告的玩家
利用B.a和B.b的数据可以计算触发该次广告的相应事件的观看率,分析该事件收益率
利用A.b、A.c、A.d和B.c可以分析是否是奖励太低?或者广告太长?
利用A.d分析计算某类型广告成功观看率,拒绝观看率,跳过观看率等

1.完全不看广告的用户是怎么样的一类人【比如可以从人气角度，游戏天数，各种资产情况等等进行归类统计】【一段时间内已经没看广告了】
2.看广告最疯狂的一类人【比如可以从人气角度，游戏天数，各种资产情况等等进行归类统计】【一段时间内看广告的次数很高】


