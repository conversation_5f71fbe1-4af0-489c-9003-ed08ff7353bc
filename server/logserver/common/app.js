let express = require('express');
let app = express();
let bodyParser = require('body-parser');
const {clientConfig} = require("./cls/TencentCloudConfig");

app.__init = function () {
    app.use(express.static('wwwroot'));
    app.use(bodyParser.json({ limit: '10mb', strict: false }));
    app.use(bodyParser.urlencoded({ limit: '10mb', extended: false }));

    //设置跨域访问
    app.all('*', function (req, res, next) {
        res.header("Access-Control-Allow-Origin", "*");
        res.header("Access-Control-Allow-Headers", "Authorization,X-Requested-With,content-type");
        res.header("Access-Control-Allow-Methods", "PUT,POST,GET,DELETE,OPTIONS");
        res.header("X-Powered-By", ' 3.2.1')
        res.header("Content-Type", "application/json;charset=utf-8");
        next();
    });

    const server = require('http').createServer(app)
    server.listen(clientConfig.port, () => {
        console.log(process.pid, 'Express server listening on port ' + server.address().port)
        if (process.send) {
            process.send("ready")
        }
    })

    process.on('SIGINT', () => {
        console.log(process.pid, "SIGINT signal received, Exit...")
        server.close(function (err) {
            if (err) {
                console.error("server close error", process.pid, err);
                process.exit(1)
            }
        })
    })

    process.on('message', (msg) => {
        if (msg == 'shutdown') {
            console.info('Closing all connections...', process.pid)
            setTimeout(() => {
                console.info('Finished closing connections', process.pid)
                process.exit(0)
            }, 5000)
        }
    })
}

module.exports = app;