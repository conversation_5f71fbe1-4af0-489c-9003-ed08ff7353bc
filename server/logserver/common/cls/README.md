# 腾讯云日志查询sql记录,主要是用来生成图表或者分析数据

## 1 查询指定用户的蜡烛变动 (demo可以直接转化为时序图)

* *|select max(cast(heart as bigint)),date FROM (select json_extract(json_extract(json_parse(extendsInfo),'$.changes'),'$[0].cur') as heart, cast(from_unixtime(time/1000) as DATE) as date where eventKey = '4' AND uid = '2675204c-fe34-4c71-85df-f50cc5908c17' order by time desc) group by date order by date
* *|select json_extract(json_extract(json_parse(extendsInfo),'$.changes'),'$[0].cur') as heart,from_unixtime(time/1000) where eventKey = '4' AND uid = '2675204c-fe34-4c71-85df-f50cc5908c17' order by time desc

  上面两种都可以，只是维度不同

## 2 查询礼包弹窗上报数据

* *|select json_extract(json_extract(json_parse(extendsInfo),'$.changes'),'$.storeId') as storeId,json_extract(json_extract(json_parse(extendsInfo),'$.changes'),'$.type') as type,json_extract(json_extract(json_parse(extendsInfo),'$.changes'),'$.point') as point where eventKey = '2001' AND uid = '1fd18dd5-58e7-4be6-96c3-d9f5b8cdec56' order by time desc
* *| select storeId,type,point FROM (
  select cast(json_extract(json_extract(json_parse(extendsInfo),'$.changes'),'$.storeId') as bigint) as storeId,cast(json_extract(json_extract(json_parse(extendsInfo),'$.changes'),'$.type') as bigint) as type,cast(json_extract(json_extract(json_parse(extendsInfo),'$.changes'),'$.point')  as bigint) as point FROM (
  select distinct time,extendsInfo where eventKey='2001' AND time >1665417600000
  )
  ) where storeId = 1050 AND type = 2
