import {cls} from "tencentcloud-sdk-nodejs";
import {clientConfig} from './TencentCloudConfig';
import {AsyncClient, LogGroup, LogItem, PutLogsRequest, Content,} from "tencentcloud-cls-sdk-js"

export default class ClsRequest {
    private client: any = new cls.v20201016.Client(clientConfig);
    private pullLogClient: AsyncClient;
    private logger: any = {};

    constructor() {
        this.logger = console.log;
        let uri = clientConfig.debug ? clientConfig.profile.httpProfile.remote : clientConfig.profile.httpProfile.local;
        this.pullLogClient = new AsyncClient({
            endpoint: clientConfig.region + '.' + uri,
            secretId: clientConfig.credential.secretId,
            secretKey: clientConfig.credential.secretKey,
            sourceIp: clientConfig.sourceIp,
            retry_times: clientConfig.retry_times,
            compress: clientConfig.compress
        });
    }

    public async reqTopicList(): Promise<any []> {
        let res: any = [];
        let params = {};
        return await this.client.DescribeTopics(params);
    }

    /**
     * logGroup里面最多可以放5条日志，后期考虑多放几条再统一投递
     * @param logGroup
     * @param topic 默认投递到行为事件里面
     */
    public async putLog(logGroup: LogGroup, topic: string) {
        let request = new PutLogsRequest(topic, logGroup);
        let data = await this.pullLogClient.PutLogs(request);
        if (!data || data.httpStatusCode !== 200) {
            this.logger.warn("Pull logs error ,res:", JSON.stringify(data));
        }
    }

    /**
     * 填充投递消息的头部
     * @param req
     * @param logItem
     */
    public async fillHeader(req: any = {}, logItem: LogItem) {
        req.type && (logItem.pushBack(new Content("type", req.type.toString())));
        req.uid && (logItem.pushBack(new Content("uid", req.uid)));
        req.gameVer && (logItem.pushBack(new Content("gameVer", req.gameVer)));
        logItem.pushBack(new Content("platform", req.platform.toString()));
        req.time && (logItem.pushBack(new Content("time", req.time.toString())));
        req.eventKey && (logItem.pushBack(new Content("eventKey", req.eventKey.toString())));
        return req.eventKey;
    }

    public async advertisingEvent(logItem: LogItem, req: any = {}) {
        let extendsInfo = req.extendsInfo;
        logItem.pushBack(new Content("adType", extendsInfo.adType.toString()));
        logItem.pushBack(new Content("startTime", extendsInfo.startTime.toString()));
        logItem.pushBack(new Content("selectType", extendsInfo.selectType.toString()));
        logItem.pushBack(new Content("totalCount", extendsInfo.totalCount.toString()));
        logItem.pushBack(new Content("changes", JSON.stringify(extendsInfo.changes)));
    }

    public async getLogItemByReq(req: any = {}) {
        if (!Object.keys(req).length || typeof req === 'string' || !req.eventKey) {
            this.logger.info("pull log error with empty req!");
            return;
        }
        //this.logger.info("pull log: ", JSON.stringify(req));
        let item = new LogItem();
        let event = await this.fillHeader(req, item);
        if (event === 1001) {
            await this.advertisingEvent(item, req);
        } else {
            req.extendsInfo && (item.pushBack(new Content("extendsInfo", JSON.stringify(req.extendsInfo))));
            req.uiList && (item.pushBack(new Content("uiList", JSON.stringify(req.uiList))));
        }
        return item;
    }

    /**
     * 通用的事件投递
     * @param req
     */
    public async eventPush(req: any = {}) {
        let item;
        try {
            item = await this.getLogItemByReq(req)
        } catch (e) {
            this.logger.error(e);
        }
        if (!item) {
            return;
        }
        item.setTime(Math.floor(Date.now() / 1000));
        let logGroup = new LogGroup();
        logGroup.addLogs(item);
        let r = clientConfig.region.substring(clientConfig.region.indexOf("-") + 1).toUpperCase();
        let config = clientConfig.topics[r];
        if (req.eventKey === 1001) {
            await this.putLog(logGroup, config.watchAd.TopicId);
        } else {
            await this.putLog(logGroup, config.eventLog.TopicId);
        }
    }

    public async getTopicConfig() {
        let r = clientConfig.region.substring(clientConfig.region.indexOf("-") + 1).toUpperCase();
        return clientConfig.topics[r];
    }

    public async doSearch(Context, TopicId, From, To, Query = '', Limit = 1000, Sort = 'desc') {
        let params = {TopicId, From, To, Query, Limit, Sort}
        if (Context) {
            params.Context = Context;
        }
        return await this.client.SearchLog(params);
    }

}