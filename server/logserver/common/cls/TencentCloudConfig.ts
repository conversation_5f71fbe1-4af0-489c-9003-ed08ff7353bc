export let clientConfig = {
    type: 'inland',
    debug: false,
    port: 8060,
    region: "ap-shanghai",
    sourceIp: '127.0.0.1',
    credential: {
        secretId: "AKIDgEdvypSvBE4hBPnCmFTXO4XpskRtLA0d",
        secretKey: "Xt25uXxEG5h5II4d0A7qRyNaaOzMYhfP",
    },
    profile: {
        httpProfile: {
            endpoint: "cls.tencentcloudapi.com",
            remote: "cls.tencentcs.com", //外网
            local: "cls.tencentyun.com", //局域网 debug=false
        },
    },
    retry_times: 10,
    compress: true,
    topics: {
        SHANGHAI: {
            eventLog: {
                TopicId: 'ef2f766d-f426-4ce4-a18d-d29c16662dae',
                TopicName: '用户行为数据收集',
                LogsetId: '6abd632f-6568-4045-8875-b2c9b6a1d817',
            },
            watchAd: {
                TopicId: '4e36dc68-5352-4b36-8d69-bb3f0b8de8cd',
                TopicName: '广告数据分析',
                LogsetId: '6abd632f-6568-4045-8875-b2c9b6a1d817',
            }
        },
        SINGAPORE: {
            eventLog: {
                TopicId: 'fb7a73d5-8914-436a-aed2-0ec0fe01daa4',
                TopicName: '用户行为数据收集',
                LogsetId: 'f3c2637f-3bac-4ed8-8b01-d302ff83e49e',
            },
            watchAd: {
                TopicId: '05244ddf-65b7-4471-aeba-75b739669a08',
                TopicName: '广告数据分析',
                LogsetId: 'f3c2637f-3bac-4ed8-8b01-d302ff83e49e',
            }
        },
    },
};