enum PlatformType {
    NONE, // 未知
    WX,
    QQ,
    IOS,
    ANDROID,
}

// 信息类型
enum UseBehaviorMsgType {
    INFO = 1, // 普通信息
    WARN, // 警告
    ERROR, // 错误
}

// 玩家行为数据事件类型
enum UserBehaviorEventKeyType {
    ADD_PROP = 1, // 添加道具
    SET_BISCUITS, // 改变饼干
    SET_CANDIES, // 改变糖果
    SET_HEART, // 改变蜡烛
    EXCHANGE_CODE, // 兑换码奖励
    UNLOCK_FURN = 101, // 解锁装饰
    UNLOCK_BUILD, // 解锁设施
    UNLOCK_MAIN_SKIN, // 解锁大楼皮肤
    UNLOCK_WUDONG_SKIN, // 解锁乌冬皮肤
    DIRECT_UNLOCK, // 直接解锁（无消耗的方式）
    YOYO_COST = 201, // 转转花费
    YOYO_GET, // 转转奖励
    CINEMA_SIGNED, // 签约鸭消耗
    USE_PROP = 301, // 使用道具
    USE_OFFLINE_BISCUITS, // 使用离线饼干包
    USER_AD_DATA = 1001, // 广告数据
}

// 玩家对广告的选择
enum UserAdSelectType {
    NONE = -1,
    LOADING, // 广告加载中
    CLOSE, // 直接关闭
    NOT_FINISH, // 没有看完
    FINISH, // 看完了
    SKIP, // 使用道具跳过
}

// 看广告增加的道具类型
enum AdPropType {
    // 注： 禁止改变已有顺序 主要用来统计
    NONE = 0, // 默认空
    MAIN_CARD, // 主楼揽客体力卡片（被动）1
    WUDONG_STAMINA, // 主动增加乌冬体力 2
    PAOPAO_COMP, // 泡泡合成材料 3
    TAITAN_STAMINA, // 被动增加泰坦体力 4
    EXTRA_TIP,// 额外小费 5
    TAITAN_STAMINA_2, // 主动增加泰坦体力 6
    PAOPAO_SPEEDUP, // 合成加速 7
    DOUBLE_CLAIM, // 双倍领取奖励 8
    GRANDMA_TIP,// 老奶奶乐队 9
    OFFLINE_EXTRA,// 离线额外奖励 10
    PRAISE_EXTRA,// 额外口碑 11
    YOYO,// 摇摇乐 12
    PAOPAO_STAMINA_1, // 主动增加泡泡体力 13
    PAOPAO_STAMINA_2, // 被动增加泡泡体力 14
    FRIST_AD,// 首看豪礼 15

    DOUBLE_ROLE = 101, //双倍来客 101
    FUGUI_MONEY, //富贵的私房钱 102
    BISCUITS, // 饼干资产不足提示 103
    CANDIES, // 糖果资产不足提示 104
    HOLIDAY_SIGN, // 节日活动补签 105
    NEW_YEAR_GAME, // 新年小游戏 106
    ELF_EGG_INCUBATE, // 快速孵化精灵蛋 107
}

export {
    PlatformType,
    UseBehaviorMsgType,
    UserBehaviorEventKeyType,
    UserAdSelectType,
    AdPropType
}