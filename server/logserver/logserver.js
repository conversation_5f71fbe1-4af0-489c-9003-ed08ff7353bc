require('ts-node').register();

const warpApp = require("./common/warpApp");
// let {clientConfig} = require("./common/cls/TencentCloudConfig");

warpApp.init((func) => {
    return async (req, res, next) => {
        try {
            let params = req.__data || (req.method == "GET" ? req.query : req.body);
            await func(params);
            res.json({});
        } catch (error) {
            next(error);
        }
    }
})

require("./http/LogSaver");