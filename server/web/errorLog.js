const warpApp = require("../common/warpApp");
const db = require("../db/db");
const response = require("./ResponseCode")

let clientErrorModel = db.createClientErrorModel();

warpApp.get("/getClientError", async ({uid, page, version, Num, type}) => {
    let pageNum = parseInt(page);
    let wherestr = {};
    if (uid) {
        wherestr.uid = uid;
    }
    wherestr.type = type || null
    if (version) {
        wherestr.version = version;
    } else {
        let doc = await clientErrorModel.findOne(wherestr).sort({'_id': -1})
        if (doc) {
            wherestr.version = doc.version
        }
    }
    if (!pageNum) {
        pageNum = 1;
    }
    Num = parseInt(Num);

    // let count = await clientErrorModel.countDocuments(wherestr);
    // let docs = await clientErrorModel.find(wherestr).skip((pageNum-1)*Num).limit(Num).sort("-timestamp");

    let docs = await clientErrorModel.find(wherestr).limit(10000);
    let hashMap = {}
    for (let doc of docs) {
        let exp = doc.exception
        if (!hashMap[exp]) hashMap[exp] = []
        hashMap[exp].push(doc)
    }
    let data = []
    for (let key in hashMap) {
        let docs = hashMap[key]
        let map = {}
        for (let doc of docs) {
            map[doc.uid] = true
        }
        data.push(Object.assign({
            count: docs.length,
            userCount: Object.keys(map).length,
            uids: Object.keys(map).slice(0, 10),
        }, docs[0].toObject()))
    }
    return response.SUCCESS_NO_DESC_WITH_RESULT(data);
})

warpApp.get("/deleteClientError", async ({version}) => {
    let wherestr = {version: {"$ne": version}};
    await clientErrorModel.deleteMany(wherestr);
    return response.SUCCESS_NO_RESULT();
})