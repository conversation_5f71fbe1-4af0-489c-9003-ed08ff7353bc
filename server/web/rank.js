const util = require("../common/util").default;
const warpApp = require("../common/warpApp");
const rankMod = require("../mod/rankMod");
const recordMod = require("../mod/recordMod");
const userMod = require("../mod/userMod");
const verifyMod = require("../mod/verifyMod");
const response = require('./ResponseCode')

warpApp.get("/getRankList", async({serverId, userType})=> {
    let key = rankMod.getRankName(serverId, userType)
    let rankList = await rankMod.getTop(key, 50)
    return response.SUCCESS_WITH_RESULT(rankList);
})

warpApp.get("/removeFromRank", async({uid}) => {
    await rankMod.remove(uid)
    await verifyMod.addBlack(uid)
    return response.SUCCESS_NO_RESULT();
})

warpApp.get("/getRankInfoByServerId", async({serverId, userType}) => {
    let key = rankMod.getRankName(serverId, userType)
    let topRankList = await rankMod.getTopInfo(key, 50)
    topRankList = await util.promiseMap(topRankList, async(info)=>{
        let userDoc = await userMod.findOne(info.uid)
        let record = await recordMod.getRecord(info.uid)
        if (userDoc) {
            info.signupTime = userDoc.signupTime
            info.playTime = userDoc.playTime
            info.loginTime = userDoc.loginTime
        }
        if (record) {
            info.watchVideoCount = recordMod.getFromRecord("global.accTotalAdCount", record) || 0
            info.biscuits = recordMod.getFromRecord("global.accTotalBiscuits", record) || 0
            info.candies = recordMod.getFromRecord("global.accTotalCandies", record) || 0
        }
        return info
    })

    return response.SUCCESS_WITH_RESULT(topRankList);
})

warpApp.get("/debug_getTop", async ({serverId}) => {
    let key = rankMod.getRankName(serverId)
    let rankList = await rankMod.getTop(key, 50)
    return response.SUCCESS_WITH_RESULT(rankList);
})



