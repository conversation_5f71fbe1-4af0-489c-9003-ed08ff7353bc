const jwt = require('jsonwebtoken');
const app = require("../common/app");
const warpApp = require("../common/warpApp");
const util = require('../common/util').default;
const cryptoHelper = require("../cypto/cryptoHelper");
const response = require('./ResponseCode')
const config = require('../config')
const db = require("../db/db");
let accountModel = db.createAccountModel();
const permissionC = require('./Permission');
const serverConfig = require("../config");
const {error} = require("winston");

const UserState = {
    NORMAL: 0, // 正常
    BAN: 1, //封禁
    UNAVAILABLE: 2, //未审核(不允许登录)
    DEL: 3, // 删除状态
};

// 这里可以去掉了
// (async function init() {
//     let info = {
//         userName: "root",
//         password: cryptoHelper.md5("twomiles2020bb_qFEreWFW4GI3"),
//         permission: await permissionC.getAllPermission(),
//         state: 0,
//     }
//     await accountModel.updateOne({userName: info.userName}, info, {upsert: true});
// })()

// 中转登陆验证
warpApp.post('/checkTransiLogin', async ({token, permission, id, name}) => {
    if (!token || !id || !name || !permission) {
        return response.ERROR_NO_RESULT();
    }
    let _o = await jwt.verify(token, serverConfig.token.SECRET, async (err, data) => {
        if (err) {
            return undefined;
        }
        return data;
    });
    if (!_o) {
        return response.ERROR_WITH_DESC_NO_RESULT('登陆检验失败');
    }
    if (_o && _o.permission) {
        try {// 兼容旧版本token
            let _parse = JSON.parse(_o.permission);
            if (typeof _parse === 'number') _parse = {"wd": String(_o.permission)}
            if (_parse && _parse['wd'] !== permission)
                return response.ERROR_WITH_DESC_NO_RESULT('登陆检验失败');
        } catch (e) {
            if (_o.permission !== permission)
                return response.ERROR_WITH_DESC_NO_RESULT('登陆检验失败');
        }
    }
    let now = Date.now();
    if (!_o.timestamp || now >= _o.timestamp) {
        return await response.SESSION_EXPIRED();
    }
    let rep = {
        token: token,
        _id: id,
        permission: permission,
        name: name
    }
    return response.SUCCESS_WITH_DESC_WITH_RESULT('', rep);
});

warpApp.get('/getAllAccount', async ({permission}) => {
    let docs = await accountModel.find({}, err => {
        if (err) {
            return response.SERVER_ERROR();
        }
    });
    for (let i = 0; i < docs.length; i++) {
        let doc = docs[i];
        doc.password = null;
        doc.token = null;
    }
    return response.SUCCESS_WITH_RESULT('查询完成!', docs);
})

warpApp.get('/resetPassword', async ({userName, password}) => {
    let wherestr = {
        userName: userName,
    };
    let md5Pwd = cryptoHelper.md5(password);
    await accountModel.findOneAndUpdate(wherestr, {password: md5Pwd}, err => {
        if (err) {
            return response.SERVER_ERROR();
        }
    })
    return response.SUCCESS_NO_RESULT();
})

// 弃用
warpApp.get('/setAccountPermission', async ({objId, permission}) => {
    let wherestr = {_id: objId};
    let updatestr = {permission: parseInt(permission)};
    accountModel.update(wherestr, updatestr, err => {
        if (err) {
            //数据库异常
            return response.SERVER_ERROR();
        }
    });
    return response.SUCCESS_WITH_DESC_NO_RESULT('修改成功！');
})

warpApp.get('/login', async ({userName, password}) => {
    if (!userName || !password || typeof password == "undefined") {
        return response.ERROR_WITH_DESC_NO_RESULT('用户名或密码错误');
    }
    password = cryptoHelper.md5(password);
    let wherestr = {userName: userName};

    let res = await accountModel.findOne(wherestr, err => {
        if (err) {
            return response.SERVER_ERROR();
        }
    });
    if (!res || res.password !== password) {
        return response.ERROR_WITH_DESC_NO_RESULT('用户名或密码错误');
    }
    // 账号检查状态
    let state = res.state;
    if (state === UserState.BAN) {
        return response.ERROR_WITH_DESC_NO_RESULT('账号被封禁,请联系相关工作人员.');
    }
    if (state === UserState.UNAVAILABLE) {
        return response.ERROR_WITH_DESC_NO_RESULT('账号未审核,请联系相关工作人员.');
    }
    if (state === UserState.DEL) {
        return response.ERROR_WITH_DESC_NO_RESULT('账号被删除,请联系相关工作人员.');
    }
    let id = res._id,
        permission = await permissionC.getAllPermission(),
        timestamp = Date.now() + config.token.OVER_TIME;
    // 生成token并更新
    let token = jwt.sign({id, permission, timestamp, userName}, config.token.SECRET);
    await accountModel.update(wherestr, {token}, err => {
        if (err) {
            return response.SERVER_ERROR();
        }
    })
    let data = {
        token: token,
        id: id,
        permission: permission
    }
    return response.SUCCESS_WITH_DESC_WITH_RESULT('登录成功!', data);
});

// 弃用
warpApp.get('/signup', async ({userName, password, permission}) => {
    let md5Pwd = cryptoHelper.md5(password);
    let doc = accountModel.findOne({userName: userName}, err => {
        if (err) {
            return response.SERVER_ERROR();
        }
    });
    if (doc) {
        return response.ERROR_WITH_DESC_NO_RESULT('账号已经存在!');
    } else {
        let account = new accountModel({
            userName: userName,
            password: md5Pwd,
            permission: permission,
        })
        account.save(function (err) {
            if (err) {
                return response.SERVER_ERROR();
            }
        });
        return response.SUCCESS_NO_RESULT();
    }
});


//<editor-fold desc="旧方法">

// app.use(function (req, res, next) {
//     let nowurl = req.originalUrl;
//     if (nowurl.indexOf("/login") != -1 || nowurl.indexOf("debug_") != -1) {
//         next();
//         return;
//     }
//     let idToken = req.headers.authorization;
//     if (!idToken) {
//         let response = {
//             status: -11,
//         };
//         res.send(JSON.stringify(response));
//         return;
//     }
//
//     jwt.verify(idToken, TOKEN_SECRET, function (err, decoded) {
//         if (!err) {
//             let the_name = decoded.name.slice(0, -6);
//             // let the_num = decoded.name.slice(-6);
//             accountModel.findOne({userName: the_name}, function (errs, doc) {
//                 if (errs) {
//                     console.log(errs);
//                     let response = {
//                         status: -14,
//                     };
//                     res.send(JSON.stringify(response));
//                 } else {
//                     if (doc && doc.token == idToken) {
//                         next();
//                     } else {
//                         let response = {
//                             status: -12,
//                         };
//                         res.send(JSON.stringify(response));
//                     }
//                 }
//             })
//         } else {
//             // console.log("Token 解析错误");
//             // console.warn(err);
//             let response = {
//                 status: -13,
//             };
//             res.send(JSON.stringify(response));
//         }
//     })
// })

// app.get('/getAllAccount', function (req, res) {
//     accountModel.find({}, function (err, docs) {
//         if (err) {
//             let response = {
//                 status: -101,
//             }
//             res.send(JSON.stringify(response));
//         } else {
//             for (let i = 0; i < docs.length; i++) {
//                 let doc = docs[i];
//                 doc.password = null;
//                 doc.token = null;
//
//             }
//
//             let response = {
//                 status: 0,
//                 data: docs,
//             }
//             res.send(JSON.stringify(response));
//         }
//     })
// });

// app.get('/resetPassword', function (req, res) {
//     let userName = req.query.userName;
//     let password = req.query.password;
//     let md5Pwd = cryptoHelper.md5(password);
//     let wherestr = {
//         userName: userName,
//     }
//     accountModel.findOneAndUpdate(wherestr, {password: md5Pwd}, function (err, doc) {
//         if (err) {
//             let response = {
//                 status: -101,
//             };
//             res.send(JSON.stringify(response));
//         } else {
//             let response = {
//                 status: 0,
//             }
//             res.send(JSON.stringify(response));
//         }
//     })
// })

// app.get('/setAccountPermission', function (req, res) {
//     let objId = req.query.objId;
//     let permissionC = req.query.permissionC;
//     let wherestr = {_id: objId};
//
//     let updatestr = {permissionC: parseInt(permissionC)};
//     accountModel.update(wherestr, updatestr, function (err, dbRes) {
//         if (err) {
//             //数据库异常
//             let response = {
//                 status: -101,
//             };
//             res.send(JSON.stringify(response));
//         } else {
//             // for (let i = 0; i < dbRes.length; i++) {
//             //     let dbData = dbRes[i];
//             //     dbData.password = null;
//             //     dbData.activeFbToken = null;
//             //     dbData.fbTokenList = null;
//             // }
//
//             let response = {
//                 status: 0,
//             };
//             res.send(JSON.stringify(response));
//         }
//     });
// });

// app.get('/login', function (req, res) {
//     let userName = req.query.userName;
//
//     if (!userName || !req.query.password || typeof req.query.password == "undefined") {
//         let response = {
//             status: -101,
//         };
//         res.send(JSON.stringify(response));
//     }
//
//     let password = cryptoHelper.md5(req.query.password);
//
//     let randomNum = util.getRandomString(6);
//
//     let wherestr = {userName: userName};
//     accountModel.findOne(wherestr, function (err, dbData) {
//         if (err) {
//             //数据库异常
//             let response = {
//                 status: -101,
//             };
//             res.send(JSON.stringify(response));
//         } else {
//             if (dbData) {
//                 if (dbData.password == password) {
//                     const token = jwt.sign({
//                         name: dbData.userName + randomNum
//                     }, TOKEN_SECRET, {
//                         expiresIn: 60 * 60 * 24 * 3
//                     });
//                     const dbToken = dbData.token;
//                     if (dbToken) {
//                         jwt.verify(dbToken, TOKEN_SECRET, function (errToke, decoded) {
//                             if (errToke) {
//                                 accountModel.updateOne(wherestr, {token: token}, function (errs, doc) {
//                                     if (errs) {
//                                         console.log(errs);
//                                         let response = {
//                                             status: -101,
//                                         };
//                                         res.send(JSON.stringify(response));
//                                     } else {
//                                         let response = {
//                                             status: 0,
//                                             token: token,
//                                             permissionC: dbData.permissionC
//                                         };
//                                         res.send(JSON.stringify(response));
//                                     }
//                                 });
//                             } else {
//                                 let response = {
//                                     status: 0,
//                                     token: dbToken,
//                                     permissionC: dbData.permissionC
//                                 };
//                                 res.send(JSON.stringify(response));
//                             }
//                         })
//                     } else {
//                         accountModel.updateOne(wherestr, {token: token}, function (errs, doc) {
//                             if (errs) {
//                                 console.log(errs);
//                                 let response = {
//                                     status: -101,
//                                 };
//                                 res.send(JSON.stringify(response));
//                             } else {
//                                 let response = {
//                                     status: 0,
//                                     token: token,
//                                     permissionC: doc.permissionC
//                                 };
//                                 res.send(JSON.stringify(response));
//                             }
//                         });
//                     }
//                 } else {
//                     //密码错误
//                     let response = {
//                         status: -2,
//                     };
//                     res.send(JSON.stringify(response));
//                 }
//             } else {
//                 //用户名不存在
//                 let response = {
//                     status: -1,
//                 };
//                 res.send(JSON.stringify(response));
//             }
//         }
//     });
// });

// app.get('/signup', function (req, res) {
//     let userName = req.query.userName;
//     let password = req.query.password;
//     let md5Pwd = cryptoHelper.md5(password);
//
//     let permissionC = parseInt(req.query.permissionC);
//     accountModel.findOne({userName: userName}, function (err, doc) {
//         if (err) {
//             console.log(err);
//             let response = {
//                 status: -101
//             }
//             res.send(JSON.stringify(response));
//         } else {
//             if (doc) {
//                 // console.log("账号已经存在");
//                 let response = {
//                     status: -1
//                 }
//                 res.send(JSON.stringify(response));
//             } else {
//                 let account = new accountModel({
//                     userName: userName,
//                     password: md5Pwd,
//                     permissionC: permissionC,
//                 })
//                 account.save(function (err) {
//                     if (err) {
//                         console.log(err);
//                         let response = {
//                             status: -101
//                         }
//                         res.send(JSON.stringify(response));
//                     } else {
//                         let response = {
//                             status: 0
//                         }
//                         res.send(JSON.stringify(response));
//                     }
//                 })
//             }
//         }
//     })
// })
//</editor-fold>
