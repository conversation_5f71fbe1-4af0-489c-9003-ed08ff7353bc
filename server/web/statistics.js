const util = require("../common/util").default;
const warpApp = require("../common/warpApp");

let db = require("../db/db");
const userMod = require("../mod/userMod");
let taskProgressCol = db.createTaskProgressModel()
const taskAvgCol = db.createTaskAvgModel();
const statisticsMod = require("../mod/statisticsMod")
const response = require("./ResponseCode");

warpApp.get("/getTaskInfo", async ({type, key}) => {
    // let [doc] = await taskProgressCol.find({type}).sort({_id: -1}).limit(1)
    // // let doc = await taskProgressCol.findOne({type, key})
    // if (!doc) {
    //     doc = {};
    // }
    // return response.SUCCESS_WITH_RESULT(doc);
    return {}
})

warpApp.get("/getCustomerTaskAvg", async () => {
    // let today = Math.floor(Date.now() / util.Time.Day);
    // if (new Date().getHours() >= 8) {
    //     today--;
    // }
    // let key = `date_${today}`
    // let [doc] = await taskProgressCol.find({type: 'customerTask', key}).sort({_id: -1}).limit(1);
    // let obj = JSON.parse(doc.result)
    // return response.SUCCESS_NO_DESC_WITH_RESULT(obj);
    return {}
})

warpApp.get("/getSpecialTask", async () => {
    // let today = util.getNumberDay();
    // while (true) {
    //     let key = `date_${today}`
    //     let [doc] = await taskProgressCol.find({type: 'specialTask', key}).sort({_id: -1}).limit(1);
    //     if (doc) {
    //         let obj = JSON.parse(doc.result)
    //         return response.SUCCESS_NO_DESC_WITH_RESULT(obj);
    //     }
    //     today--;
    //     if (util.getNumberDay() - today > 3) {
    //         break;
    //     }
    // }
    return response.SUCCESS_NO_DESC_WITH_RESULT({});
})

warpApp.get("/getFurnUsage", async ({day}) => {
    // let key = `date_${day}`
    // let [doc] = await taskProgressCol.find({type: 'furnitureUsageTask', key}).sort({_id: -1}).limit(1);
    // let r = {};
    // if (doc) {
    //     r.p = doc.done;
    //     r.fMap = JSON.parse(doc.result).allMap.fMap;
    // }
    // return response.SUCCESS_NO_DESC_WITH_RESULT(r);
    return {}
})

warpApp.get("/getUserLoginDaysAvg", async () => {
    // let type = "userLoginDaysTask"
    // let key = `userLoginDaysTask`
    // let [doc] = await taskProgressCol.find({type, key}).sort({_id: -1}).limit(1);
    // let r = doc && doc.result ? JSON.parse(doc.result) : {};
    // return response.SUCCESS_NO_DESC_WITH_RESULT(r);
    return {}
})
warpApp.get("/getWindAvg", async () => {
    // let type = "task_wind_avg"
    // let [doc] = await taskProgressCol.find({type});
    // if (!doc || (doc && doc.expireTime < Date.now())) {
    //     // 需要开始统计
    //     statisticsMod.r1101_a()
    //     return response.SUCCESS_NO_DESC_WITH_RESULT({gen: true});
    // }
    // if (doc && doc.done === 0) {
    //     // 正在开始统计 让前端等结果
    //     return response.SUCCESS_NO_DESC_WITH_RESULT({wait: true});
    // }
    // let r = doc && doc.result ? JSON.parse(doc.result) : {};
    // return response.SUCCESS_NO_DESC_WITH_RESULT({map: r});
    return {}
})

warpApp.get("/getCommonLevel", async ({type}) => {
    // let types = ['role', 'cinema', 'soup', 'dining'];
    // if (types.indexOf(type) === -1) {
    //     return response.ERROR_WITH_DESC_NO_RESULT('错误的类型查询');
    // }
    // let today = util.getNumberDay();
    // let _type = "commonLv";
    // let key = `date_${today}`
    // let [doc] = await taskProgressCol.find({type: `${_type}_${type}`, key}).sort({_id: -1}).limit(1);
    // let r = doc && doc.result ? JSON.parse(doc.result) : {};
    // return response.SUCCESS_NO_DESC_WITH_RESULT(r);
    return {}
})


warpApp.post('/startTask', async ({type, key}) => {
    // let doc = await taskProgressCol.findOne({type, key})
    // if (doc) {
    //     return response.ERROR_NO_RESULT();
    // }
    // if (type == "hearDis") {
    //     statisticsMod.heartDis()
    // }
    return response.SUCCESS_NO_RESULT();
});


const eventTable = db.createUmengModel();
warpApp.get("/getEventInfo", async ({eventKey, date}) => {
    // let doc = null
    // doc = await eventTable.find({})
    // if (doc) {
    //     return response.SUCCESS_WITH_RESULT(doc);
    // }
    return response.SUCCESS_NO_RESULT();
})
