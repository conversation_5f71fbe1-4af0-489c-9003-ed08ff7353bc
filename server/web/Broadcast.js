const warpApp = require("../common/warpApp");
const response = require('./ResponseCode')
const subscribeMod = require('../mod/subscribeMod')

const str = '推送信息保存成功,如果停留在Loading界面请自行刷新或者关闭页面.'

// 3fd273a4-bbf6-4e2b-b2a8-cdcd05f02d68
warpApp.post('/broadcast', async ({info, notifyTime, verLimit, loginTimeLimit}) => {
    let msgType = "BI_INITIATIVE_PUSH";
    if (!info.length) {
        return response.ERROR_WITH_DESC_NO_RESULT('数据错误');
    }
    let taskId = `BI_BROADCAST_${Date.now()}`;
    await subscribeMod.saveBroadcast_BI(taskId, 'all', notifyTime, msgType, verLimit, loginTimeLimit, "", "", info);
    let promise = subscribeMod.broadcastBiForAll(taskId, msgType, info, notifyTime, verLimit, loginTimeLimit);
    return response.SUCCESS_WITH_DESC_NO_RESULT(str);
});

warpApp.post('/broadcastToTarget', async ({title, uid, content, notifyTime}) => {
    let msgType = "BI_INITIATIVE_PUSH";
    if (!title || !uid || !content) {
        return response.ERROR_WITH_DESC_NO_RESULT('数据错误');
    }
    let taskId = `BI_BROADCAST_TARGET_UID_${Date.now()}`;
    await subscribeMod.saveBroadcast_BI(taskId, 'one', notifyTime, msgType, {}, {}, uid, title, content);
    let promise = subscribeMod.broadcastBiForOne(taskId, msgType, title, uid, content, notifyTime);
    return response.SUCCESS_WITH_DESC_NO_RESULT(str);
});

warpApp.post('/getBroadcastList', async () => {
    return response.SUCCESS_NO_DESC_WITH_RESULT(await subscribeMod.getBroadcastList());
});

warpApp.post('/reverseBroadcast', async ({taskId}) => {
    subscribeMod.removeTargetTaskIdSubscribe(taskId);
    return response.SUCCESS_NO_RESULT();
});