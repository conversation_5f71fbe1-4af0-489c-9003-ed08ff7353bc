const warpApp = require("../common/warpApp");
const rankMod = require("../mod/rankMod");
const userMod = require("../mod/userMod");
const verifyMod = require("../mod/verifyMod");
const config = require("../config")
const got = require('got')
const util = require('../common/util').default;
const redisKey = require("../db/redisKey");
let db = require("../db/db");
const recordMod = require("../mod/recordMod");
const response = require("./ResponseCode");
const {verfiyRecordMod} = require("../mod/verfiyRecordMod");
let redis = db.redis

warpApp.get("/getWhiteList", async () => {
    let data = await verifyMod.getWhiteList()
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.get("/addWhiteList", async ({uid}) => {
    await verifyMod.addWhite(uid)
    return response.SUCCESS_NO_RESULT();
})

warpApp.get("/delWhiteList", async ({uid}) => {
    await verifyMod.delWhite(uid)
    return response.SUCCESS_NO_RESULT();
})

warpApp.get("/getAllGM", async () => {
    let data = await verifyMod.getAllGM()
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.get("/getGM", async ({uid}) => {
    let data = await verifyMod.findGM(uid)
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.get("/addGM", async ({uid}) => {
    try {
        await verifyMod.addGM(uid)
        return response.SUCCESS_NO_RESULT();
    } catch (error) {
        return response.ERROR_NO_RESULT();
    }
})

warpApp.get("/updateGM", async ({active, nickName, uid}) => {
    let info = {}
    if (typeof active != 'object') {
        info.active = active
    }
    if (nickName) {
        info.nickName = nickName
    }
    await verifyMod.updateGM(uid, info)
    return response.SUCCESS_NO_RESULT();
})

warpApp.get("/delGM", async ({uid}) => {
    try {
        await verifyMod.delGM(uid)
        return response.SUCCESS_NO_RESULT();
    } catch (error) {
        return response.ERROR_NO_RESULT();
    }
})

warpApp.get("/addBlackList", async ({uid, reason, level}) => {
    let userDoc = await userMod.findOne(uid)
    if (!userDoc) return {status: -1}
    level = level || 4
    await verifyMod.addBlack(uid, reason, level)
    await rankMod.remove(uid)
    return response.SUCCESS_NO_RESULT();
})

warpApp.get("/getDiffRecordList", async ({uid, antistop}) => {
    let doc = await recordMod.getDiffRecordList(uid, antistop)
    return response.SUCCESS_WITH_RESULT(doc);
})

warpApp.get("/getBlackList", async ({uid, startTime, endTime, page, pageSize}) => {
    if (uid) {
        let doc = await verifyMod.getBlackById(uid)
        if (!doc) {
            return response.SUCCESS_WITH_RESULT({count: 0, data: []});
        }
        return response.SUCCESS_WITH_RESULT({count: 1, data: [doc]});
    } else {
        let {count, data} = await verifyMod.getBlackList(startTime, endTime, page, pageSize)
        return response.SUCCESS_WITH_RESULT({count, data})
    }
})

warpApp.get("/getBlackById", async ({uid}) => {
    let data = await verifyMod.getBlackById(uid)
    return response.SUCCESS_WITH_RESULT(data)
})

warpApp.get("/deleteBlackList", async ({uid}) => {
    let computeServer = config.computeServer
    if (!computeServer) {
        await verfiyRecordMod.deblock(uid, false, true);
        return response.SUCCESS_NO_RESULT()
    }
    let {ip, port} = computeServer
    let url = `http://${ip}:${port}/compute/deblockUser`
    let {status} = await got.post(url, {
        json: {
            uid
        },
        timeout: 10 * util.Time.Second
    }).json()
    return response.createResponseNoData(status, '');
})

warpApp.post("/debug_setSwtich", async ({type, active}) => {
    await verifyMod.setSwtich(type, active)
    return response.SUCCESS_NO_RESULT();
})

warpApp.post("/debug_recordLock", async ({uid, block, time}) => {
    let key = redisKey.recordUploadLock(uid)
    if (block) {
        await redis.set(key, 1)
        redis.pexpire(time * util.Time.Hour)
    } else {
        await redis.del(key)
    }
    return response.SUCCESS_NO_RESULT();
})

warpApp.post("/debug_setUserCanDeblock", async ({uid, can}) => {
    await verifyMod.setUserCanDeblock(uid, can)
    return response.SUCCESS_NO_RESULT();
})
