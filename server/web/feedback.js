const warpApp = require("../common/warpApp");
const response = require('./ResponseCode')
let db = require("../db/db");
const feedbackMod = require("../mod/feedbackMod");
const userMod = require("../mod/userMod");
let feedbackMsgCol = db.createFeedbackMsgModel()
let feedbackOrderCol = db.createFeedbackOrderModel()
const {FEED_BACK_SENDER_TYPE} = require('../common/constant');
const {ObjectId} = require("mongoose/lib/types");
const util = require("../common/util").default;
let blackListCol = db.createBlackListModel()
let loginInfoCol = db.createLoginInfoModel()

warpApp.get("/getFeedBackOrderList", async ({page, status, language, account, type, uid, content}) => {
    page = parseInt(page);
    status = parseInt(status);
    let pageSize = 20;
    let wherestr;
    if (type == undefined) type = 0
    if (status == 0) {
        wherestr = {
            '$or': [{status: status}, {status: null}],
        }
    } else {
        wherestr = {
            status: status,
        }
    }
    wherestr.type = type

    if (language) {
        wherestr.language = language;
    }
    uid && (wherestr.uid = uid);
    let count = 0;
    if (uid) {
        count = 1;
    } else {
        count = await feedbackOrderCol.countDocuments(wherestr);
    }
    let pageCount = Math.ceil(count / pageSize);
    if (page > pageCount) {
        page = Math.max(1, pageCount);
    }
    let docs;
    if (content) {
        let msgDocs = await feedbackMsgCol.find({content: {$regex: content}}, {orderId: 1});
        let orderIds = {};
        msgDocs.forEach(m => {
            orderIds[ObjectId(m.orderId)] = 1;
        })
        wherestr._id = {$in: Object.keys(orderIds)};
        count = await feedbackOrderCol.countDocuments(wherestr);
        pageCount = Math.ceil(count / pageSize);
        if (page > pageCount) {
            page = Math.max(1, pageCount);
        }
        docs = await feedbackOrderCol.find(wherestr).skip((page - 1) * pageSize).limit(pageSize).sort("-timestamp")
    } else {
        docs = await feedbackOrderCol.find(wherestr).skip((page - 1) * pageSize).limit(pageSize).sort("-timestamp")
    }
    let data = await util.promiseMap(docs, async (doc) => {
        let lastMsgDoc = await feedbackMsgCol.findOne({orderId: doc._id}).sort({timestamp: -1})
        let deviceId
        let updateTime
        if (type == 3) {
            let loginDoc = await loginInfoCol.findOne({uid: doc.uid}, {deviceId: 1})
            let userDoc = await userMod.findOne(doc.uid)
            if (loginDoc && userDoc) {
                updateTime = userDoc.updateTime
                if (lastMsgDoc.timestamp > updateTime) {
                    deviceId = loginDoc.deviceId
                }
            }
        }
        let blockReason = null
        let blockLevel = 0
        if (type == 1) {
            let blackDoc = await blackListCol.findOne({uid: doc.uid})
            if (blackDoc) {
                blockReason = blackDoc.reason
                blockLevel = blackDoc.level
            }
        }
        return {
            uid: doc.uid,
            timestamp: doc.timestamp,
            status: doc.status,
            account: doc.account,
            _id: doc._id,
            lastMsg: lastMsgDoc && lastMsgDoc.content,
            blockReason,
            blockLevel,
            deviceId,
            updateTime,
        }
    })
    let res = {
        status: 0,
        data: data,
        totalCount: count,
    }
    return response.SUCCESS_NO_DESC_WITH_RESULT(res);
})

warpApp.get('/getFeedBackMsgList', async ({uid, type}) => {
    let userDoc = await userMod.findOne(uid)
    let data = await feedbackMod.getMsgList(uid, type)
    if (userDoc) {
        data.forEach(element => {
            if (element.senderType == FEED_BACK_SENDER_TYPE.PLAYER) {
                element.nickName = userDoc.nickName
            }
        })
    }
    return response.SUCCESS_NO_DESC_WITH_RESULT(data);
});

warpApp.post("/replyFeedBack", (async ({uid, content, nickName, type}) => {
    let info = {content, senderType: FEED_BACK_SENDER_TYPE.SERVICE, nickName: nickName}
    await feedbackMod.sendMsg(uid, type, info)
    return response.SUCCESS_NO_RESULT();
}))

warpApp.get("/delFeedBackMsg", (async ({msgId}) => {
    await feedbackMod.delMsg(msgId)
    return response.SUCCESS_NO_RESULT();
}))


warpApp.post("/changeFeedBackOrder", async ({orderId, info}) => {
    await feedbackMod.updateOrderById(orderId, info)
    return response.SUCCESS_NO_RESULT();
})

// app.get("/updateFeedBackOrderStatus", function (req, res) {
//     let _id = req.query._id;
//     let status = parseInt(req.query.status);
//     let account = req.query.account;

//     let updateInfo = {
//         status: status
//     }

//     if (account) {
//         updateInfo.account = account;
//     }
//     feedbackModel.updateOne({ _id: _id }, updateInfo, function (err, doc) {
//         if (err) {
//             console.log(err)
//             let response = {
//                 status: -1
//             }
//             res.send(JSON.stringify(response));
//         } else {
//             let response = {
//                 status: 0
//             }
//             res.send(JSON.stringify(response));
//         }
//     })

// })