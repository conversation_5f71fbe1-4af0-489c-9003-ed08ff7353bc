const warpApp = require("../common/warpApp");
let db = require("../db/db");
const util = require("../common/util").default;;
const redisKey = require("../db/redisKey");
const shareMod = require("../mod/shareMod");
let dailyShareCol = db.createDailyShareModel();
let shareImageCol = db.createShareImageModel()
let redis = db.redis;
const response = require('./ResponseCode')

warpApp.get("/getShareImages", async()=> {
    // let docs = await shareImageCol.find({})
    // let data = docs.map((doc)=>{
    //     doc = doc.toObject()
    //     doc.score = shareMod.getScore(doc.clickCount, doc.newUserCount, doc.oldUserCount);
    //     doc.shareRate = doc.clickCount / doc.shareCount
    //     return doc
    // })
    // return response.SUCCESS_WITH_RESULT(data);
    return {}
})

warpApp.get("/getShareKings", async()=> {
    // let key = redisKey.shareClickRank()
    // let num = 100;
    // let ids = await redis.zrevrange(key, 0, num - 1)
    // let data = await util.promiseMap(ids, async(uid)=>{
    //     let docs = await dailyShareCol.find({uid})
    //     let shareCount = 0, clickCount = 0, newUserCount = 0, oldUserCount = 0;
    //     for (let doc of docs) {
    //         shareCount += doc.shareCount || 0;
    //         clickCount += doc.clickCount || 0;
    //         newUserCount += doc.newUserCount || 0;
    //         oldUserCount += doc.oldUserCount || 0;
    //     }
    //     let score = shareMod.getScore(clickCount, newUserCount, oldUserCount);
    //     return {uid, shareCount, clickCount, newUserCount, oldUserCount, score}
    // })
    // return response.SUCCESS_WITH_RESULT(data);
    return {}
})

warpApp.get("/getShareDetail", async(uid)=> {
    let data = await dailyShareCol.find({uid})
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.post("/debug_invite", async({uids, sourceUid})=>{
    for (let uid of uids) {
        await shareMod.updateInviteInfo(uid, sourceUid, {isNew: true})
    }
})

warpApp.get("/debug_delInvite", async({uid})=>{
    await shareMod.delInvite(uid)
})

warpApp.get("/debug_getInvite", async({uid})=>{
    let data = await shareMod.getInviteInfo(uid, 0)
    return response.SUCCESS_WITH_RESULT(data);
})


