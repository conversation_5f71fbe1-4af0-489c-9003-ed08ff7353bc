const util = require("../common/util").default;
const warpApp = require("../common/warpApp");
const response = require('./ResponseCode')
let db = require("../db/db");
const userMod = require("../mod/userMod");
let motherMsgCol = db.createMotherMsgModel()

warpApp.get("/getMotherMsg", async ({ page, pageSize, status, minLen}) => {
    page = parseInt(page);
    if (status != null) {
        status = parseInt(status);
    }
    pageSize = parseInt(pageSize)

    let wherestr = {}
    if (status != null) {
        wherestr.status = status
    }
    if (minLen != null) {
        wherestr.contentLen = {
            $gte: minLen
        }
    }

    let count = await motherMsgCol.countDocuments(wherestr);
    let pageCount = Math.ceil(count / pageSize);
    if (page > pageCount) {
        page = Math.max(1, pageCount);
    }
    let docs = await motherMsgCol.find(wherestr).skip((page - 1) * pageSize).limit(pageSize).sort("-_id")

    if (status == 3) {
        docs = await util.promiseMap(docs, async(doc)=>{
            let {nickName} = await userMod.getNameAndAvatar(doc.uid) || {}
            let obj = doc.toObject()
            if (nickName) obj.nickName = nickName
            return obj
        })
    }

    let res = {
        data: docs,
        totalCount: count,
    }
    return response.SUCCESS_NO_DESC_WITH_RESULT(res);
})

warpApp.post('/changeMotherMsg', async ({ _id, info}) => {
    await motherMsgCol.updateOne({_id}, info)
    return response.SUCCESS_NO_RESULT();
});