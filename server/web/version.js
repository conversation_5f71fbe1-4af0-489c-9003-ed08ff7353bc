const logger = require("../common/log").getLogger("version");
const app = require("../common/app");
const util = require("../common/util").default;
const warpApp = require("../common/warpApp");

const db = require("../db/db");
const loginMod = require("../mod/loginMod");
const hotUpdateCol = db.createHotUpdateModel();
const bigVersionUpdateCol = db.createBigVersionUpdateModel();
const versionMod = require('../mod/versionMod');
const response = require("./ResponseCode");

warpApp.get('/getBigVersionInfo', async () => {
    let data = await bigVersionUpdateCol.find({}) || [];
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.get('/deleteBigVersionInfo', async ({_id}) => {
    await bigVersionUpdateCol.deleteOne({_id: _id});
    return response.SUCCESS_NO_RESULT();
})

warpApp.post('/changeBigVersionInfo', async (info) => {
    const {_id, platform} = info;
    if (_id) {
        wherestr = {_id};
    } else {
        wherestr = {platform};
    }
    await bigVersionUpdateCol.updateOne(wherestr, info, {upsert: true});
    return response.SUCCESS_NO_RESULT();
})

warpApp.get('/getHotUpdateVersion', async ({platform}) => {
    let data = await versionMod.getHotUpdateInfo(platform);
    data = await util.promiseMap(data, async (doc) => {
        let info = doc.toObject();
        info.releaseNum = await versionMod.getHotUpdateNum(platform, info.version);
        return info;
    })
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.post('/releaseHotUpdateVersion', async (info) => {
    await versionMod.releaseHotUpdateVersion(info);
    return response.SUCCESS_NO_RESULT();
})

warpApp.get('/rollbackHotUpdateVersion', async ({platform}) => {
    await versionMod.rollbackHotUpdateVersion(platform);
    return response.SUCCESS_NO_RESULT();
})

warpApp.post('/changeHotUpdateInfo', async (info) => {
    const {_id} = info;
    delete info._id;
    delete info.__v;
    await hotUpdateCol.updateOne({_id}, info);
    return response.SUCCESS_NO_RESULT();
})


warpApp.get('/updateLoginVersion', async (info) => {
    let succ = await loginMod.updateLoginGameVersion(info)
    return response.SUCCESS_WITH_RESULT({succ});
})

warpApp.get('/getLoginVersion', async () => {
    let data = await loginMod.getLoginGameVersion()
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.get('/getHotUpdateVersionHistory', async ({platform, num = 10}) => {
    let data = await versionMod.getNearVersionInfo(platform, num);
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.get('/compareVersion', async ({platform,versionA, versionB}) => {
    if (!versionA || !versionB) return response.ERROR_NO_RESULT();
    let data = await versionMod.compareVersionSize(platform,versionA, versionB);
    if (!data[0]) return response.ERROR_WITH_DESC_NO_RESULT(data[1]);
    return response.SUCCESS_WITH_RESULT(data[1]);
})