const jwt = require('jsonwebtoken');
const serverConfig = require('../config');
const response = require("./ResponseCode");
const {ObjectId} = require("mongoose/lib/types");

// 对应 two-miles-games 用来给前端解码权限的 暂未使用
const dex = [116, 119, 111, 45, 109, 105, 108, 101, 115, 45, 103, 97, 109, 101, 115];

// [server] 是否开启对token的db检测,即每一次请求发送上来的token都会与数据库存放的匹配,主要是应对账号删除后就无法操作后台了
// 同时也会增加数据库负担
const check_db_token = false;


// [server & client] 对于未注册拦截的，是否直接放行,保持false即可
const force = false;
// [server & client] 对于拦截的url,如果最终没找到配置是否放行,保持true即可,否则会出现没配置且未忽略的接口返回权限不足
const sure = true;

// index 默认从0开始，按顺序++
// url : 访问拦截地址可以是全路径或者父路径 不要重复
// name : 对象唯一名
// matchAll : 是否同时拦截子路径,如果url是'/manager' 并且matchAll为true,那么访问 '/manager/aa/bb' 都会被拦截进行权限控制
//             matchAll 不会向上拦截, 即 '/manager/aa/bb' 不会拦截 '/manager/bb/xx' 和 '/manager/aa/cc'
//             由上到下逐级匹配：访问'/manager/aa/bb' 时 ,如果配置了'/manager' 或者 '/manager/aa '时，则会优先拦截权限
// open : 是否开启拦截
// extends : 扩展参数列表
// [server & client]
const got = require("got");

/**
 * 如果使用数字存储权限，也只能最大支持到64位权限，要想控制到量级可能不够
 * 所以采用二进制形式的字符串 "000000000000000000000000000000..."
 * 即便是支持几百种权限数据量也不会太大
 */
class Permission {

    constructor() {
        console.log("====================== Module Permission Load ======================")
        this.views = [];
        // 需要排除的接口,比如说register 和login 这种注册接口是不会携带permission访问的
        // 排除的接口是优先触发的
        this.excludeUrls = ['/login', '/register', '/debug_*', '/checkTransiLogin'];
        this.checkAndLoad();
        if (check_db_token) {
            this.db = require("../db/db");
            this.permissionCol = this.db.createAccountModel();
        }
    }

    checkAndLoad() {
        got.get(serverConfig.permission_url, {
            timeout: 10 * 1000,
        }).then(r => {
            if (r.statusCode === 200) {// don't use try-catch,it should be "fast fail".
                let resp = r.body;
                resp && (resp = JSON.parse(resp));
                resp.data && (resp = JSON.parse(resp.data));
                for (const v of resp) {
                    if (this.views.find(t => t.name === v.name || t.url === v.url)) {
                        console.error("Duplicate View :" + JSON.stringify(v))
                        console.error("====================== Module Permission Load Failed ======================")
                        return;
                    }
                    this.views.push(v);
                }
                for (const v of this.views) {
                    v.name = v.name.trim();
                    v.url = v.url.trim();
                }
                console.log("====================== Module Permission Load SUCCESS ======================")
            }
        })
    }

    /**
     * 增加权限
     * @param p 旧权限
     * @param index 目标位置
     * @returns {Promise<string>}
     */
    async add(p, index) {
        if (await this.is(p, index)) {
            return p;//已经有权限了
        }
        let pArray = p.split('');
        pArray[index] = '1';
        return pArray.toString().replaceAll(',', '');
    }

    // 移除权限
    async del(p, index) {
        if (await this.is(p, index)) {
            let pArray = p.split('');
            pArray[index] = '0';
            return pArray.toString().replaceAll(',', '');
        }
        return p;// 无权限则不用移除
    }

    /**
     * 获取指定位置的权限
     * @param p
     * @param index
     * @returns {Promise<string>}
     */
    async get(p, index) {
        return !p[index] ? '0' : p[index];
    }

    /**
     * 判断指定位置是否拥有权限 不会返回undefined 或者null
     * @param p
     * @param index
     * @returns {Promise<boolean>}
     */
    async is(p, index) {
        return !p[index] ? false : (await this.get(p, index) === '1');
    }

    // 混淆秘钥输出成字符串
    async dexToString() {
        return String.fromCharCode.apply(String, dex);
    }

    /**
     * 获取url 是否开启了拦截
     * @param url
     * @returns {Promise<boolean>}
     */
    async urlIsIntercept(url) {
        let x = this.views.find(t => t.url === url)
        if (x) return !x.open;
        return sure;
    }

    /**
     * 用默认配置的name来判断是否可以访问或者操作
     * 局限性较大,多数情况可能不知道name
     * @param name
     * @param permission
     * @returns {Promise<boolean>}
     */
    async isViewCanAccessByName(name, permission) {
        if (!name || !permission) {
            return false;
        }
        for (const v of this.views) {
            if (v.name === name) {
                const i = this.views.indexOf(v);
                return await this.is(permission, i);
            }
        }
        return false;
    }

    async urlJust(url, permission) {
        if (!url || !permission) {
            return false;
        }
        if (await this.urlIsIntercept(url)) {
            return true;
        }
        for (const v of this.views) {
            if (v.url === url) {
                const i = this.views.indexOf(v);
                return await this.is(permission, i);
            }
        }
        return false;
    }

    /**
     * 用默认配置的url来判断是否可以访问或者操作
     * @param url
     * @param permission
     * @returns {Promise<boolean>}
     */
    async isViewCanAccessByUrl(url, permission) {
        if (this.excludeUrls.indexOf(url) > -1) {
            return true;
        }
        let paths = url.split('/');
        if (paths.length > 1) {
            let temp = '';
            for (let p of paths) {
                if (!p) {
                    continue;
                }
                temp = `${temp}/${p}`;
                if (!await this.urlJust(temp, permission)) {
                    return false;
                }
            }
        }
        return await this.urlJust(url, permission);
    }

    // 全权限
    async getAllPermission() {
        return await this.getDefaultPermission('1');
    }

    // 0权限
    async getNonePermission() {
        return await this.getDefaultPermission('0');
    }

    /**
     * 获取一个默认的权限
     * @returns {Promise<string>}
     */
    async getDefaultPermission(o) {
        let p = '';
        for (let v of this.views) {
            p += o;
        }
        return p;
    }

    /**
     * 合并权限  将p合并到target里面
     * 如p是 '000101' target是 '111111111'
     * 结果为 '000101111'
     * @param p
     * @param target
     * @returns {Promise<*>}
     */
    async merge(p, target) {
        if (!target) return p;
        if (!p) return target;
        if (p.length === target.length) {
            return p;
        }
        if (p.length > target.length) {
            return p;
        }
        if (p.length < target.length) {
            return p + target.slice(p.length);
        }
    }

    /**
     * 判断url是否注册拦截
     * @returns {Promise<boolean>}
     */
    async hasUrlRegister(url) {
        let i = 0;
        for (let v of this.views) {
            if (v.url === url) {
                i++;
            }
        }
        return i === 0;
    }

    /**
     * 处理url 防止GET方式携带参数进入判断
     * @param url
     * @returns {Promise<void>}
     */
    async dealUrl(url) {
        let netSymbol = url.indexOf("//")
        if (netSymbol > -1) {
            url = url.substring(netSymbol+2)
            url = url.substring(url.indexOf("/"))
        }
        if (!url.startsWith('/')) {
            console.log(`error with url :${url}`)
        }
        if (url.indexOf('?') !== -1) {
            url = url.substring(0, url.indexOf('?'));
        }
        return url;
    }

    async checkExcludeUrl(url) {
        url = await this.dealUrl(url);
        let arr = permission.excludeUrls.filter(u => {
            if (u.length <= url.length) {
                let x = 0;
                for (let i = 0; i < u.length; i++) {
                    if (u[i] === "*") x++;
                    if (u[i] === url[i]) x++;
                    if (x === u.length - 1)
                        return u;
                }
            }
        })
        return arr && arr.length > 0;
    }

    /**
     * 服务器统一检测入口
     * 返回值如果为空则检测通过，否则返回一个封装好的非法请求
     * @param url
     * @param token
     * @returns {Promise<*>}
     */
    async permissionCheck(url, token) {
        url = await this.dealUrl(url);
        if (await permission.checkExcludeUrl(url)) {
            return null;
        }
        if (!url || !token) {
            return await response.INSUFFICIENT_PERMISSIONS();
        }
        // token验证
        if ((typeof token === 'undefined' || !token)) {
            return await response.SESSION_EXPIRED();
        }
        try {
            // 没注册的直接放行
            if (await this.hasUrlRegister(url) && force) {
                return null;
            } else {
                // 检测token
                let _o = jwt.verify(token, serverConfig.token.SECRET);
                let now = Date.now();
                if (!_o.timestamp || now >= _o.timestamp) {
                    // token 过期 重新登录
                    return await response.SESSION_EXPIRED();
                }
                // db检测
                // if (check_db_token) {
                //     let doc = await this.permissionCol.findOne({_id: ObjectId(_o.id)});
                //     if (!(doc && doc.token === token)) {
                //         // token 不一致了 其他地方登陆过
                //         return await response.ACCOUNT_LOGIN();
                //     }
                // }
                let permission = _o.permission.toString();
                permission.indexOf("{") > -1 && (permission = JSON.parse(permission));
                permission = permission['wd'] || permission;
                // 检测权限
                if (!await this.isViewCanAccessByUrl(url, permission)) {
                    return await response.INSUFFICIENT_PERMISSIONS();
                }
            }
        } catch (err) {
            return await response.SESSION_EXPIRED();
        }
    }

}

const permission = new Permission();
module.exports = permission;



