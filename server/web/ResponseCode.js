/**
 * 响应编码
 */

class Res {
    constructor(status, desc) {
        this.status = status;
        this.desc = desc;
    }
}

let ResponseCode = {
    _SUCCESS: new Res(0, ''),
    _ERROR: new Res(2, ''),
    _ILLEGAL_REQUEST: new Res(3, '非法请求'),
    _INSUFFICIENT_PERMISSIONS: new Res(4, '权限不足'),
    _SESSION_EXPIRED: new Res(5, '会话过期,请重新登录'),
    _SERVER_ERROR: new Res(5, '服务器500'),
    _ACCOUNT_LOGIN: new Res(5, '该账号在其他地方登陆,请重新登录.'),
};
/*  SUCCESS CODE START */
// 默认的 _SUCCESS, 不携带其他数据
ResponseCode.SUCCESS_NO_RESULT = function () {
    return ResponseCode.createResponseData(ResponseCode._SUCCESS, null);
};
// 默认的 _SUCCESS, 携带其他数据
ResponseCode.SUCCESS_WITH_RESULT = function (data) {
    return ResponseCode.createResponseData(ResponseCode._SUCCESS, data);
};
// 用desc组成一个新的 _SUCCESS 状态, 不携带其他数据
ResponseCode.SUCCESS_WITH_DESC_NO_RESULT = function (desc) {
    return ResponseCode.createResponseData({status: 0, desc}, null);
};
// 用desc组成一个新的 _SUCCESS 状态, 携带其他数据
ResponseCode.SUCCESS_WITH_DESC_WITH_RESULT = function (desc, data) {
    return ResponseCode.createResponseData({status: 0, desc}, data);
};
//  _SUCCESS 状态, 携带其他数据不返回desc
ResponseCode.SUCCESS_NO_DESC_WITH_RESULT = function (data) {
    return ResponseCode.createResponseData({status: 0, desc: ''}, data);
};

/*  SUCCESS CODE END */

/*  ERROR CODE START */
// 默认的 _ERROR, 不携带其他数据
ResponseCode.ERROR_NO_RESULT = function () {
    return ResponseCode.createResponseData(ResponseCode._ERROR, null);
};
// 默认的 _ERROR, 携带其他数据
ResponseCode.ERROR_WITH_RESULT = function (data) {
    return ResponseCode.createResponseData(ResponseCode._ERROR, data);
};
// 用desc组成一个新的 _ERROR 状态, 不携带其他数据
ResponseCode.ERROR_WITH_DESC_NO_RESULT = function (desc) {
    return ResponseCode.createResponseData({status: 2, desc}, null);
};
// 用desc组成一个新的 _ERROR 状态, 携带其他数据
ResponseCode.ERROR_WITH_DESC_WITH_RESULT = function (desc, data) {
    return ResponseCode.createResponseData({status: 2, desc}, data);
};
/*  ERROR CODE END */

ResponseCode.ILLEGAL_REQUEST = function () {
    return ResponseCode.createResponseData(ResponseCode._ILLEGAL_REQUEST, null);
};

ResponseCode.INSUFFICIENT_PERMISSIONS = function () {
    return ResponseCode.createResponseData(ResponseCode._INSUFFICIENT_PERMISSIONS, null);
}
// 会话过期
ResponseCode.SESSION_EXPIRED = function () {
    return ResponseCode.createResponseData(ResponseCode._SESSION_EXPIRED, null);
}
// 账号在其他地方登陆
ResponseCode.ACCOUNT_LOGIN = function () {
    return ResponseCode.createResponseData(ResponseCode._ACCOUNT_LOGIN, null);
}


// 服务器500
ResponseCode.SERVER_ERROR = function () {
    return ResponseCode.createResponseData(ResponseCode._SERVER_ERROR, null);
}

ResponseCode.createResponseData = function (status, data) {
    let res = {};
    res.status = status.status;
    res.desc = status.desc;
    if (data) {
        res.data = JSON.stringify(data);
    }
    return res;
}
ResponseCode.createResponseNoData = function (status) {
    let res = {};
    res.status = status.status;
    res.desc = status.desc;
    return res;
}

module.exports = ResponseCode;
