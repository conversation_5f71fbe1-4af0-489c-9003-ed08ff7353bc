const db = require("../db/db");
let redeemCodeModel = db.createRedeemCodeModel()
const app = require("../common/app");
const util = require("../common/util").default;
const warpApp = require("../common/warpApp");
const redisKey = require("../db/redisKey");
const response = require('./ResponseCode')
let redis = db.redis
let redeemCodeHistoryModel = db.createRedeemCodeHistoryModel()
const {redeemCodeMod} = require("../mod/redeemCodeMod")
const userMod = require("../mod/userMod");
const cryptoHelper = require("../cypto/cryptoHelper");
let compensationRewardHistoryCol = db.createCompensationRewardHistory()
let compensationRewardCol = db.createCompensationReward()
let customerCdkPlanCol = db.createCustomerCdkPlanModel();

warpApp.post('/deleteCodeTemplate', async ({name}) => {
    let r = await customerCdkPlanCol.deleteOne({name});
    let d = response.ERROR_WITH_DESC_NO_RESULT(r);
    r && r.deletedCount && (d = response.SUCCESS_WITH_DESC_NO_RESULT("删除成功!"))
    return d;
})

warpApp.post('/saveCodeTemplate', async ({name, template}) => {
    let err = '';
    name && (name = name.trim());
    !err && !name && (err = '名称错误');
    !err && !template && (err = '奖励模板错误');
    let doc = await customerCdkPlanCol.find({name});
    doc.length && (err = '名称重复啦!');
    if (err) return response.ERROR_WITH_DESC_NO_RESULT(err);
    let save = new customerCdkPlanCol({name, template});
    await save.save();
    return response.SUCCESS_WITH_DESC_NO_RESULT("保存成功!");
})
warpApp.post('/getCodeTemplates', async () => {
    return response.SUCCESS_NO_DESC_WITH_RESULT(await customerCdkPlanCol.find({}));
})


warpApp.get('/createCode', async ({
                                      type,
                                      value,
                                      date,
                                      unique,
                                      validTime,
                                      verified,
                                      account,
                                      diyCode,
                                      permission,
                                      num
                                  }) => {
    date = parseInt(date);
    validTime = parseInt(validTime)
    permission = parseInt(permission) || 3;

    let data = [];

    for (let i = 0; i < num; i++) {
        let success = await created();
        if (!success) {
            return {status: -1}
        }
    }
    return response.SUCCESS_WITH_RESULT(data);

    async function created() {
        let code = util.getRandomString(10);
        if (diyCode) {
            code = diyCode
        }
        let wherestr = {
            code: code,
        }

        let doc = await redeemCodeModel.findOne(wherestr)
        if (doc) {
            if (diyCode) {
                return false
            } else {
                return await created();
            }
        }
        let model = new redeemCodeModel({
            type: type,
            value: value,
            date: date,
            code: code,
            unique: unique,
            verified: verified,
            account: account,
            permission: permission,
            validTime: validTime,
        })
        let his = new redeemCodeHistoryModel({
            code: code,
            type: type,
            value: value,
            date: date,
            unique: unique,
            verified: verified,
            account: account,
            permission: permission,
            validTime: validTime,
        })
        await model.save()
        await his.save();

        data.push({
            type: type,
            value: value,
            code: code,
            validTime: validTime,
        })
        return true;
    }
})

warpApp.get('/setUserCodeStatus', async ({uid, add}) => {
    let redis = db.redis
    let key = redisKey.reddemCode(code)
    if (add) {
        await redis.sadd(key, uid)
    } else {
        await redis.srem(key, uid)
    }
    return response.SUCCESS_NO_RESULT();
})
warpApp.get('/passCode', async ({code}) => {
    let wherestr = {code: code,}
    await redeemCodeModel.findOneAndUpdate(wherestr, {verified: true}, function (err) {
        if (err) {
            return response.ERROR_WITH_DESC_NO_RESULT('');
        }
    })
    return response.SUCCESS_NO_RESULT();
});
warpApp.get('/changeCodeState', async ({code}) => {
    let wherestr = {code: code,}
    await redeemCodeModel.findOneAndUpdate(wherestr, {dispatched: true}, function (err) {
        if (err) {
            return response.ERROR_WITH_DESC_NO_RESULT('');
        }
    })
    return response.SUCCESS_NO_RESULT();
});
warpApp.get('/deleteCode', async ({code}) => {
    let wherestr = {code: code,}
    await redeemCodeModel.findOneAndDelete(wherestr, function (err) {
        if (err) {
            return response.ERROR_WITH_DESC_NO_RESULT('');
        }
    })
    return response.SUCCESS_NO_RESULT();
});
warpApp.get('/getOneCode', async ({code}) => {
    let doc = await redeemCodeModel.findOne({code: code}, err => {
        if (err) {
            return response.ERROR_NO_RESULT();
        }
    });
    if (doc) {
        let resData = {};
        resData.code = doc.code;
        resData.date = doc.date;
        resData.type = doc.type;
        resData.value = doc.value;

        let unique = doc.unique;
        if (!unique || unique <= 1) {
            unique = 0;
        }

        if (unique === 0) {
            redeemCodeModel.remove({code: code}, function (err, doc_u) {
                if (err) {
                    return response.ERROR_NO_RESULT();
                } else {
                    return response.SUCCESS_NO_DESC_WITH_RESULT(resData);
                }
            })
        } else {
            redeemCodeModel.updateOne({code: code}, {"$inc": {"unique": -1}}, function (err, doc_u) {
                if (err) {
                    return response.ERROR_NO_RESULT();
                } else {
                    return response.SUCCESS_NO_DESC_WITH_RESULT(resData);
                }
            })
        }
    }
    return response.ERROR_WITH_DESC_NO_RESULT('未找到');
});

warpApp.get('/getAllCode', async ({page, verified, permission, accountName, sTime, eTime, code, his}) => {
    let wherestr = {};
    if (permission === 0) {
        wherestr.account = accountName;
    }
    if (permission === 1) {
        wherestr['$or'] = [
            {account: accountName}, {permission: 0}
        ]
    }
    let num = 300;
    wherestr.verified = verified;
    if (sTime && eTime) {
        wherestr.date = {$lte: eTime, $gte: sTime};
    }
    code && (code = code.trim());
    if (code) {
        wherestr.code = code;
    }
    let count;
    !his && (count = await redeemCodeModel.countDocuments(wherestr)) || (count = await redeemCodeHistoryModel.countDocuments(wherestr))
    let pageCount = Math.ceil(count / num);
    if (page > pageCount) {
        page = pageCount;
    } else if (page < 1) {
        page = 1;
    }
    let docs;
    if (his) {
        docs = await redeemCodeHistoryModel.find(wherestr).skip((page - 1) * num).limit(num).sort("-date");
    } else {
        docs = await redeemCodeModel.find(wherestr).skip((page - 1) * num).limit(num).sort("-date");
    }
    docs = await util.promiseMap(docs, async (doc) => {
        let code = doc.code
        doc = doc.toObject()
        if (!his) {
            let key = redisKey.reddemCode(code)
            let count = await redis.scard(key)
            if (count <= 0) {
                let redeemCodeHistoryCol = db.createRedeemCodeHistoryModel()
                let historyDoc = await redeemCodeHistoryCol.find({code})
                if (historyDoc && historyDoc.count) {
                    count = historyDoc.count
                }
            }
            doc.count = count || 0
        }
        return doc
    })
    let data = {
        data: docs,
        pageCount: pageCount,
    }
    return response.SUCCESS_NO_DESC_WITH_RESULT(data);
})

warpApp.get('/getCodeHistory', async ({page, verified, permission, accountName}) => {
    let wherestr = {};

    if (permission === 0) {
        wherestr.account = accountName;
    }
    if (permission === 1) {
        wherestr['$or'] = [
            {account: accountName}, {permission: 0}
        ]
    }
    let num = 30;
    wherestr.verified = verified;
    let nowTime = '' + (new Date()).valueOf();
    wherestr.date = {$lt: nowTime}
    let count = await redeemCodeHistoryModel.countDocuments(wherestr)
    let pageCount = Math.ceil(count / num);
    if (page > pageCount) {
        page = pageCount;
    } else if (page < 1) {
        page = 1;
    }
    let docs = await redeemCodeHistoryModel.find(wherestr).skip((page - 1) * num).limit(num).sort("-date")
    let data = {
        data: docs,
        pageCount: pageCount,
    }
    return response.SUCCESS_NO_DESC_WITH_RESULT(data);
})

warpApp.post('/addCompensationReward', async ({type, rewards, account, sendType, uidData, languages, ver, limit}) => {
    if (!rewards) {
        return response.ERROR_WITH_DESC_NO_RESULT('奖励列表 不正确!');
    }
    uidData = JSON.parse(uidData);
    languages = JSON.parse(languages);
    rewards = rewards.trim();
    limit = Number(limit) || 0

    let langKeys = Object.keys(languages);
    let lang = {};
    for (let key of langKeys) {
        lang[languages[key]["lang"]] = languages[key]["content"];
    }
    if (!Object.keys(lang).length) {
        return response.ERROR_WITH_DESC_NO_RESULT('文案列表不正确!');
    }
    let save = async () => {
        if (sendType === 1) {
            let info = await cryptoHelper.pkDecrypt(uidData.uid);
            let uid = info ? info.decStr : uidData.uid;
            if (uid) {
                await userMod.addCompensationRewardMultiple(uid, rewards, JSON.stringify(lang), ver, type, limit);
            } else {
                return response.ERROR_WITH_DESC_NO_RESULT('uid 解码失败!');
            }
        } else if (sendType === 2) {
            let _data = JSON.parse(uidData['uidStr']);
            for (let _f of _data) {
                let uids = _f['str'].split("\n");
                for (let uid of uids) {
                    uid = uid.replace("\r", "");
                    let info = await cryptoHelper.pkDecrypt(uid);
                    let _uid = info && info.decStr;
                    _uid = _uid || uid;
                    await userMod.addCompensationRewardMultiple(_uid, rewards, JSON.stringify(lang), ver, type, limit);
                }
            }
        } else if (sendType === 3) {
        }
    }

    save();
    return response.SUCCESS_NO_RESULT();
})
warpApp.get('/getCompensationRewardList', async ({uid, currentPage, sTime, eTime}) => {
    let max = 100;
    let wherestr = {};
    if (uid) {
        wherestr.uid = uid;
    }
    if (sTime && eTime) {
        wherestr.timestamp = {$lte: eTime, $gte: sTime};
    }
    let totalCount = await compensationRewardHistoryCol.find(wherestr).countDocuments();
    let pageCount = Math.ceil(totalCount / max) || 1;
    currentPage = currentPage > pageCount ? pageCount : currentPage;
    let docs = await compensationRewardHistoryCol.find(wherestr).skip((currentPage - 1) * max).limit(max).sort("-timestamp");
    docs = await util.promiseMap(docs, async (doc) => {
        if (!doc.uid || !doc.timestamp) return
        let isf = await userMod.isCompensationRewardReceived(doc.uid, doc.timestamp);
        let o = {
            uid: doc.uid,
            type: doc.type,
            reward: doc.reward,
            timestamp: doc.timestamp,
            needVer: doc.needGameVer,
            lang: doc.lang
        };
        o.candelete = isf;
        return o;
    });
    let data = {
        data: docs,
        pageCount: pageCount
    }
    return response.SUCCESS_WITH_RESULT(data);
})

warpApp.get('/deleteCompensationReward', async ({uid, timestamp}) => {
    if (!uid || !timestamp) {
        return response.ERROR_WITH_DESC_NO_RESULT('params error!');
    }
    // 删除发奖记录时,先删除逻辑发奖表的数据，再删除记录
    // 首先保证该记录还没有被领取,否则不予删除,类似于撤回，只能撤回,只能撤回没被领取的奖励记录
    let isf = await userMod.isCompensationRewardReceived(uid, timestamp);
    if (!isf) {
        return response.ERROR_WITH_DESC_NO_RESULT('奖励已经被玩家领取,不能撤销!');
    }
    let hasError = false;
    await compensationRewardCol.deleteOne({uid, timestamp}, {}, err => {
        if (err) {
            hasError = true;
        }
    });
    if (hasError) {
        return response.SERVER_ERROR();
    }
    await compensationRewardHistoryCol.deleteOne({uid, timestamp}, {}, err => {
        if (err) {
            hasError = true;
        }
    });
    return hasError ? response.SERVER_ERROR() : response.SUCCESS_WITH_DESC_NO_RESULT('删除成功!');
})


// app.get('/passCode', function (req, res) {
//     let code = req.query.code;
//
//     let wherestr = {
//         code: code,
//     }
//     redeemCodeModel.findOneAndUpdate(wherestr, {verified: true}, function (err) {
//         if (err) {
//             console.log(err);
//             let response = {
//                 status: -101
//             }
//             res.send(JSON.stringify(response));
//         } else {
//             let response = {
//                 status: 0
//             }
//             res.send(JSON.stringify(response));
//         }
//     })
//
// });

// app.get('/changeCodeState', function (req, res) {
//     let code = req.query.code;
//     let wherestr = {
//         code: code,
//     }
//     redeemCodeModel.findOneAndUpdate(wherestr, {dispatched: true}, function (err) {
//         if (err) {
//             console.log(err);
//             let response = {
//                 status: -101
//             }
//             res.send(JSON.stringify(response));
//         } else {
//             let response = {
//                 status: 0
//             }
//             res.send(JSON.stringify(response));
//         }
//     })
// })

// app.get('/deleteCode', function (req, res) {
//     let code = req.query.code;
//
//     let wherestr = {
//         code: code,
//     }
//
//     redeemCodeModel.findOneAndDelete(wherestr, function (err) {
//         if (err) {
//             console.log(err);
//             let response = {
//                 status: -101
//             }
//             res.send(JSON.stringify(response));
//         } else {
//             let response = {
//                 status: 0
//             }
//             res.send(JSON.stringify(response));
//         }
//     })
// });

// app.get('/getOneCode', function (req, res) {
//     function responseSuccess(data) {
//         response = {
//             data: data,
//             status: 0,
//         }
//         res.send(JSON.stringify(response));
//     }
//
//     function responseError(status) {
//         response = {
//             status: status,
//         }
//         res.send(JSON.stringify(response));
//     }
//
//     let code = req.query.code;
//
//     redeemCodeModel.findOne({code: code}, function (err, doc) {
//         if (err) {
//             responseError(-101);
//         } else {
//             if (doc) {
//                 let resData = {};
//                 resData.code = doc.code;
//                 resData.date = doc.date;
//                 resData.type = doc.type;
//                 resData.value = doc.value;
//
//                 let unique = doc.unique;
//                 if (!unique || unique <= 1) {
//                     unique = 0;
//                 }
//
//                 if (unique === 0) {
//                     redeemCodeModel.remove({code: code}, function (err, doc_u) {
//                         if (err) {
//                             responseError(-101);
//                         } else {
//                             responseSuccess(resData);
//                         }
//                     })
//                 } else {
//                     redeemCodeModel.updateOne({code: code}, {"$inc": {"unique": -1}}, function (err, doc_u) {
//                         if (err) {
//                             responseError(-101);
//                         } else {
//                             responseSuccess(resData);
//                         }
//                     })
//                 }
//             } else {
//                 responseError(-100);
//             }
//         }
//     })
// })

// app.get("/getAllCode", async (req, res) => {
//     let page = req.query.page;
//     let verified = req.query.verified;
//     let permission = parseInt(req.query.permission);
//     let accountName = req.query.accountName;
//
//     let wherestr = {};
//
//     if (permission == 0) {
//         wherestr.account = accountName;
//     }
//     if (permission == 1) {
//         wherestr['$or'] = [
//             {account: accountName}, {permission: 0}
//         ]
//     }
//     let num = 300;
//     wherestr.verified = verified;
//     let nowTime = '' + (new Date()).valueOf();
//     wherestr.date = {$lt: nowTime}
//     let count = await redeemCodeModel.countDocuments(wherestr)
//     let pageCount = Math.ceil(count / num);
//     if (page > pageCount) {
//         page = pageCount;
//     } else if (page < 1) {
//         page = 1;
//     }
//     let docs = await redeemCodeModel.find(wherestr).skip((page - 1) * num).limit(num).sort("-date")
//     docs = await util.promiseMap(docs, async (doc) => {
//         let code = doc.code
//         doc = doc.toObject()
//         let key = redisKey.reddemCode(code)
//         let count = await redis.scard(key)
//         if (count <= 0) {
//             let redeemCodeHistoryCol = db.createRedeemCodeHistoryModel()
//             let historyDoc = await redeemCodeHistoryCol.find({code})
//             if (historyDoc && historyDoc.count) {
//                 count = historyDoc.count
//             }
//         }
//         doc.count = count || 0
//         return doc
//     })
//     let response = {
//         status: 0,
//         data: docs,
//         pageCount: pageCount,
//     }
//     res.send(JSON.stringify(response));
// });

// app.get("/getCodeHistory", async (req, res) => {
//     let page = req.query.page;
//     let verified = req.query.verified;
//     let permission = parseInt(req.query.permission);
//     let accountName = req.query.accountName;
//
//     let wherestr = {};
//
//     if (permission == 0) {
//         wherestr.account = accountName;
//     }
//     if (permission == 1) {
//         wherestr['$or'] = [
//             {account: accountName}, {permission: 0}
//         ]
//     }
//     let num = 30;
//     wherestr.verified = verified;
//     let nowTime = '' + (new Date()).valueOf();
//     wherestr.date = {$lt: nowTime}
//     let count = await redeemCodeHistoryModel.countDocuments(wherestr)
//     let pageCount = Math.ceil(count / num);
//     if (page > pageCount) {
//         page = pageCount;
//     } else if (page < 1) {
//         page = 1;
//     }
//     let docs = await redeemCodeHistoryModel.find(wherestr).skip((page - 1) * num).limit(num).sort("-date")
//     let response = {
//         status: 0,
//         data: docs,
//         pageCount: pageCount,
//     }
//     res.send(JSON.stringify(response));
// });

warpApp.get('/getCodePlans', async () => {
    let plans = await redeemCodeMod.getPlans()
    return response.SUCCESS_WITH_RESULT(plans);
})

warpApp.post('/saveCodePlan', async (info) => {
    await redeemCodeMod.savePlan(info)
    return response.SUCCESS_NO_RESULT();
})
