const warpApp = require("../common/warpApp");
const response = require('./ResponseCode')
const roomMod = require("../mod/roomMod");
const userMod = require("../mod/userMod");
const recordMod = require("../mod/recordMod");
const verifyMod = require("../mod/verifyMod");
const rankMod = require("../mod/rankMod");
const db = require("../db/db");
const {ObjectId} = require("mongoose/lib/types");
const ModelMgr = require("../client/ModelMgr").default;
const ClsRequest = require("../logserver/common/cls/ClsRequest").default;
const cls = new ClsRequest()
const functionMod = require('../mod/functionMod');

// 精选方案 相关接口
/**
 * @param rooms 房间列表 [201,202,301...]
 * @param state 1:已精选
 * @param limit={min: 0, max:9999}
 * @param page 多少页
 * @param pageSize 每页多少条数据
 */
warpApp.post("/debug_roomList", async ({rooms, state, limit, page, pageSize, uid, _id, startTime, endTime}) => {
    if (startTime > endTime) return [];
    return await roomMod.getRoomList3_0(rooms, state, limit, page, pageSize, uid, _id, startTime, endTime);
})

// 改变方案状态
warpApp.post("/debug_changePlanState", async ({uid, roomId, state}) => {
    return await roomMod.changePlanState3_0(uid, roomId, state);
})

// 玩家数据查询 -> cls
warpApp.post("/doClsQuery", async ({type = 1, uid, Context, From, To, platform, msgType, key, Query}) => {
    let config = await cls.getTopicConfig();
    config = type === 1 ? config.eventLog : config.watchAd;
    let _ret = await cls.doSearch(Context, config.TopicId, From, To, Query);
    return response.SUCCESS_WITH_RESULT(_ret);
})
// 批量封号
warpApp.post("/batchBlackList", async ({uids, reason}) => {
    let logic = async function () {
        try {
            for (let uid of uids) {
                let userDoc = await userMod.findOne(uid)
                if (!userDoc) continue;
                let isBlack = await verifyMod.isBlack(uid);
                if (isBlack) continue;
                let level = 1;
                reason = reason || {};
                await verifyMod.addBlack(uid, reason, level)
                await rankMod.remove(uid)
            }
        } catch (e) {
            console.error('Common.js', e)
        }
    }
    logic().then();
    return response.SUCCESS_NO_RESULT();
})
// 获取用户的房间和方案信息
warpApp.post("/getPlanData", async ({uid}) => {
    let record = await recordMod.getRecord(uid);
    if (!record) return response.ERROR_NO_RESULT();
    let modelMgr = new ModelMgr(), data = [];
    modelMgr.init(record);
    let keFangs = modelMgr.world.getKefangs();
    if (!keFangs) return response.SUCCESS_WITH_RESULT(data);
    for (let mod of keFangs) {
        let roomId = mod['no'], unlockFurns = mod['unlockFurns'] || [], furnSaves = mod['furnSaves'] || [],
            temp = {};
        temp.id = roomId, temp.name = roomId, temp.children = [];
        for (let furnSave of furnSaves) {
            temp.children.push({id: furnSave.id, name: furnSave.name || '未命名方案'});
        }
        data.push(temp);
    }
    return response.SUCCESS_WITH_RESULT(data);
})
warpApp.post("/copyPlanData", async ({uid, _uid, plans}) => {
    let target = _uid;
    if (!uid || !target || !plans.length) return response.ERROR_WITH_DESC_NO_RESULT('数据错误');
    let record1 = await recordMod.getRecord(uid);
    if (!record1) return response.ERROR_WITH_DESC_NO_RESULT('玩家没有存档数据,请先上传存档.');
    let record2 = await recordMod.getRecord(target);
    if (!record2) return response.ERROR_WITH_DESC_NO_RESULT('玩家没有存档数据,请先上传存档.');
    for (let plan of plans) {
        let roomId = plan.roomId;
        let room1 = recordMod.getFromRecord(`kefang_${roomId}`, record1);
        let room2 = recordMod.getFromRecord(`kefang_${roomId}`, record2);
        if (!room1 || !room2) return response.ERROR_WITH_DESC_NO_RESULT(`room:${roomId} 不存在`);
        let unlockFurns1 = room1['unlockFurns'] || [], furnSaves1 = room1['furnSaves'] || [],
            unlockFurns2 = room2['unlockFurns'] || [], furnSaves2 = room2['furnSaves'] || []
        // 合并解锁的家具列表 unlockFurns1 和 unlockFurns2
        unlockFurns2 = unlockFurns2.concat(unlockFurns1.filter(e => {
            return !unlockFurns2.filter(t => {
                return t.id === e.id;
            }).length;
        }));
        // 将方案放在默认位置
        let default_pos = furnSaves1.filter(e => {
            return e.id === plan.id;
        })
        default_pos = default_pos[0] || null;
        default_pos && (furnSaves2[0] = default_pos);
    }

    await recordMod.setRecord(target, record2);
    await userMod.updateOne(target, {forceDownload: true});
    return response.SUCCESS_WITH_DESC_NO_RESULT("复制成功!");
})

warpApp.post("/savePlanData", async ({uid, roomId, planId, planName}) => {
    if (!uid || !roomId || !planId || !planName) return response.ERROR_WITH_DESC_NO_RESULT('数据错误');
    let record = await recordMod.getRecord(uid);
    if (!record) return response.ERROR_WITH_DESC_NO_RESULT('玩家没有存档数据,请先上传存档.');
    let data = {},
        room = recordMod.getFromRecord(`kefang_${roomId}`, record),
        furnSaves = room['furnSaves'];
    data.unlockFurns = room['unlockFurns'];
    data.furnSave = furnSaves.filter(e => {
        return e.id === planId;
    })[0];
    let saver = new roomFurnSaveCol({
        originalData: JSON.stringify({uid, roomId, planId, planName}),
        planName,
        info: JSON.stringify(data),
        timestamp: Date.now()
    })
    await saver.save();
    return response.SUCCESS_WITH_DESC_NO_RESULT("成功!");
})

warpApp.post("/loadPlanData", async ({}) => {
    let docs = await roomFurnSaveCol.find({}, {planName: 1, originalData: 1});
    return response.SUCCESS_WITH_RESULT(docs);
})


warpApp.post("/restorePlanData", async ({uid, planId}) => {
    let record = await recordMod.getRecord(uid),
        plan = await roomFurnSaveCol.findOne({_id: ObjectId(planId)}, {info: 1, originalData: 1});
    if (!record) return response.ERROR_WITH_DESC_NO_RESULT('用户不存在或者用户没有线上存档.');
    if (!plan) return response.ERROR_WITH_DESC_NO_RESULT('没有找到该方案.');
    let mod = plan.info, originalData = plan.originalData;
    mod = JSON.parse(mod), originalData = JSON.parse(originalData);
    let roomId = originalData.roomId, unlockFurns = mod.unlockFurns, furnSave = mod.furnSave;
    let room = recordMod.getFromRecord(`kefang_${roomId}`, record);
    if (!room) return response.ERROR_WITH_DESC_NO_RESULT(`room:${roomId} 不存在,未解锁!`);

    room['unlockFurns'] = room['unlockFurns'].concat(unlockFurns.filter(e => {
        return !room['unlockFurns'].filter(t => {
            return t.id === e.id;
        }).length;
    }));
    // 将方案放在默认位置
    room['furnSaves'][0] = furnSave;
    await recordMod.setRecord(uid, record);
    await userMod.updateOne(uid, {forceDownload: true});
    return response.SUCCESS_WITH_DESC_NO_RESULT('完成!');
})

warpApp.post("/setFunctionOpen", async ({funcName, open, extend}) => {
    return await functionMod.setOpen(funcName, open, extend) ? response.SUCCESS_WITH_DESC_NO_RESULT('成功!') : response.ERROR_WITH_DESC_NO_RESULT('错误的功能名');
})
warpApp.post("/isFunctionOpen", async ({funcName}) => {
    return response.SUCCESS_NO_DESC_WITH_RESULT(await functionMod.get(funcName));
})

let roomFurnSaveCol = db.createRoomFurnSaveModel();