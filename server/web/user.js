const util = require("../common/util").default;
;
const warpApp = require("../common/warpApp");
const db = require("../db/db");
const recordMod = require("../mod/recordMod");
const userMod = require("../mod/userMod");
const loginMod = require("../mod/loginMod");
const idMod = require("../mod/idMod");
const config = require("../config")
const response = require("./ResponseCode");
const { copy } = require("request/lib/helpers");
const verifyMod = require("../mod/verifyMod");
const accountMod = require("../mod/accountMod");
const logger = require("../common/log").getLogger("user")
const { USER_TYPE } = require('../common/constant')
const { asyncWarp } = require("../common/middleware");
const currencyModel = require("../mod/currencyModel");
const activityMod = require("../mod/activityMod");
const got = require("got");

let userCol = db.createUserModel();
let countInfoModel = db.createCountInfoModel();
let serverInfoModel = db.createServerInfoModel();
let serverLimitUserModel = db.createServerLimitUserModel();
let recordSnapshootModel = db.createRecordSnapshootModel()
let userHeartCol = db.createStatUserHeartModel()
let backupUserDataCol = db.createBackupUserDataModel();
const catchUsersCol = db.createCatchUserModel();
const balanceCol = db.createCurrencyBalanceModel()
const orderCol = db.createCurrencyOrderModel()
const specialActivityCol = db.createSpecialActivityModel()

warpApp.get("/searchUserInfo", async ({ heart, day, uid, starSum, nickName, page, tpid, isGetBlack = 0 }) => {
    uid && (uid = uid.trim());
    nickName && (nickName = nickName.trim());
    tpid && (tpid = tpid.trim());

    let num = 10;
    let wherestr = {};
    if (tpid) {
        uid = await idMod.getID(tpid)
        if (!uid) {
            wherestr.openid = tpid
        }
    }
    if (uid) {
        wherestr.uid = uid;
    }
    if (nickName) {
        wherestr.diyName = nickName;
    }
    if (heart || day) {
        heart = parseInt(heart), day = parseInt(day);
        heart <= 0 && (heart = 0);
        day <= 0 && (day = 0);
        let _count = 5, data = [], where = {}, lastId = 0, limit = 5;
        heart && day && (limit = 1000, _count = 1);
        while (data.length < _count) {
            lastId && (where._id = { $gt: lastId });
            let docs;
            if (heart && !lastId) {
                docs = await userHeartCol.find({ heart: { $gte: heart } }).sort("-heart").limit(limit);
            } else {
                docs = await userHeartCol.find(where).sort("-heart").limit(limit);
            }
            for (let doc of docs) {
                if (doc && doc.heart > heart) continue;
                let ud = await userMod.findOne({ uid: doc.uid }), info = {};
                if (!ud) continue;
                if (day && ud.loginTime > Date.now() - util.Time.Day * day) continue;
                info = ud.toObject()
                info.record = await recordMod.getRecord(doc.uid);
                data.push(info);
                if (heart && day) break;
            }
            if (docs.length > 0) {
                lastId = docs[docs.length - 1]._id;
            }
        }
        return response.SUCCESS_WITH_RESULT({
            data: data,
            pageCount: 1
        })
    }
    let userDocs, count, pageCount
    if (config.debug) {
        count = await userCol.countDocuments(wherestr);
        if (count == 0) {
            return { status: 0 }
        }
        pageCount = Math.ceil(count / num);
        if (page > pageCount) {
            page = pageCount;
        } else if (page < 1) {
            page = 1;
        }
        userDocs = await userCol.find(wherestr).skip((page - 1) * num).limit(num).sort("-signupTime");
    } else {
        pageCount = 1
        userDocs = await userCol.find(wherestr).limit(5);
    }
    isGetBlack = Number(isGetBlack);
    let data = await util.promiseMap(userDocs, async (doc) => {
        let info = doc.toObject();
        info.isBlack = await verifyMod.getBlackLevel(uid);
        info.isGetBlack = isGetBlack ? info.isBlack : !1;
        info.record = info.isGetBlack ? await recordMod.getRecordBlack(doc.uid) : await recordMod.getRecord(doc.uid);
        info.act = await activityMod.getRewardAndLimit(uid) || {}
        return info;
    })
    return response.SUCCESS_WITH_RESULT({
        data: data,
        pageCount: pageCount,
    });
});

warpApp.get('/clearRecord', async ({ uid }) => {
    recordMod.changeRecord(uid, (record) => {
        return {};
    }, true)
    return response.SUCCESS_NO_RESULT();
});

warpApp.get("/deleteUser", async ({ uid, type }) => {
    //TODO 0819 删除玩家账号更改为注销用户  但是这个到底是立即删除还是冷静期删除 需要考虑设计
    let user = await userMod.findOne(uid);
    if (!user) return response.ERROR_NO_RESULT();
    type = Number(type);
    // 小程序的还是保持原样
    if (type === 1) {
        await userMod.deleteUser(uid);
        // await accountMod.save({uid}, 0);
        return response.SUCCESS_NO_RESULT();
    } else {
        await accountMod.save({ uid }, 7);
    }
    return response.SUCCESS_NO_RESULT();
})

warpApp.post('/editRecord', async ({ uid, record }) => {
    await recordMod.setRecord(uid, record);
    await userMod.updateOne(uid, { forceDownload: true })
    return response.SUCCESS_NO_RESULT();
})

warpApp.post('/changeUserInfo', async (info) => {
    await userMod.updateOne(info.uid, info)
    return response.SUCCESS_NO_RESULT();
})

warpApp.post('/debug_changeUserInfo', async (info) => {
    await userMod.updateOne(info.uid, info)
    return response.SUCCESS_NO_RESULT();
})

warpApp.get("/addRankServer", async () => {
    let doc = await countInfoModel.findOneAndUpdate({ name: "serverCount" }, { $inc: { count: 1 } }, {
        upsert: true,
        new: true
    });
    return response.SUCCESS_WITH_RESULT({ count: doc.count });
})

warpApp.get('/changeServerLimitUser', async ({ limitCount }) => {
    await serverLimitUserModel.findOneAndUpdate({}, { limitCount: limitCount }, { upsert: true });
    return response.SUCCESS_NO_RESULT();
})

warpApp.get('/getServerLimitUser', async () => {
    let doc = await serverLimitUserModel.findOne({});
    let data = {
        status: 0
    }
    if (doc) {
        data.limitCount = doc.limitCount
    }
    return response.SUCCESS_WITH_RESULT(data);
})

let getServerInfo_time = Date.now() - util.Time.Hour, activeDataMap = {};
warpApp.get("/getServerInfo", async () => {
    let limitUsedoc = await serverLimitUserModel.findOne({});
    let dbRes = await countInfoModel.findOne({ name: 'serverCount' });
    let serverCount = 1;
    if (dbRes) {
        serverCount += dbRes.count;
    }
    let dataMap = {}, serverlist = [];
    let activeTime = Date.now() - util.Time.Day * 7;
    for (let i = 0; i < serverCount; i++) {
        serverlist.push(i);
    }
    let doc = await serverInfoModel.find({ serverId: { '$in': serverlist } });
    if (doc && doc.length) {
        for (let j in doc) {
            dataMap[doc[j].serverId] = doc[j].userCount;
        }

        function findActive(sid) {
            if (sid >= serverCount) {
                return;
            }
            userCol.countDocuments({ loginTime: { '$gt': activeTime }, serverId: sid })
                .exec(function (err1, docCount1) {
                    if (err1) {
                        console.log(err1);
                    } else {
                        activeDataMap[sid] = docCount1;
                        findActive(sid + 1);
                    }
                })
        }

        let now = Date.now();
        let data = {
            status: 0,
            serverCount: serverCount,
            data: dataMap,
            activeData: activeDataMap,
        }
        if (limitUsedoc) {
            data.limitCount = limitUsedoc.limitCount
        }
        if (now - getServerInfo_time > util.Time.Hour) {
            getServerInfo_time = Date.now();
            start = Date.now();
            // findActive(0);
        }
        return response.SUCCESS_WITH_RESULT(data);
    } else {
        let data = {
            serverCount: serverCount,
        }
        if (limitUsedoc) {
            data.limitCount = limitUsedoc.limitCount
        }
        return response.SUCCESS_WITH_RESULT(data);
    }
})

warpApp.get("/getUid", async ({ uid }) => {
    return response.SUCCESS_WITH_RESULT({ uid });
})

warpApp.get("/newUser", async ({ uid, tpid, copyId }) => {
    let userDoc
    if (uid) {
        userDoc = await userMod.findOne(uid);
        if (userDoc) {
            return response.ERROR_NO_RESULT();
        }
        userDoc = await loginMod.signup({ uid, tpid });
    }

    if (copyId) {
        let success = await recordMod.copy(copyId, userDoc.uid)
        if (!success) {
            return response.ERROR_NO_RESULT();
        }
    }
    return response.SUCCESS_NO_RESULT();
})

warpApp.get("/copyUser", async ({ uid, copyId, snapshootId }) => {
    let userDoc = await userMod.findOne(uid);
    if (!userDoc) {
        return response.ERROR_NO_RESULT();
    }

    let success = false
    if (copyId) {
        success = await recordMod.copy(copyId, userDoc.uid)
    } else if (snapshootId) {
        success = await recordMod.copyBySnapshoot(snapshootId, userDoc.uid)
    }

    if (!success) {
        return response.ERROR_NO_RESULT();
    }
    await userMod.updateOne(uid, { forceDownload: true })
    return response.SUCCESS_NO_RESULT();
})
warpApp.get("/moveUser", async ({ uid, _uid, del, white, moveAct }) => {
    if (uid === _uid) {
        return response.ERROR_WITH_DESC_NO_RESULT('账号相同,不需要迁移.');
    }
    // 禁止低蜡烛向高蜡烛同步
    let doc1 = await userHeartCol.findOne({ uid });
    let doc2 = await userHeartCol.findOne({ uid: _uid });
    if (doc1 && doc2 && (doc1.heart < doc2.heart)) {
        return response.ERROR_WITH_DESC_NO_RESULT('禁止低蜡烛向高蜡烛账号覆盖');
    }
    return await userMod.MoveUsr(uid, _uid, del, white, moveAct)
})
warpApp.post("/getUserBackupData", async () => {
    // 不给返回data数据，查看时再查询
    let doc = await backupUserDataCol.find({}, { data: 0 });
    return response.SUCCESS_NO_DESC_WITH_RESULT(doc);
})
warpApp.post("/getUserBackupDataInfo", async ({ _id }) => {
    // 只返回data
    let doc = await backupUserDataCol.find({ _id }, { data: 1 });
    return response.SUCCESS_NO_DESC_WITH_RESULT(doc);
})

warpApp.post("/restoreUserData", async ({ _id }) => {
    let r = await userMod.restoreUserData(_id);
    return r ? response.SUCCESS_WITH_DESC_NO_RESULT("还原成功") : response.ERROR_WITH_DESC_NO_RESULT('还原失败');
})

warpApp.get("/debug_copy", async ({ uid, tpid }) => {
    if (!uid && tpid) {
        uid = await idMod.getID(tpid)
    }
    if (!uid) {
        return response.ERROR_WITH_DESC_NO_RESULT('用户不存在');
    }
    let [userDoc, recordDoc, activityDoc] = await Promise.all([userMod.findOne(uid), recordMod.findOne(uid), activityMod.getActivityData(uid)])
    let idDocs = []
    if (userDoc) {
        idDocs = await Promise.all([idMod.getFromDB(userDoc.unionid), idMod.getFromDB(userDoc.openid)])
        idDocs = idDocs.filter(m => m != null)
    }
    return response.SUCCESS_WITH_RESULT({ userDoc, recordDoc, activityDoc, idDocs });
})

warpApp.get("/debug_getUidByTpid", async ({ tpid, tpid2 }) => {
    let uid = await idMod.getID(tpid)
    if (!uid) {
        uid = await idMod.getID(tpid2)
    }
    return { uid }
})


warpApp.get("/recordSnapshoots", async ({ type }) => {
    type = parseInt(type) || 0;
    let where = {}
    if (type !== 0) {
        where = { type }
    } else {
        //TODO type暂定 0和1 为了兼容之前的存档数据,只要不是类型1的默认都是类型0
        where = { 'type': { $ne: 1 } }
    }
    let docs = await recordSnapshootModel.find(where, { recordBuff: 0 })
    return response.SUCCESS_WITH_RESULT({ snapshoots: docs });
})

warpApp.post("/addRecordSnapshoot", async (info) => {
    let record = await recordMod.getRecord(info.copyId)
    if (!record) {
        return response.ERROR_NO_RESULT();
    }
    let recordBuff = await recordMod.zipRecord(record)
    let doc = await recordSnapshootModel.findOne({}, { uid: 1 }).sort({ uid: -1 })
    let uid = 1
    if (doc) {
        uid = parseInt(doc.uid) + 1
    }
    let updateInfo = {
        name: info.name,
        heart: recordMod.getStarSum(record),
        type: info.queryType,
        sourceUid: info.copyId,
        createTime: Date.now(),
        recordBuff,
    }
    let docs = await recordSnapshootModel.updateOne({ uid }, updateInfo, { upsert: true })
    return response.SUCCESS_WITH_RESULT({ snapshoots: docs });
})

warpApp.post("/deleteRecordSnapshoot", async ({ uid }) => {
    await recordSnapshootModel.deleteOne({ uid })
    return response.SUCCESS_NO_RESULT();
})

warpApp.post("/deblockSwitchDevice", async ({ uid }) => {
    await recordMod.switchDevice(uid)
    return response.SUCCESS_NO_RESULT();
})

warpApp.post("/getCatchUsers", async ({ }) => {
    let doc = await catchUsersCol.find({});
    return response.SUCCESS_NO_DESC_WITH_RESULT(doc);
})
warpApp.post("/tagCatchUsers", async ({ _id }) => {
    let doc = await catchUsersCol.findOne({ _id });
    if (doc) {
        await catchUsersCol.updateOne({ _id }, { count: 0, editCount: doc.count });
        return response.SUCCESS_NO_RESULT();
    }
    return response.ERROR_WITH_DESC_NO_RESULT('没得这个id');
})
warpApp.post("/fixUser", async ({ uid }) => {
    if (!uid) return response.ERROR_WITH_DESC_NO_RESULT('没有这个uid');
    let record = await recordMod.getRecord(uid)
    if (!record) return response.ERROR_WITH_DESC_NO_RESULT('没有这个uid');
    try {
        recordMod.fixRecordOnDownload(record)
        await recordMod.setRecord(uid, record)
    } catch (e) {
        return response.ERROR_WITH_DESC_NO_RESULT("修复失败，可能是这个玩家存档没有问题!");
    }
    return response.SUCCESS_WITH_DESC_NO_RESULT("修复成功!");
})

warpApp.post("/webGetTotalPay", async ({ uid }) => {
    if (!uid) return { status: -1 };
    let pay = await currencyModel.getUserTotalPay(uid);
    return response.SUCCESS_WITH_RESULT(pay)
})

const NewMoveUsrData = {
    "1": "http://101.34.207.48:8040",
    "2": "http://101.34.207.48:8052",
}

warpApp.get("/newMoveUser", async ({ uid, _uid, url }) => {
    if (uid === _uid) {
        return response.ERROR_WITH_DESC_NO_RESULT('账号相同,不需要迁移.');
    }

    const target = await userMod.findOne(_uid)
    if (!target) return response.ERROR_WITH_DESC_NO_RESULT('目标账号不存在!');

    let link = NewMoveUsrData[url]
    if (!link) return response.ERROR_WITH_DESC_NO_RESULT('迁移地址不存在!');
    const sUrl = `${link}/debug_copy?uid=${uid}`
    let { status, data } = await got(sUrl, {
        timeout: 5 * util.Time.Second,
    }).json();
    if (!data) return response.ERROR_WITH_DESC_NO_RESULT('迁移失败,没有数据返回');
    let userDoc, recordDoc, idDocs, activityDoc
    data = JSON.parse(data)
    userDoc = data.userDoc
    recordDoc = data.recordDoc
    activityDoc = data.activityDoc
    idDocs = data.idDocs
    if (!userDoc) return response.ERROR_WITH_DESC_NO_RESULT('迁移失败,没有用户数据');
    if (!recordDoc) return response.ERROR_WITH_DESC_NO_RESULT('迁移失败,没有存档数据');
    const promiseList = []
    const baseInfo = {}
    if (userDoc.playTime) baseInfo.playTime = userDoc.playTime
    if (userDoc.signupTime) baseInfo.signupTime = userDoc.signupTime
    if (userDoc.likeNum) baseInfo.likeNum = userDoc.likeNum
    promiseList.push(userMod.updateOne(_uid, baseInfo, true))
    delete recordDoc._id
    recordDoc.uid = _uid;
    promiseList.push(recordMod.updateOne(_uid, recordDoc, true))
    if (activityDoc) {
        delete activityDoc._id
        activityDoc.uid = _uid;
        if (activityDoc.limit) {
            activityDoc.limit.forEach(l => delete l._id)
        }
        if (activityDoc.record) {
            activityDoc.record.forEach(l => delete l._id)
        }
        promiseList.push(specialActivityCol.updateOne({ uid: _uid }, activityDoc, { upsert: true }))
    }
    await Promise.all(promiseList)
    return response.SUCCESS_WITH_DESC_NO_RESULT("迁移成功!");
})
