const config = require('../config')
const db = require("../db/db");
const warpApp = require("../common/warpApp");
const response = require("./ResponseCode");
const {ObjectId} = require("mongoose/lib/types");
const curLogMod = require('../mod/curLogMod');
const {default: util} = require("../common/util");


let logStatCol = db.createLogStatModel();


warpApp.post('/saveConfig', async ({name, desc, config}) => {
    if (!name || !desc || !config) return response.ERROR_WITH_DESC_NO_RESULT('数据错误!');
    name = name.trim(), desc = desc.trim(), config = JSON.stringify(config);
    let exists = await logStatCol.findOne({name});
    exists = !!exists;
    if (exists) return response.ERROR_WITH_DESC_NO_RESULT('重复的名字!');
    let saver = new logStatCol({
        name,
        desc,
        config,
        open: false,
        createTs: Date.now()
    })
    await saver.save();
    return response.SUCCESS_WITH_DESC_NO_RESULT('保存成功!');
});

warpApp.post('/getConfig', async ({}) => {
    let arr = await logStatCol.find({});
    //await util.wait(1000);
    return response.SUCCESS_WITH_RESULT(arr);
});

// warpApp.post('/setOpen', async ({_id, open}) => {
//     return await curLogMod.setOpen(_id, open);
// });
//
// warpApp.post('/loadLogData', async ({_id, start, end}) => {
//     return await curLogMod.loadLogData(_id, start, end);
// });
