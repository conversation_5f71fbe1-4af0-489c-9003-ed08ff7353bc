const warpApp = require("../common/warpApp");
const response = require('./ResponseCode');
const storeMode = require('../mod/storeMod');
const roomMod = require('../mod/roomMod')

warpApp.post('/refunds', async ({page, uid}) => {
    let data = await storeMode.getRefunds(page, uid);
    return response.SUCCESS_NO_DESC_WITH_RESULT(data);
});

warpApp.post('/buildPlanCast', async (obj) => {
    return response.SUCCESS_NO_DESC_WITH_RESULT(await roomMod.transfer_data(obj));
});

