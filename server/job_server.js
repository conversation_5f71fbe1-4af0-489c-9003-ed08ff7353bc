//只定时处理游戏逻辑相关的数据，统计/备份用的数据放到statistics_server.js

require('ts-node').register();

const db = require("./db/db");
const model = require("./mod/model");
const schedule = require('node-schedule');
let config = require("./config");
const router = require("./router/router");
const logger = require("./common/log").getLogger("job_server");
const {dhlog, DH_LOG_EVENT} = require("./common/DHLog");

if (config.debug) {
    config.port = 8070;
}

db.init();
model.init();
router.init();
let redis = db.redis

// 微信订阅通知任务
const subscribeMod = require("./mod/subscribeMod");
subscribeMod.check()


const statisticsMod = require("./mod/statisticsMod")

// 定时清理存档缓存
const recordMod = require("./mod/recordMod");
const util = require("./common/util").default;
const redisKey = require("./db/redisKey");
const recommendMod = require('./mod/recommendMod');
const newsMod = require("./mod/NewsMod");
const accountMod = require("./mod/accountMod");
const roomMod = require("./mod/roomMod");

let rule = new schedule.RecurrenceRule();
rule.hour = 3;
rule.minute = 0;
rule.second = 0;
const clearRecordCache = schedule.scheduleJob(rule, async () => {
    logger.info("job start clearRecordCache......")
    await recordMod.clearRecordCache()
    logger.info("job end clearRecordCache")
});

// 定时清理乌冬过期邀请和工作记录
const wdWorkMod = require('./mod/wdWorkMod');
const verifyMod = require('./mod/verifyMod');
const userMod = require('./mod/userMod');
const wxMod = require('./mod/wxMod');
const storeMod = require("./mod/storeMod");

const partyMod = require("./mod/partyMod");
partyMod.checkAndRun()

const gardenMod = require("./mod/gardenMod");
const activityMod = require("./mod/activityMod");
gardenMod.checkAndRunt2()

let wdWorkRule = new schedule.RecurrenceRule();
wdWorkRule.hour = 4;
wdWorkRule.minute = 0;
wdWorkRule.second = 0;
const clearWdWork = schedule.scheduleJob(wdWorkRule, async () => {
    logger.info("job start clearWdWork......")
    await wdWorkMod.clearWdWork()
    logger.info("job end clearWdWork")
});

recommendMod.updateRefreshUserAll()

let autoSetDeblock = async (_id) => { //自动解封
    try {
        let feedbackOrderCol = db.createFeedbackOrderModel()
        let blackListCol = db.createBlackListModel()
        let blackListHistoryCol = db.createBlackListHistoryModel()

        let where = {}
        if (_id) {
            where = {_id: {$lt: _id}}
        }
        let docs = await feedbackOrderCol.find({type: 1}).sort("-_id").limit(10)
        for (let doc of docs) {
            let blackDoc = await blackListCol.findOne({uid: doc.uid})
            if (!blackDoc || !blackDoc.types || blackDoc.types.includes(10) || blackDoc.canDeblock) continue //手动封的不解
            let level = blackDoc.level
            if (level >= 6) continue
            if (blackDoc.types.includes(100) || blackDoc.types.includes(101) || blackDoc.types.includes(102)) {
                // 如果是广告次数/饼干包 时间增加
                level = 2160;
            }
            let blockTime = level * util.Time.Hour
            let now = Date.now()
            // if (blackDoc.timestamp < new Date("2021-5-01").getTime()) continue //先测试一下
            let count = await blackListHistoryCol.countDocuments({uid: doc.uid})
            if (count > 5) continue
            if (now - blockTime > blackDoc.timestamp) {
                await verifyMod.setUserCanDeblock(doc.uid)
            }
        }
        if (docs.length > 0) {
            await util.wait(util.Time.Second)
            return autoSetDeblock(docs[docs.length - 1]._id)
        } else {
            await util.wait(util.Time.Hour)
            return autoSetDeblock()
        }
    } catch (error) {
        logger.error("autoSetDeblock", error)
    }
}
autoSetDeblock()


let updateWorkDayRule = new schedule.RecurrenceRule();
updateWorkDayRule.date = 1
schedule.scheduleJob(updateWorkDayRule, async () => {
    logger.info("job start static updateWorkDayRule .....")
    await statisticsMod.updateWorkDay()
    logger.info("job end static updateWorkDayRule")
});
// 每3个小时处理一次谷歌的退款数据
schedule.scheduleJob('0 0 0/3 * * ?', async () => {
    logger.info("job start : google refund .....")
    await storeMod.deal_google_refund_order_info();
    logger.info("job end : refund .....")
});

// 每小时转储redis中的event到db中
schedule.scheduleJob('0 0 0/1 * * ?', async () => {
    logger.info("job start : cashEventJob .....")
    await statisticsMod.cashEventSaveDb();
    logger.info("job end : cashEventJob .....")
});
// 每天午夜时需要转储一次event数据
schedule.scheduleJob('59 59 23 * * ?', async () => {
    logger.info("job start : cashEventJob (MidNight).....")
    await statisticsMod.cashEventSaveDb();
    logger.info("job end : cashEventJob (MidNight).....")
});
// 每天6点刷新今日趣闻事件
schedule.scheduleJob('0 0 6 * * ?', async () => {
    logger.info("job start : random today news event.....")
    let _ = await newsMod.doFlush();
    if (_) {
        setTimeout(async () => {
            await newsMod.doFlush();
        }, _ + 1000);
    }
});
// 每天执行
schedule.scheduleJob('0 0 0 1/1 * ? ', async () => {
    roomMod.clearPlans();
});

let removeOfflineUsers = async () => {
    try {
        await userMod.removeOfflineUsers()
        await util.wait(10 * util.Time.Second)
        return removeOfflineUsers()
    } catch (error) {
        console.error("removeOfflineUsers", error)
    }
}
removeOfflineUsers()

let reportOnelineUser = async () => {
    try {
        if (!config.dhlog) return
        let count = await userMod.getOnlineUsersCount()
        let platform = config.type == "global" ? "global" : "inland"
        let now = Date.now()
        let intervalTime = 2 * util.Time.Minute
        let dump_time = util.utcDateFormat("yyyy-MM-dd hh:mm:ss", now - now % (intervalTime))
        dhlog.report(DH_LOG_EVENT.ONLINE, null, null, {server_id: "1", platform, online_num: count, dump_time})
        let nextTime = now - (now % intervalTime) + intervalTime + util.Time.Second
        await util.wait(Math.max(0, nextTime - now))
        return reportOnelineUser()
    } catch (error) {
        console.error("reportOnelineUser", error)
    }
}
reportOnelineUser()

