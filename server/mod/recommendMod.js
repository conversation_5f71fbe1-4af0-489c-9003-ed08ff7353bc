const userMod = require("./userMod");
const util = require("../common/util").default;
const db = require("../db/db");
const roomMod = require('../mod/roomMod');
const recordMod = require("./recordMod");
const logger = require("../common/log").getLogger('recommendMod');

const ACTIVATION_HISTORY_DAY = 3// 统计最近3天活跃度
const CALC_SCORE_INTERVAL = 1 * 60 * 1000// 计算活跃用户得分间隔

const UPDATE_ACTIVATION = 'update_activation'// 更新活跃度
const UPDATE_FURNITURES = 'update_furnitures'// 更新家具数量
const UPDATE_RECOMMENDED = 'update_recommended'// 更新被推荐次数
const UPDATE_ROOMLIKE = 'update_roomlike'// 更新被点赞次数

const ACTIVATION_MAX_SCORE = 20// 活跃度最大得分
const FURNITURES_MAX_SCORE = 20// 解锁家具最大得分
const LIKEABILITY_MAX_SCORE = 60// 喜爱度最大得分

let redis
let recommendCol

let recommendMod = {
    async init() {
        redis = db.redis
        recommendCol = db.createRecommendModel()
        // await recommendMod.mockData()
        // recommendMod.updateRefreshUserAll()
    },

    // 获取推荐列表
    async getRecommendList(uid, filter) {
        if (!filter || filter.length == 0) {
            return []
        }
        // 记录玩家刷新次数
        

        let result = []
        for (let i = 0; i < 4; i++) {
            let roomId = filter[Math.floor(Math.random() * filter.length)]
            let user = null
            if (result.length == 0) {
                user = await recommendMod.randomUser(0, 499, roomId)// 从 top 500 中随机
            } else if (result.length == 1 || result.length == 2) {
                let rank = await redis.zrevrank(recommendMod.getRecommendRankKey(roomId), uid)
                if (rank == null) {// 无排名
                    user = await recommendMod.randomUser(0, 499, roomId)// 从 top 500 中随机
                } else {
                    user = await recommendMod.randomUser(rank - 100, rank + 99, roomId)// 从前后 100 名中随机
                }
            } else {
                user = await recommendMod.randomUser(0, 499, roomId)// 从 top 500 中随机
            }

            if (user == null) {
                result.push(user)
            } else {
                let repetition = false
                result.filter(m => !!m).forEach(m => {
                    if (m.uid == user) {
                        repetition = true
                        return
                    }
                })

                if (repetition == false) {
                    result.push({ uid: user, roomId: roomId })
                    recommendMod.addBehaviourRecord(user, roomId, UPDATE_RECOMMENDED, 1)
                }
            }
        }

        let mission = []
        result.filter(m => !!m).forEach(m => mission.push(recommendMod.getRecord(m)))
        await Promise.all(mission)

        return result
    },

    async getRecord(user) {
        let [info, record, roomlike] = await Promise.all([userMod.getNameAndAvatar(user.uid), recordMod.getRecord(user.uid), roomMod.getLike(user.uid, user.roomId)])
        let recordData = {}
        recordData[`kefang_${user.roomId}`] = recordMod.getFromRecord(`kefang_${user.roomId}`, record)
        recordData[`kefang_${user.roomId}_map`] = recordMod.getFromRecord(`kefang_${user.roomId}_map`, record)
        recordData['kefangUnlocks'] = recordMod.getFromRecord('world', record)?.kefangUnlocks

        user.nickName = info.nickName
        user.avatarUrl = info.avatarUrl
        user.record = recordData
        user.roomlike = roomlike
    },

    // 从指定区间内随机用户
    async randomUser(start, stop, roomId) {
        let range = await redis.zrevrange(recommendMod.getRecommendRankKey(roomId), Math.max(start, 0), stop) || []
        if (!range || range.length == 0) {
            return null
        }

        return range[Math.floor(Math.random() * range.length)]// 随机用户
    },

    // 添加用户行为记录
    async addBehaviourRecord(uid, roomId, type, count) {
        let info = await userMod.getNameAndAvatar(uid)
        if (!info || !info.nickName || !info.avatarUrl) {// 未授权用户不再加入推荐列表
            return
        }

        if (type == UPDATE_ACTIVATION) {
            let date = util.getUTCDateStr(new Date())
            let doc = await recommendCol.findOne({ uid, roomId }) || { activation: [] }
            let activation = doc.activation
            let oldCount = -1
            activation.forEach(m => {
                if (m.date == date) {
                    oldCount = m.count
                    m.count += count
                }
            });
            oldCount == -1 && activation.push({ date: date, count: count })

            await recommendCol.updateOne({ uid, roomId }, { activation }, { upsert: true })//覆盖活跃记录
        } else if (type == UPDATE_FURNITURES) {
            await recommendCol.updateOne({ uid, roomId }, { furnitures: count }, { upsert: true })//覆盖家具数量 TODO:增加数量大于判断
        } else if (type == UPDATE_RECOMMENDED) {
            await recommendCol.updateOne({ uid, roomId }, { '$inc': { recommended: count } }, { upsert: true })//增加被推荐次数
        } else if (type == UPDATE_ROOMLIKE) {
            await recommendCol.updateOne({ uid, roomId }, { '$inc': { roomlike: count } }, { upsert: true })//增加被点赞次数
        }

        await recommendMod.addRefreshUser(uid, roomId)
    },

    // 添加待刷新用户
    async addRefreshUser(uid, roomId) {
        await redis.hset(recommendMod.getRecommendRefreshKey(), uid, roomId)
    },

    // 删除待刷新用户
    async delRefreshUser(uid) {
        await redis.hdel(recommendMod.getRecommendRefreshKey(), uid)
    },

    // 刷新所有待刷新用户
    async updateRefreshUserAll() {
        try {
            let all = await redis.hgetall(recommendMod.getRecommendRefreshKey()) || {}
            console.log("updateRefreshUserAll count", Object.keys(all).length)
            let startTime = Date.now()
            for (let key in all) {
                let now = Date.now()
                let uid = key
                let roomId = all[key]
                let score = await recommendMod.calcUserScore(uid, roomId)

                await recommendMod.updateUserScore(uid, roomId, score)
                await recommendMod.delRefreshUser(uid)
                let passTime = Date.now() - now
                let waitTime = Math.max(0, 50 - passTime) //最快xms处理一个
                await util.wait(waitTime)
            }

            let passTime = Date.now() - startTime
            let waitTime = Math.max(0, CALC_SCORE_INTERVAL - passTime)
            await util.wait(waitTime)
            return recommendMod.updateRefreshUserAll()
        } catch (error) {
            logger.error('updateRefreshUserAll error', error)
        }
    },

    // 计算用户得分
    async calcUserScore(uid, roomId) {
        let score = 0
        let date = util.getUTCDateStr(new Date(Date.now() - ACTIVATION_HISTORY_DAY * 24 * 3600 * 1000))// 过期期限
        let doc = await recommendCol.findOne({ uid, roomId })
        if (doc) {
            let activation = 0
            let history = doc.activation || []
            history.forEach(m => {
                if (m.date >= date) {
                    activation += m.count
                }
            })
            let { furnitures, recommended, roomlike } = doc

            score += Math.min(activation / 100, 1) * ACTIVATION_MAX_SCORE// 近期活跃度得分
            score += Math.min(furnitures / 30, 1) * FURNITURES_MAX_SCORE// 已解锁家具得分
            score += Math.min(roomlike / (Math.max(recommended, 50)), 1) * LIKEABILITY_MAX_SCORE// 喜爱度得分
        }

        return score// 总分
    },

    // 刷新用户得分
    async updateUserScore(uid, roomId, newScore) {
        let score = await redis.zscore(recommendMod.getRecommendRankKey(roomId), uid) || 0
        await redis.zincrby(recommendMod.getRecommendRankKey(roomId), Math.floor(newScore - score), uid)
    },

    // // 模拟数据
    // async mockData() {
    //     await recommendCol.deleteMany()// 清理旧数据
    //     await redis.del('RECOMMEND_RANK')
    //     await redis.del(recommendMod.getRecommendRankKey(201))
    //     await redis.del(recommendMod.getRecommendRankKey(202))
    //     await redis.del(recommendMod.getRecommendRankKey(301))
    //     await redis.del(recommendMod.getRecommendRankKey(302))
    //     await redis.del('RECOMMEND_REFRESH')

    //     let rooms = [201, 202, 301, 302]
    //     let result = await db.createUserModel().find()
    //     let size = result.length
    //     for (let i = 0; i < size; i++) {
    //         let uid = result[i].uid

    //         while (Math.random() < 0.90) {
    //             let r = rooms[Math.floor(Math.random() * rooms.length)]
    //             let rd = Math.random()
    //             if (rd < 0.1) {
    //                 await recommendMod.addBehaviourRecord(uid, r, UPDATE_ROOMLIKE, 1)
    //             } else if (rd < 0.3) {
    //                 await recommendMod.addBehaviourRecord(uid, r, UPDATE_RECOMMENDED, 1)
    //             } else {
    //                 await recommendMod.addBehaviourRecord(uid, r, UPDATE_ACTIVATION, 1)
    //             }
    //         }

    //         rooms.forEach(m => recommendMod.addBehaviourRecord(uid, m, UPDATE_FURNITURES, 30))
    //     }

    //     logger.info('initialized')
    // },

    // redis 所有用户排名 key
    getRecommendRankKey(roomId) {
        return `RECOMMEND_RANK_${roomId}`
    },

    // redis 待刷新用户缓存 key
    getRecommendRefreshKey() {
        return 'RECOMMEND_REFRESH'
    }
}

module.exports = recommendMod;