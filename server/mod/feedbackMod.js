const logger = require("../common/log").getLogger("feedbackMod");
let redis;
let feedbackMsgCol
let feedbackOrderCol

let feedbackMod = {

    init() {
        let db = require("../db/db");
        redis = db.redis;
        feedbackMsgCol = db.createFeedbackMsgModel()
        feedbackOrderCol = db.createFeedbackOrderModel()
    },

    async getMsgList(uid, type) {
        if (type == undefined) type = 0
        let doc = await feedbackOrderCol.findOne({ uid, type})
        let data = []
        if (doc) {
            data = await feedbackMsgCol.find({ orderId: doc._id })
        }
        return data
    },

    async updateOrderById(_id, info) {
        await feedbackOrderCol.updateOne({_id}, info)
    },

    async sendMsg(uid, type, info) {
        if (type == undefined) type = 0
        let now = Date.now()
        let doc = await feedbackOrderCol.findOneAndUpdate({uid, type}, { timestamp: now, status: 0, language: info.lang}, { upsert: true, new: true })
        if (doc) {
            info.orderId = doc._id
            info.timestamp = now
            await feedbackMsgCol.create(info)
        }
    },

    async delOrder(uid, type) {
        if (type == undefined) type = 0
        let doc = await feedbackOrderCol.findOne({ uid, type})
        if (doc) {
            await Promise.all([
                feedbackMsgCol.deleteMany({ orderId: doc._id }),
                feedbackOrderCol.deleteOne({ _id: doc._id })
            ])
        }
    },

    async delMsg(id) {
        await feedbackMsgCol.deleteOne({ _id: id })
    }
}

module.exports = feedbackMod;