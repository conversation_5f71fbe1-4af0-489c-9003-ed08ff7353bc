const logger = require("../common/log").getLogger('robotMod');
const robotCfg = require("../client/json/robotBase.json")

// 好友数超过 TRIGGER_ROBOT_FRIEND_CNT 不触发 15
const TRIGGER_ROBOT_FRIEND_CNT = 2
// 加好友触发概率 基础 5
const CHANCE_BASE = 5
// 机器人列表(硬配置)
let ROBOTS = []
// 机器人邀请玩家工作概率 5
const CHANCE_WORK_INVITE = 10
// 机器人被邀请工作同意概率 30
const CHANCE_WORK_INVITE_AGREE = 20

let redis
module.exports = {
    async init() {
        redis = db.redis
        this.workingCol = db.createWdWorkingModel()
        this.applyCol = db.createWdWorkApplyModel()
        this.initRobots()
    },
    initRobots() {
        let field = ""
        if (config.type === "fb") {
            ROBOTS = []
            return
        }
        if (config.type === "global") {
            field = config.debug ? "uid_oversea_test" : "uid_oversea"
        }
        if (config.type === "inland") {
            field = config.debug ? "uid_test" : "uid"
        }
        robotCfg.map(r => ROBOTS.push(r[field]))
    },
    // 上一次添加好友/拒绝好友申请的时间
    async getLastDealTime(uid) {
        let time = await redis.get(`LAST_DEAL_APPLY_LIST_TIME_${uid}`)
        return time ? Number(time) : 0
    },
    async setLastDealTime(uid) {
        await redis.set(`LAST_DEAL_APPLY_LIST_TIME_${uid}`, Date.now())
    },
    async todayCanDo(key) {
        let val = await redis.get(key)
        if (!val) return true
        return util.getTodayZeroTime(6) > Number(val)
    },
    async setTodayDone(key) {
        await redis.set(key, Date.now(), "px", 24 * util.Time.Day)
    },
    // 机器人好友申请
    async dispatchPlayerRobotFriend(uid) {
        if (ROBOTS.length === 0) return
        // 当天是否触发过
        if (!await this.todayCanDo(`ROBOT_FRIEND_REQ_${uid}`)) return

        // 好友数过多不触发
        if (await friendMod.getFriendCount(uid) > TRIGGER_ROBOT_FRIEND_CNT) return
        // 申请列表已有数据，不触发
        const applyList = await friendMod.getApplyList(uid, false)
        if (applyList.length) return
        // 概率触发 每隔一天概率增加15%
        let chance_add = 0
        let time = await this.getLastDealTime(uid)
        if (time > 0) {
            chance_add = Math.floor((Date.now() - time) / util.Time.Day)
            chance_add *= 15
            // 重置上一次处理的时间
            await this.setLastDealTime(uid)
        }
        if (util.random(0, 100) >= CHANCE_BASE + chance_add) return
        const radius = await this.getNotFriendRobots(uid)
        // 可以被操作的机器人列表
        if (!radius.length) return
        // 随机出机器人，进行好友申请
        const rid = util.random(0, radius.length - 1)
        await this.simulateApply(radius[rid], uid)
        await this.setTodayDone(`ROBOT_FRIEND_REQ_${uid}`)
    },
    // 机器人模拟 申请成为好友
    async simulateApply(robot, plr) {
        await friendMod.applyFriend(robot, plr)
        await this.simulateOnline(robot)
    },
    // 机器人模拟 上线一次 force=> 强制上线一次
    async simulateOnline(robot, force = false) {
        const rb = await userMod.findOne(robot)
        if (!rb) return void logger.error("机器人存档缺失!", robot)
        // 不超过一分钟的话，不上线
        if (!force && Date.now() - rb.loginTime < util.Time.Minute) return
        await userMod.updateOne(robot, {
            loginTime: Date.now()
        })
    },
    // 获取未成为好友的机器人列表
    async getNotFriendRobots(uid) {
        const list = await friendMod.getFriendList(uid, false)
        if (!list.length) return ROBOTS
        return ROBOTS.filter(e => !list.find(t => t.uid === e))
    },
    // 获取已成为好友的机器人列表
    async getIsFriendRobots(uid) {
        const list = await friendMod.getFriendList(uid, false)
        if (!list.length) return []
        return ROBOTS.filter(e => list.find(t => t.uid === e))
    },
    // 机器人邀请打工
    async dispatchPlayerRobotWork(uid) {
        if (ROBOTS.length === 0) return
        if (!await this.todayCanDo(`ROBOT_WORK_INVITE_${uid}`)) return
        // 是否超过24小时没有参与打工了 计算一下需要增加的概率
        const doc = await this.workingCol.find({uid})
        let diff = 0
        if (doc && doc.length) {
            const last = doc[doc.length - 1]
            diff = last.date / util.Time.Day
            // 未超过24小时 不触发
            if (diff <= 24) return
        }
        // 获取玩家已添加的机器人好友
        let list = await this.getIsFriendRobots(uid)
        // 排除正有邀请的机器人
        const rl = []
        for (let l of list) {
            // 当前已有未处理对的邀请
            if (await this.applyCol.findOne({uid: l, toUid: uid, date: {$gt: Date.now() - 12 * 3600 * 1000}})) continue
            // 当前已经为其打工
            let forWork = await wdWorkMod.synchroWudongWorkState(l, 2)
            if (forWork.forWork && forWork.forWork.uid === uid) continue
            rl.push(l)
        }
        list = rl
        if (!list.length) return
        // 计算触发几率
        if (util.random(0, 100) >= CHANCE_WORK_INVITE + diff) return
        const rid = util.random(0, list.length - 1)
        await wdWorkMod.inviteWork(list[rid], uid)
        await this.simulateOnline(list[rid])
        await this.setTodayDone(`ROBOT_WORK_INVITE_${uid}`)
    },
    isRobot(uid) {
        return ROBOTS.includes(uid);
    },
    // 是否同意打工
    isAgreeWorkInvite(rate = 0) {
        rate = rate || CHANCE_WORK_INVITE_AGREE
        return util.random(0, 100) < rate
    },
}

const {default: util} = require("../common/util");
const userMod = require("./userMod");
const friendMod = require("./friendMod");
const wdWorkMod = require("./wdWorkMod");
const db = require("../db/db");
const config = require("../config");
