const firebase = require('firebase-admin');
const fs = require("fs");
const { Platform } = require('../common/constant');
const {google} = require("../config");
const db = require('../db/db');
const logger = require("../common/log").getLogger("googleMod")

let googleMod = {
    init() {
        this.deviceTokenCol = db.createDeviceToken()
        if (google) {
            let json = fs.readFileSync(google.firebase.certPath).toString()
            let cert = JSON.parse(json)
            const app = firebase.initializeApp({
                credential: firebase.credential.cert(cert)
            });
            app.auth();
        }
        return this
    },

    async notify(token, data = {}, uid) {
        const message = {
            token,
        }

        for (let key in data) {
            message[key] = data[key]
        }

        try {
            // logger.debug("send google", uid, token)
            let res = await firebase.messaging().send(message)
            // logger.debug("rev google", res)
            return true
        } catch (error) {
            if (error.errorInfo.message == "Requested entity was not found.") {
                logger.warn("delete bad token", uid, token, error.errorInfo)
                await this.deviceTokenCol.deleteOne({uid, token})
            }
            else {
                logger.error("google notify fail", uid, token, error, error.errorInfo)
            }
            return false
        }
    },

    async sendSubscribeMessage(data, type, uid) {
        let {msg, token, title} = JSON.parse(data)
        if (!token) {
            let doc = await this.deviceTokenCol.findOne({uid, platform: Platform.ANDROID})
            if (!doc) return false
            token = doc.token
        }
        return this.notify(token, {
            notification: {body: msg, title}
        }, uid)
    },
}

module.exports = googleMod