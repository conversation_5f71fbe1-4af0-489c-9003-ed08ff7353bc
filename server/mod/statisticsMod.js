const util = require("../common/util").default;
const userMod = require("./userMod");
const {
    USER_TYPE,
    SYNC_STATE,
    SpAwardType,
    SwitchType,
    CURRENCY_ORDER_STATUS,
    CURRENCY
} = require("../common/constant");
const Excel = require("../common/excel");
const db = require("../db/db");
const rankMod = require("./rankMod")
const recordMod = require("./recordMod")
const got = require("got")
const {dhlog, DH_LOG_EVENT} = require("../common/DHLog")
const config = require("../config")
const redisKey = require("../db/redisKey");
const {async} = require("regenerator-runtime");
const logger = require("../common/log").getLogger("statisticsMod");
const ModelMgr = require("../client/ModelMgr").default;
const worker = require("../common/Worker");
const {default: ScriptCaller} = require("../common/script/ScriptCaller");
const {default: AsyncObj} = require("../common/script/AsyncObj");
const {EventConst} = require("../common/script/EventConst");
const {getLogger} = require("../common/log");
const {log} = require("winston");
const {start} = require("../common/multiprocess");
const xlsx = require("node-xlsx");
const thinkingData = require("./thinkingData");
const {ObjectId} = require("mongoose/lib/types");
global.scriptMgr = global.scriptMgr || new ScriptCaller();

let statisticsMod = {
    userCol: null,
    spAwardCol: null,
    redis: null,

    init() {
        this.userCol = db.createUserModel();
        this.spAwardCol = db.createSpecialAwardModel()
        this.userTokenCol = db.createUserTokenModel()
        this.invitedCol = db.createInvitedModel()
        this.taskProgressCol = db.createTaskProgressModel()
        this.userHeartCol = db.createStatUserHeartModel()
        this.userGuideCol = db.createStatUserGuideModel()
        this.workDayCol = db.createWorkDayModel()
        this.umengCol = db.createUmengModel()
        this.currnecyOrderCol = db.createCurrencyOrderModel()
        this.recommendCol = db.createRecommendModel();
        this.redis = db.redis;
        this.hotUpdateStat = db.createHotUpdateStatModel();
        this.currencyBalanceCol = db.createCurrencyBalanceModel()

        worker.init();
    },

    //统计双端登录的玩家
    async wxAppUsers() {
        let data = []
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let date = new Date("2021-7-01 00:00:00").getTime()
            let docs = await this.spAwardCol.find(where).sort("-_id").limit(1000)
            let userDocs = await util.promiseMap(docs, async (doc) => {
                if (doc.awards && doc.awards.indexOf(SpAwardType.APP_WX_LOGIN) >= 0) {
                    let userDoc = await this.userCol.findOne({uid: doc.uid})
                    if (userDoc) {
                        if (userDoc.loginTime >= date && userDoc.openid && userDoc.unionid && userDoc.wxAppOpenid) {
                            let tokenDocs = await this.userTokenCol.find({uid: doc.uid})
                            let flag = 0
                            for (let doc of tokenDocs) {
                                if (doc.token) {
                                    let {type} = userMod.parseToken(doc.token)
                                    if (type == USER_TYPE.WX || !type) flag |= 1
                                    if (type == USER_TYPE.APP_WX) flag |= 2
                                }
                            }
                            if (flag == 3) {
                                return userDoc
                            }
                        }
                    }
                    return 0
                }
            })

            for (let doc of userDocs) {
                if (!doc) continue
                data.push(doc)
            }
            console.log("完成: ", data.length)
            if (docs.length > 0) {
                await util.wait(1000)
                return work(docs[docs.length - 1]._id)
            }
        }

        await work()
        let sheet = [
            ["openid", "unionid", "uid", " 注册时间"]
        ]
        for (let doc of data) {
            let subAry = [doc.openid || "", doc.unionid || "", doc.uid, util.dateFormat('yyyy-MM-dd hh:mm:ss', doc.signupTime)];
            sheet.push(subAry)
        }
        let excel = new Excel({sheet})
        excel.writeFile("双端登录玩家.xlsx")
    },


    async inviteInfo() {
        let countMap = {};
        let inviterMap = new Map()
        let len = 0
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let docs = await this.invitedCol.find(where).sort("-_id").limit(1000)
            for (let doc of docs) {
                let id = doc.inviter
                let count = inviterMap.get(id) || 0
                inviterMap.set(id, count + 1)
            }

            len += docs.length
            console.log("完成: ", len)
            if (docs.length > 0) {
                await util.wait(100)
                return work(docs[docs.length - 1]._id)
            }
        }
        await work()

        let total = 0
        let sum = 0
        inviterMap.forEach((count) => {
            if (!countMap[count]) countMap[count] = 0
            countMap[count]++

            total += 1
            sum += count
        })
        console.log(countMap)
        let avg = sum / total
        console.log(avg, sum, total)
    },

    async heartDis() {
        let signupMap = {}

        let now = Date.now()
        let date = now - now % util.Time.Day
        let type = "heartDis_2"
        let key = `date_${date}`
        let len = 0
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let now = Date.now()
            let limit = 10000
            if (config.type == "global") limit = 2000
            let docs = await this.userCol.find(where).sort("-_id").limit(limit)
            await util.promiseMap(docs, async (doc) => {
                try {
                    let active = now - doc.updateTime <= util.Time.Day * 7
                    let signupTime = doc.signupTime
                    let uid = doc.uid
                    if (!uid) {
                        console.log("uid emty", doc._id)
                        return
                    }
                    let heart = await recordMod.getHeartByUid(uid, doc.serverId)

                    let signupDay = signupTime - signupTime % util.Time.Day
                    if (now - signupDay > 60 * util.Time.Day) { //只分开存最近60天的，其他存一起
                        signupDay = 0
                    }
                    signupDay /= util.Time.Day
                    if (!signupMap[signupDay]) signupMap[signupDay] = {}

                    let allMap = signupMap[signupDay]

                    heart = util.retainHightDigit(heart, 2)

                    if (!allMap[heart]) allMap[heart] = {}
                    if (!allMap[heart]["wx"]) allMap[heart]["wx"] = {active: 0, inactive: 0}
                    if (!allMap[heart]["double"]) allMap[heart]["double"] = {active: 0, inactive: 0}
                    if (!allMap[heart]["app"]) allMap[heart]["app"] = {active: 0, inactive: 0}

                    let userType = doc.userType

                    let obj
                    if (doc.openid && doc.wxAppOpenid) { //双端用户
                        obj = allMap[heart]['double']
                    } else if (userType == USER_TYPE.GUEST || userType == USER_TYPE.APP_WX || userType == USER_TYPE.APPLE) {
                        obj = allMap[heart]["app"]
                    } else {
                        obj = allMap[heart]['wx']
                    }
                    if (active) {
                        obj.active++
                    } else {
                        obj.inactive++
                    }
                } catch (error) {
                    console.log(error)
                }
            })

            len += docs.length

            if (docs.length > 0) {
                await this.taskProgressCol.updateOne({type, key}, {
                    $inc: {done: docs.length},
                    result: JSON.stringify({allMap: signupMap, lastId: docs[docs.length - 1]._id})
                }, {upsert: true})
                let passTime = Date.now() - now
                let waitTime = Math.max(0, util.Time.Second - passTime)
                await util.wait(waitTime)
                console.log("完成: ", len, docs[docs.length - 1]._id, passTime)
                return work(docs[docs.length - 1]._id)
            } else {
                console.log("done")
            }
        }
        let doc = await this.taskProgressCol.findOne({type, key})
        let lastId
        if (doc && doc.result) {
            let result = JSON.parse(doc.result)
            lastId = result.lastId
            signupMap = result.allMap
        }
        if (!lastId) {
            await this.taskProgressCol.updateOne({type, key}, {
                done: 0,
                total: 1000000000,
                expireTime: util.Time.Day
            }, {upsert: true})
        }
        await work(lastId)
    },

    async heartDisNew() {
        let signupMap = {}

        let now = Date.now()
        let date = now - now % util.Time.Day
        let type = "heartDis_new"
        let key = `date_${date}`
        let len = 0
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let now = Date.now()
            let limit = 10000
            if (config.type == "global") limit = 1000
            let docs = await this.userHeartCol.find(where).sort("-_id").limit(limit)
            await util.promiseMap(docs, async ({uid, heart}) => {
                try {
                    if (!uid) return
                    let doc = await userMod.findOne(uid)
                    if (!doc) return
                    let active = now - doc.loginTime <= util.Time.Day * 7
                    let signupTime = doc.signupTime

                    let signupDay = signupTime - signupTime % util.Time.Day
                    if (now - signupDay > 60 * util.Time.Day) { //只分开存最近60天的，其他存一起
                        signupDay = 0
                    }
                    signupDay /= util.Time.Day
                    if (!signupMap[signupDay]) signupMap[signupDay] = {}

                    let allMap = signupMap[signupDay]

                    heart = util.retainHightDigit(heart, 2)

                    if (!allMap[heart]) allMap[heart] = {}
                    if (!allMap[heart]["wx"]) allMap[heart]["wx"] = {active: 0, inactive: 0}
                    if (!allMap[heart]["double"]) allMap[heart]["double"] = {active: 0, inactive: 0}
                    if (!allMap[heart]["app"]) allMap[heart]["app"] = {active: 0, inactive: 0}

                    let userType = doc.userType

                    let obj
                    if (doc.openid && doc.wxAppOpenid) { //双端用户
                        obj = allMap[heart]['double']
                    } else if (userType == USER_TYPE.GUEST || userType == USER_TYPE.APP_WX || userType == USER_TYPE.APPLE) {
                        obj = allMap[heart]["app"]
                    } else {
                        obj = allMap[heart]['wx']
                    }
                    if (active) {
                        obj.active++
                    } else {
                        obj.inactive++
                    }
                } catch (error) {
                    console.log(error)
                }
            })

            len += docs.length

            if (docs.length > 0) {
                await this.taskProgressCol.updateOne({type, key}, {
                    $inc: {done: docs.length},
                    result: JSON.stringify({allMap: signupMap, lastId: docs[docs.length - 1]._id})
                }, {upsert: true})
                let passTime = Date.now() - now
                let waitTime = Math.max(0, util.Time.Second - passTime)
                await util.wait(waitTime)
                console.log("完成: ", len, docs[docs.length - 1]._id, passTime)
                return work(docs[docs.length - 1]._id)
            } else {
                console.log("done")
            }
        }
        let doc = await this.taskProgressCol.findOne({type, key})
        let lastId
        if (doc && doc.result) {
            let result = JSON.parse(doc.result)
            lastId = result.lastId
            signupMap = result.allMap
        }
        if (!lastId) {
            await this.taskProgressCol.updateOne({type, key}, {
                done: 0,
                total: 1000000000,
                expireTime: util.Time.Day
            }, {upsert: true})
        }
        await work(lastId)
    },

    async guideDis() {
        let signupMap = {}

        let now = Date.now()
        let date = now - now % util.Time.Day
        let type = "guideDis"
        let key = `date_${date}`
        let len = 0
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let now = Date.now()
            let limit = 10000
            if (config.type == "global") limit = 2000
            let docs = await this.userGuideCol.find(where).sort("-_id").limit(limit)
            await util.promiseMap(docs, async ({uid, progress}) => {
                try {
                    if (!uid) return
                    let doc = await userMod.findOne(uid)
                    if (!doc) return
                    let active = now - doc.loginTime <= util.Time.Day * 7
                    let signupTime = doc.signupTime

                    let signupDay = signupTime - signupTime % util.Time.Day
                    if (now - signupDay > 60 * util.Time.Day) { //只分开存最近60天的，其他存一起
                        signupDay = 0
                    }
                    signupDay /= util.Time.Day
                    if (!signupMap[signupDay]) signupMap[signupDay] = {}

                    let allMap = signupMap[signupDay]

                    if (!allMap[progress]) allMap[progress] = {}
                    if (!allMap[progress]["wx"]) allMap[progress]["wx"] = {active: 0, inactive: 0}
                    if (!allMap[progress]["double"]) allMap[progress]["double"] = {active: 0, inactive: 0}
                    if (!allMap[progress]["app"]) allMap[progress]["app"] = {active: 0, inactive: 0}

                    let userType = doc.userType

                    let obj
                    if (doc.openid && doc.wxAppOpenid) { //双端用户
                        obj = allMap[progress]['double']
                    } else if (userType == USER_TYPE.GUEST || userType == USER_TYPE.APP_WX || userType == USER_TYPE.APPLE) {
                        obj = allMap[progress]["app"]
                    } else {
                        obj = allMap[progress]['wx']
                    }
                    if (active) {
                        obj.active++
                    } else {
                        obj.inactive++
                    }
                } catch (error) {
                    console.log(error)
                }
            })

            len += docs.length

            if (docs.length > 0) {
                await this.taskProgressCol.updateOne({type, key}, {
                    $inc: {done: docs.length},
                    result: JSON.stringify({allMap: signupMap, lastId: docs[docs.length - 1]._id})
                }, {upsert: true})
                let passTime = Date.now() - now
                let waitTime = Math.max(0, util.Time.Second - passTime)
                await util.wait(waitTime)
                console.log("guideDis 完成: ", len, docs[docs.length - 1]._id, passTime)
                return work(docs[docs.length - 1]._id)
            } else {
                console.log("guideDis done")
            }
        }
        let doc = await this.taskProgressCol.findOne({type, key})
        let lastId
        if (doc && doc.result) {
            let result = JSON.parse(doc.result)
            lastId = result.lastId
            signupMap = result.allMap
        }
        if (!lastId) {
            await this.taskProgressCol.updateOne({type, key}, {
                done: 0,
                total: 1000000000,
                expireTime: util.Time.Day
            }, {upsert: true})
        }
        await work(lastId)
    },

    async updateUserHeart(uid, heart) {
        let heartDoc = await this.userHeartCol.findOneAndUpdate({uid}, {heart}, {upsert: true})
        let before_lv = (heartDoc && heartDoc.heart) || 0
        userMod.getDHLogInfo(uid).then((userInfo) => {
            if (!userInfo) return
            if (before_lv >= heart) return
            dhlog.report(DH_LOG_EVENT.LEVEL_UP, null, userInfo, {before_lv, after_lv: heart})
        })
    },

    async updateUserGuide(uid, guideId, progress) {
        let doc = await this.userGuideCol.findOne({uid, guideId})
        if (doc) {
            progress = Math.max(progress, doc.progress)
        }
        await this.userGuideCol.updateOne({uid, guideId}, {progress}, {upsert: true})
    },

    async updateWorkDay(retryCount = 10) {
        let size = 400
        let year = new Date().getFullYear()
        let url = `http://api.apihubs.cn/holiday/get?year=${year}&workday=2&&size=${size}`
        try {
            let {code, data, msg} = await got(url).json()
            if (code == "0") {
                let {list} = data
                for (let {date} of list) {
                    await this.workDayCol.updateOne({date}, {date}, {upsert: true})
                }
            } else {
                if (retryCount > 0) {
                    this.updateWorkDay(retryCount - 1)
                } else {
                    console.error(code, msg)
                }
            }
        } catch (error) {
            console.error(error)
            this.updateWorkDay(retryCount - 1)
        }
    },

    async statGuessAdult() {
        this.guessAdultCol = db.createGuessAdultModel()
        let count = 0
        let len = 0
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let docs = await this.guessAdultCol.find(where).sort("-_id").limit(10000)
            await util.promiseMap(docs, async ({uid, prop}) => {
                if (prop >= 2) {
                    let doc = await userMod.findOne(uid, {wxAppOpenid: 1})
                    if (doc && doc.wxAppOpenid) {
                        count++
                    }
                }
            })

            len += docs.length
            console.log("完成: ", len, count)
            if (docs.length > 0) {
                return work(docs[docs.length - 1]._id)
            }
        }
        await work()
    },

    async cashEvent({eventId, params}) {
        for (let key in params) {
            let val = params[key];
            await this.redis.hincrby(redisKey.eventCache(eventId), key, val);
        }
        // save event
        await this.redis.sadd(redisKey.eventSet(), eventId);
    },

    async cashEventSaveDb() {
        // 获取存储触发过的事件集合
        let _events = await this.redis.smembers(redisKey.eventSet());
        if (!_events) return;
        for (let event in _events) {
            let eventId = _events[event];
            let hash = await this.redis.hgetall(redisKey.eventCache(eventId));
            let params = {};
            let ids = [];
            for (let _key in hash) {
                params[_key] = parseInt(hash[_key]);
                ids.push(_key);
            }
            if (!hash) continue;
            await this.umeng({eventId, params});
            await this.redis.hdel(redisKey.eventCache(eventId), ids);
        }
    },

    async umeng({eventId, params}) {
        let date = util.getDateOfDay();
        params = params || {}
        params.__count = Object.keys(params).length;

        let doc = await this.umengCol.findOne({eventId, date})
        if (doc) {
            for (let {key, val} of doc.params) {
                if (!params[key]) params[key] = 0
                params[key] += val
            }
        }
        let update = {params: []}
        for (let key in params) {
            let val = params[key]
            update.params.push({key, val})
        }
        await this.umengCol.updateOne({eventId, date}, update, {upsert: true})
    },

    async customerDis() {
        let signupMap = {}

        let now = Date.now()
        let date = now - now % util.Time.Day
        let type = "customerDis_1"
        let key = `date_${date}`
        let len = 0
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let now = Date.now()
            let limit = 10000
            if (config.type == "global") limit = 1000
            let docs = await this.userHeartCol.find(where).sort("-_id").limit(limit)
            await util.promiseMap(docs, async ({uid, heart}) => {
                try {
                    if (!uid) return
                    let [doc, record] = await Promise.all([userMod.findOne(uid), recordMod.getRecord(uid)])
                    if (!doc) return
                    let signupTime = doc.signupTime

                    let signupDay = signupTime - signupTime % util.Time.Day
                    if (now - signupDay > 60 * util.Time.Day) { //只分开存最近60天的，其他存一起
                        signupDay = 0
                    }
                    signupDay /= util.Time.Day
                    if (!signupMap[signupDay]) signupMap[signupDay] = {}

                    let allMap = signupMap[signupDay]

                    heart = util.retainHightDigit(heart, 2)
                    let roleAttrs = recordMod.getFromRecord("world.roleAttrs", record) || []
                    let len = roleAttrs.length

                    if (!allMap[heart]) allMap[heart] = {}
                    if (!allMap[heart][len]) allMap[heart][len] = 0
                    allMap[heart][len]++

                } catch (error) {
                    console.log(error)
                }
            })

            len += docs.length

            if (docs.length > 0) {
                await this.taskProgressCol.updateOne({type, key}, {
                    $inc: {done: docs.length},
                    result: JSON.stringify({allMap: signupMap, lastId: docs[docs.length - 1]._id})
                }, {upsert: true})
                let passTime = Date.now() - now
                let waitTime = Math.max(0, util.Time.Second - passTime)
                await util.wait(waitTime)
                console.log("完成: ", len, docs[docs.length - 1]._id, passTime)
                return work(docs[docs.length - 1]._id)
            } else {
                console.log("done")
            }
        }
        let doc = await this.taskProgressCol.findOne({type, key})
        let lastId
        if (doc && doc.result) {
            let result = JSON.parse(doc.result)
            lastId = result.lastId
            signupMap = result.allMap
        }
        if (!lastId) {
            await this.taskProgressCol.updateOne({type, key}, {
                done: 0,
                total: 1000000000,
                expireTime: util.Time.Day
            }, {upsert: true})
        }
        await work(lastId)
    },

    async statOrder() {
        let count = 0
        let len = 0
        let iapMap = {}
        let exchangeMap = {}
        let adMap = {}
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let docs = await this.currnecyOrderCol.find(where).sort("-_id").limit(10000)
            await util.promiseMap(docs, async ({status, action}) => {
                if (status == CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS || status == CURRENCY_ORDER_STATUS.COMPLETED) {
                    if (action.startsWith("iap")) {
                        if (!iapMap[action]) iapMap[action] = 0
                        iapMap[action]++
                    } else if (action.startsWith("exchange_")) {
                        if (!exchangeMap[action]) exchangeMap[action] = 0
                        exchangeMap[action]++
                    } else if (action.endsWith("_ad")) {
                        if (!adMap[action]) adMap[action] = 0
                        adMap[action]++
                    }
                }
            })

            len += docs.length
            console.log("完成: ", len, count)
            if (docs.length > 0) {
                return work(docs[docs.length - 1]._id)
            }
        }
        await work()

        let showList = (name, map) => {
            let sum = 0
            for (let key in map) {
                sum += map[key]
            }
            let list = []
            for (let key in map) {
                list.push([key, (map[key] / sum || 0) * 100])
            }
            console.log(name, list, sum)
        }

        showList("iap", iapMap)
        showList("exchange", exchangeMap)
        showList("ad", adMap)
    },
    // 以100为区间 取每个区间内玩家拥有的特殊家具的数量的平均数 和中位数
    async staFurnitureAvgAndMed() {
        let sTime = Date.now();
        console.log("start and wait ...")
        let result = [];
        let totalCount = 0;
        let count = await this.userHeartCol.find({}).count(),
            limit = 10000;
        let _key = 0;
        let conditions = {
            heart: {$lt: (_key + 1) * 100, $gte: _key * 100},
        };
        // 区间特殊家具总数
        let specObj = {};
        // 区间-人数
        let temp = {};
        let _pCount = {};
        for (; ;) {
            if (_key >= 100) {
                console.log('break');
                break;
            }
            let docs = await this.userHeartCol.find(conditions).sort({_id: -1}).limit(limit);
            if (!docs || docs.length === 0) {
                _key++;
                continue;
            }
            if (count === totalCount) {
                break;
            }
            let _count = docs.length;
            totalCount += _count;
            let pTime = Date.now();
            await util.promiseMap(docs, async ({uid, heart}) => {
                let r = 0;
                if (uid) {
                    let record = await recordMod.getRecord(uid);
                    if (record) {
                        let modelMgr = new ModelMgr();
                        modelMgr.init(record);
                        r = modelMgr.getSpecFurnCount();
                    }
                }
                // 存储每个区间特定的特殊家具数和对应的人数
                if (!_pCount[_key]) {
                    let o = {};
                    o[r] = 1;
                    _pCount[_key] = o;
                } else if (!_pCount[_key][r]) {
                    _pCount[_key][r] = 1;
                } else {
                    _pCount[_key][r] += 1;
                }
                // 记录区间特殊家具总数用于计算均值
                specObj[_key] = specObj[_key] ? specObj[_key] += r : r;
                // 记录a区间人数
                temp[_key] = temp[_key] ? temp[_key] += 1 : 1;
            });
            if (_count === limit) {
                conditions._id = {$lt: docs[docs.length - 1]._id};
            } else {
                _key++;
                conditions.heart = {$lt: (_key + 1) * 100, $gte: _key * 100};
                if (conditions._id) {
                    delete conditions._id;
                }
            }
            console.log("处理:", _count, "条(共", totalCount, "条), 耗时: ", Date.now() - pTime);
        }
        // 整合数据
        console.log('整合数据');
        for (let key of Object.keys(temp)) {
            let r = {}
            r['区间'] = `${(parseInt(key) + 1) * 100} > [] >= ${parseInt(key) * 100}`;
            r['均值'] = (specObj[key] / temp[key]).toFixed(2);
            r['总特殊家具'] = specObj[key];
            r['实际用户数'] = temp[key];
            let p = temp[key];
            if (p === 1) {
                r['中值'] = specObj[key];
            } else {
                if (temp[key] % 2 === 0) {
                    // 总数偶数，中值取两个算平均值
                    let _p = Math.floor(temp[key] / 2);
                    let _q = _p + 1;
                    let obj = _pCount[key];
                    if (!obj) {
                        r['中值'] = 0;
                    } else {
                        let s1 = -1, s2 = -1;
                        for (let _k of Object.keys(obj)) {
                            if (s1 === -1) {
                                if (_p <= obj[_k]) {
                                    s1 = parseInt(_k);
                                } else {
                                    _p -= obj[_k];
                                }
                            }
                            if (s2 === -1) {
                                if (_q <= obj[_k]) {
                                    s2 = parseInt(_k);
                                    // 按理来说 s2 是在 s1之后的数字,可以提前截断循环
                                    break;
                                } else {
                                    _q -= obj[_k];
                                }
                            }
                        }
                        s1 = s1 === -1 ? 0 : s1;
                        s2 = s2 === -1 ? 0 : s2;
                        r['中值'] = ((s1 + s2) / 2).toFixed(2);
                    }
                } else {
                    let _p = Math.floor(temp[key] / 2);
                    let obj = _pCount[key];
                    if (!obj) {
                        r['中值'] = 0;
                    } else {
                        for (let _k of Object.keys(obj)) {
                            if (_p <= obj[_k]) {
                                r['中值'] = parseInt(_k);
                                break;
                            }
                        }
                    }
                }
            }
            if (!r['中值']) {
                r['中值'] = 0;
            }
            result.push(r);
        }
        let eTime = Date.now();
        console.log('result :', result);
        console.log('共', totalCount, '条记录,总耗时: ', eTime - sTime, "ms");
    },

    async commonDis() {
        let signupMap = {}

        let now = Date.now()
        let date = now - now % util.Time.Day
        let type = "commonDis"
        let key = `date_${date}`
        let len = 0
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let limit = 10000
            if (config.type == "global") limit = 1000
            let now = Date.now()
            let docs = await this.userHeartCol.find(where).sort("-_id").limit(limit)

            await util.promiseMap(docs, async ({uid, heart}) => {
                try {
                    if (!uid) return
                    let [doc, record] = await Promise.all([userMod.findOne(uid), recordMod.getRecord(uid)])
                    if (!doc || !record) return
                    let signupTime = doc.signupTime

                    let signupDay = signupTime - signupTime % util.Time.Day
                    if (now - signupDay > 60 * util.Time.Day) { //只分开存最近60天的，其他存一起
                        signupDay = 0
                    }
                    signupDay /= util.Time.Day
                    if (!signupMap[signupDay]) signupMap[signupDay] = {}

                    let allMap = signupMap[signupDay]
                    let countType = (key, val) => {
                        if (!allMap[heart]) allMap[heart] = {}
                        if (!allMap[heart][key]) allMap[heart][key] = {}
                        if (!allMap[heart][key][val]) allMap[heart][key][val] = 0
                        allMap[heart][key][val]++
                    }
                    let countVal = (key, val) => {
                        if (!allMap[heart]) allMap[heart] = {}
                        if (!allMap[heart][key]) allMap[heart][key] = 0
                        allMap[heart][key] += val
                    }

                    heart = util.retainHightDigit(heart, 2)
                    let accTotalSolicitRoleNum = recordMod.getFromRecord("global.accTotalSolicitRoleNum", record) || 0
                    let diyAvatar = doc.diyAvatar || "default"

                    let modelMgr = new ModelMgr()
                    modelMgr.init(record)
                    let offline = modelMgr.get('offline')
                    let offlineIncome = offline.getOfflineExpectedIncome()
                    let onlineIncom = offline.getOnlineExpectedIncome()

                    countVal("solRole", accTotalSolicitRoleNum)
                    countType("avatar", diyAvatar)
                    countVal("cnt", 1)
                    countVal("offin", offlineIncome)
                    countVal("onin", onlineIncom)

                } catch (error) {
                    console.log(error)
                }
            })

            len += docs.length

            if (docs.length > 0) {
                await this.taskProgressCol.updateOne({type, key}, {
                    $inc: {done: docs.length},
                    result: JSON.stringify({allMap: signupMap, lastId: docs[docs.length - 1]._id})
                }, {upsert: true})
                let passTime = Date.now() - now
                let waitTime = Math.max(0, util.Time.Second - passTime)
                await util.wait(waitTime)
                console.log("完成: ", len, docs[docs.length - 1]._id, passTime)
                return work(docs[docs.length - 1]._id)
            } else {
                console.log("done")
            }
        }
        let doc = await this.taskProgressCol.findOne({type, key})
        let lastId
        if (doc && doc.result) {
            let result = JSON.parse(doc.result)
            lastId = result.lastId
            signupMap = result.allMap
        }
        if (!lastId) {
            await this.taskProgressCol.updateOne({type, key}, {
                done: 0,
                total: 1000000000,
                expireTime: util.Time.Day
            }, {upsert: true})
        }
        await work(lastId)
    },

    /**
     * @target: 前端查询特定周期内登录过的不同蜡烛段的用户，平均完成多少个客人任务
     * 只存最近60天的登录数据
     * 按天查询，所以要按天存储
     * 以蜡烛数分段
     * 天:{
     *     蜡烛段:{
     *         人数,
     *         任务数
     *     }
     * }
     * TODO: 1优化跨天统计，0天的数据可以复用。2跳过统计当天未登录的uid
     */
    async customerTask() {
        let now = Date.now()
        // 当天
        let today = Math.floor(now / util.Time.Day);
        let type = "customerTask"
        let key = `date_${today}`
        // 头一天
        let lastKey = `date_${today - 1}`;
        let totalCount = 0;
        //蜡烛 20000以上忽略
        let max_heart = 20000;
        let max_save_day = 60;
        let map = {};
        let worker = async (id, date) => {
            let limit = 10000;
            let where = {};
            if (id) {
                where = {_id: {$gt: id}}
            }
            let docs = await this.userHeartCol.find(where).sort({_id: 1}).limit(limit);
            await util.promiseMap(docs, async ({uid, heart}) => {
                if (!uid || heart > max_heart) return;
                heart = util.retainHightDigit(heart, 2);
                let [doc, record] = await Promise.all([userMod.findOne(uid), recordMod.getRecord(uid)])
                if (!doc || !record) return;
                let lastLoginTime = doc.loginTime || 0;
                // 计算玩家所在天
                let day = Math.floor(lastLoginTime / util.Time.Day);
                if (day <= date) return;
                let number = heart;
                let interval = number === 0 ? number : Math.ceil(number);
                // 模拟该用户完成了多少个客人任务0-10个
                //let finishedTaskCount = Math.floor(Math.random() * 10);
                let modelMgr = new ModelMgr();
                modelMgr.init(record);
                let com = modelMgr.getRoleFinishComTaskCount();
                let exc = modelMgr.getRoleFinishExcTaskCount();
                let finishedTaskCount = com + exc;
                // 超过最大存储时间 就统一存放到0里面
                if (today - day > max_save_day) {
                    day = 0;
                }
                map[day] = map[day] || {};
                map[day][interval] = map[day][interval] || {};
                // 人数
                map[day][interval].r = map[day][interval].r + 1 || 1;
                map[day][interval].t = map[day][interval].t + finishedTaskCount || finishedTaskCount;
                totalCount++;
            });
            if (docs.length > 0) {
                let lastId = docs[docs.length - 1]._id;
                await this.taskProgressCol.updateOne({type, key}, {
                    $inc: {done: docs.length},
                    result: JSON.stringify({allMap: map, lastId})
                }, {upsert: true});
                console.log("lastId: " + lastId);
                return worker(lastId, date);
            }
        }
        let doc = await this.taskProgressCol.findOne({type, key});
        let lastId, lastDate = 0;
        if (doc && doc.result) {
            let result = JSON.parse(doc.result)
            lastId = result.lastId
            map = result.allMap
        } else {
            let lastDoc = await this.taskProgressCol.findOne({type, key: lastKey});
            if (lastDoc && lastDoc.result) {
                let result = JSON.parse(lastDoc.result);
                if (Object.keys(result.allMap).length) {
                    map = result.allMap;
                    lastDate = today - 1;
                }
            }
        }
        if (!lastId) {
            await this.taskProgressCol.updateOne({type, key}, {
                done: 0,
                total: 1000000000,
                expireTime: util.Time.Day
            }, {upsert: true})
        }
        try {
            await worker(lastId, lastDate);
        } catch (e) {
            console.error(e);
        }
        console.log("共", totalCount, "条,耗时:", Date.now() - now, "ms");
    },
    // 特定周期内不同蜡烛段玩家接取到的特定id任务的人数
    // 特定周期内不同蜡烛段玩家完成特定id任务的人数
    // async specialTaskCompletion() {
    //     let mission = [45104, 125104];
    //     let now = Date.now()
    //     let today = Math.floor(now / util.Time.Day);
    //     let type = "specialTask"
    //     let key = `date_${today}`
    //     let lastKey = `date_${today - 1}`;
    //     let totalCount = 0;
    //     let max_heart = 20000;
    //     let max_save_day = 60;
    //     let map = {};
    //     let worker = async (id, date) => {
    //         let limit = 10000;
    //         let where = {};
    //         if (id) {
    //             where = {_id: {$gt: id}}
    //         }
    //         let docs = await this.userHeartCol.find(where).sort({_id: 1}).limit(limit);
    //         await util.promiseMap(docs, async ({uid, heart}) => {
    //             if (!uid || heart > max_heart) return;
    //             heart = util.retainHightDigit(heart, 2);
    //             let [doc, record] = await Promise.all([userMod.findOne(uid), recordMod.getRecord(uid)])
    //             if (!doc || !record) return;
    //             let lastLoginTime = doc.loginTime || 0;
    //             // 计算玩家所在天
    //             let day = Math.floor(lastLoginTime / util.Time.Day);
    //             if (day <= date) return;
    //             let number = heart;
    //             let interval = number === 0 ? number : Math.ceil(number);
    //             let modelMgr = new ModelMgr();
    //             try {
    //                 modelMgr.init(record);
    //             } catch (e) {
    //             }
    //             // 0221 老邓说接取了就会放到接取和完成里面，完成了就会从接取里面移除
    //             // 所以接取的判断则是判断都有 完成的判断则是判断完成有，接取没有
    //             for (let id of mission) {
    //                 let g = modelMgr.hasWishExcTask(id) ? 1 : 0;
    //                 let f = modelMgr.hasRoleFinishExcTask(id) ? 1 : 0;
    //                 if (g && f) {
    //                     g = 1;
    //                     f = 0;// 接取了但是没完成
    //                 } else if (!g && f) {
    //                     g = f = 1;// 接取了并且已经完成
    //                 } else {
    //                     g = 0;
    //                     f = 0 // 没接取也没完成
    //                 }
    //                 if (g === 0 && f === 0) {
    //                     totalCount++;
    //                     return;
    //                 }
    //                 // 超过最大存储时间 就统一存放到0里面
    //                 if (today - day > max_save_day) {
    //                     day = 0;
    //                 }
    //                 // 天
    //                 map[day] = map[day] || {};
    //                 // 蜡烛区间
    //                 map[day][interval] = map[day][interval] || {};
    //                 // 任务
    //                 map[day][interval][id] = map[day][interval][id] || {};
    //                 // 计数 g:接取, f:完成
    //                 map[day][interval][id].g = map[day][interval][id].g + g || g;
    //                 map[day][interval][id].f = map[day][interval][id].f + f || f;
    //             }
    //             totalCount++;
    //         });
    //         if (docs.length > 0) {
    //             let lastId = docs[docs.length - 1]._id;
    //             await this.taskProgressCol.updateOne({type, key}, {
    //                 $inc: {done: docs.length},
    //                 result: JSON.stringify({allMap: map, lastId})
    //             }, {upsert: true});
    //             console.log("lastId: " + lastId);
    //             return worker(lastId, date);
    //         }
    //     }
    //     let doc = await this.taskProgressCol.findOne({type, key});
    //     let lastId, lastDate = 0;
    //     if (doc && doc.result) {
    //         let result = JSON.parse(doc.result)
    //         lastId = result.lastId
    //         map = result.allMap
    //     } else {
    //         let lastDoc = await this.taskProgressCol.findOne({type, key: lastKey});
    //         if (lastDoc && lastDoc.result) {
    //             let result = JSON.parse(lastDoc.result);
    //             if (Object.keys(result.allMap).length) {
    //                 map = result.allMap;
    //                 lastDate = today - 1;
    //             }
    //         }
    //     }
    //     if (!lastId) {
    //         await this.taskProgressCol.updateOne({type, key}, {
    //             done: 0,
    //             total: 1000000000,
    //             expireTime: util.Time.Day
    //         }, {upsert: true})
    //     }
    //     try {
    //         await worker(lastId, lastDate);
    //     } catch (e) {
    //         console.error(e);
    //     }
    //     console.log("共", totalCount, "条,耗时:", Date.now() - now, "ms");
    // },
    // 统计前一日家具使用率
    // 因为统计过程是比较耗时的 在这个过程中登陆了的用户可能会被纳入下一天，无法避免
    // 任务时间调整为凌晨0点开始
    async statisticsOnFurnitureUsage() {
        // 当天0点到23:59:59
        let start = 0, end = 0;
        start = new Date(new Date(new Date().toLocaleDateString()).getTime()).getTime() - util.Time.Day;
        end = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1).getTime() - util.Time.Day;
        let now = start + util.Time.Day;
        let today = util.getNumberDay();
        let type = "furnitureUsageTask"
        let key = `date_${today}`
        let map = {};
        let totalCount = 0;
        let worker = async (id) => {
            let limit = 10000;
            let where = {
                'loginTime': {$gte: start, $lte: end}
            };
            if (id) {
                where._id = {
                    $gt: id
                }
            }
            let docs = await this.userCol.find(where).sort({_id: 1}).limit(limit);
            await util.promiseMap(docs, async ({uid, loginTime}) => {
                let record = await recordMod.getRecord(uid);
                if (!record) return;
                let modelMgr = new ModelMgr();
                try {
                    modelMgr.init(record);
                    modelMgr.getUsingFurn();
                    // 特殊家具
                    map["fMap"] = modelMgr.getUsingFurn(map["fMap"]);
                } catch (e) {
                    logger.error('getUsingFurn:', e);
                }
                totalCount++;
            });
            if (docs.length > 0) {
                let lastId = docs[docs.length - 1]._id;
                await this.taskProgressCol.updateOne({type, key}, {
                    $inc: {done: docs.length},
                    result: JSON.stringify({allMap: map, lastId})
                }, {upsert: true});
                console.log("lastId: " + lastId);
                return worker(lastId);
            }
        }
        let doc = await this.taskProgressCol.findOne({type, key});
        let lastId;
        if (doc && doc.result) {
            let result = JSON.parse(doc.result)
            lastId = result.lastId
            map = result.allMap
        }
        if (!lastId) {
            await this.taskProgressCol.updateOne({type, key}, {
                done: 0,
                total: 1000000000,
                expireTime: util.Time.Day
            }, {upsert: true})
        }
        try {
            await worker(lastId);
        } catch (e) {
            console.error(e);
        }
        console.log("共", totalCount, "条,耗时:", Date.now() - now, "ms");

        // let sheet = [
        //     ["日期", "家具ID", "使用率", "摆放人数", "日活人数"]
        // ];
        // let dayKeys = Object.keys(map);
        // for (let day of dayKeys) {
        //     let formatDay = day * 24 * 60 * 60 * 1000;
        //     let playerCount = map[day]["p"] || "";
        //     let fMap = map[day]["fMap"];
        //     let _fMap = Object.keys(fMap);
        //     _fMap.forEach(f => {
        //         let row = [];
        //         row[0] = util.dateFormat("yyyy-MM-dd", formatDay);
        //         row[1] = f;
        //         row[2] = (fMap[f] / playerCount).toFixed(2);
        //         row[3] = fMap[f];
        //         row[4] = playerCount;
        //         sheet.push(row);
        //     })
        // }
        // let excel = new Excel({sheet});
        // excel.writeFile("家具使用率.xlsx");
        // console.log("out done.")
    },
    // 根据玩家注册时间 统计不同蜡烛段用户的平均登录天数
    // async statUserLoginDaysAvg() {
    //     let type = "userLoginDaysTask"
    //     let key = `userLoginDaysTask`
    //     let map = {};
    //     let totalCount = 0;
    //     let limit = 10000;
    //     let worker = async (lastRunDay, id) => {
    //         let where = {}
    //         if (id) {
    //             where._id = {$gt: id}
    //         }
    //         let docs = await this.userHeartCol.find(where).sort({_id: 1}).limit(limit);
    //         await util.promiseMap(docs, async ({uid, heart}) => {
    //             if (!uid) return;
    //             let [doc, record] = await Promise.all([userMod.findOne(uid), recordMod.getRecord(uid)]);
    //             if (!doc || !record) return;
    //             let signupDay = util.getNumberDay(doc.signupTime);
    //             if (lastRunDay && signupDay < lastRunDay) {
    //                 return;
    //             }
    //             heart = util.retainHightDigit(heart, 2)
    //             let loginDay = recordMod.getLoginDay(record);
    //             if (loginDay > 0) {
    //                 map[heart] = map[heart] || {};
    //                 map[heart][signupDay] = map[heart][signupDay] || {};
    //                 map[heart][signupDay]["d"] = map[heart][signupDay]["d"] + loginDay || loginDay;
    //                 map[heart][signupDay]["c"] = map[heart][signupDay]["c"] + 1 || 1;
    //             }
    //             totalCount++;
    //         });
    //         if (docs.length > 0) {
    //             let lastId = docs[docs.length - 1]._id;
    //             await this.taskProgressCol.updateOne({type, key}, {
    //                 $inc: {done: docs.length},
    //                 result: JSON.stringify({allMap: map, lastId})
    //             }, {upsert: true});
    //             console.log("lastId: " + lastId);
    //             return worker(lastRunDay, lastId);
    //         }
    //     };
    //     let doc = await this.taskProgressCol.findOne({type, key});
    //     let lastId, lastRunDay;
    //     if (doc && doc.result) {
    //         let result = JSON.parse(doc.result)
    //         map = result.allMap
    //         lastRunDay = doc.lastRunDay || 0;
    //         map = lastRunDay && lastRunDay === util.getTodayZeroTime() ? result.allMap : {};
    //         lastId = lastRunDay && lastRunDay === util.getTodayZeroTime() ? result.lastId : 0;
    //     }
    //     if (!lastId) {
    //         await this.taskProgressCol.updateOne({type, key}, {
    //             done: 0,
    //             total: 1000000000,
    //             expireTime: util.Time.Day,
    //             lastRunDay: util.getTodayZeroTime()
    //         }, {upsert: true})
    //         lastRunDay = 0;
    //     }
    //     try {
    //         await worker(lastRunDay, lastId);
    //         console.log(totalCount)
    //     } catch (e) {
    //         console.error(e);
    //     }
    // },

    async statCommonLevelAvg() {
        try {
            // 根据玩家注册时间 统计不同蜡烛段用户 1 所有客人平均等级 2 所有电影平均等级 3 所有菜品平均等级 4 所有汤药平均等级
            let types = ['role', 'cinema', 'soup', 'dining'];
            let today = util.getNumberDay();
            let mission_StatCommonLevelAvg = new worker.mission("commonLv", `date_${today}`);
            mission_StatCommonLevelAvg.beforeRun = async () => {
                // 分开读 再合成一个对象
                for (let type of types) {
                    let readType = `${mission_StatCommonLevelAvg.type}_${type}`;
                    let doc = await this.taskProgressCol.findOne({
                        type: readType,
                        key: mission_StatCommonLevelAvg.key
                    });
                    if (doc && doc.result) {
                        let result = JSON.parse(doc.result)
                        mission_StatCommonLevelAvg.map[type] = result.allMap
                        mission_StatCommonLevelAvg.lastId = result.lastId;
                    }
                }
            };
            mission_StatCommonLevelAvg.injectVal = async (arr, signupDay, heart, name) => {
                mission_StatCommonLevelAvg.map[name][signupDay][heart] = mission_StatCommonLevelAvg.map[name][signupDay][heart] || {};
                mission_StatCommonLevelAvg.map[name][signupDay][heart]["total"] =
                    mission_StatCommonLevelAvg.map[name][signupDay][heart]["total"] ?
                        mission_StatCommonLevelAvg.map[name][signupDay][heart]["total"] + 1 : 1;
                if (arr && arr.length) {
                    arr.forEach(role => {
                        mission_StatCommonLevelAvg.map[name][signupDay][heart][role.id] =
                            mission_StatCommonLevelAvg.map[name][signupDay][heart][role.id] ?
                                mission_StatCommonLevelAvg.map[name][signupDay][heart][role.id] + role.lv : role.lv;
                    });
                }
            };
            mission_StatCommonLevelAvg.deal = async (heart, uid, userDoc, recordDoc) => {
                //注册时间 0309 换成了loginTime
                let signupDay = util.getNumberDay(userDoc.loginTime);
                today - signupDay > 30 && (signupDay = 0);
                if (signupDay === 0) {
                    return false;
                }
                let modelMgr = new ModelMgr();
                modelMgr.init(recordDoc);
                for (let i = 0; i < types.length; i++) {
                    let type = types[i];
                    mission_StatCommonLevelAvg.map[type] = mission_StatCommonLevelAvg.map[type] || {};
                    mission_StatCommonLevelAvg.map[type][signupDay] = mission_StatCommonLevelAvg.map[type][signupDay] || {};
                    mission_StatCommonLevelAvg.map[type][signupDay][heart] = mission_StatCommonLevelAvg.map[type][signupDay][heart] || {};
                    await mission_StatCommonLevelAvg.dealType(type, modelMgr, signupDay, heart);
                }
                return true;
            };
            mission_StatCommonLevelAvg.dealType = async (type, modelMgr, signupDay, heart) => {
                let info;
                type === 'role' && (info = modelMgr.getRoleLvInfo());
                type === 'cinema' && (info = modelMgr.getCinemaLvInfo());
                type === 'soup' && (info = modelMgr.getSoupLvInfo());
                type === 'dining' && (info = modelMgr.getDiningLvInfo());
                await mission_StatCommonLevelAvg.injectVal(info, signupDay, heart, type);
            };
            mission_StatCommonLevelAvg.saveDb = async (docs) => {
                // 每次都要把对象拆开写
                for (let type of types) {
                    let writeType = `${mission_StatCommonLevelAvg.type}_${type}`;
                    let map = mission_StatCommonLevelAvg.map[type];
                    await this.taskProgressCol.updateOne({type: writeType, key: mission_StatCommonLevelAvg.key}, {
                        $inc: {done: docs.length},
                        result: JSON.stringify({allMap: map, lastId: mission_StatCommonLevelAvg.lastId}),
                        expireTime: util.Time.Day
                    }, {upsert: true});
                }
            };

            // 根据玩家注册时间 统计不同蜡烛段用户的平均登录天数
            let m2 = new worker.mission("userLoginDaysTask", 'userLoginDaysTask');
            m2.lastRunDay = 0;
            m2.deal = async (heart, uid, userDoc, record) => {
                let lastRunDay = m2['lastRunDay'];
                let signupDay = util.getNumberDay(userDoc.signupTime);
                if (lastRunDay && signupDay < lastRunDay) {
                    return false;
                }
                let loginDay = recordMod.getLoginDay(record);
                if (loginDay > 0) {
                    let map = m2.map;
                    map[heart] = map[heart] || {};
                    map[heart][signupDay] = map[heart][signupDay] || {};
                    map[heart][signupDay]["d"] = map[heart][signupDay]["d"] + loginDay || loginDay;
                    map[heart][signupDay]["c"] = map[heart][signupDay]["c"] + 1 || 1;
                }
                return true;
            }
            m2.beforeRun = async () => {
                let doc = await this.taskProgressCol.findOne({type: m2.type, key: m2.key});
                if (doc && doc.result) {
                    let result = JSON.parse(doc.result)
                    m2.map = result.allMap
                    m2.lastRunDay = doc.lastRunDay || 0;
                    m2.map = m2.lastRunDay && m2.lastRunDay === util.getTodayZeroTime() ? result.allMap : {};
                    m2.lastId = m2.lastRunDay && m2.lastRunDay === util.getTodayZeroTime() ? result.lastId : 0;
                }
                if (!m2.lastId) {
                    await this.taskProgressCol.updateOne({type: m2.type, key: m2.key}, {
                        done: 0,
                        total: 1000000000,
                        expireTime: util.Time.Day,
                        lastRunDay: util.getTodayZeroTime()
                    }, {upsert: true})
                    m2.lastRunDay = 0;
                }
            };
            //特定周期内不同蜡烛段玩家接取到的特定id任务的人数 and 特定周期内不同蜡烛段玩家完成特定id任务的人数
            let m3 = new worker.mission("specialTask", `date_${today}`);
            m3.lastKey = '';
            m3.mission = [45104, 125104];
            m3.beforeRun = async () => {
                let doc = await this.taskProgressCol.findOne({type: m3.type, key: m3.key});
                if (doc && doc.result) {
                    let result = JSON.parse(doc.result)
                    m3.lastId = result.lastId
                    m3.map = result.allMap
                } else {
                    let lastDoc = await this.taskProgressCol.findOne({type: m3.type, key: m3.lastKey});
                    if (lastDoc && lastDoc.result) {
                        let result = JSON.parse(lastDoc.result);
                        if (Object.keys(result.allMap).length) {
                            m3.map = result.allMap;
                            m3.lastDate = today - 1;
                        }
                    }
                }
                if (!m3.lastId) {
                    await this.taskProgressCol.updateOne({type: m3.type, key: m3.key}, {
                        done: 0,
                        total: 1000000000,
                        expireTime: util.Time.Day
                    }, {upsert: true})
                }
            }
            m3.deal = async (heart, uid, userDoc, record) => {
                let lastLoginTime = userDoc.loginTime || 0;
                // 计算玩家所在天
                let day = Math.floor(lastLoginTime / util.Time.Day);
                if (day <= m3.lastDate) return;
                let number = heart;
                let interval = number === 0 ? number : Math.ceil(number);
                let modelMgr = new ModelMgr();
                try {
                    modelMgr.init(record);
                } catch (e) {
                }
                // 0221 老邓说接取了就会放到接取和完成里面，完成了就会从接取里面移除
                // 所以接取的判断则是判断都有 完成的判断则是判断完成有，接取没有
                for (let id of m3['mission']) {
                    let g = modelMgr.hasWishExcTask(id) ? 1 : 0;
                    let f = modelMgr.hasRoleFinishExcTask(id) ? 1 : 0;
                    if (g && f) {
                        g = 1;
                        f = 0;// 接取了但是没完成
                    } else if (!g && f) {
                        g = f = 1;// 接取了并且已经完成
                    } else {
                        g = 0;
                        f = 0 // 没接取也没完成
                    }
                    if (g === 0 && f === 0) {
                        return true;
                    }
                    // 超过最大存储时间 就统一存放到0里面
                    if (today - day > 60) {
                        day = 0;
                    }
                    // 天
                    m3.map[day] = m3.map[day] || {};
                    // 蜡烛区间
                    m3.map[day][interval] = m3.map[day][interval] || {};
                    // 任务
                    m3.map[day][interval][id] = m3.map[day][interval][id] || {};
                    // 计数 g:接取, f:完成
                    m3.map[day][interval][id].g = m3.map[day][interval][id].g + g || g;
                    m3.map[day][interval][id].f = m3.map[day][interval][id].f + f || f;
                }
                return true;
            }

            let MI = new worker.MI();
            await MI.addMission(mission_StatCommonLevelAvg);
            await MI.addMission(m2);
            await MI.addMission(m3);
            await worker.work_default(MI);
        } catch (e) {
            console.error(e);
        }
    },

    /**
     * 清理统计数据 7天前的
     * @returns {Promise<void>}
     */
    async clearData() {
        let today = util.getNumberDay();
        let day = today - 7;
        let docs = await this.taskProgressCol.find({'key': {$regex: `date_19`}})
        await util.promiseMap(docs, async (type, key) => {
            let _date = parseInt(key);
            if (_date < day) {
                await this.taskProgressCol.deleteOne({type, key});
            }
        })

    },
    // 装修方案点赞数
    async noteFavNum() {
        let arr = [0, 10, 30, 50, 70, 100, 200, 300, 500, 1000];
        let heart = [50, 100, 150, 200, 300, 400, 500, 700, 1000, 1500, 2000, 3000, 4000, 5000];
        let rooms = [201, 202, 301, 302, 401, 402];
        let total = 0;
        arr = arr.reverse();
        heart = heart.reverse();
        let _t = {};
        // arr.forEach(i => {
        //     _t[i] = 0;
        // })
        rooms.forEach(room => {
            heart.forEach(h => {
                _t[room] = _t[room] || {};
                _t[room][h] = 0;
            })
        })

        let col = this.recommendCol;
        let heartCol = this.userHeartCol;
        let getInterval = async function (uid, roomId, roomlike) {
            let doc = await heartCol.findOne({uid});
            if (doc && doc.heart >= 50) {
                for (let number of heart) {
                    if (doc.heart >= number) {
                        _t[roomId][number] += 1;
                        break;
                    }
                }
            }

        };
        let logic = async function (lastId) {
            console.log(total, JSON.stringify(_t))
            try {
                let where = {roomlike: {$gte: 100}};
                lastId && (where._id = {$gt: lastId});
                let docs = await col.find(where, {uid: 1, roomId: 1, roomlike: 1}).sort({_id: 1}).limit(10000);
                await util.promiseMap(docs, async ({uid, roomId, roomlike}) => {
                    await getInterval(uid, roomId, roomlike);
                    total += 1;
                });
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            } catch (e) {
                console.log(e);
            }
            return 0;
        }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },

    // 各个平台的游客。。
    async platformTourists() {
        let t = {
            ios: {
                total: 0,
                tourists: 0,
            },
            android: {
                total: 0,
                tourists: 0,
            }
        }
        let loginInfoCol = db.createLoginInfoModel();
        let userCol = db.createUserModel();
        let logic = async (lastId) => {
            console.log(JSON.stringify(t));
            try {
                let where = {};
                lastId && (where._id = {$gt: lastId});
                let docs = await loginInfoCol.find(where, {uid: 1, platform: 1}).sort({_id: 1}).limit(10000);
                await util.promiseMap(docs, async ({uid, platform}) => {
                    let doc = await userCol.findOne({uid}, {userType: 1});
                    if (doc && doc["userType"] && platform) {
                        if (platform === "ios") {
                            t.ios.total += 1
                            doc["userType"] === "app_guest" && (t.ios.tourists += 1);
                        }
                        if (platform === "android") {
                            t.android.total += 1
                            doc["userType"] === "app_guest" && (t.android.tourists += 1);
                        }
                    }
                });
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            } catch (e) {

            }
            return 0;
        }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },

    // 五一摸鱼人活动 bug
    async statisticsActivityUsers() {
        let userModel = db.createUserModel();
        let _arr = [];
        const _pre = Date.now() - util.Time.Day * 7;// 十天前登录的不在范围内
        console.log(_pre);
        let logic = async (lastId) => {
            console.log(_arr.length, JSON.stringify(_arr));
            try {
                let where = {loginTime: {$gte: _pre}};
                lastId && (where._id = {$gt: lastId});
                let docs = await userModel.find(where, {uid: 1}).sort({_id: 1}).limit(10000);
                await util.promiseMap(docs, async ({uid}) => {
                    if (!uid) return;
                    let record = await recordMod.getRecord(uid);
                    if (!record) return;
                    let modelMgr = new ModelMgr();
                    try {
                        modelMgr.init(record);
                        let _n = false;
                        !_n && modelMgr.labour && modelMgr.labour.realStarTime
                        && modelMgr.labour.finishIds && modelMgr.labour.finishIds.length < 8 && (_n = true);
                        _n && (_arr.push(uid));
                    } catch (e) {
                        console.log('error ---->', e);
                        return 0;
                    }
                });
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            } catch (e) {
                console.log('error ====>', e);
                return 0;
            }
            return 0;
        };
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        console.log("")
        console.log(JSON.stringify(_arr));
    },
    async _test() {
        await recordMod.debugCopy('972a00e2-9d7f-483c-a66e-a9ff2b96b9a4');
        let record = await recordMod.getRecord('972a00e2-9d7f-483c-a66e-a9ff2b96b9a4');
        let modelMgr = new ModelMgr();
        modelMgr.init(record);
        //modelMgr.labour.realStarTime
        //modelMgr.labour.finishIds.length
        console.log(1)
    },

    async hotUpdateStart(info) {
        await this.hotUpdateStat.updateOne({uid: info.uid}, {
            curVer: info.curVer,
            version: info.version
        }, {upsert: true})
    },

    async hotUpdateEnd(info) {
        await this.hotUpdateStat.updateOne({uid: info.uid}, {
            curVer: info.curVer,
            version: info.version,
            time: info.time,
            files: info.files,
            size: info.size,
            isFinish: true,
            platform: info.platform
        }, {upsert: true})
    },
    async r0601(uids = [], eventKey, gameVer = "3.3.0") {
        let redeemCodeModel = db.createRedeemCodeModel()
        let ClsRequest = require('../logserver/common/cls/ClsRequest').default;
        let cls = new ClsRequest();
        let config = await cls.getTopicConfig();
        let end = Date.now(), start = end - util.Time.Day * 3;
        for (let uid of uids) {
            let cdk_prefix = uid.substring(0, 4);
            let query = `uid:"${uid}" AND eventKey: "${eventKey}" AND gameVer: "${gameVer}"`;
            let res = await cls.doSearch(null, config.eventLog.TopicId, start, end, query);
            let data = [];
            let cdk = {};
            res.Results && (data = res.Results);
            for (let line of data) {
                line = JSON.parse(line.LogJson);
                line = JSON.parse(line.extendsInfo);
                let furn = line.target;
                if (!furn.id || !furn.tag) continue;
                cdk[furn.count] = cdk[furn.count] || {};
                cdk[furn.count]['diy'] = cdk[furn.count]['diy'] || `${cdk_prefix}_${furn.count}`;
                cdk[furn.count].rewards = cdk[furn.count].rewards || "";
                let temp = `${furn.type},${furn.id},${furn.count},${furn.tag}@1`;
                if (cdk[furn.count].rewards.indexOf(temp) > -1) continue
                cdk[furn.count].rewards && (cdk[furn.count].rewards += "|");
                cdk[furn.count].rewards += temp;
            }
            let keys = Object.keys(cdk);
            for (let key of keys) {
                let model = new redeemCodeModel({
                    type: cdk[key].rewards,
                    date: *************,
                    code: cdk[key].diy,
                    unique: 1,
                    verified: true,
                    account: 'husong',
                    permission: 1111111,
                    validTime: 144,
                })
                await model.save();
                console.log(`uid:${uid},   cdk:${cdk[key].diy}`);
            }
        }


    },

    async r0720() {
        const currencyModel = require("./currencyModel")
        let count = 0,
            logic = async (lastId) => {
                let where = {timestamp: {$gte: *************, $lte: *************}, status: 3};
                lastId && (where._id = {$gt: lastId});
                let docs = await this.currnecyOrderCol.find(where, {uid: 1, action: 1}).sort({_id: 1}).limit(10000);
                await util.promiseMap(docs, async ({uid, action}) => {
                    if (action.startsWith("exchange_")) {
                        let balance = await currencyModel.asynCurrencyBalance(uid);
                        if (balance[CURRENCY.WINDMILL] < 0) {
                            console.log(`${uid}\t${balance[CURRENCY.WINDMILL]}\t${action}`), count++;
                            let lang = {key: "ui.announce_005"}
                            await userMod.addCompensationRewardMultiple(uid, `16,-1,${Math.abs(balance[CURRENCY.WINDMILL]) + 10}`
                                , JSON.stringify(lang), "", 100);
                        }
                    }
                })
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        console.log(count);
    },
    async r0802(From, To = From + util.Time.Day) {
        const ClsRequest = require("../logserver/common/cls/ClsRequest").default;
        const cls = new ClsRequest()
        const catchUsersCol = db.createCatchUserModel();
        const verifyMod = require("../mod/verifyMod");
        let config = await cls.getTopicConfig();
        let Context
            , Query = `eventKey:"4" AND uiList:"[\\"common/Task\\"]" AND time :>=${From} AND time :<=${To}`;

        let catchUsers = {}, total = 0, lines = 0, ListOver = false;
        let startTime = Date.now(), m5 = 0;
        console.log(`${From} ,${startTime} ...${config.eventLog.TopicId}`);
        console.log(`${Query}`)
        do {
            let _ret = await cls.doSearch(Context, config.eventLog.TopicId, From, To, Query, 1000, 'asc');
            _ret['Context'] && (Context = _ret['Context']);
            _ret['ListOver'] && (ListOver = _ret['ListOver']);
            let result = _ret['Results'] ? _ret['Results'] : [];

            total += result.length;
            for (let info of result) {
                let line = info.LogJson;
                line && (line = JSON.parse(line));
                let {uid, gameVer, time, extendsInfo, platform} = line;
                extendsInfo = JSON.parse(extendsInfo);
                time = Number(time);
                if (extendsInfo.changes && extendsInfo.changes.length === 1) {
                    let c = extendsInfo.changes[0];
                    if (c.change === 15) { //改变的值
                        // 处理刷子
                        catchUsers[uid] = catchUsers[uid] || new Set();
                        catchUsers[uid].add(time);
                    }
                }
                From = time;
            }
            if (total === 10000 || ListOver) {
                Context = void 0;
                Query = `eventKey:"4" AND uiList:"[\\"common/Task\\"]" AND time :>=${From} AND time :<=${To}`;
                lines += total;
                total = 0;
                console.log(From, ListOver, lines)

            }
        } while (!ListOver);
        total = 0;
        for (let uid in catchUsers) {
            let count = catchUsers[uid].size;
            if (count > 5) {
                m5++;
                count *= 3;
                // 暂时先拉入黑名单
                let isBlack = await verifyMod.isBlack(uid);
                !isBlack && await verifyMod.addBlack(uid, {"desc": "每日任务bug刷子0910"});
                // 移除排行榜
                await rankMod.remove(uid);
                // 更新玩家catch数据
                await catchUsersCol.update({uid}, {
                    $inc: {count},
                    lastEditTimeStamp: Date.now()
                }, {upsert: true});
            }
            total += 1;
        }
        console.log(`data over : ${lines}， ${From}, ${To}`);
        console.log(`end :${Date.now() - startTime}ms... total:${total}... m5:${m5}`);
    },
    async r0825() {
        let total = 0, _signupTime = 1660233600000
            , lastLoginTimeEnd = 1661788800000
            , lastLoginTimeStart = 1654012800000
            , where = {}
            , logic = async (lastId) => {
            lastId && (where._id = {$gt: lastId});
            let docs = await this.userCol.find(where, {
                uid: 1,
                loginTime: 1,
                signupTime: 1
            }).sort({_id: 1}).limit(10000);
            await util.promiseMap(docs, async ({uid, loginTime, signupTime}) => {
                if (!uid || !loginTime || !signupTime) return;
                (signupTime <= _signupTime && loginTime <= lastLoginTimeEnd && loginTime >= lastLoginTimeStart) && total++;
            })
            console.log(`===> total:${total}`);
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        console.log(`end .. total:${total}`);
    },
    async r0825_thinking() {
        const thinkingData = require('./thinkingData');
        console.log(`open csv file start.`)
        let sheets = xlsx.parse('/root/u1.csv');
        let sheet = sheets[0]['data'];
        let loginInfoCol = db.createLoginInfoModel();
        console.log(`open csv file end.`)
        for (let i = 1; i < sheet.length; i++) {
            try {
                let line = sheet[i];
                if (!line || !line.length) continue;
                let uid = line[0] || "";
                if (!uid) continue;
                let user_doc = await this.userCol.findOne({uid}, {signupTime: 1, userType: 1}),
                    login_info_doc = await loginInfoCol.findOne({uid}, {deviceId: 1, platform: 1});
                if (!user_doc) {
                    console.warn(`no u2.xlsx :${uid}`)
                    continue;
                }
                let device_id, platform, time, os_version;
                user_doc && user_doc.signupTime && (time = user_doc.signupTime);
                if (login_info_doc) {
                    login_info_doc.platform && (platform = login_info_doc.platform);
                }
                if (platform === USER_TYPE.APPLE) os_version = 'ios';
                if (platform === USER_TYPE.GOOGLE) os_version = 'android';
                if (!time) {
                    console.warn(`no time :${uid}`)
                    continue;
                }
                login_info_doc && login_info_doc.deviceId && (device_id = login_info_doc.deviceId);
                await thinkingData.update_register_info(uid, device_id, platform, os_version, time);
                console.info(`===> ${uid}, ${device_id}, ${platform},${os_version}, ${time}`);
            } catch (e) {
                console.error(e);
            }
        }
    },
    async getBuildPlanNumber() {
        this.buildPlanCol = db.createBuildPlanModel();
        let where = {}, logic = async (lastId) => {
            lastId && (where._id = {$gt: lastId});
            let docs = await this.buildPlanCol.find(where).sort({_id: 1}).limit(5000);
            await util.promiseMap(docs, async ({uid, saveInfo, state}) => {
                if (!saveInfo) return void console.warn(`no save info :${uid}`);
                try {
                    if (state === 1 || state === 2) {
                        saveInfo = JSON.parse(saveInfo);
                        if (saveInfo.furnitures.length <= 5) console.warn(`5 ===> ${uid}`);
                    }
                } catch (e) {
                    console.error(`err data :${uid}`);
                }
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },
    async r0830() {
        let rooms = [201, 202, 301, 302, 401, 402], total = 0;
        const recordCol = db.createGameRecordModel();
        let where = {}, logic = async (lastId) => {
            lastId && (where._id = {$gt: lastId});
            let docs = await recordCol.find(where).sort({_id: 1}).limit(10000);
            total += docs.length;
            console.log('lines ===> ', total)
            await util.promiseMap(docs, async ({uid, gameVer, recordBuff}) => {
                if (!uid || !gameVer || !recordBuff) return;
                if (util.cmpVersion('4.2.0', gameVer) > 0) return;
                try {
                    let record = await recordMod.unzipRecord(recordBuff, true);
                    let temp_room = [], save = false;
                    for (let roomId of rooms) {
                        let room = recordMod.getFromRecord(`kefang_${roomId}`, record);
                        if (!room) continue;
                        let furnSaves = room['furnSaves'] || [];
                        furnSaves.forEach(e => {
                            //console.log(`name value ===> ${typeof e.name}`);
                            //return e.name !== "" || (e.name !== "" && !e.name)
                            if (e && e.name && e.name !== "?") {
                                e.name = "?", save = true, temp_room.push(roomId);
                            }
                        });
                        //_.length && (temp_room.push(roomId));
                    }
                    //temp_room.length && console.warn(`${uid}, ${temp_room}`), total += 1;
                    temp_room.length && save && (await recordMod.setRecord(uid, record), await userMod.updateOne(uid, {forceDownload: true}), console.warn(`${uid},${temp_room}`));
                } catch (e) {
                    console.error(`err data :${uid}`);
                }
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        console.warn(`total : ${total}`);
    },
    async r0831() {
        let where = {}, logic = async (lastId) => {
            lastId && (where._id = {$gt: lastId});
        }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },

    // 这个方法用来一次性上报实名认证信息 那边会处理重复的问题
    async r0920_1() {
        let userIdCardCol = db.createUserIDCardModel();
        let where = {}, exec_ = true, lastId = 0, limit = 10000,
            logic = async (lastId) => {
                lastId && (where._id = {$gt: lastId});
                let docs = await userIdCardCol.find(where).sort({_id: 1}).limit(limit);
                await util.promiseMap(docs, async ({uid, name, idNum}) => {
                    if (!uid || !name || !idNum) return;
                    await userMod.cardInfoSubmitByUid(uid, name, idNum);
                })
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            };
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },
    // 这个方法用来一次性上报猜测实名验证通过的信息 那边会处理重复的问题
    async r0920_2() {
        let guessAdultCol = db.createGuessAdultModel(),
            where = {}, exec_ = true, lastId = 0, limit = 10000,
            logic = async (lastId) => {
                lastId && (where._id = {$gt: lastId});
                let docs = await guessAdultCol.find(where).sort({_id: 1}).limit(limit);
                await util.promiseMap(docs, async ({uid, prop}) => {
                    if (!uid || !prop) return;
                    await userMod.cardInfoSubmitByUid(uid, '', '', prop);
                })
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            };
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },
    async r0922() {
        const currencyMod = require('../mod/currencyModel');
        let gameVer = '4.4.0', lock_start_time = 1665553515000,
            orderCol = db.createCurrencyOrderModel(),
            where = {status: {$in: [1, 3]}},
            logic = async (lastId) => {
                lastId && (where._id = {$gt: lastId});
                let docs = await orderCol.find(where, {
                    uid: 1,
                    action: 1,
                    timestamp: 1
                }).sort({_id: 1}).limit(10000);
                await util.promiseMap(docs, async ({uid, action, timestamp}) => {
                    if (!action.startsWith('iap_')) return;
                    // 为了防止订单被重复增加cost,所以在服务器更新后(lock_start_time)才能跑这个脚本来增加用户付费
                    if (timestamp >= lock_start_time) return;
                    let cfg = currencyMod.getCfgByAction(action, gameVer);
                    cfg && cfg.cost && await userMod.addPayNum(uid, cfg.cost);
                })
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            };
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },
    // 获取非精选但是进了redis的数据
    async r0922_x(remove = false) {
        let rooms = [201, 202, 301, 302, 401, 402], buildPlanCol = db.createBuildPlanModel(), count = 0;
        for (let roomId of rooms) {
            let ids = await this.redis.zrange(redisKey.buildPlansRankInRoom(roomId), 0, -1);
            for (let _id of ids) {
                let doc = await buildPlanCol.findOne({_id: ObjectId(_id)});
                if (doc.state !== 1 && doc.state !== 2) {
                    console.warn(`${_id}, ${doc.state}`), count++;
                    remove && await this.redis.zrem(redisKey.buildPlansRankInRoom(roomId), _id);
                }
            }
        }
        console.log(`total:${count}`);
    },
    // 各个人气段玩家拥有的平均风车数量
    async r1101_a() {
        if (config.type !== 'global') return
        //蜡烛 20000以上忽略
        let max_heart = 20000, limit = 10000;
        let type = "task_wind_avg", map = {}, total = 0
        await this.taskProgressCol.updateOne({type}, {done: 0}, {upsert: true})
        let logic = async (lastId) => {
            let where = {};
            lastId && (where._id = {$gt: lastId});
            let docs = await this.currencyBalanceCol.find(where).sort({_id: 1}).limit(limit)
            total += docs.length
            console.log(total)
            await util.promiseMap(docs, async ({uid, currency, balance}) => {
                if (!uid || !currency || (currency && currency !== 'windmill') || !balance) return
                let doc = await this.userHeartCol.findOne({uid})
                if (!doc || (doc && doc.heart > max_heart)) return
                let udoc = await this.userCol.findOne({uid}, {signupTime: 1})
                if (!udoc) return
                let heart = util.retainHightDigit(doc.heart, 2)
                let signupTime = udoc.signupTime;
                let day = Math.floor(signupTime / util.Time.Day);
                heart = Math.ceil(heart) || 0
                if (!map[day]) map[day] = {}
                if (!map[day][heart]) map[day][heart] = 0
                map[day][heart] += balance
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        await this.taskProgressCol.updateOne({type}, {
            done: 1,
            result: JSON.stringify(map),
            expireTime: util.Time.Day + Date.now()
        }, {upsert: true})
        console.log('end..')
    },
    // 筛选购买过特定礼包的用户基础数据
    async r1205_a(pkgs = []) {
        if (pkgs.length === 0) return
        let limit = 10000, data = []
        let logic = async (lastId) => {
            let where = {
                status: {$in: [1, 3]}
            };
            lastId && (where._id = {$gt: lastId});
            let docs = await this.currnecyOrderCol.find(where).sort({_id: 1}).limit(limit)
            await util.promiseMap(docs, async ({uid, action}) => {
                if (!uid) return
                if (!pkgs.includes(action)) return;
                let [user, record] = await Promise.all([await userMod.findOne(uid), await recordMod.getRecord(uid)])
                if (!user || !record) return
                let temp = await this.currencyBalanceCol.find({uid}), windmill = 0
                temp && temp.forEach(e => e.currency === 'windmill' && (windmill = e.balance))
                data.push({
                    uid,
                    action,
                    lang: user.lang,
                    heart: recordMod.getFromRecord("global.heart", record),
                    biscuit: recordMod.getFromRecord("global.biscuits", record),
                    candy: recordMod.getFromRecord("global.candies", record),
                    windmill,
                    loginDay: recordMod.getFromRecord("loginDayCounts", record),
                    totalPay: user.pay
                })
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        let sheet = [
            ["uid", "action", "lang", "heart", "biscuit", "candy", "windmill", "loginDay", "totalPay"]
        ]
        for (let doc of data) {
            let subAry = [doc.uid || ""
                , doc.action || ""
                , doc.lang || ""
                , doc.heart || 0
                , doc.biscuit || 0
                , doc["candy"] || 0
                , doc["windmill"] || 0
                , doc['loginDay'] || 0
                , doc["totalPay"] || 0
            ];
            sheet.push(subAry)
        }
        let excel = new Excel({sheet})
        let file = `r1205_a-${Date.now()}.xlsx`
        excel.writeFile(file)
        console.log(`共${data.length}条,写入:${file}.`)
    },
    async r1205_b(regS = 0, regE = 0, lastLoginTime = 0) {
        let limit = 2000, data = [], total = 0
        let logic = async (lastId) => {
            let where = {};
            lastId && (where._id = {$gt: lastId});
            let docs = await this.userCol.find(where).sort({_id: 1}).limit(limit)
            total += docs.length
            console.log(total)
            await util.promiseMap(docs, async ({uid, lang, signupTime, loginTime, pay}) => {
                if (regS && signupTime < regS) return
                if (regE && signupTime > regE) return
                if (lastLoginTime && loginTime < lastLoginTime) return
                let record = await recordMod.getRecord(uid)
                let temp = await this.currencyBalanceCol.find({uid}), windmill = 0
                temp && temp.forEach(e => e.currency === 'windmill' && (windmill = e.balance))
                data.push({
                    uid,
                    lang: lang,
                    heart: recordMod.getFromRecord("global.heart", record),
                    biscuit: recordMod.getFromRecord("global.biscuits", record),
                    candy: recordMod.getFromRecord("global.candies", record),
                    windmill,
                    loginDay: recordMod.getFromRecord("loginDayCounts", record),
                    totalPay: pay,
                    signupTime,
                    loginTime
                })
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        console.log(data.length)
        let index = 0
        let sheet = [
            ["uid", "lang", "heart", "biscuit", "candy", "windmill", "loginDay", "totalPay", "signupTime", "loginTime"]
        ]
        for (let doc of data) {
            index++
            let subAry = [doc.uid || ""
                , doc.lang || ""
                , doc.heart || 0
                , doc.biscuit || 0
                , doc["candy"] || 0
                , doc["windmill"] || 0
                , doc['loginDay'] || 0
                , doc["totalPay"] || 0,
                util.dateFormat('yyyy-MM-dd hh:mm:ss', doc.signupTime),
                util.dateFormat('yyyy-MM-dd hh:mm:ss', doc.loginTime)
            ];
            sheet.push(subAry)
            if (index % 100000 === 0) {
                console.log('write')
                let excel = new Excel({sheet})
                let file = `r1205_b-${index / 100000}-${Date.now()}.xlsx`
                excel.writeFile(file)
                sheet = [
                    ["uid", "lang", "heart", "biscuit", "candy", "windmill", "loginDay", "totalPay", "signupTime", "loginTime"]
                ]
            }
        }
        console.log(`共${data.length}条.`)
    },
    async r0114() {
        let col = db.createPartyModel()
        let logic = async (lastId) => {
            let where = {}
            lastId && (where._id = {$gt: lastId});
            let docs = await col.find(where).sort({_id: 1}).limit(5000)
            console.log(docs.length)
            await util.promiseMap(docs, async ({uid, partyId, time, status, joiner, overtime}) => {
                let record = await recordMod.getRecord(uid)
                if (!record) return
                let id = recordMod.getFromRecord("homeParty.selectPartyID", record)
                if (id !== 0) return
                let array = recordMod.getFromRecord("homeParty.partyInfos", record)
                let save = false
                for (let obj of array) {
                    if (obj.partyStatus === 1 || obj.partyStatus === 2 || obj.partyStatus === 3) {
                        obj.partyStatus = 0
                        save = true
                    }
                }
                save && (console.log("fix:", uid), await recordMod.setRecord(uid, record), await userMod.updateOne(uid, {forceDownload: true}))
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },
    async r0114_2() {
        let col = db.createPartyModel()
        let logic = async (lastId) => {
            let where = {}
            lastId && (where._id = {$gt: lastId});
            let docs = await this.userCol.find(where).sort({_id: 1}).limit(10000)
            await util.promiseMap(docs, async ({uid, partyJoin}) => {
                let update = false, temp = []
                let cur
                while (cur = partyJoin.shift()) {
                    let e = await col.findOne({_id: ObjectId(cur)})
                    if (!e) {
                        update = true
                    } else {
                        if (e.joiner && e.joiner.length) {
                            e.joiner.indexOf(uid) > -1 && temp.push(cur)
                            update = true
                        }
                    }
                }
                update && (await this.userCol.updateOne({uid}, {partyJoin: temp}))
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },
    async r119() {
        const col = db.createBlackListModel()
        let docs = await col.find({timestamp: {$gte: 1681097736000}})
        await util.promiseMap(docs, async ({_id, uid, reason, level}) => {
            if (!reason || level > 3) return
            try {
                const obj = JSON.parse(reason)
                for (let o of obj) {
                    if (o.type === 29 && o["curDiff"] <= 100000) {
                        // console.log(_id)
                        await col.deleteOne({_id})
                    }
                }
            } catch (e) {

            }
        })
    },
    async r120() {
        let limit = 10000
        const btoa = (str) => {
            if (!str) return ""
            return Buffer.from(str).toString('base64')
        }
        const atob = (b64) => {
            if (!b64) return ""
            return Buffer.from(b64, 'base64').toString()
        }
        const replace = (str) => {
            return !str || str.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g, '*')
        }
        const changeValIfName = (key, record, base64) => {
            let t = recordMod.getFromRecord(key, record)
            if (!t) return
            if (base64) {
                t = atob(t)
                t = decodeURIComponent(t)
            }
            let y = replace(t)
            if (t !== y) {
                if (base64) {
                    y = encodeURIComponent(y)
                    y = btoa(y)
                }
                recordMod.setToRecord(key, y, record)
            }
        }
        const changeKefang = (key, record) => {
            let obj = recordMod.getFromRecord(key, record)
            if (!obj) return
            if (!obj.furnSaves) return
            if (!obj.furnSaves.length) return
            for (let furnSave of obj.furnSaves) {
                let name = furnSave.name
                let name2 = furnSave.name2
                name = decodeURIComponent(atob(name))
                name2 = decodeURIComponent(atob(name2))
                furnSave.name && (furnSave.name = btoa(encodeURIComponent(replace(name))))
                furnSave.name2 && (furnSave.name2 = btoa(encodeURIComponent(replace(name2))))
            }
        }
        const changeBeauty = (record) => {
            let obj = recordMod.getFromRecord("beauty.currentSpokesPerson", record)
            if (!obj) return
            if (!obj.length) return
            for (let p of obj) {
                let name = p.name
                if (!name) continue
                name = decodeURIComponent(atob(name))
                p.name = btoa(encodeURIComponent(replace(name)))
            }
        }
        const changeGardeningWorks = (record) => {
            let obj = recordMod.getFromRecord("gardening.gardeningWorks", record)
            if (!obj) return
            if (!obj.length) return
            for (let p of obj) {
                let name = p.name
                if (!name) continue
                name = decodeURIComponent(atob(name))
                p.name = btoa(encodeURIComponent(replace(name)))
            }
        }

        let logic = async (lastId) => {
            let where = {uid: '1ec75ccd-26c3-492d-98f3-8c8b74662ef0'};
            lastId && (where._id = {$gt: lastId});
            let docs = await this.userCol.find(where).sort({_id: 1}).limit(limit)
            await util.promiseMap(docs, async ({uid, nickName, diyName}) => {
                const nicknameT = replace(nickName)
                const diyNameT = replace(diyName)
                if (nicknameT !== nickName || diyNameT !== diyName) {
                    await this.userCol.updateOne({uid}, {
                        nickName: nicknameT,
                        diyName: diyNameT
                    })
                }

                let record = await recordMod.getRecord(uid);
                if (!record) return;
                changeValIfName("user.nickname", record, false)
                changeValIfName("world.welcomeWord", record, true)
                changeKefang("kefang_201", record);
                changeKefang("kefang_202", record);
                changeKefang("kefang_301", record);
                changeKefang("kefang_302", record);
                changeKefang("kefang_401", record);
                changeKefang("kefang_402", record);
                changeBeauty(record)
                changeGardeningWorks(record)
                await recordMod.setRecord(uid, record)
                await this.userCol.updateOne({uid}, {
                    forceDownload: true
                })
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },
    async r121() {
        let limit = 10000
        let logic = async (lastId) => {
            let where = {};
            lastId && (where._id = {$gt: lastId});
            let docs = await this.userCol.find(where).sort({_id: 1}).limit(limit)
            await util.promiseMap(docs, async ({uid}) => {
                let record = await recordMod.getRecord(uid);
                if (!record) return
                let mod = recordMod.getFromRecord("gardenasstes", record)
                if (!mod) return
                let unlockPlants = mod["unlockPlants"]
                if (!unlockPlants) return
                let update = false
                for (let u of unlockPlants) {
                    let logs = u["activeLog"]
                    if (!logs || !logs.length) continue
                    logs.splice(15)
                    update = true
                }
                if (update) {
                    await recordMod.setRecord(uid, record)
                    await this.userCol.updateOne({uid}, {
                        forceDownload: true
                    })
                }
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
    },
    async yearExport() {
        const sheet = [
            ["uid", "寄语", "时间", " 精选", "种子"],
            ["uid", "msg", "time", " tag", "seed"]
        ]
        const col = db.createYearActivityModel()
        let logic = async (lastId) => {
            let where = {tag: false};
            lastId && (where._id = {$gt: lastId});
            let docs = await col.find(where).sort({_id: 1}).limit(1000);
            await util.promiseMap(docs, async ({uid, msg, time}) => {
                if (!uid || !msg) return;
                sheet.push([uid, msg, time, "", ""]);
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            } else {
                return 0;
            }
        }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        let excel = new Excel({sheet})
        excel.writeFile(`周年庆寄语导出${Date.now()}.xlsx`)
    },
    async yearImport() {
        const col = db.createYearActivityModel()
        let sheet = xlsx.parse('./import.xlsx');
        let data = sheet[0].data;
        for (let line of data) {
            if (!line[0] || line[0] === "uid") continue
            const del = line[3] === "" || line[3] === undefined
            if (del) {
                await col.deleteOne({uid: line[0], time: line[2]})
                continue
            }
            let seed = line[4]
            if (seed === undefined) seed = 1
            seed = Number(line[4])
            await col.updateOne({uid: line[0], time: line[2]}, {tag: true, seed})
        }
    }
}

module.exports = statisticsMod;
