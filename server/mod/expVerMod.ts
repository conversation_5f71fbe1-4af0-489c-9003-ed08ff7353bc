const logger = require("../common/log").getLogger("expVerMod");
let db = require("../db/db");
import util from "../common/util"
import got from "got"
import recordMod from "./recordMod"
import userMod from "./userMod"
import config from "../config"

class ExpVerMod {
    private expVerCol: any = null
    public init() {
        this.expVerCol = db.createExpVerModel()
    }

    public async needSync(uid: string, version: string) {
        let expVerDoc = await this.expVerCol.findOne({uid})
        if (expVerDoc) {
            return util.cmpVersion(expVerDoc.version, config.experienceVer) < 0
        }
        return true
    }

    public async syncByTpid(tpid1: string, tpid2: string | null, version: string) {
        let info = await recordMod.debugCopyByTpid(tpid1, tpid2)
        return await this.sync(info.uid, info.record, version)
    }

    public async syncByUid(uid: string, version: string) {
        let record = await recordMod.debugCopy(uid)
        return await this.sync(uid, record, version)
    }

    private async sync(uid: string, record, version: string) {
        if (!uid) {
            return
        }
        if (record) {
            record = JSON.parse(record)
            let newRecord = recordMod.fixGameNameSpace(record)
            await recordMod.setRecord(uid, newRecord)
        }
    
        let doc
        try {
            doc = await userMod.updateOne(uid, {forceDownload: true})
        } catch (error) {
        }
        await this.expVerCol.updateOne({uid}, {version}, {upsert: true})
        return doc
    }
}

export const expVerMod = new ExpVerMod()