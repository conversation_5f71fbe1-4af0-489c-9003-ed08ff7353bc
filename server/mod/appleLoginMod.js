const logger = require("../common/log").getLogger("appleLoginMod");
const jwt = require('jsonwebtoken');
const got = require('got');
const fs = require('fs');
const util = require("../common/util").default;
const jwkToPem = require('jwk-to-pem');
const FormData = require('form-data');
const request = require('request');
const config = require("../config");
const apn = require("@parse/node-apn");
const {Platform} = require("../common/constant");
const db = require("../db/db");
const apple = config.appleLogin

let appleLoginMod = {
    _applePriveteKey: null,
    _appPublicKeys: null,
    provider: null,
    deviceTokenCol: null,

    init() {
        if (apple) {
            let {kid, iss, filePath, production} = apple
            if (apple.apn) {
                ({kid, iss, filePath} = apple.apn)
            }
            if (production !== false) production = true
            let options = {
                token: {
                    key: filePath,
                    keyId: kid,
                    teamId: iss,
                },
                production,
            };

            this.provider = new apn.Provider(options)
            this.deviceTokenCol = db.createDeviceToken()
        }
        return this
    },

    jwtVerify(arg1, arg2) {
        return new Promise((resolve, reject) => {
            jwt.verify(arg1, arg2, (err, info) => {
                if (err) {
                    reject(null);
                } else {
                    resolve(info);
                }
            });
        });
    },

    async checkCode(token, code) {
        let formMap = new FormData();
        formMap.append("client_id", config.appleLogin.sub);
        formMap.append("client_secret", token);
        formMap.append("code", code);
        formMap.append("grant_type", "authorization_code");

        let url = "https://appleid.apple.com/auth/token";

        let options = {
            // timeout: 5 * 1000,
            body: formMap,
        };

        // let data = await got.post(url, options).json();
        let data = await this.testRequest(code, token);
        logger.info("checkCode data is ", data, code, token);
        let {refresh_token, id_token} = data;
        if (refresh_token && id_token) {
            let headerStr = id_token.split(".")[0];
            let kid = JSON.parse(Buffer.from(headerStr, 'base64')).kid;
            let publicKey = await this.getPublicKey(kid);
            let info = await this.jwtVerify(id_token, publicKey);
            return info;
        } else {
            return {};
        }
    },

    async getPublicKey(kid) {
        if (!this._appPublicKey) {
            let data = await got("https://appleid.apple.com/auth/keys", {
                timeout: 10 * util.Time.Second,
            }).json();
            this._appPublicKeys = data;
            let {keys} = this._appPublicKeys, realKey;
            for (let i = 0; i < keys.length; i++) {
                if (keys[i].kid == kid) {
                    realKey = keys[i];
                    break;
                }
            }
            let pem = jwkToPem(realKey);
            return pem;
        } else {
            let keys = this._appPublicKeys.keys, realJwk;
            for (let i = 0; i < keys.length; i++) {
                if (keys[i].kid == kid) {
                    realJwk = keys[i];
                    break;
                }
            }
            let pem = jwkToPem(realJwk);
            return pem;
        }
    },

    getPriveteKey() {
        return new Promise((resolve, reject) => {
            if (!this._applePriveteKey) {
                fs.readFile(config.appleLogin.filePath, function (err, file) {
                    if (err) {
                        reject(null);
                        logger.info("getPrivetKey is err ", err);
                    } else {
                        this._applePriveteKey = file.toString();
                        resolve(this._applePriveteKey);
                    }
                });
            } else {
                resolve(this._applePriveteKey);
            }
        })
    },

    async createToken() {
        let priveteKey = await this.getPriveteKey();
        let token, validity_period = 80 * 24 * 3600;
        try {
            token = jwt.sign({
                iss: config.appleLogin.iss,
                iat: Date.parse(new Date()) / 1000,
                exp: Date.parse(new Date()) / 1000 + validity_period,
                aud: "https://appleid.apple.com",
                sub: config.appleLogin.sub
            }, priveteKey, {
                algorithm: "ES256",
                header: {alg: "ES256", kid: config.appleLogin.kid}
            });
        } catch (e) {
            token = null;
            logger.error("apple createToken", e);
        }
        return token;
    },

    async testRequest(code, token) {
        return new Promise((resolve, reject) => {
            let bodyMap = {
                "client_id": config.appleLogin.sub,
                "client_secret": token,
                "code": code,
                "grant_type": "authorization_code",
            };

            let options = {
                url: "https://appleid.apple.com/auth/token",
                timeout: 10 * 1000,
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                },
                form: bodyMap
            }

            request(options, function (err, resp, result) {
                if (err) {
                    logger.error("apple request", err, code, token);
                    reject(null);
                } else {
                    resolve(JSON.parse(result));
                }
            });
        })
    },

    async notify(deviceToken, args = {}, uid) {
        let msg = new apn.Notification();
        try {
            if (args.expiry == undefined) args.expiry = Math.floor((Date.now() + util.Time.Day) / 1000) //默认一天过期
            msg.topic = apple.sub
            for (let key in args) {
                msg[key] = args[key]
            }
            let res = await this.provider.send(msg, deviceToken)
            if (res.failed && res.failed.length > 0) {
                let fail = res.failed[0]
                if (fail.response && fail.response.reason == "BadDeviceToken") {
                    logger.warn("delete bad token", uid, deviceToken)
                    await this.deviceTokenCol.deleteOne({uid, token: deviceToken})
                } else {
                    logger.error("apple notify fail", uid, res)
                }
                return false
            } else {
                logger.debug(res)
            }
            return true
        } catch (error) {
            logger.error("apple notify fail", uid, deviceToken, error)
            return false
        }
    },

    async sendSubscribeMessage(data, type, uid) {
        let {msg, title, token} = JSON.parse(data)
        if (!token) {
            let doc = await this.deviceTokenCol.findOne({uid, platform: Platform.IOS})
            if (!doc) return false
            token = doc.token
        }
        let info
        if (title) {
            info = {alert: {body: msg, title}}
        } else {
            info = {alert: msg}
        }
        return this.notify(token, info, uid)
    },
    // 撤销令牌
    async revoke(token, access_token) {
        return new Promise((resolve, reject) => {
            let bodyMap = {
                "client_id": config.appleLogin.sub,
                "client_secret": token,
                "token": access_token,
                //"token_type_hint": "access_token",
            };
            let options = {
                url: "https://appleid.apple.com/auth/revoke",
                timeout: 10 * 1000,
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                },
                form: bodyMap
            }
            request(options, function (err, resp, result) {
                if (err) {
                    logger.error("apple request", err, access_token, token);
                    reject(null);
                } else {
                    let obj = {};
                    result && (obj = JSON.parse(result));
                    resolve(obj);
                }
            });
        })
    },

}

module.exports = appleLoginMod;