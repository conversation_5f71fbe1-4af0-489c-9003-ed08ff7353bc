const userMod = require("./userMod");
const recordMod = require("./recordMod");
const friendMod = require("./friendMod");
const subscribeMod = require("./subscribeMod");
const db = require("../db/db");
const rankMod = require("./rankMod");
const {default: util} = require("../common/util");
const redisKey = require("../db/redisKey");
// const loginMod = require("./loginMod");
const logger = require("../common/log").getLogger('wdWorkMod');
const {SubscribeType} = require("../common/constant")
const robotMod = require("./robotMod");

const MAX_APPLY_COUNT = 20// 最大申请数量
const WORK_INVITE_EXCEED = 12 * 3600 * 1000// 工作邀请过期时限
const WORKING_HOURS = 6 * 3600 * 1000// 工作时长-6小时
const WORK_REWARD = '4,9100,15'// 工作完成奖励

// const WORK_INVITE_EXCEED = 5 * 60 * 1000// 测试数据
// const WORKING_HOURS = 10 * 60 * 1000// 测试数据

const SUCCESS = 0

const UID_EQUALITY = 2001// uid 相同
const ALREADY_TO_WORK = 2003// 乌冬已经派出去工作
const ALREADY_FOR_WORK = 2004// 已经乌冬为Ta工作
const ALREADY_LOCKED = 2005// 打工邀请已被锁定
const RECEIVE_WORK_REWARD_FAIL = 2010// 领取工作奖励失败

const WORK_SUBSCRIBE_TEMPLATE_ID = 'o8dK-4OwErRze2vsFXcga3VFbDnssCr_WaKkSWzPU_w'

let workingCol
let applyCol
let redis
let loginInfoCol

let wdWorkMod = {

    async init() {
        workingCol = db.createWdWorkingModel()
        applyCol = db.createWdWorkApplyModel()
        loginInfoCol = db.createLoginInfoModel()
        redis = db.redis
    },

    // 获取好友工作列表
    async getFriendWorkList(uid) {
        let friends = await friendMod.getFriendList(uid, false)
        return await util.promiseMap(friends, async (item) => {
            if (!robotMod.isRobot(item.uid)) {
                let work = await workingCol.findOne({uid: item.uid, receive: 0})
                if (work) {// 该好友已同意其他玩家的工作邀请
                    return
                }
            }
            let apply = await applyCol.findOne({
                uid: uid,
                toUid: item.uid,
                date: {$gt: Date.now() - WORK_INVITE_EXCEED}
            })
            if (apply) {// 已向该好友发出工作邀请
                return
            }

            let user = await wdWorkMod.suppUserData(item.uid)
            if (user) {
                let favor = await friendMod.getFavorability(uid, item.uid)
                user.level = favor.level
                user.exp = favor.exp
            }

            return user
        })
    },

    // 获取邀请工作列表
    async getInviteWorkList(uid, detail = true) {
        let list = await applyCol.find({toUid: uid}).sort({date: -1}).limit(5)// 只获取最新5条工作邀请
        return detail ? await Promise.all(list.map(m => wdWorkMod.suppUserData(m.uid, m.date))) : list
    },

    // 邀请好友工作
    async inviteWork(uid, toUid, rate = 0) {
        if (uid == toUid) {
            return UID_EQUALITY
        }

        let doc = await applyCol.findOne({uid, toUid, date: {$gt: Date.now() - WORK_INVITE_EXCEED}})
        if (doc) {// 重复申请
            return SUCCESS
            // return WORK_REAPPLY
        }
        // 判断目标是不是机器人
        if (robotMod.isRobot(toUid)) {
            //rate 单个邀请 20的概率同意
            //rate 多个邀请 10的概率同意
            if (robotMod.isAgreeWorkInvite(rate)) {
                await applyCol.updateOne({uid, toUid, date: Date.now()}, {}, {upsert: true})
                await wdWorkMod.agreeInviteWork(toUid, uid)
            }
        } else {
            await applyCol.updateOne({uid, toUid, date: Date.now()}, {}, {upsert: true})
        }

        let list = await applyCol.find({uid}, {_id: 1})// 已申请列表
        if (list.length >= MAX_APPLY_COUNT) {// 申请数量超过上限
            await applyCol.deleteOne({_id: list[0]._id})
        }

        // // 测试代码，被邀请人默认同意
        // wdWorkMod.agreeInviteWork(toUid, uid)

        return SUCCESS
    },

    // 一键邀请工作
    async inviteAllWork(uid, list) {
        let robotDone = false
        // 先邀请机器人
        const robots = list.filter(m => robotMod.isRobot(m))
        if (robots.length !== 0) {
            const index = util.random(0, robots.length)
            await wdWorkMod.inviteWork(uid, robots[index], 10)
            robotDone = true
        }
        if (robotDone) return SUCCESS
        await Promise.all(list.map(m => {
            if (robotMod.isRobot(m)) return
            wdWorkMod.inviteWork(uid, m, 10)
        }))
        return SUCCESS
    },

    // 同意工作邀请
    async agreeInviteWork(uid, toUid) {
        // 非机器人才判断乌冬状态
        if (!robotMod.isRobot(uid) && !robotMod.isRobot(toUid)) {
            let toWork = await wdWorkMod.synchroWudongWorkState(uid, 1)
            if (toWork.toWork) {// 乌冬已经派出去工作
                await applyCol.deleteOne({uid: toUid, toUid: uid})
                return ALREADY_TO_WORK
            }
            let forWork = await wdWorkMod.synchroWudongWorkState(toUid, 2)
            if (forWork.forWork) {// 已经乌冬为Ta工作
                await applyCol.deleteOne({uid: toUid, toUid: uid})
                return ALREADY_FOR_WORK
            }
        }

        let lock = await redis.getset(redisKey.inviteWorkLock(toUid), 1)
        redis.expire(redisKey.inviteWorkLock(toUid), 6)
        if (lock) {// 打工邀请已被锁定
            await applyCol.deleteOne({uid: toUid, toUid: uid})
            return ALREADY_LOCKED
        }

        await workingCol.updateOne({uid, toUid, date: Date.now()}, {receive: 0}, {upsert: true})

        await applyCol.deleteMany({toUid: uid, date: {$gt: Date.now() - WORK_INVITE_EXCEED}})// 拒绝其他工作邀请
        await applyCol.updateMany({
            uid: toUid,
            date: {$gt: Date.now() - WORK_INVITE_EXCEED}
        }, {date: Date.now() - WORK_INVITE_EXCEED})//将已发送的其他工作邀请标记过期

        subscribeMod.notifyNow(toUid, SubscribeType.WUDONG_WORK)

        return SUCCESS
    },

    // 拒绝工作邀请
    async rejectInviteWork(uid, toUid) {
        await applyCol.deleteOne({uid: toUid, toUid: uid})
        return SUCCESS
    },

    // 领取工作奖励
    async receiveWorkReward(uid) {
        let doc = await workingCol.findOne({uid, receive: 0})
        if (!doc || doc.date + WORKING_HOURS > Date.now()) {// 没有可领取的奖励
            return RECEIVE_WORK_REWARD_FAIL
        }

        await workingCol.updateOne({uid, receive: 0}, {receive: 1})

        return SUCCESS
    },

    // 获取我的乌冬在谁家工作
    async getWudongToWork(uid) {
        let user = null
        let doc = await workingCol.findOne({uid, receive: 0})
        if (doc) {
            user = await wdWorkMod.suppUserData(doc.toUid, doc.date, doc.date + WORKING_HOURS - Date.now())
            if (doc.toUid && !user) {
                await workingCol.deleteOne({uid, receive: 0})
            }
        }

        return {status: SUCCESS, user: user}
    },

    // 获取谁的乌冬在我家工作
    async getWudongForWork(uid) {
        let user = null
        let doc = await workingCol.findOne({toUid: uid, date: {$gt: Date.now() - WORKING_HOURS}})
        if (doc) {
            user = await wdWorkMod.suppUserData(doc.uid, doc.date, doc.date + WORKING_HOURS - Date.now())
        }

        // let isFix = await loginMod.isFixWudong(uid)
        // if (isFix) {
        //     return {status: SUCCESS}
        // }

        return {status: SUCCESS, user: user}
    },

    // 获取我的乌冬工作记录
    async getWudongWrokRecord(uid) {
        let list = await workingCol.find({uid, receive: 1}).sort({date: -1}).limit(3)// 只获取最新3条工作记录
        return await Promise.all(list.map(m => wdWorkMod.suppUserData(m.toUid, m.date)))
    },

    // 获取我的乌冬来访记录
    async getWudongComeRecord(uid) {
        let list = await workingCol.find({toUid: uid}).sort({date: -1}).limit(3)// 只获取最新3条来访记录
        for (let i = 0; i < list.length; i++) {
            list[i] = {uid: list[i].uid, start: list[i].date, leave: list[i].date + WORKING_HOURS}
        }

        return list
    },

    // 同步乌冬工作状态
    async synchroWudongWorkState(uid, type = 0) {
        let toWork, forWork, subscribe
        if (type == 0) {
            [toWork, forWork, subscribe] = await Promise.all([workingCol.findOne({
                uid,
                receive: 0
            }), workingCol.findOne({
                toUid: uid,
                date: {$gt: Date.now() - WORKING_HOURS}
            }), subscribeMod.findSub(uid, SubscribeType.WUDONG_WORK)])
        } else if (type == 1) {
            toWork = await workingCol.findOne({uid, receive: 0})
        } else {
            forWork = await workingCol.findOne({toUid: uid, date: {$gt: Date.now() - WORKING_HOURS}})
        }

        return {
            status: SUCCESS,
            toWork: toWork ? {
                uid: toWork.toUid,
                cd: toWork.date + WORKING_HOURS - Date.now()
            } : null,
            forWork: forWork ? {
                uid: forWork.uid,
                cd: forWork.date + WORKING_HOURS - Date.now()
            } : null,
            subscribe: subscribe != null
        }
    },

    // 同步乌冬详细数据
    async synchroWudongWorkDetail(uid) {
        let userDoc = await userMod.getNameAndAvatar(uid)
        let record = await recordMod.getRecord(uid)
        return {status: SUCCESS, wudong: {uid, nickName: userDoc.nickName, skinId: recordMod.getWudongSkin(record)}}
    },

    // 清理乌冬过期邀请和工作记录
    async clearWdWork() {
        await wdWorkMod.whileClear(workingCol)
        await wdWorkMod.whileClear(applyCol)
    },

    // 循环清理
    async whileClear(col, _id) {
        let exceed = 24 * 3600 * 1000// 过期时间
        let where = {}
        if (_id) {
            where = {_id: {$lt: _id}}
        }

        let docs = await col.find(where).sort("-_id").limit(1000)
        for (let doc of docs) {
            // 删除1天以前并且领取过奖励的工作记录 删除7天以前未领取过奖励的工作记录
            const diff = Date.now() - doc.date
            if (col == workingCol) {
                if (doc.receive === 1 && diff > exceed) {
                    await col.deleteOne({_id: doc._id})
                    continue
                }
                if (doc.receive === 0 && diff > exceed * 7) {
                    await col.deleteOne({_id: doc._id})
                    continue
                }
            }
            // 删除1天以前所有的邀请工作记录
            if (col == applyCol && diff > exceed) {
                await col.deleteOne({_id: doc._id})
            }
        }

        if (docs.length > 0) {
            await wdWorkMod.whileClear(col, docs[docs.length - 1]._id)
        }
    },

    // 补全用户数据-头像&昵称&蜡烛&日期&倒计时
    async suppUserData(uid, date, cd) {
        if (!uid) {
            return null
        }

        let userDoc = await userMod.getUserCacheInfo(uid)
        if (!userDoc) {
            return null
        }

        let heart = await recordMod.getHeartByUid(uid, userDoc.serverId)
        let nickName = userDoc.nickName
        let avatarUrl = userDoc.avatarUrl

        return {uid, nickName, avatarUrl, heart, date, cd, reward: WORK_REWARD}
    }
}

module.exports = wdWorkMod;
