const logger = require("../common/log").getLogger("motherMsgMod");

let motherMsgMod = {

    motherMsgCol: null,

    init() {
        let db = require("../db/db");
        this.motherMsgCol = db.createMotherMsgModel()
    },

    async submit(uid, content) {
        let contentLen = content.length
        let now = Date.now()
        await this.motherMsgCol.create({uid, content, status: 0, contentLen, timestamp: now})
    }
}

module.exports = motherMsgMod;