const { default: util } = require("../common/util");
const wxMod = require('../mod/wxMod');
const logger = require("../common/log").getLogger('couponMod');
const crypto = require('crypto')
const got = require('got')

const COUPON_ALPHA_RATE = 1// 灰度用户比例

const COUPON_GROUP = {
    ALL: {
        size: 2,
        category: null
    },// 全部
    GROUP_1: {
        size: 1,
        category: [
            112999// 美团外卖
        ]
    }// 实验分组
}

// 优惠券触发渠道及权重配置
const COUPON_CHANNEL = {
    DAILY_TASK: {
        ALL: 50,
        GROUP_1: 200
    }// 每日任务
}

let couponMod = {

    async init() {

    },

    // 获取优惠券列表
    async getCouponList(uid, openid, channel, isApp) {
        if (Math.random() > COUPON_ALPHA_RATE) {// 非灰度选中用户
            return []
        }

        // -------- 分组实验 --------
        let group = COUPON_GROUP.ALL// 默认分组
        let weights = []
        for (let key in COUPON_CHANNEL[channel]) {
            weights.push({ key, value: COUPON_CHANNEL[channel][key] })
        }
        if (weights.length > 0) {
            let current = 0
            let random = Math.random() * weights.reduce((sum, m) => sum + m.value, 0)
            for (let i = 0; i < weights.length; i++) {
                current += weights[i].value
                if (random <= current) {
                    let channel = weights[i].key
                    group = COUPON_GROUP[channel]
                    break
                }
            }
        }
        // -------- 分组实验 --------

        let { plaintext, signature } = couponMod.getSignature()
        try {
            let url = isApp ? 'https://daihuo.qq.com/union/recommend/product/list' : 'https://daihuo.qq.com/recommend/product/list'
            let json = {
                open_id: openid,
                mp_appid: wxMod.getConfig().appid,
                size: group.size,
                query: {
                    cate_list: group.category
                },
                union_req_info: isApp ? {
                    union_id: uid,
                    position_id: 3052119852356968
                } : null
            }
            let headers = {
                'X-SR-Authorization': `${plaintext}&sign=${signature}`
            }

            let data = await got.post(url, {// 请求 daihuo.qq.com 域名偶发报错
                json,
                headers,
                timeout: 10 * util.Time.Second
            }).json();

            if (!data || data.ret != 0) {
                return []
            }

            return data.product_list
        } catch (error) {
            return []
        }
    },

    // 优惠券曝光度提交
    async submitCouponExposure(uid, openid, url) {
        let { plaintext, signature } = couponMod.getSignature()
        try {
            await got(url, {
                headers: {
                    'X-SR-Authorization': `${plaintext}&sign=${signature}`
                }
            })
        } catch (error) {

        }

        return 0
    },

    // 优惠券点击数提交
    async submitCouponClick(uid, openid, url) {
        let { plaintext, signature } = couponMod.getSignature()
        try {
            await got(url, {
                headers: {
                    'X-SR-Authorization': `${plaintext}&sign=${signature}`
                }
            })
        } catch (error) {

        }

        return 0
    },

    getSignature() {
        const appID = 'wx29ce061628bce09e'
        const appSecret = '66d043033e6fda2a387199205369ce66'
        const plaintext = `appid=${appID}&nonce_str=${util.getRandomString(10)}&time_stamp=${Math.floor(Date.now() / 1000)}`
        const signature = crypto.createHmac('sha1', appSecret).update(plaintext).digest('hex').toLowerCase()

        return { plaintext, signature }
    }
}

module.exports = couponMod;