const logger = require("../common/log").getLogger("VerfiyRecordMod");
let db = require("../db/db");
import util from "../common/util"
import recordMod from "./recordMod"
import userMod from "./userMod"
import config from "../config"
import ModelMgr from "../client/ModelMgr";
import OfflineSysModel from "../client/OfflineSysModel";
import DBModel from "../client/DBModel";
import { ConditionType, MapSceneType } from "../client/constant/Enums";
import { assetsMgr } from "../client/AssetsMgr";

const { redeemCodeMod } = require("./redeemCodeMod")
import verifyMod from "./verifyMod"
import rankMod from "./rankMod"
import { gameHelper } from "../client/GameHelper";
import { YOYO_PROP_ID } from "../client/constant/Constant";
import redisKey from "../db/redisKey"
import { CURRENCY, CURRENCY_ACTION, CURRENCY_ORDER_STATUS, } from "../common/constant"

const shopRewardConfig = require("../client/json/tutuShopReward.json");
const shopExchangeConfig = require("../client/json/tutuShopExchange.json");
import activityMod from "./activityMod"

const reqMap = {}

let whiteList = []

let blacks = []
// 蜡烛增量基数
const HEART_BASE = 35636
// 金叶子任务基数
const LEAVE_BASE = 35800

const LEAVE_ADD = 4 * 60

class VerfiyRecordMod {

    private blackListCol: any = null
    private ipCol: any = null
    private deviceCol: any = null
    private redis: any = null
    private blackListHistoryCol: any = null
    private currencyOrderCol: any = null
    private tempBlackTagCol: any = null
    private specialActivityCol: any = null
    private blackTagCol: any = null
    private tutuData: { id: number, reward: string }[] = null

    public init() {
        this.blackListCol = db.createBlackListModel()
        this.ipCol = db.createIpModel()
        this.deviceCol = db.createDeviceModel()
        this.redis = db.redis
        this.blackListHistoryCol = db.createBlackListHistoryModel()
        this.currencyOrderCol = db.createCurrencyOrderModel()
        this.tempBlackTagCol = db.createTempBlackTagModel()
        this.specialActivityCol = db.createSpecialActivityModel()
        this.blackTagCol = db.createBlackTagModel()
        this.tutuData = []
        shopRewardConfig.forEach(r => {
            const exists = this.tutuData.find(l => l.reward == r.reward)
            if (exists) return
            this.tutuData.push({ id: r.id, reward: r.reward })
        })
        shopExchangeConfig.forEach(r => {
            const exists = this.tutuData.find(l => l.reward == r.reward)
            if (exists) return
            this.tutuData.push({ id: r.id, reward: r.reward })
        })
    }

    public async check(uid, record, reCheck, ip?, deviceId?, gameVer?): Promise<number> {
        if (config.debug) return 0
        try {
            if (!reCheck) {
                let [lv, good] = await Promise.all([
                    verifyMod.getBlackLevel(uid),
                    verifyMod.isGood(uid),
                ])
                if (good) {
                    logger.info("check good", uid)
                    return 0
                }
                if (whiteList.includes(uid)) return 0
                if (lv >= 3) return lv
            }
            if (ip) {
                if (util.cmpVersion(gameVer, "3.0.0") < 0) {
                    logger.warn("verfiy low_version2", uid, ip, deviceId, gameVer)
                    return 100
                }
            }

            let level = 0
            const fator = 2

            let promiseList = [userMod.findOne(uid, { signupTime: 1, updateTime: 1, forceDownload: 1 })]
            // if (ip) {
            //     promiseList.push(this.ipCol.findOne({uid, ip}))
            // }
            // if (deviceId) {
            //     promiseList.push(this.deviceCol.findOne({uid, deviceId}))
            // }
            promiseList.push(redeemCodeMod.getCodesByUid(uid))

            let key = redisKey.recordUploadLock(uid)
            promiseList.push(this.redis.get(key))

            promiseList.push(this.getUserCurrencyOrders(uid))

            promiseList.push(recordMod.getRecord(uid))

            promiseList.push(activityMod.getActivityData(uid))


            let modelMgr = new ModelMgr()
            modelMgr.init(record)
            let main = modelMgr.getMain()

            let offline = modelMgr.get<OfflineSysModel>('offline')
            let db = modelMgr.getDB()
            let global = modelMgr.global
            let offlineIncome = offline.getOfflineExpectedIncome()
            let onlineIncome = offline.getOnlineExpectedIncome()
            let accTotalBiscuits = Math.max(global.getBiscuits(), global.getAccTotalBiscuits())
            let accTotalCandies = Math.max(global.getCandies(), global.getAccTotalCandies())
            let heart = global.getHeart()
            let playTime = recordMod.getPlayTime(record)
            let passDay = recordMod.getLoginDay(record)
            let accTotalCompFinishCount = global.getAccTotalCompFinishCount()
            let accTotalAdCount = global.getAccTotalAdCount()
            let addBCS_Value = Number(recordMod.getFromRecord('addBCS_Value', record) || 0) //饼干随机奖励的总和
            let addCA_Value = Number(recordMod.getFromRecord('addCA_Value', record) || 0)
            let achieveAward = modelMgr.getAchieveAwardSum()
            let roleAward = modelMgr.getRoleAwardSum()
            let galiCardAward = modelMgr.getGaliPostcardAwardSum()
            let bisBuildCost = modelMgr.getAllBuildCost(ConditionType.BISCUITS)
            let bisMainSkinCost = modelMgr.getAllMainSkinCost(ConditionType.BISCUITS)
            let bisWudongSkinCost = modelMgr.getAllWudongSkinCost(ConditionType.BISCUITS)
            let candyBuildCost = modelMgr.getAllBuildCost(ConditionType.CANDIES)
            let candyMainSkinCost = modelMgr.getAllMainSkinCost(ConditionType.CANDIES)
            let candyWudongSkinCost = modelMgr.getAllWudongSkinCost(ConditionType.CANDIES)
            let mainAttrCost = modelMgr.getMainAttrCos()
            let mapBiscuits = modelMgr.world.getAllDropSumCount()
            let aTipBiscuits = main.getTipMoney()
            let curStamina = recordMod.getFromRecord("wudong.curStamina", record)
            let bag = modelMgr.bag
            let user = recordMod.getFromRecord('user', record)
            if (user.uid != uid) {
                logger.error("verfiy uid diff", uid, user.uid, ip, deviceId, gameVer)
                return 10
            }
            //财神
            let accTotalSeeoffMammonCount = global.getAccTotalSeeoffMammonCount()
            const bisSeeofMamonVal = Math.ceil(((10 + 0.9 * onlineIncome) * 100 * 0.01))
            const bisSeeofMamonExpClick = 30
            let bisSeeofMamonLimit = accTotalSeeoffMammonCount * bisSeeofMamonVal * bisSeeofMamonExpClick

            let [userDoc, codes, isLock, currencyMap, oldRecord, activityData] = await Promise.all(promiseList) //异步在这里wait，上面是耗时计算，下面基本就是判断

            let codeMap = this.getCodeMap(codes)

            // if (isLock) {
            //     logger.error("record lock", uid, ip, deviceId, isLock)
            //     return 10
            // }
            if (!userDoc) {
                logger.error("verfiy user not found", uid)
                return 0
            }
            if (userDoc.forceDownload) {
                logger.error("verfiy user forceDownload", uid)
                return 0
            }
            //TODO 0818,新增一个 ignoreBanPick  可以让玩家直接上传存档,客服方便处理部分玩家长时间没上传存档的封号 这个标记只能用一次
            if (userDoc.ignoreBanPick) {
                logger.info("check : ignoreBanPick", uid);
                await userMod.updateOne({ uid }, { ignoreBanPick: false });
                return 0
            }

            let reasons = []

            let signupTime = userDoc.signupTime
            let realPassTime = Date.now() - signupTime //防止本地数据已经被篡改
            playTime = Math.min(playTime, realPassTime)
            passDay = Math.min(passDay, Math.ceil(realPassTime / util.Time.Day))

            let playTimeMinute = Math.ceil(playTime / util.Time.Minute)
            let onlineLimit = heart * 10000 + playTimeMinute * onlineIncome * 20
            let offlineLimit = passDay * offlineIncome * 22 * 60
            let biscuitsLimit = onlineLimit + offlineLimit + 10000 //产出上限
            let bisCompLimit = accTotalCompFinishCount * 50000
            let bisDoubelTime = 5
            let bisDoubleAdLimit = Math.min(playTimeMinute / bisDoubelTime, accTotalAdCount * 0.5) * onlineIncome * 20 * 3 * bisDoubelTime
            let otherLimit = (achieveAward.biscuits * 2 + roleAward.biscuits + galiCardAward.biscuits) + bisCompLimit + bisDoubleAdLimit + bisSeeofMamonLimit //其他奖励
            biscuitsLimit += otherLimit
            biscuitsLimit += addBCS_Value
            biscuitsLimit += codeMap["1_1"] || codeMap["1_-1"] || 0
            let bisCurrencyLimit = (currencyMap["1_-1"] || currencyMap["1_1"] || 0) * 2
            biscuitsLimit += bisCurrencyLimit

            // 两次存档的时间差
            let timeDiff = Date.now() - userDoc.updateTime;
            // 两次存档的payTime时间差 统一转换为秒计算
            const playTimeDiff = Math.min(playTime - recordMod.getPlayTime(oldRecord) / 1000 + 5 * 60, timeDiff / 1000, 5 * 3600);
            const garden = modelMgr.world.getGarden()
            // 花园叶子计算
            // if (garden) {
            //     let oldModelMgr = new ModelMgr()
            //     oldModelMgr.init(oldRecord)
            //     // 分钟
            //     let diff = playTimeDiff / 1000 + LEAVE_ADD
            //         , output = garden.getCurrentLeavesOutPut()
            //         , curDiff = garden.getTotalLeaves() - oldModelMgr.world.getGarden().getTotalLeaves()
            //     // 产出判断
            //     if (curDiff && curDiff > garden.getCurrentLeavesOutPut() * diff) {
            //         reasons.push({type: 29, diff, output, curDiff});
            //         level += 3
            //     }
            // }

            if (!config.type || (config.type && config.type !== 'global')) {
                // 存档对比广告次数
                // 两次存档的payTime时间差
                let playTimeDiff = playTime - recordMod.getPlayTime(oldRecord);
                // 两次存档的时间差
                let timeDiff = Date.now() - user.updateTime;
                // 两次存档广告次数
                let _old = Number(recordMod.getFromRecord("global.accTotalAdCount", oldRecord) || 0)
                    , _new = Number(recordMod.getFromRecord("global.accTotalAdCount", record) || 0);
                // 统一转换为秒计算
                playTimeDiff = Math.min(playTimeDiff / 1000 + 5 * 60, timeDiff / 1000, 5 * 3600);
                let adCount = _new - _old;
                if ((adCount > playTimeDiff / 10 && adCount > 15)
                    || (adCount > playTimeDiff / 15 && adCount > 20)
                    || (adCount > playTimeDiff / 20 && adCount > 25)
                    || (adCount > playTimeDiff / 25 && adCount > 30)) {
                    reasons.push({ type: 100, adCount, playTimeDiff });
                    level += 3;
                }
                // 检查广告次数合理性 maxAdCount = min(100+游戏时长 【小时】* 30, 100+(注册天数+1) * 40)
                let playTimeHour = playTime / util.Time.Hour
                    , signupDay = (Date.now() - signupTime) / util.Time.Day + 1
                    , maxAdCount = Math.min(playTimeHour * 30, signupDay * 40);
                maxAdCount += 500;
                maxAdCount = Math.ceil(maxAdCount);
                //TODO 广告均时 调整为20s
                let adAvgTime = 20000;
                if ((maxAdCount < accTotalAdCount && accTotalAdCount - maxAdCount > 50) && playTime / adAvgTime / 2 < maxAdCount) {
                    //console.log(`maxAdCount:${maxAdCount}, accTotalAdCount:${accTotalAdCount},uid:${uid}`);
                    reasons.push({ type: 101, maxAdCount, accTotalAdCount });
                    level += 3
                }
                // 检查饼干包 国内暂时没得付费 maxCPCount = 40+注册天数 * 25
                let coinPackIds = [9201, 9202, 9203, 9204], coinPackCount = 0, maxCoinPackCount = 0;
                for (let biscuitsPackageId of coinPackIds) {
                    coinPackCount += bag.getPropCount(biscuitsPackageId);
                }
                coinPackCount > 0 && (maxCoinPackCount = 40 + signupDay * 25);
                coinPackCount > 0 && maxCoinPackCount < coinPackCount && (level += 6, reasons.push({
                    type: 102,
                    maxCoinPackCount,
                    coinPackCount
                }));
                // if (tags.length) {
                //     await this.tempBlackTagCol.updateOne({uid}, {
                //         reason: JSON.stringify(tags),
                //         timestamp: Date.now(),
                //         recordBuff: await recordMod.zipRecord(record)
                //     }, {upsert: true});
                //     tags = [];
                // }
            }
            accTotalBiscuits += mapBiscuits + aTipBiscuits
            let adFator = Math.min(3.5, 1 + accTotalAdCount / 1000)

            biscuitsLimit *= adFator
            let bisRatio = accTotalBiscuits / biscuitsLimit
            //超出饼干限制
            if (bisRatio > 1) {
                logger.warn("biscuitsLimit: ", uid, `limit: ${biscuitsLimit}, total: ${accTotalBiscuits}, onlineIncome: ${onlineIncome}, offlineIncome: ${offlineIncome}`,
                    `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
                reasons.push({
                    type: 0,
                    biscuitsLimit,
                    accTotalBiscuits,
                    onlineIncome,
                    offlineIncome,
                    bisCompLimit,
                    bisDoubleAdLimit,
                    bisCurrencyLimit,
                    accTotalAdCount,
                    otherLimit,
                    addBCS_Value,
                    bisSeeofMamonLimit,
                    mapBiscuits,
                    aTipBiscuits,
                    adFator
                })
                level += 1
                if (bisRatio > 1.1) {
                    level += 2
                }
                if (bisRatio > 1.2) {
                    level += 3
                }
            }
            // logger.debug("biscuitsLimit: ", uid, `limit: ${biscuitsLimit}, total: ${accTotalBiscuits}, onlineIncome: ${onlineIncome}, offlineIncome: ${offlineIncome}`,
            // `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
            //超出糖果上限
            let candyLimit = achieveAward.candies * 2 + roleAward.candies + galiCardAward.candies + 500 + passDay * 100
            candyLimit += addCA_Value
            candyLimit += codeMap["2_1"] || codeMap["2_-1"] || 0
            let candyCurrencyLimit = (currencyMap["2_-1"] || currencyMap["2_1"] || 0) * 2
            candyLimit += candyCurrencyLimit

            let fixDate = new Date("2021-5-20 00:00:00").getTime()
            if (signupTime < fixDate) { //在addCA_Value统计之前注册的话，做一个补正
                candyLimit += 2000
            }
            let fixDate2 = new Date("2021-6-24 00:00:00").getTime()
            if (signupTime < fixDate2 && modelMgr.world.isUnlockMap(MapSceneType.CINEMA)) { //电影院抽奖漏记
                candyLimit += 1000
            }
            candyLimit += 2000 + 4000 //临时
            candyLimit *= adFator
            let cdRatio = accTotalCandies / candyLimit
            if (cdRatio > 1) {
                logger.warn("candyLimit: ", uid, `limit: ${candyLimit}, total: ${accTotalCandies}, achieve: ${achieveAward.candies}, role: ${roleAward.candies}, galiCard: ${galiCardAward.candies}`,
                    `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
                reasons.push({
                    type: 1,
                    candyLimit,
                    accTotalCandies,
                    achieveAward,
                    roleAward,
                    galiCardAward,
                    addCA_Value,
                    candyCurrencyLimit,
                    adFator
                })
                level += 1
                if (cdRatio > 1.1) {
                    level += 2
                }
                if (cdRatio > 1.2) {
                    level += 3
                }
            }
            // logger.debug("candyLimit: ", uid, `limit: ${candyLimit}, total: ${accTotalCandies}, achieve: ${achieveAward.candies }, role: ${roleAward.candies}, galiCard: ${galiCardAward.candies}`,
            // `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)

            //饼干开销不够
            // let bisCostSum = bisBuildCost + bisMainSkinCost + bisWudongSkinCost
            // let bisSum = 100000 + accTotalBiscuits * 1.2
            // let bisCostRatio = bisCostSum / bisSum
            // if (bisCostRatio > 1) {
            //     logger.warn("bisCostSum: ", uid, `need: ${bisCostSum}, total: ${bisSum}, cost: [${bisBuildCost}, ${bisMainSkinCost}, ${bisWudongSkinCost}]`,
            //     `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
            //     reasons.push({type: 2, bisCostSum, bisSum, bisBuildCost, bisMainSkinCost, bisWudongSkinCost})
            //     level += 5
            // }
            // logger.debug("bisCostSum: ", uid, `need: ${bisCostSum}, total: ${bisSum}, cost: [${bisBuildCost}, ${bisMainSkinCost}, ${bisWudongSkinCost}]`,
            // `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)

            //糖果开销不够
            // let candyCostSum = candyBuildCost + candyMainSkinCost + candyWudongSkinCost + mainAttrCost.candies
            // let candySum = 1000 + accTotalCandies * 1.2
            // let cdCostRatio = candyCostSum / candySum
            // if (cdCostRatio > 1) {
            //     logger.warn("candyCostSum: ", uid, `need: ${candyCostSum}, total: ${candySum}, cost: [${candyBuildCost}, ${candyMainSkinCost}, ${candyWudongSkinCost}, ${mainAttrCost.candies}]`,
            //     `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
            //     reasons.push({type: 3, candyCostSum, candySum, candyBuildCost, candyMainSkinCost, candyWudongSkinCost, candyMainCost: mainAttrCost.candies})
            //     level += 5
            // }
            // logger.debug("bisCostSum: ", uid, `need: ${candyCostSum}, total: ${candySum}, cost: [${candyBuildCost}, ${candyMainSkinCost}, ${candyWudongSkinCost}]`,
            // `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)

            let heatLimit = Math.ceil(HEART_BASE + (passDay / 2) * 5)
            heatLimit += codeMap["3_1"] || codeMap["3_-1"] || 0
            let heartCurrencyLimit = currencyMap["3_-1"] || currencyMap["3_1"] || 0
            heatLimit += heartCurrencyLimit

            // 兔兔商店道具道具蜡烛增量
            if (activityData && activityData.heart) {
                heatLimit += activityData.heart
            }

            let htRatio = heart / heatLimit
            if (htRatio > 1) {
                logger.warn("heatLimit: ", uid, `limit: ${heatLimit}, total: ${heart}`,
                    `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
                reasons.push({ type: 4, heatLimit, heart, passDay })
                level += 3
                if (htRatio > 1.1) {
                    level += 3
                }
                if (htRatio > 1.2) {
                    level += 3
                }
            }

            // if (config.type == "global") {
            //     let timeGanef = Number(recordMod.getFromRecord("timeGanef", record) || 0)
            //     const timeGanefLimit = 200 * adFator + 200
            //     if (timeGanef > timeGanefLimit) {
            //         logger.warn("timeGanefLimit: ", uid, `limit: ${timeGanefLimit}, total: ${timeGanef}`,
            //         `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
            //         reasons.push({type: 5, timeGanef, adFator})
            //         level += Math.min(3, Math.ceil(timeGanef / 500))
            //     }
            // }

            let praiseLimit = 1000 + passDay * 10000 + playTimeMinute * 10
            praiseLimit += codeMap["13_1"] || codeMap["13_-1"] || 0
            let praiseCurrencyLimit = currencyMap["13_-1"] || currencyMap["13_1"] || 0
            praiseLimit += praiseCurrencyLimit

            praiseLimit *= adFator
            let cinema = modelMgr.world.getCinema()
            let praise = global.getAccTotalPraise()
            if (cinema) {
                praise = Math.max(cinema.getPraise(), praise)
            }
            let praiseRatio = praise / praiseLimit
            if (praiseRatio > 1.1) {
                logger.warn("praiseLimit: ", uid, `limit: ${praiseLimit}, total: ${praise}`,
                    `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
                reasons.push({ type: 6, praiseLimit, praise, adFator })
                level += 1
                if (praiseRatio > 1.5) {
                    level += 2
                }
                if (praiseRatio > 2) {
                    level += 3
                }
            }

            let yoyoStartDate = new Date("2021-7-6 00:00:00").getTime()
            let yoyoPassDay = Math.ceil((Date.now() - yoyoStartDate) / util.Time.Day)
            let yoyoPropLimit = 3725 + Math.min(passDay, yoyoPassDay) * 317
            yoyoPropLimit += codeMap["4_9100"] || 0
            let yoyoCurrencyLimit = currencyMap["4_9100"] || 0
            yoyoPropLimit += yoyoCurrencyLimit
            yoyoPropLimit *= adFator

            let yoyo = Math.max(bag.getPropCount(YOYO_PROP_ID), global.getAccYoyoPropCount())
            let yoyoRatio = yoyo / yoyoPropLimit
            if (yoyoRatio > 1) {
                logger.warn("yoyoPropLimit: ", uid, `limit: ${yoyoPropLimit}, total: ${yoyo}`,
                    `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
                reasons.push({ type: 7, yoyoPropLimit, yoyo, adFator })
                level += 1
                if (yoyoRatio > 1.5) {
                    level += 2
                }
                if (yoyoRatio > 2) {
                    level += 3
                }
            }

            let bisTot = global.getAccTotalBiscuits()
            let bisCur = global.getBiscuits()
            let bisSumRatio = bisCur / bisTot || 0
            let diff = bisCur - bisTot
            if (bisSumRatio > 1 && diff > 1000000) {
                logger.warn("bis cur > sum: ", uid, `limit: ${bisCur}, total: ${bisTot}`,
                    `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
                level += 3
                reasons.push({ type: 8, bisCur, bisTot })
                if (bisSumRatio > 1.2) {
                    level += 3
                }
            }

            let cdTot = global.getAccTotalCandies()
            let cdCur = global.getCandies()
            let cdSumRatio = cdCur / cdTot || 0
            diff = cdCur - cdTot
            if (cdSumRatio > 1 && diff > 1000) {
                logger.warn("candy cur > sum: ", uid, `limit: ${cdCur}, total: ${cdTot}`,
                    `playTime: ${playTime}, passDay: ${passDay}, heart: ${heart}`)
                level += 3
                reasons.push({ type: 9, cdCur, cdTot })
                if (cdSumRatio > 1.2) {
                    level += 3
                }
            }
            // 兼容未解锁兔兔商店的玩家数据
            if (!activityData || !activityData.reward) {
                activityData = {
                    reward: []
                }
            }

            // 兔兔商店道具获取检查
            if (!config.type || (config.type && (config.type !== 'global' && config.type !== 'fb')) && shopRewardConfig.length) {
                const reward = activityData.reward || []
                const exchange = []
                if (activityData.limit) {
                    activityData.limit.forEach(l => {
                        if (l.id === -1) return
                        // if (l.time < 1723021560000) return
                        const idx = reward.indexOf(l.id)
                        if (idx >= 0) {
                            reward.splice(idx, 1)
                        }
                        exchange.push(l.id)
                    })
                }
                const checks = []
                const ignore = []
                this.tutuData.forEach(e => {
                    if (reward.includes(e.id)) {
                        return
                    }
                    if (exchange.includes(e.id)) {
                        return
                    }
                    checks.push(e)
                })
                const arr = []
                const cs = new Set(checks)
                cs.forEach(function (item) {
                    if (ignore.includes(item.reward)) return
                    const ns = item.reward.split(",")
                    let type = Number(ns[0])
                    let id = Number(ns[1])
                    let num = Number(ns[2])
                    switch (type) {
                        // 大楼皮肤
                        case 11:
                            const skins = recordMod.getFromRecord('main.unlockMainSkins', record)
                            id = Number(id)
                            if (skins && skins.length && skins.includes(id)) {
                                arr.push({ type: 11, id: id, cfg: item.id })
                            }
                            break
                        // 道具类型
                        case 4:
                            if (id > 21000) {
                                // 小家的特殊家具
                                id = Number(id)
                                const speFurns = recordMod.getFromRecord('kefang_10000.unlockSpeFurns', record)
                                if (speFurns && speFurns.length && speFurns.find(s => s.id == id)) {
                                    arr.push({ type: 4, id: id, cfg: item.id })
                                }
                            }
                            break
                        // 小家的专属家具地板
                        case 307:
                            const furns = recordMod.getFromRecord('kefang_10000.unlockFurns', record)
                            if (furns && furns.length && furns.find(s => item.reward.includes(s.id))) {
                                arr.push({ type: 4, id: id, cfg: item.id })
                            }
                            break
                        case 10:
                            const wdSkins = recordMod.getFromRecord('main.unlockWudongSkins', record)
                            if (wdSkins && wdSkins.length && wdSkins.includes(id)) {
                                arr.push({ type: 10, id: id, cfg: item.id })
                            }
                            break
                        default:
                            break
                    }
                })
                if (arr.length) {
                    level += 6
                    reasons.push({ type: 19, arr })
                }
            }
            // 魔法飞毯 20 暂时只标记
            const bagData = recordMod.getFromRecord("bag.props", record)
            if (bagData && bagData.length) {
                try {
                    let item = bagData.find(i => i.id == 7007)
                    if (item) {
                        const max = Math.max(0, 20 + passDay * .75)
                        if (item.count > max) {
                            const data = {
                                heart,
                                playTime,
                                passDay,
                                num: item.count,
                                max
                            }
                            // this.blackTagCol.updateOne({uid}, {
                            //     timestamp: Date.now(),
                            //     cal: JSON.stringify(data)
                            // }, {upsert: true}).then()
                            level += 1
                            reasons.push({ type: 20, data })
                        }
                    }
                    item = bagData.find(i => i.id == 6002)
                    if (item && item.count) {
                        level += 6
                        reasons.push({ type: 20, item: "非法道具史莱姆拌饭", num: item.count })
                    }
                } catch (e) {
                    console.log(e)
                }
            }

            if (reasons.length > 0) {
                let types = reasons.map(m => m.type)
                let blackDoc = await this.blackListCol.findOne({ uid })
                let updateStr = {
                    uid,
                    timestamp: Date.now(),
                    level,
                    $addToSet: { types: { $each: types } },
                    heart,
                    playTime,
                    passDay,
                    reason: JSON.stringify(reasons)
                }
                if (blackDoc) {
                    delete updateStr.timestamp
                } else {
                    this.blackListHistoryCol.create(updateStr).then()
                }
                this.blackListCol.updateOne({ uid }, updateStr, { upsert: true }).then()
                rankMod.remove(uid)
                if (blacks.includes(uid)) return 9
                return level
            }

            // logger.info("good", uid, [biscuitsLimit, accTotalBiscuits], candyLimit, bisCostSum, candyCostSum)

        } catch (error) {
            logger.error(uid, error)
            // return 10
        }

        if (blacks.includes(uid)) return 8

        return 0
    }

    public async deblock(uid, deduct = true, web = false) {
        let record = await recordMod.getRecord(uid)
        let level = await this.check(uid, record, true)
        logger.info('deblock recheck', uid, level)
        let doc = await this.blackListCol.findOne({ uid })
        if (!deduct && !web && doc && doc.types.includes(10)) {
            // 如果是手动封号，则不可以通过下载存档
            return false
        }
        if(level >= 6){
            return false
        }
        if (deduct && (level > 0 || doc.types.includes(8) || doc.types.includes(9))) {
            logger.info("deblock", doc)
            let reasons = JSON.parse(doc.reason)
            let modelMgr = new ModelMgr()
            modelMgr.init(record)
            if (Array.isArray(reasons)) {
                let global = recordMod.getFromRecord('global', record)
                for (let reason of reasons) {
                    let type = reason.type
                    if (type == 0) {
                        let diff = (reason.accTotalBiscuits - reason.biscuitsLimit)
                        diff = Math.abs(diff)
                        diff *= 1.2
                        global.accTotalBiscuits -= diff
                        global.biscuits -= diff

                        if (global.biscuits > diff) {
                            global.biscuits -= diff
                        }
                    } else if (type == 1) {
                        let diff = (reason.accTotalCandies - reason.candyLimit)
                        diff = Math.abs(diff)
                        diff *= 1.2
                        global.accTotalCandies -= diff
                        global.candies -= diff

                        if (global.candies > diff) {
                            global.candies -= diff
                        }
                    } else if (type == 2) {
                        let diff = reason.bisCostSum - reason.bisSum
                        diff = Math.abs(diff)
                        global.accTotalBiscuits += diff
                        global.biscuits = Math.min(global.biscuits - 2 * diff, 0)
                    } else if (type == 3) {
                        let diff = reason.candyCostSum - reason.candySum
                        diff = Math.abs(diff)
                        global.accTotalCandies += diff
                        global.candies -= 2 * diff
                        global.candies = Math.min(global.candies - 2 * diff, 0)
                    } else if (type == 4) {
                        return false
                        // global.heart -= (reason.heart - reason.heatLimit) - reason.heatLimit * 0.1 //这里应该人工解
                    } else if (type == 5) {
                        let timeGanef = reason.timeGanef
                        let time = timeGanef * 0.5 // 30s
                        let offline = modelMgr.get<OfflineSysModel>('offline')
                        let onlineIncome = offline.getOnlineExpectedIncome()
                        global.biscuits -= time * onlineIncome * 20
                    } else if (type == 6) {
                        let { praiseLimit, praise } = reason
                        let diff = Math.floor(praise - praiseLimit)
                        diff = Math.abs(diff)
                        diff *= 1.2
                        global.accTotalPraise -= Math.max(0, global.accTotalPraise - diff)
                        let cinema = recordMod.getFromRecord('cinema', record)
                        cinema.signed.praise = Math.max(cinema.signed.praise - diff, 0)
                        recordMod.setToRecord("cinema", cinema, record)
                    } else if (type == 7) {
                        let { yoyoPropLimit, yoyo } = reason
                        let diff = Math.floor(yoyo - yoyoPropLimit)
                        diff = Math.abs(diff)
                        diff *= 1.2
                        global.accYoyoPropCount = Math.max(0, global.accYoyoPropCount - diff)
                        let bag = recordMod.getFromRecord('bag', record)
                        if (bag) {
                            let props = bag.props
                            for (let i = 0; i < props.length; i++) {
                                let prop = props[i]
                                if (prop.id == 9100) {
                                    props.splice(i, 1)
                                    break
                                }
                            }
                        }
                        recordMod.setToRecord('bag', bag, record)
                    } else if (type == 8) {
                        if (global.biscuits > 0) {
                            global.biscuits = 0
                        }
                    } else if (type == 9) {
                        if (global.candies > 0) {
                            global.candies = 0
                        }
                    }
                }

                let main = recordMod.getFromRecord('main', record)
                let json = assetsMgr.getJson('hostelAttr')
                if (global.candies < 0) {
                    let delMainAttr = (attr) => {
                        if (!attr) return
                        while (attr.lv > 1) {
                            let attrId = attr.id * 1000 + (attr.lv - 1)
                            let data = json.getById(attrId)
                            if (data) {
                                let costs = gameHelper.stringToConditions(data.cost)
                                costs.forEach((cost) => {
                                    if (cost.type == ConditionType.CANDIES) {
                                        global.candies += cost.count
                                    }
                                })
                            }
                            attr.lv--
                            if (global.candies >= 0) {
                                break
                            }
                        }
                    }
                    let attr = main.mainAttr
                    delMainAttr(attr.comeRoleTimeAttr)
                    delMainAttr(attr.offlineTimeAttr)
                    delMainAttr(attr.tipLimtAttr)
                    recordMod.setToRecord('main', main, record)
                }

                global.accTotalCandies = Math.max(0, Math.floor(global.accTotalCandies))
                global.candies = Math.floor(global.candies)
                global.accTotalBiscuits = Math.max(0, Math.floor(global.accTotalBiscuits))
                global.biscuits = Math.floor(global.biscuits)

                if (global.biscuits > global.accTotalBiscuits) {
                    global.biscuits = 0
                }

                if (global.candies > global.accTotalCandies) {
                    global.candies = 0
                }

                recordMod.setToRecord('global', global, record)
                recordMod.setToRecord('timeGanef', '0', record)
                await recordMod.setRecord(uid, record)
            }
            await userMod.updateOne({ uid: uid }, { forceDownload: true });
        }

        await this.blackListCol.deleteOne({ uid })
        return true
    }

    public getCodeMap(codes) {
        let codeMap = {}
        for (let { type, value } of codes) {
            if (!codeMap[type]) codeMap[type] = 0
            codeMap[type] += value
        }
        return codeMap
    }

    public async getUserCurrencyOrders(uid) {
        let where = {
            uid,
            $or: [{ status: CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS }, { status: CURRENCY_ORDER_STATUS.COMPLETED }]
        }
        let docs = await this.currencyOrderCol.find(where, { action: 1, extra: 1 })
        let shopDatas = assetsMgr.getJson("shop").datas
        let currencyMap = {}
        for (let { action, extra } of docs) {
            let reward
            if (extra) {
                try {
                    extra = JSON.parse(extra)
                    if (action.startsWith("iap")) {
                        reward = extra.reward
                    } else {
                        reward = extra
                    }
                } catch (error) {

                }
            }
            if (!reward) {
                let data = shopDatas.find((m) => m.action == action) || {}
                reward = data.reward
            }
            if (reward) {
                gameHelper.stringToConditions(reward).forEach(({ type, id, count }) => {
                    let key = type + "_" + id
                    if (!currencyMap[key]) currencyMap[key] = 0
                    currencyMap[key] += count || 0
                })
            }
        }
        return currencyMap
    }
}

export const verfiyRecordMod = new VerfiyRecordMod()
