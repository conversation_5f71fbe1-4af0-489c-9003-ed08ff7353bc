const db = require("../db/db");
const { default: util } = require("../common/util");
const logger = require("../common/log").getLogger("activityMod");
const taskBaseConfig = require("../client/json/tutuShopTaskBase.json");
const taskConditionConfig = require("../client/json/taskCondition2.json");
const shopBaseConfig = require("../client/json/tutuShopBase.json");
const shopRewardConfig = require("../client/json/tutuShopReward.json");
const tutuShopExchange = require("../client/json/tutuShopExchange.json");
const userMod = require("./userMod");
const { exceptions } = require("winston");

const SPECIAL_SHOP_KEY = `SPECIAL_SHOP_5` // 特殊商店活动key
const SPECIAL_SHOP_KEY_LAST = ['SPECIAL_SHOP_1', 'SPECIAL_SHOP_2', 'SPECIAL_SHOP_3', 'SPECIAL_SHOP_4'] // 之前特殊商店活动key，用于清理数据
const GROUP_1 = 1
const GROUP_2 = 2
const GROUP_3 = 3
const GROUP_100 = 100
// 可完成的任务数量限制和奖励数量
const TASK_LIMIT_DATA = {
    [GROUP_1]: { limit: 50, rewardCnt: 1 },
    [GROUP_2]: { limit: 65, rewardCnt: 1 },
    [GROUP_3]: { limit: 25, rewardCnt: 5 },
    [GROUP_100]: { limit: 999, rewardCnt: 5 },
}
// 皮肤碎片的
const SKIN_SHARD = {
    9401: {
        rid: 42, // 对应碎片奖励id 42
        id: 12, // 对应皮肤id 12
        sid: 1, // 对应皮肤奖励id
    },
    9402: {
        rid: 43, // 对应碎片奖励id 43
        id: 14, // 对应皮肤id 14
        sid: 2, // 对应皮肤奖励id
    },
    9403: {
        rid: 9403, // 对应碎片奖励id 43
        id: 18, // 对应皮肤id 14
        sid: 60, // 对应皮肤奖励id
    },
    9404: {
        rid: 9404, // 对应碎片奖励id 43
        id: 21, // 对应皮肤id 14
        sid: 20021, // 对应皮肤奖励id
    },
}

const CONDITION_INVITE_NEW = 211 // 邀请新玩家
const CONDITION_INVITE_OLD = 212 // 邀请老玩家
const CONDITION_LOGIN_TOTAL_30 = 213 // 累计登录30天
const CONDITION_LOGIN_TOTAL_3 = 204 // 累计登录3天


// 特殊任务 给5张券
const SPECIAL_TASK_REWARD = 5

// 免费抽奖还是用券
const DRAW_TYPE = {
    FREE: 1,
    COUPON: 2,
    COUPON_POINT: 3,
}
// up定向类型
const UP_TYPE = {
    NORMAL: 1, // 不做选择
    SPECIAL_1: 2, // 选择第一个
    SPECIAL_2: 3, // 选择第二个
}
// 彩蛋奖励
const VIDEO_GET = {
    [0]: 4,
    [1]: 4,
    [2]: 4,
    [3]: 4,
    [4]: 4,
}
let activityMod = {
    _mod: {
        start: -1,
        end: -1,
        lottery_cd: -1,
        tasks: [],
        rewards: [],
        originalRewards: [],
        first: 0.0,
        price: 0,
        exchange: [],
        startDay: 0,
    },
    get mod() {
        return JSON.parse(JSON.stringify(this._mod))
    },
    async isOpen() {
        const now = Date.now()
        return this.mod.start <= now && this.mod.end > now
    },
    async init() {
        this.redis = db.redis;
        this.specialActivityCol = db.createSpecialActivityModel()
        this.loginInfoCol = db.createLoginInfoModel()
        //this.yearActivityCol = db.createYearActivityModel()
        this.invitedCol = db.createInvitedModel()
        const cfg = shopBaseConfig.find(e => e.id === 1)
        if (!cfg) {
            console.error("activityMod init err, no config found!")
            throw new err("activityMod init err, no config found!")
        }
        const { start, end, lottery_cd, tasks_1, tasks_2, tasks_3, extra_tasks, rewards, zhenbaoquan_price } = cfg
        // 扩大1000倍，方便计算
        this._mod.first = cfg["1st_prize_p"] * 1000
        this._mod.start = this.transDate(start)
        this._mod.startDay = util.getNumberDay(new Date(this._mod.start).getTime())
        this._mod.end = this.transDate(end)
        this._mod.lottery_cd = lottery_cd * util.Time.Minute
        this._mod.price = zhenbaoquan_price
        this.loadTasks(tasks_1 + "," + tasks_2 + "," + tasks_3 + "," + extra_tasks)
        this.loadRewards(rewards)
        this.loadExchange()
    },
    transDate(ds) {
        const dateParts = ds.split("-");
        const jsDateString = dateParts[0] + "-" + dateParts[1] + "-" + dateParts[2] + " " + dateParts[3] + ":" + dateParts[4] + ":00";
        return new Date(jsDateString).getTime()
    },
    // 兑换表数据
    loadExchange() {
        tutuShopExchange.forEach(e => {
            this._mod.exchange.push({
                id: e.id,
                limit: e.limit,
                cost: e.cost,
            })
        })
    },
    /**
     * 根据base表的任务配置，加载本期任务配置
     * @param tasks
     */
    loadTasks(tasks) {
        const arr = tasks.split(",")
        this._mod.tasks = taskBaseConfig.filter(t => arr.includes(String(t.id)))
        this._mod.tasks.forEach(t => {
            const con = taskConditionConfig.find(c => t["conditon"] === c.id)
            t.count = con.count || 0
            t.rand_count = con.rand_count || null
            if (con.rand_count) {
                const _ = con.rand_count.split(",")
                t.rand_count = [Number(_[0]), Number(_[1])]
                return
            }
            t.rand_count = null
        })
    },
    /**
     * 根据base表的奖励配置，加载本期奖池奖励
     * @param rewards
     */
    loadRewards(rewards) {
        rewards = rewards.split(",")
        const cfgData = JSON.parse(JSON.stringify(shopRewardConfig))
        for (const cfg of cfgData) {
            const _ = cfg.reward.split(",")
            cfg.reward = {
                type: Number(_[0]),
                id: _[1],
                num: Number(_[2]),
            }
            if (rewards.includes(String(cfg.id))) {
                this._mod.rewards.push(cfg)
            }
            this._mod.originalRewards.push(cfg)
        }
    },
    /**
     * 抽奖
     * @param uid
     * @param type
     * @return {Promise<{status: number}>}
     * -1 : 抽奖类型不对
     * -2 : 当前不可以进行免费抽奖
     * -3 : 奖券不足
     */
    async playerDraw(uid, type) {
        let onSuccessCallback = null
        switch (type) {
            // 已经去掉了免费抽奖
            case DRAW_TYPE.FREE:
                return { status: -2 }
            case DRAW_TYPE.COUPON:
                if (!await this.isHasCoupon(uid)) return { status: -3 }
                onSuccessCallback = async () => await this.changeCoupon(uid, -1)
                break
            case DRAW_TYPE.COUPON_POINT:
                if (!await this.isHasEnoughCoupon(uid)) return { status: -3 }
                onSuccessCallback = async () => await this.changeCouponPoint(uid, -10)
                break
            default:
                return { status: -1 }
        }
        // 如果有未选择的道具，就不能抽
        const ada = await this.getActivityData(uid, { choose: 1, up: 1 })
        if (ada && ada.choose) {
            return { status: -4 }
        }
        // 如果没有选择up类型，就不能抽
        if (ada && !ada.up) {
            return { status: -4 }
        }
        // 开始抽奖
        const id = await this.newDrawLogic(uid)
        onSuccessCallback && await onSuccessCallback()
        return { status: 0, id }
    },
    Key(uid) {
        return `${SPECIAL_SHOP_KEY}_${uid}`
    },
    clearLastKey(uid) {
        SPECIAL_SHOP_KEY_LAST.forEach(async (k) => await this.redis.del(`${k}_${uid}`))
    },
    /**
     * 获取玩家上一次免费获取抽奖券的时间
     * @param uid
     * @return {Promise<number|number>}
     */
    async getLastFreeTime(uid) {
        const key = this.Key(uid)
        const at = await this.redis.get(key)
        return at ? Number(at) : 0
    },
    /**
     * 设置玩家上一次免费获取抽奖券的时间
     * @param uid
     * @return {Promise<void>}
     */
    async setLastFreeTime(uid) {
        const key = this.Key(uid)
        await this.redis.set(key, Date.now())
        // 免费抽奖完成后 -> 清理任务
        await this.clearNormalTask(uid)
    },
    /**
     * 当前是否可以进行免费抽奖
     * @param uid
     * @return {Promise<boolean>}
     */
    async isCanFree(uid) {
        const ada = await this.getActivityData(uid)
        if (!ada || !ada.task || !ada.task.length) return false
        let count = 1
        for (const t of ada.task) {
            t && t.group === 3 && t.current === t.count && count--
            !t && count--
        }
        return count === 0
    },
    /**
     * 抽奖券是否足够
     * @param uid
     * @return {Promise<boolean>}
     */
    async isHasCoupon(uid) {
        const data = await this.getActivityData(uid, { coupon: 1 })
        if (!data) return false
        return data.coupon > 0
    },
    /**
     * 抽奖点是否足够
     * @param uid
     * @return {Promise<boolean>}
     */
    async isHasEnoughCoupon(uid) {
        const data = await this.getActivityData(uid, { couponPoint: 1 })
        if (!data) return false
        return data.couponPoint > 10
    },
    /**
     * 更新抽奖券
     * @param uid
     * @param cnt
     * @return {Promise<void>}
     */
    async changeCoupon(uid, cnt) {
        await this.updateActivityData(uid, { $inc: { coupon: cnt } })
    },
    async changeCouponPoint(uid, cnt) {
        await this.updateActivityData(uid, { $inc: { couponPoint: cnt } })
    },
    /**
     * 获取活动数据
     * @param uid
     * @param opts
     * @return {Promise<*>}
     */
    async getActivityData(uid, opts = {}) {
        opts = opts || {}
        return this.specialActivityCol.findOne({ uid }, opts);
    },
    /**
     * 删除活动数据
     * @param uid
     * @return {Promise<*>}
     */
    async delActivityData(uid) {
        return this.specialActivityCol.deleteOne({ uid });
    },
    /**
     * 更新活动数据 并返回活动数据， getNew = true 返回更新后的数据
     * @param uid
     * @param info
     * @param getNew
     * @return {Promise<*>}
     */
    async updateActivityData(uid, info = {}, getNew = true) {
        let where = {}
        if (typeof uid === "string") {
            where.uid = uid
        } else {
            where = uid
        }
        return this.specialActivityCol.findOneAndUpdate(where, info, { upsert: true, new: getNew });
    },
    // 抽奖逻辑
    async filterMyPool(uid) {
        const ada = await this.getActivityData(uid, { reward: 1, skin: 1, up: 1 })
        // 1 过滤掉有最大拥有数量限制的道具
        let limitItems = this.mod.rewards.filter(r => r.counts >= 1)
        ada.reward = ada.reward || []
        ada.skin = ada.skin || []
        if (limitItems.length && (ada.reward.length || ada.skin.length)) {
            let needRemoveItemIds = []
            for (let i = 0; i < limitItems.length; i++) {
                const pooItem = limitItems[i]
                const item = pooItem.reward
                // 碎片特殊判断
                if (item.type === 4) {
                    const shard = SKIN_SHARD[item.id]
                    if (shard) {
                        // 1 如果已经拥抽到了完整的皮肤  就不能再获得对应的碎片了
                        const _skin = ada.reward.filter(rid => rid === shard.rid)
                        if (_skin.length) {
                            needRemoveItemIds.push(pooItem.id)
                            continue
                        }
                        // 2 碎片数量不能大于3  注意 item.id 可能是number 也可能是string
                        const _cnt = ada.skin.find(s => s.id == item.id)
                        if (_cnt >= 3) {
                            needRemoveItemIds.push(pooItem.id)
                            continue
                        }
                    }
                }
                // 皮肤特殊判断
                if (item.type === 11) {
                    const _skin = ada.reward.filter(rid => rid === pooItem.id)
                    if (_skin.length) {
                        needRemoveItemIds.push(pooItem.id)
                        continue
                    }
                }
                const hasCnt = ada.reward.filter(rid => rid === pooItem.id)
                // 大于收集数量
                if (hasCnt.length >= pooItem["counts"]) {
                    needRemoveItemIds.push(pooItem.id)
                }
            }
            limitItems = limitItems.filter(r => !needRemoveItemIds.includes(r.id))
        }
        // 权重key，根据up变动
        let weightKey = ""
        if (ada.up === UP_TYPE.NORMAL) {
            weightKey = "weight"
        } else if (ada.up === UP_TYPE.SPECIAL_1) {
            weightKey = "weight_1"
        } else if (ada.up === UP_TYPE.SPECIAL_2) {
            weightKey = "weight_2"
        } else {
            throw new Error("没选择up")
        }
        // 创建基础奖池数据
        const basePool = this.mod.rewards.filter(r => r.counts === -1)
        // 2 处理无收集上限的道具，如果拥有数量达到限制，则权重降低90%
        basePool.forEach(item => {
            const hasCnt = ada.reward.filter(rid => rid === item.id)
            if (hasCnt.length >= item["collect_counts"]) {
                item[weightKey] *= 0.1
            }
        })
        // 合并过滤池和基础池
        basePool.push(...limitItems)
        // 拆分奖等
        const lvPool = []
        let fst = null
        let totalWeight = 0
        basePool.forEach(item => {
            const lv = item["reward_lv"]
            const weight = item[weightKey]
            let lvData = lvPool.find(l => l.lv === lv)
            if (!lvData) {
                lvData = {
                    lv,
                    weight: 0
                }
                lvPool.push(lvData)
            }
            if (lv !== 1) {
                lvData.weight += weight
                totalWeight += weight
            } else {
                fst = lvData
            }
        })
        // 特殊处理1等奖
        // 1等奖固定权重
        if (fst !== null) {
            let w = Math.floor(totalWeight * 1000 / (1000 - this.mod.first))
            fst.weight = Math.floor(w * this.mod.first / 1000)
        }
        // 随机奖池
        const lv = util.randomByWeight(lvPool)
        const lv_ = lvPool[lv].lv
        // 再随机奖励
        const realPool = basePool.filter(r => r["reward_lv"] === lv_)
        if (lv_ === 1) {
            return realPool[util.random(0, realPool.length - 1)]
        }
        const itemIdx = util.randomByWeight(realPool, weightKey)
        return realPool[itemIdx]
    },
    /**
     * 抽奖逻辑
     * @param uid
     * @return {Promise<*>}
     */
    newDrawLogic: async function (uid) {
        const reward = await this.filterMyPool(uid)
        await this.updateActivityData(uid, { choose: reward.id })
        return reward.id
    },
    /**
     * 选择是否要抽奖结果换成积分
     * @param uid
     * @param id
     * @param replace
     * @return {Promise<{status: number}>}
     */
    async choose(uid, id, replace) {
        const ada = await this.getActivityData(uid, { choose: 1 })
        // 没有可选择的抽奖结果
        if (!ada || !ada.choose || ada.choose !== id) return { status: -1 }
        const reward = this.mod.originalRewards.find(r => r.id === id)
        if (!reward) {
            // 不应该出现这种情况
            return { status: -2 }
        }
        const newData = await this.sendAward(uid, reward, replace)
        return { status: 0, point: newData.point }
    },
    /**
     * 添加奖励
     * @param uid
     * @param reward 奖励配置
     * @param replace 是不是要换成积分
     * @param extraInfo 额外需要更新的info数据
     * @param record 是否要记录到record中
     * @return {Promise<*>}
     */
    async sendAward(uid, reward, replace, extraInfo = null, record = true) {
        const where = { uid }
        const info = {}
        if (extraInfo) {
            Object.assign(info, extraInfo)
        }
        info.choose = 0
        info.$inc = info.$inc || {}
        info.$push = info.$push || {}
        if (replace) {
            // 换成积分
            // 积分增加，并记录
            info.$inc.point = reward["sell_price"]
            info.$push.record = {
                $each: [{
                    id: reward.id,
                    time: Date.now(),
                    ch: true
                }]
            }
        } else {
            // 领取奖励
            // 碎片奖励单独处理
            const ada = await this.getActivityData(uid, { skin: 1 });
            // 判断是不是碎片
            let isShard = false
            for (const key in SKIN_SHARD) {
                if (reward.reward.id == key) {
                    isShard = true
                    break
                }
            }
            // 碎片放入skin，否则放入reward
            if (isShard) {
                let add = true
                // 存在则加数量  否则执行新增
                if (ada.skin != null && ada.skin.length) {
                    const idx = ada.skin.findIndex(s => s.id === reward.id)
                    if (idx !== -1) {
                        where["skin.id"] = reward.id
                        info.$inc["skin.$.num"] = 1
                        add = false
                    }
                }
                if (add) {
                    info.$addToSet = { skin: { id: reward.id, num: 1 } }
                }
            } else {
                // 蜡烛增加
                if (reward.heart) {
                    info.$inc.heart = reward.heart
                }
                // 如果是完整的大楼皮肤，获取后要判断是不是拥有该大楼碎片，如果有碎片，则把碎片转换成积分
                info.$push.reward = { $each: [reward.id] }
                if (ada && ada.skin && ada.skin.length) {
                    for (const key in SKIN_SHARD) {
                        if (SKIN_SHARD[key].sid === reward.id) {
                            // 判断是否拥有该皮肤的碎片
                            const idx = ada.skin.findIndex(s => s.id === SKIN_SHARD[key].rid)
                            if (idx !== -1) {
                                if (ada.skin[idx].num > 0) {
                                    // 增加积分
                                    const cfg = this.mod.exchange.find(e => e.id === SKIN_SHARD[key].rid)
                                    if (!cfg) {
                                        logger.error("找不到碎片换积分的配置!", uid, SKIN_SHARD[key].rid)
                                        continue
                                    }
                                    info.$inc.point = cfg.cost * ada.skin[idx].num
                                }
                                // 删除碎片
                                info.$pull = { skin: { id: SKIN_SHARD[key].rid } }
                            }
                            break
                        }
                    }
                }
            }
            if (record) {
                // 记录
                info.$push.record = {
                    $each: [{
                        id: reward.id,
                        time: Date.now(),
                    }]
                }
            }
        }
        return await this.updateActivityData(where, info)
    },
    async exchange(uid, id) {
        const ex = this.mod.exchange.find(e => e.id === id)
        // 找不到配置则是兔兔券换积分
        const ada = await this.getActivityData(uid, { coupon: 1, point: 1, limit: 1 })
        if (!ex) {
            if (!ada || !ada.coupon || ada.coupon < 1) return { status: -1 }
            // 1张 = 10分
            await this.updateActivityData(uid, {
                $inc: { coupon: -1, point: 10 },
                $push: {
                    limit: { $each: [{ id: -1, time: Date.now() }] }
                }
            })
            return { status: 0, point: ada.point + 10 }
        }
        // 积分不足
        if (!ada || !ada.point || ada.point < ex.cost) return { status: -2 }
        // 限购判断
        if (ada && ada.limit.length) {
            const cnt = ada.limit.filter(l => l.id === id).length
            if (ex.limit !== -1 && cnt >= ex.limit) return { status: -3 }
        }
        const info = {
            point: ada.point
        }
        info.$push = {
            limit: { $each: [{ id, time: Date.now() }] }
        }
        info.point -= ex.cost
        const reward = this.mod.originalRewards.find(r => r.id == id)
        // 追加更新info，但是不必record， 因为兑换记录是放入limit单独存放的
        await this.sendAward(uid, reward, false, info, false)
        return { status: 0, point: info.point }
    },
    // 随机任务 exclude是要排出的任务
    async randomTasks(group, exclude = []) {
        const tasks = this.mod.tasks
        let day = 9999;
        if (group !== GROUP_100) {
            // 特殊任务周期 活动要结束了排除某些任务
            day = (this.mod.end - util.getSixAmTimestamp()) / util.Time.Day - 1
            if (day <= 0) day = 1
        }
        // 获取同组数据
        const g1 = tasks.filter(t => {
            if (t.group !== group) return false
            if (exclude && exclude.length) {
                return exclude.findIndex(e => e && e.id === t.id) === -1
            }
            return true
        })
        if (!g1.length) return null
        const random = function (group) {
            const index = util.randomByWeight(group)
            return group[index]
        }
        let taskA = random(g1)
        const randomCondition = function (taskCfg) {
            let task = JSON.parse(JSON.stringify(taskCfg))
            if (task.rand_count) {
                task.count = util.random(task.rand_count[0], task.rand_count[1])
            }
            if (task.group === 3 && task.count >= day) task.count = day
            task.current = 0
            task.group = group
            task.done = false
            delete task.rand_count
            delete task.weight
            return task
        }
        return randomCondition(taskA)
    },
    async refreshTask(uid, group) {
        // group不正确
        if (group !== GROUP_1 && group !== GROUP_100) return { status: -1 }
        const ada = await this.getActivityData(uid, { task: 1 })
        // 当前没有活动数据
        if (!ada || !ada.task || !ada.task.length) return { status: -2 }
        // 分组三，从身上的任务里切换
        if (group === GROUP_100) {
            const tasks = ada.task.filter(t => t && t.group === GROUP_100)
            const idx = util.random(0, tasks.length - 1)
            for (let i = 0; i < tasks.length; i++) {
                if (tasks[i].show) {
                    tasks[i].show = false
                }
                if (i === idx) tasks[i].show = true
            }
            await this.updateActivityData(uid, { task: ada.task })
            return { status: 0, tasks }
        }

        const idx = ada.task.findIndex(t => t.group === group)
        // 没有该分组的任务
        if (idx === -1) return { status: -3 }
        const task = ada.task.splice(idx, 1)[0]
        // 任务已经完成
        if ((task.count !== 0 && task.current === task.count) || task.done) return { status: -4 }
        const newTask = await this.randomTasks(group, [task])
        // 这里只是兼容一下找不到 实际上应该不会出现
        if (!newTask) return { status: -5 }
        ada.task.push(newTask)
        await this.updateActivityData(uid, { task: ada.task })
        return { status: 0, task: newTask }
    },
    // 获取自己的任务
    async getTasks(uid) {
        let refreshNormal = false, refreshSpecial = false
        let ada = await this.getActivityData(uid, { key: 1, task: 1, login: 1, bigLogin: 1 })
        // 清理上一期数据
        let record = false
        if (!ada || ada.key !== SPECIAL_SHOP_KEY) {
            ada = await this.updateActivityData(uid, {
                task: [],
                key: SPECIAL_SHOP_KEY,
                login: 0,
                bigLogin: "",
                update: 0,
                up: 0,
                taskLimitData: [],
                videoGet: 0,
            })
            // 删除redis key
            await this.clearLastKey(uid)
            refreshNormal = true
            refreshSpecial = true
            record = true
        }
        if (!ada || !ada.key || !ada.task || !ada.task.length) {
            refreshNormal = true
            refreshSpecial = true
        }
        let task = []
        if (!refreshNormal && !refreshSpecial) {
            return {
                status: 0,
                task: ada.task.map(t => t),
                login: ada.login,
                bigLogin: ada.bigLogin || "",
                record: false,
            }
        }
        // 普通任务刷新
        if (refreshNormal) {
            task.push(await this.randomTasks(GROUP_1), await this.randomTasks(GROUP_2), await this.randomTasks(GROUP_3))
        }
        // 特殊任务刷新
        if (refreshSpecial) {
            // 默认展示第一个任务
            const first = await this.randomTasks(GROUP_100, task)
            first.show = true;
            task.push(first)
            task.push(await this.randomTasks(GROUP_100, task))
            task.push(await this.randomTasks(GROUP_100, task))
        }
        if (!task[0] && !task[1] && !task[2]) {
            if (task[3] && task[4] && task[5]) {
                task = task.splice(3, 3)
            } else {
                return { status: -2 } //活动已经关闭
            }
        }
        const info = {
            task: task,
            login: 0,
            bigLogin: refreshSpecial ? "" : ada.bigLogin,
        }
        const today = util.getNumberDay(util.getTodayZeroTime())
        if (!ada || (ada && ada.update !== today)) {
            info.login = this.setLoginDay(0)
            info.bigLogin = this.newSetLoginDay("")
            info.update = today
        }
        const newData = await this.updateActivityData(uid, info)
        return {
            status: 0,
            task: newData.task.map(t => {
                return {
                    id: t.id,
                    group: t.group,
                    count: t.count,
                    current: t.current,
                    done: t.done,
                    show: !!t.show
                }
            }),
            login: info.login,
            bigLogin: info.bigLogin,
            record,
        }
    },
    async combine(uid, id) {
        const item = this.mod.originalRewards.find(r => r.id === id)
        // 目标合成奖励不存在
        if (!item) return { status: -1 }
        const ada = await this.getActivityData(uid, { skin: 1, reward: 1 })
        if (ada == null || ada.skin == null) return { status: -2 }
        // 已经拥有该皮肤
        // if (ada.reward && ada.reward.includes(id)) return {status: -3}
        // 合成材料不足
        const skinData = ada.skin.find(s => s.id === id)
        if (!skinData || skinData.num < 3) return { status: -4 }
        const info = {}
        const where = {
            uid
        }
        // 扣除碎片
        skinData.num -= 3
        if (skinData.num === 0) {
            // 如果碎片数量为0，删除该皮肤的数据
            info.$pull = { skin: { id } }
        } else {
            // 否则更新碎片数量
            where["skin.id"] = id
            info["skin.$.num"] = skinData.num
        }
        // 添加奖励  和兑换记录
        const map = SKIN_SHARD[item.reward.id]
        // 合成后的奖励id
        info.$push = {
            reward: {
                $each: [map.sid]
            },
            limit: { $each: [{ id: map.sid, time: Date.now() }] }
        }

        const newData = await this.updateActivityData(where, info)
        return { status: 0, reward: newData.reward.map(i => i) }
    },
    // 请求完成任务   force 强制完成(debug)
    async finishTask(uid, id, force = false) {
        const ada = await this.getActivityData(uid, { task: 1, login: 1, coupon: 1, bigLogin: 1, taskLimitData: 1 })
        if (!ada || !ada.task || !ada.task.length) return { status: -1 } // 当前没有任务
        const targetTask = ada.task.find(t => t && t.id === id)
        let resp = { status: -2, coupon: 0, couponPoint: 0 }
        if (!targetTask || targetTask.done) return resp // 当前没有这个任务 或者任务已经完成了
        const config = TASK_LIMIT_DATA[targetTask.group]
        // 判断任务完成次数是否超限
        if (ada.taskLimitData && config) {
            const limit = ada.taskLimitData.find(l => l.group === targetTask.group)
            if (limit && limit.count >= config.limit) return { status: -3 }
        }
        // 分组1、2的任务服务器不进行计算
        if (!force && (targetTask.group === GROUP_3 || targetTask.group === GROUP_100)) {
            switch (targetTask.conditon) {
                // 邀请玩家
                case CONDITION_INVITE_NEW:
                case CONDITION_INVITE_OLD:
                    targetTask.count <= targetTask.current && (resp.status = 0)
                    break
                case 207: // 连续登录N天
                    this.checkConsecutiveNDays(ada.login, targetTask.count) && (resp.status = 0)
                    break
                case CONDITION_LOGIN_TOTAL_30:
                    this.newGetTotalLoginDays(ada.bigLogin) >= targetTask.count && (resp.status = 0)
                    break
                case CONDITION_LOGIN_TOTAL_3:
                    // 累计登录N天
                    this.getTotalLoginDays(ada.login) >= targetTask.count && (resp.status = 0)
                    break
            }
        } else {
            resp.status = 0
        }
        let clearLoginData = targetTask.conditon === 207 || targetTask.conditon === CONDITION_LOGIN_TOTAL_30 || targetTask.conditon === CONDITION_LOGIN_TOTAL_3
        if (resp.status === 0) {
            // 保存一下任务记录 防止丢档
            targetTask.current = targetTask.count
            targetTask.done = true
            const info = {
                task: ada.task
            }

            // 更新分组任务完成次数
            let limit = ada.taskLimitData.find(l => l.group === targetTask.group)
            if (!limit) {
                limit = {
                    group: targetTask.group,
                    count: 1
                }
                ada.taskLimitData.push(limit)
            } else {
                limit.count++
            }
            info.taskLimitData = ada.taskLimitData

            if (targetTask.group === GROUP_100) {
                // 将所有特殊任务标记成done 特殊任务只能完成一个
                info.task.forEach(t => t.group === GROUP_100 && (t.done = true))
                info.$inc = { coupon: config.rewardCnt }
                resp.coupon += config.rewardCnt
            } else {
                info.$inc = { couponPoint: config.rewardCnt }
                // 清理登录数据
                if (clearLoginData) {
                    info.login = 0
                }
                resp.couponPoint += config.rewardCnt
                if (limit.count < config.limit) {
                    const exclude = ada.task.filter(t => t !== targetTask)
                    const rr = await this.randomTasks(targetTask.group, exclude);
                    exclude.push(rr)
                    info.task = exclude
                }
            }
            await this.updateActivityData(uid, info)
        }

        return resp
    },
    // 任务进度更新，针对看广告任务丢进度的问题
    async advanceTask(uid, cnt) {
        const ada = await this.getActivityData(uid, { task: 1 })
        if (!ada || !ada.task || !ada.task.length) return { status: -1 } // 当前没有活动数据
        const targetTask = ada.task.find(t => t && t.group === GROUP_2 && !t.done)
        if (!targetTask) return { status: -2 } // 当前没有可以增加进度的任务
        targetTask.current = cnt
        const info = {
            task: ada.task
        }
        await this.updateActivityData(uid, info)
        return { status: 0 }
    },
    // 邀请任务
    async handleInviteTask(uid, isNew, isOld, cnt = 1) {
        logger.info(`handleInviteTask ${uid} ${isNew} ${isOld} ${cnt}`)
        if (!await this.isOpen()) return
        const ada = await this.getActivityData(uid, { task: 1 })
        if (!ada || !ada.task || !ada.task.length) return
        // 需要有邀请任务才能计数
        let _continue = ada.task.filter(t => t && (t.conditon === CONDITION_INVITE_NEW || t.conditon === CONDITION_INVITE_OLD))
        _continue = _continue.length
        if (!_continue) return
        let inviteTask = null
        if (isOld) {
            inviteTask = ada.task.find(t => t && t.conditon === CONDITION_INVITE_OLD)
        }
        if (isNew) {
            inviteTask = ada.task.find(t => t && t.conditon === CONDITION_INVITE_NEW)
        }
        if (!inviteTask) return
        inviteTask.current = inviteTask.current || 0
        inviteTask.current += cnt
        logger.info(`handleInviteTask ${uid} ${isNew} ${isOld} ${cnt} ${inviteTask.current}`)
        const idx = ada.task.findIndex(t => t && t.id === inviteTask.id)
        ada.task[idx] = inviteTask
        await this.updateActivityData(uid, {
            task: ada.task
        })
    },
    // 登陆相关的任务(连续/累计)
    async handleLoginTask(uid) {
        if (!await this.isOpen()) return
        const ada = await this.getActivityData(uid, { task: 1, update: 1, login: 1, bigLogin: 1 })
        if (!ada || !ada.task || !ada.task.length) return
        const today = util.getNumberDay(util.getSixAmTimestamp())
        const info = {}
        // 当日登录计数已经用掉
        if (ada.update !== today) {
            // 需要有登录任务才能计数
            let single = ada.task.filter(t => t && (t.conditon === CONDITION_LOGIN_TOTAL_3))
            if (single.length) {
                info.login = this.setLoginDay(ada.login || 0)
                info.update = today
            }
            info.bigLogin = this.newSetLoginDay(ada.bigLogin || "")
        }
        if (info.update || info.bigLogin !== ada.bigLogin) {
            await this.updateActivityData(uid, info)
        }
        return info.login || 0
    },
    // 设置当天登录
    setLoginDay(login) {
        // 通过位运算将对应的位设置为1
        const day = util.getNumberDay(util.getSixAmTimestamp())
        login |= (1 << day);
        return login
    },
    // 检查是否连续登录N天
    checkConsecutiveNDays(login, N) {
        const today = util.getNumberDay(util.getSixAmTimestamp())
        for (let i = today - N + 1; i <= today; i++) {
            if (login & (1 << i)) {
                N--
            }
        }
        return N === 0
    },
    // 获取累计登录天数
    getTotalLoginDays(login) {
        let cumulativeOnes = 0;
        for (let i = 0; i < 32; i++) {
            if (login & (1 << i)) {
                cumulativeOnes++;
            }
        }
        return cumulativeOnes
    },
    newSetLoginDay(bigLogin) {
        const day = util.getNumberDay(util.getSixAmTimestamp())
        const idx = day - this.mod.startDay
        if (bigLogin.length < idx - 1) {
            for (let i = 0; i < idx; i++) {
                if (bigLogin[i]) continue
                bigLogin += "0"
            }
        }
        bigLogin += "1"
        return bigLogin
    },
    newGetTotalLoginDays(bigLogin) {
        let total = 0
        bigLogin.split("").forEach(s => s === "1" && total++)
        return total
    },
    /**
     * 清理普通任务
     * @param uid
     * @return {Promise<void>}
     */
    async clearNormalTask(uid) {
        const ada = await this.getActivityData(uid, { task: 1 })
        if (!ada || !ada.task || !ada.task.length) return
        const task = ada.task.filter(t => t && t.group === GROUP_100)
        await this.updateActivityData(uid, { task: task, login: 0 })
    },
    /**
     * 概率定向选择
     * @param uid
     * @param type
     * @return {Promise<{status: number}>}
     */
    async upSelect(uid, type) {
        if (UP_TYPE.NORMAL === type || UP_TYPE.SPECIAL_1 === type || UP_TYPE.SPECIAL_2 === type) {
            const ada = await this.getActivityData(uid, { up: 1 })
            if (ada && ada.up) {
                // 已经选择up
                return { status: 2 }
            }
            await this.updateActivityData(uid, { up: type })
            return { status: 0 }
        }
        // up类型不正确
        return { status: 1 }
    },
    /**
     * 将所有珍宝点兑换成券
     * @param uid
     * @return {Promise<any>}
     */
    async exchangeAllCouponPoint(uid) {
        let ada = await this.getActivityData(uid, { couponPoint: 1, coupon: 1 })
        if (ada && ada.couponPoint) {
            const coupon = Math.floor(ada.couponPoint / 10)
            if (coupon >= 1) {
                logger.info(uid + "兑换珍宝点, 兑换前拥有：" + (ada.coupon || 0))
                ada = await this.updateActivityData(uid, {
                    $inc: { coupon: coupon, couponPoint: -coupon * 10 }
                })
            }
        }
        return {
            coupon: ada ? ada.coupon || 0 : 0,
            couponPoint: ada ? ada.couponPoint || 0 : 0
        }
    },
    /**
     * 领取视频彩蛋奖励
     * @param uid
     * @return {Promise<{point: (*|number), status: number}|{status: number}>}
     */
    async videoGet(uid) {
        let ada = await this.getActivityData(uid, { videoGet: 1 })
        if (ada) {
            let limit = ada.videoGet
            if (limit === null || limit === undefined) limit = 0
            let addVal = VIDEO_GET[limit]
            if (!addVal) {
                // 次数上限
                return { status: -1 }
            }
            ada = await this.updateActivityData(uid, {
                $inc: { point: addVal, videoGet: 1 },
            })
        }
        return {
            status: 0,
            point: ada ? ada.point : 0
        }
    },
    // 返回false则不通过
    async checkVer(uid) {
        const tmp = await this.loginInfoCol.findOne({ uid })
        if (!tmp) return true
        const gameVer = tmp.gameVer
        return await util.cmpVersion('9.2.3', gameVer) <= 0
    },

    async getRewardAndLimit(uid) {
        const ada = await this.getActivityData(uid, { reward: 1, limit: 1 })
        const reward = ada ? ada.reward : []
        const exchange = []
        if (ada && ada.limit) {
            ada.limit.forEach(l => {
                if (l.id === -1) return
                // if (l.time < 1723021560000) return
                const idx = reward.indexOf(l.id)
                if (idx >= 0) {
                    reward.splice(idx, 1)
                }
                exchange.push(l.id)
            })
        }
        return {
            exchange,
            reward
        }
    }

}
module.exports = activityMod
