const logger = require("../common/log").getLogger("MonitorMod");
let db = require("../db/db");

class MonitorMod {

    private monitorCol: any = null

    public init() {
        this.monitorCol = db.createMonitorModel()
    }

    public async add(uid: string, points) {
        let doc = await this.monitorCol.findOne({uid}, {points: {$slice: -1}})
        let pointsSum = []
        for (let p of points) { //根据增量算个真实值
            let last = pointsSum.last() || {}
            let sum = {}
            let keys = Object.keys(p)
            for (let k of keys) {
                sum[k] = 0
                sum[k] = p[k] + (last[k] || 0)
            }
            pointsSum.push(sum)
        }
        if (doc) {
            let {t} = doc.points[0]
            let pushList = []
            for (let i = 0; i < pointsSum.length; i++) {
                let p = pointsSum[i]
                if (p.t > t) {
                    pushList = pointsSum.slice(i)
                    break
                }
            }
            let pushInfo = {
                points: {
                    $each: pushList,
                    $slice: -1000,
                }
            }
            await this.monitorCol.updateOne({uid}, {$push: pushInfo }, {upsert: true})
        }
        else {
            let pushInfo = {
                points: {
                    $each: pointsSum,
                    $slice: -1000,
                }
            }
            await this.monitorCol.updateOne({uid}, {$push: pushInfo }, {upsert: true})
        }
    }
}

export const monitorMod = new MonitorMod()