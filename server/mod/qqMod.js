const got = require('got');
const util = require("../common/util").default;
const config = require("../config");
const logger = require("../common/log").getLogger("qqMod");
const verror = require("verror")
const WXBizDataCrypt = require("../cypto/WXBizDataCrypt");
const db = require('../db/db');
const redisKey = require('../db/redisKey');

let qqMod = {
    async getLoginInfo(code, encryptedData, iv) {
        let info = await qqMod.code2Session(code);
        let { session_key, unionid, openid } = info;
        if (!unionid) {
            if (session_key && encryptedData && iv) {
                try {
                    let data = qqMod.decryptData(encryptedData, iv, session_key);
                    logger.info("encryptedData 解析: ", data);
                    info.nickName = data.nickName;
                    info.avatarUrl = data.avatarUrl;
                    info.unionid = data.unionId;
                } catch (error) {
                    logger.warn("decryptData fail: ", openid, encryptedData, iv, session_key);
                }
            }
        }
        return info;
    },

    getConfig() {
        return config.qq;
    },

    async getToken(refresh, retry = 5) {
        let key = redisKey.qqToken()
        let redis = db.redis;
        if (!refresh) {
            let token = await redis.get(key)
            if (token) return token
        }

        const { appid, secret } = this.getConfig();
        const url = `https://api.q.qq.com/api/getToken?grant_type=client_credential&appid=${appid}&secret=${secret}`;
        const data = await got(url, {
            timeout: 10 * util.Time.Second,
        }).json();
        const { errcode, access_token, expires_in } = data;
        if (!errcode) {
            await redis.set(key, access_token)
            redis.expire(key, expires_in - 60)
            return access_token;
        }
        else if (errcode == -1 && retry > 0) { //需要重试
            await util.wait(1000);
            return await this.getToken(refresh, retry - 1);
        }
        else {
            throw verror({ name: "getToken error", info: data });
        }
    },

    async refreshToken() {
        return this.getToken(true)
    },

    decryptData(encryptedData, iv, session_key) {
        let { appid } = this.getConfig();
        let pc = new WXBizDataCrypt(appid, session_key);
        let decryptData = pc.decryptData(encryptedData, iv);
        return decryptData;
    },

    async code2Session(code, retry = 5, useCache = true) {
        let key = redisKey.qqCode2Session(code);
        let data
        if (useCache) {
            data = await db.redis.hgetall(key)
            if (data) {
                return data
            }
        }

        let { appid, secret } = this.getConfig();
        let url = `https://api.q.qq.com/sns/jscode2session?appid=${appid}&secret=${secret}&js_code=${code}&grant_type=authorization_code`;
        data = await got(url, {
            timeout: 10 * 1000,
        }).json();
        const { errcode } = data;
        if (!errcode) {
            await db.redis.hmset(key, data)
            db.redis.expire(key, 1 * util.Time.Minute / 1000)
            return data;
        }
        else if (errcode == -1 && retry > 0) { //需要重试
            await util.wait(1000);
            return await this.code2Session(code, retry - 1, false);
        }
        else if (errcode == 40163) { //已被使用
            let cache = await db.redis.hgetall(key)
            if (cache) return cache
            throw verror({ name: "code2Session error", info: data });
        }
        else {
            throw verror({ name: "code2Session error", info: data });
        }
    },

    async sendSubscribeMessage(openid, template_id, tpnlData, uid, isRetry, retryCount = 5) {
        let access_token = await this.getToken(isRetry)
        const url = `https://api.q.qq.com/api/json/subscribe/SendSubscriptionMessage?access_token=${access_token}`;
        const data = await got.post(url, {
            json: {
                touser: openid,
                template_id: template_id,
                data: tpnlData,
            },
            timeout: 10 * util.Time.Second,
        }).json();
        const { errcode, errmsg } = data
        if (errcode == 0) {
            logger.info("sendSubMsg succ", uid, template_id)
            return true
        }
        else if (errcode == 46003) { //用户拒绝接受消息，如果用户之前曾经订阅过，则表示用户取消了订阅关系
            logger.warn("sendSubMsg cancel", uid, openid, template_id)
        }
        else if (errcode == 40014) { //过期
            if (!isRetry) {
                logger.warn("sendSubMsg token expire", uid, openid, template_id)
                return this.sendSubscribeMessage(openid, template_id, tpnlData, uid, true)
            }
            else {
                logger.error("sendSubMsg token error", uid, openid, template_id)
            }
        }
        // else if (errcode == 40036) { //重试
        //     if (retryCount > 0) {
        //         await util.wait(1000)
        //         return this.sendSubscribeMessage(openid, template_id, tpnlData, uid, false, retryCount - 1)
        //     }
        //     else {
        //         logger.error("sendSubMsg err 40036", uid, openid, template_id, tpnlData)
        //     }
        // }
        else {
            logger.error("sendSubMsg err", uid, openid, template_id, tpnlData, data)
        }
    },
}

module.exports = qqMod;