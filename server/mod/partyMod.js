const db = require("../db/db");
/**
 *
 */
// 派对配置
const party_config = require("../client/json/homePartyBase.json");
const {default: util} = require("../common/util");
const recordMod = require("./recordMod");
const userMod = require("./userMod");
const {PARTY_REWARD_GET} = require("../db/redisKey");
const logger = require("../common/log").getLogger("partyMod");

let partyMod = {
    __init: false,
    max_join_party_num: 2,// 最多可参加x个派对
    default_wait_time: 8 * util.Time.Hour, // 自动开启派对的等待时间
    init() {
        if (this.__init) return
        this.userCol = db.createUserModel()
        this.partyCol = db.createPartyModel()
        this.redis = db.redis;
        this.__init = true;
    },
    async getPartyState(uid, partyId) {
        let config = party_config.find(e => e.id === partyId)
        if (!config) return null
        let party = await this.partyCol.findOne({uid, partyId: config.id})
        if (party) {
            if (party.status === 2) {
                let cTime = party.overtime || party.time
                let cold_cd = Math.max(cTime + util.Time.Minute * (config.cold_cd + config.dur_time) - Date.now(), -1)
                    , status = cold_cd === -1 ? -1 : 2
                return {
                    partyId: config.id,
                    status,
                    time: -1,
                    cold_cd
                }
            }
            if (party.status === 1) {
                return {
                    partyId: config.id,
                    status: 1,
                    time: party.time,
                    cold_cd: -1
                }
            }
            if (party.status === 0) {
                return {
                    partyId: config.id,
                    status: 0,
                    time: party.time,
                    cold_cd: -1
                }
            }
        }
        return {partyId: config.id, status: -1, time: -1, cold_cd: -1}
    },
    async getLastParty(uid) {
        let arr = []
        for (let config of party_config) {
            arr.push(await this.getPartyState(uid, config.id))
        }
        return arr
    },
    // 判断uid是不是同时已经达到最大参加限制 不能参加其他派对了
    async isPartyMaxJoin(uid) {

    },
    async debug_set_party_ok(uid) {
        let party = await this.partyCol.findOneAndUpdate({uid, status: 0}, {status: 1})
        return {
            desc: party ? "ok" : "null"
        }
    },
    async debug_reset_cool_cd(uid, partyId) {
        await this.partyCol.updateMany({uid, partyId}, {time: 1})
        return {status: 1}
    },
    // 如果派对筹备完成，则改变状态
    async setPartyCanStartOrNot(_id) {
        let party = await this.partyCol.findOne({_id})
        if (!party) return -1
        if (party.status === 2) return 2
        let config = party_config.find(e => e.id === party.partyId)
        if (config.pre_cd * util.Time.Minute + party.time >= Date.now()) {
            // 筹备完成
            await this.partyCol.updateOne({_id}, {state: 1})
            return 1
        }
        return 0
    },
    // 获取我自己的派对信息，要开始办派对🎉了 force是超时系统强制开启
    async finishMyParty(uid, force = false) {
        let now = Date.now()

        let party = await this.partyCol.findOne({uid, status: 1})
        // 有已知的筹备完成派对
        if (party) {
            let config = party_config.find(e => e.id === party.partyId)
            await this.partyCol.updateOne({_id: party._id.toString()}, {
                status: 2,
                overtime: now
            })
        }
        // 处理未知的但是可能筹备完成的派对
        if (!party) {
            let temp = await this.partyCol.findOne({uid, status: 0})
            if (!temp) return {status: -1}
            let config = party_config.find(e => e.id === temp.partyId)
            if (Date.now() - temp.time >= config.pre_cd * util.Time.Minute) {
                await this.partyCol.updateOne({_id: temp._id.toString()}, {
                    status: 2,
                    overtime: now
                })
                party = temp
            }
        }
        // 没有筹备完的派对
        if (!party) return {status: -1}
        let vis = []
        let master = await this.userCol.findOne({uid})
        // 0206 如果玩家master注销后这里会有一个报错

        if (!master) {
            master = {
                diyName: "",
                lang: ""
            }
        }
        let masterName = master.diyName || master.nickName || ""
        for (let u of party.joiner) {
            let par = u.split(","), skin = "", _uid = ""
            par.length && (_uid = par.shift())
            par.length && (skin = par.shift())
            let todayGet = await this.redis.get(PARTY_REWARD_GET(_uid)) || 0
            todayGet && (todayGet = Number(todayGet))
            let user = await this.userCol.findOneAndUpdate({uid: _uid}, {
                $pull: {
                    partyJoin: party._id.toString()
                }
            })
            if (!user) {
                user = {
                    diyName: "",
                    lang: ""
                }
            }
            let myName = user.diyName || user.nickName || ""
            // 超过次数  不给奖励了
            if (todayGet < this.max_join_party_num) {
                todayGet += 1
                await this.recordPartyReward(_uid, masterName, party.partyId, user.lang)
                await this.redis.set(PARTY_REWARD_GET(_uid), todayGet, 'PX', util.Time.Day)
            }
            vis.push({
                uid: _uid,
                name: myName,
                skin
            })
        }
        // 需要给主人发奖
        if (force) {
            await this.recordPartyReward(uid, `|${vis.length}`, party.partyId, master.lang, true)
        }
        return {status: 1, partyId: party.partyId, vis}
    },
    // 走发奖接口，type固定是 100712
    async recordPartyReward(uid, name = "", partyId, lang = "", self = false) {
        let config = party_config.find(e => e.id === partyId)
        let time = Date.now() + config.dur_time * util.Time.Minute
        let reward = `${name},${partyId}`
        self && (reward = `${reward}|${uid}${name}`)
        // 如果是自己 " |派对人数,派对id|派对主人uid|派对人数 "
        // 如果是别人 " 派对主人name,派对id "
        await userMod.addCompensationRewardMultiple(uid, reward, lang, "", 100712)
    },
    // 简要的获取用户是不是在筹备派对状态 返回mongo id
    async isPlayerStartParty(uid) {
        let doc = await this.partyCol.findOne({uid, status: 0}, {_id: 1})
        return doc ? doc._id : ""
    },
    async getCanJoinPartyNum(uid) {
        let user = await this.userCol.findOne({uid})
        if (!user) return this.max_join_party_num
        return user.partyJoin ? this.max_join_party_num - user.partyJoin.length : this.max_join_party_num
    },
    // 参加好友的派对
    async joinParty(uid, targetId) {
        let user = await this.userCol.findOne({uid})
        if (!user) return {status: -1}
        // 自己参加数量上限 0808敏捷版本，不再限制参与数量，转而去限制奖励次数
        // let todayGet = await this.redis.get(PARTY_REWARD_GET(uid)) || 0
        // todayGet && (todayGet = Number(todayGet))
        // if (todayGet >= this.max_join_party_num) return {status: -2}
        let party = await this.partyCol.findOne({uid: targetId, status: 0})
        // 好友没有筹备中的派对
        if (!party) return {status: -3}
        let config = party_config.find(e => e.id === party.partyId)
        // 参与数量达到上限
        if (party.joiner && party.joiner.length >= config['max_friends']) return {status: -4}
        // 已经参与其中了
        let exists = party.joiner.find(e => e.indexOf(uid) > -1)
        if (party.joiner && exists && exists.length) return {status: -5}
        // 参与
        await this.userCol.updateOne({uid}, {
            $push: {
                partyJoin: {
                    $each: [party._id.toString()]
                }
            }
        })
        // 1228调整 皮肤id在参加时就确定
        let record = await recordMod.getRecord(uid)
        let skin = ""
        if (record) skin = await recordMod.getFromRecord("main.useWudongSkinId", record)
        await this.partyCol.updateOne({_id: party._id.toString()}, {
            $push: {
                joiner: {
                    $each: [`${uid},${skin}`]
                }
            }
        })
        return {status: 1}
    },
    /**
     * 开启一个派对
     * @param uid 用户id
     * @param partyId 派对序列号
     * @return
     */
    async startParty(uid, partyId) {
        let status = 0
            , user
            , party
            , time = Date.now()
            , config = party_config.find(e => e.id === partyId)
        if (!config) status = -1
        // !status && (user = await this.userCol.findOne({uid}), !user && (status = -2))
        // if (status) return {status}
        party = await this.partyCol.findOne({uid, status: 0})
        // 已经有正在筹备的派对
        if (party) return {status: -3}
        let {cold_cd} = await this.getPartyState(uid, partyId)
        // 该类型派对正在cd中
        if (cold_cd >= 0) return {status: -4}

        await this.partyCol.updateOne({uid, partyId}, {time, overtime: 0, joiner: [], status: 0}, {upsert: true})
        return {status: 1}
    },
    // 派对记录必须至少保持三天，要用来做cd计算用，所以没法儿开完派对就删除派对记录。
    async checkAndRun() {
        logger.info("checkAndRun....")
        try {
            let limit = 10000
            let logic = async (lastId) => {
                let where = {status: {$in: [0, 1]}};
                lastId && (where._id = {$gt: lastId});
                let docs = await this.partyCol.find(where).sort({_id: 1}).limit(limit)
                await util.promiseMap(docs, async ({_id, uid, partyId, time, status}) => {
                    let config = party_config.find(e => e.id === partyId)
                    if (!config) return void await this.partyCol.deleteOne({_id})
                    if (status === 0) {
                        if (Date.now() - time >= config.pre_cd * util.Time.Minute)
                            return void await this.partyCol.updateOne({_id}, {status: 1})
                    }
                    if (status === 1) {
                        if (Date.now() - time >= (config.pre_cd * util.Time.Minute + this.default_wait_time))
                            return void await this.finishMyParty(uid, true)
                    }
                })
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            }
            let exec_ = true
            let lastId = 0;
            while (exec_) {
                lastId = await logic(lastId);
                exec_ = !!lastId;
            }
            await util.wait(5 * 60 * util.Time.Second)
        } catch (e) {
            logger.info("checkAndRun err:", e)
        }
        logger.info("checkAndRun end")
        return this.checkAndRun()
    },
    async delPartyLine() {
        let limit = 3000
        let logic = async (lastId) => {
            let where = {status: 2};
            lastId && (where._id = {$gt: lastId});
            let docs = await this.partyCol.find(where).sort({_id: 1}).limit(limit)
            await util.promiseMap(docs, async ({_id, uid, partyId, time}) => {
                let config = party_config.find(e => e.id === partyId)
                if (!config) return void await this.partyCol.deleteOne({_id})
                if (Date.now() - time >= (config.pre_cd * util.Time.Minute + this.default_wait_time + 3 * util.Time.Day))
                    return void await this.partyCol.deleteOne({_id})
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        await util.wait(util.Time.Day)
        return await this.delPartyLine()
    }
}

module.exports = partyMod
