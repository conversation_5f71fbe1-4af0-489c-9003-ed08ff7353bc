module.exports = {
    init() {
        const idMod = require("./idMod");
        const userMod = require("./userMod");
        const recordMod = require("./recordMod");
        const versionMod = require("./versionMod");
        const rankMod = require("./rankMod");
        const verifyMod = require("./verifyMod");
        const loginMod = require("./loginMod");
        const shareMod = require("./shareMod");
        const feedbackMod = require("./feedbackMod")
        const roomMod = require("./roomMod")
        const subscribeMod = require("./subscribeMod")
        const statisticsMod = require("./statisticsMod");
        const motherMsgMod = require("./motherMsgMod");
        const txCosMod = require("./txCosMod");
        const performanceMod = require("./performanceMod")
        const friendMod = require("./friendMod")
        const wdWorkMod = require("./wdWorkMod")
        const recommendMod = require("./recommendMod")
        const couponMod = require("./couponMod")
        const adSkipCardMod = require("./adSkipCardMod")
        const currencyModel = require("./currencyModel")
        const {expVerMod} = require("./expVerMod")
        const {verfiyRecordMod} = require("./verfiyRecordMod")
        const {redeemCodeMod} = require("./redeemCodeMod")
        const {monitorMod} = require("./monitorMod")
        const {payMod} = require("../payment/PayModel")
        const textModerationMod = require("./textModerationMod")
        const appleLoginMod = require("./appleLoginMod");
        const googleMod = require("./googleMod");
        const {default: ModelMgr} = require("../client/ModelMgr");
        const storeMod = require("./storeMod");
        const newsMod = require("./NewsMod");
        const accountModel = require("./accountMod");
        const thinkingDataModel = require("./thinkingData");
        const functionOpenMod = require("./functionMod");
        const partyMod = require("./partyMod");
        const partyPlusMod = require("./partyPlusMod");

        const gardenMod = require("./gardenMod");

        const hmsLoginMod = require("./hmsLoginMod");
        const robotMod = require("./robotMod");
        const activityMod = require("./activityMod");

        //const curLogMod = require("./curLogMod");

        loginMod.init();
        idMod.init();
        userMod.init();
        recordMod.init();
        versionMod.init();
        rankMod.init();
        verifyMod.init();
        shareMod.init();
        feedbackMod.init()
        roomMod.init()
        subscribeMod.init()
        statisticsMod.init()
        motherMsgMod.init()
        txCosMod.init()
        performanceMod.init()
        friendMod.init()
        adSkipCardMod.init()
        wdWorkMod.init()
        recommendMod.init()
        couponMod.init()
        currencyModel.init()
        expVerMod.init()
        verfiyRecordMod.init()
        redeemCodeMod.init()
        monitorMod.init()
        payMod.init()
        textModerationMod.init()
        googleMod.init()
        appleLoginMod.init()
        storeMod.init();
        newsMod.init();
        accountModel.init();
        thinkingDataModel.init();
        functionOpenMod.init();
        partyMod.init()
        partyPlusMod.init()
        activityMod.init()

        gardenMod.init()

        hmsLoginMod.init()
        robotMod.init()

        //curLogMod.init();
    }
};
