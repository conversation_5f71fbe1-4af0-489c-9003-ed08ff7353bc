const recordMod = require("../mod/recordMod");
const currencyModel = require("../mod/currencyModel");
const logger = require("../common/log").getLogger("storeMod")
const { CURRENCY, CURRENCY_ACTION, CURRENCY_ORDER_STATUS, ShopItemType } = require("../common/constant");
const userMod = require("../mod/userMod");
const { gameHelper } = require("../client/GameHelper");
const { googleVerify } = require('../payment/GoogleVerify')
let db = require("../db/db");
const { default: util } = require("../common/util");
const maxLinesOfOnePage = 10;

let storeMod = {
    init() {
        this.currencyOrderCol = db.createCurrencyOrderModel();
        this.refundCol = db.createRefundModel();
        this.deal = true;// 是否开启退款扣除道具的处理
        this.packageName = "";
    },
    /**
     * 尝试保存退款信息，前提是这条退款消息不是重复的
     * @param uid
     * @param time
     * @param reason
     * @param orderId
     * @param productId
     * @param platform
     * @returns {Promise<boolean>}
     */
    async save(uid, time, reason, orderId, productId, platform) {
        let line = await this.refundCol.findOne({ orderId });
        if (line && !line.length) {
            logger.info("已经处理过的退款记录:", orderId);
            return false
        }
        let info = new this.refundCol({ uid, time, reason, orderId, productId, platform });
        await info.save();
        return true;
    },
    /**
     * 谷歌的退款需要手动去请求
     * @see https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.voidedpurchases/list
     * @returns {Promise<void>}
     */
    async deal_google_refund_order_info() {
        let response = await googleVerify.getRefund();
        let err = 0;
        !err && !response && (err = 1);
        if (err) return;
        let data = response.data;
        !err && !Object.keys(data).length && (err = 1);
        if (err) return;
        // 分页请求才会用到，因为退款数据是每天处理一次，所以并不是很多
        //let nextPageToken = data.tokenPagination.nextPageToken || "";
        let voidedPurchases = data.voidedPurchases;
        if (!voidedPurchases || !voidedPurchases.length) {
            logger.info('No lines returned.');
            return;
        }
        for (let line of voidedPurchases) {
            logger.info(`退款消息,平台:google,退款时间:${line.voidedTimeMillis},购买时间:${line.purchaseTimeMillis},订单号:${line.orderId},产品ID:nil`);
            await this._deal(line.orderId, line.voidedTimeMillis, line.voidedReason, "nil", "google");
        }
    },
    /**
     *
     * @param cancelTime 退款时间
     * @param cancelReason 退款原因 1客户因程序问题,0 其他原因
     * @param originalTime 订单发起时间
     * @param originalTransactionId 订单ID
     * @param productId 产品ID
     * @see https://developer.apple.com/documentation/appstoreservernotifications/responsebodyv1
     * @returns {Promise<void>}
     */
    async deal_apple_refund_order_info(cancelTime, cancelReason
        , originalTime, originalTransactionId, productId) {
        logger.info(`退款消息,平台:apple,退款时间:${cancelTime},购买时间:${originalTime},订单号:${originalTransactionId},产品ID:${productId}`);
        await this._deal(originalTransactionId, cancelTime, cancelReason, productId, "apple");
    },
    /**
     *
     * @param originalTransactionId
     * @param cancelTime
     * @param cancelReason
     * @param productId
     * @param platform
     * @returns {Promise<void>}
     * @private
     */
    async _deal(originalTransactionId, cancelTime, cancelReason, productId, platform) {
        let interrupt = false;
        let orderInfo = await this.getSystemPayOrderInfo(originalTransactionId);
        if (!orderInfo) return
        if (await this.save(orderInfo.uid, cancelTime, cancelReason, originalTransactionId, orderInfo.action, platform) && this.deal) {
            logger.info("处理退款消息.")
            !orderInfo && logger.info(`订单信息不存在,orderId:${originalTransactionId}`) && (interrupt = true);
            !interrupt && await this.deal_order_action(orderInfo.action, orderInfo.uid);
        }
    },

    async deal_order_action(action, uid) {
        let interrupt = false;
        !action && logger.info(`退款商品名称获取失败!`) && (interrupt = true);
        if (interrupt) return;
        logger.info(`处理退款事件,商品名称:${action}`);
        let record = await recordMod.findOne(uid);
        !record && (interrupt = true);
        if (interrupt) return;
        let gameVer = record.gameVer;
        let data = await currencyModel.getCfgByAction(action, gameVer);
        if (!data) return void logger.warn(`no cfg found:${action} ${uid}`);
        // 扣除商品所赠送的一次性道具(这一步只会扣除服务器货币)
        data && await currencyModel.changeCurrencyByData(uid, data, -1, false);
        // 如果是月卡类商品，额外扣除每日赠送过的道具，并取消订阅
        if (data.type && data.type === ShopItemType.MONTH_CARD) {
            let balanceList = await currencyModel.asynCurrencyBalance(uid);
            let balance = balanceList[action];
            let used = 30 - balance;
            // 天数归0
            await currencyModel.changeCurrencyBalance(uid, action, balance * -1, false);
            // 每天的奖励道具扣除
            let rewards = gameHelper.stringToConditions(data.extraReward)
            for (let { type, count } of rewards) {
                if (type == 16) { //风车
                    await currencyModel.changeCurrencyBalance(uid, CURRENCY.WINDMILL, count * -1 * used, false)
                } else if (type == 17) { //剪刀
                    await currencyModel.changeCurrencyBalance(uid, CURRENCY.SCISSOR, count * -1 * used, false)
                }
            }
        }
        // 尝试扣除其他道具，比如永久特权卡 家具 员工 皮肤 这些道具基本都在存档里，然后强制玩家更新
        await this.deductPropsFromRecord(uid, data);
    },
    async deductPropsFromRecord(uid, data) {
        let record = await recordMod.getRecord(uid), shop = recordMod.getFromRecord('shop', record);
        try {
            if (data.type === ShopItemType.PACKAGE) {
                await this.removePrivilegeByAction(record, data.action);
            }
            if (data.type === ShopItemType.MONTH_CARD) {
                // 月卡数据
                let monthCards = shop["monthCards"];
                monthCards.forEach(m => {
                    if (m.action === data.action) {
                        m.nextClaimTime = 0;
                        m.endTime = -1;
                    }
                })
            }
            if (data.action === CURRENCY_ACTION.IAP_GASZ) {
                //关爱手指
                shop["gaszSurplusTime"] = 0;
            }
            await this.tryRemoveItemByAction(data, record);
            if (data.action === CURRENCY_ACTION.IAP_JXTH) {
                //精选特惠 这种礼包有首冲额外奖励，所以需要恢复首冲次数
                shop["jxthTimestamp"] = 0;
                shop["fristJxth"] = true;
                await this.removePrivilegeByAction(record, CURRENCY_ACTION.IAP_JXTH_V2);
            }
            if (data.action === CURRENCY_ACTION.IAP_CXYX) {
                //畅享游戏
                shop["cxyxTimestamp"] = 0;
                shop["fristCxyx"] = true;
                await this.removePrivilegeByAction(record, CURRENCY_ACTION.IAP_CXYX_V2);
            }
            if (data.action === CURRENCY_ACTION.IAP_XSZL) {
                //新手助力
                shop["xszlTimestamp"] = 0;
                shop["fristXszl"] = true;
                await this.removePrivilegeByAction(record, CURRENCY_ACTION.IAP_XSZL_V2);
            }
            if (data.action === CURRENCY_ACTION.IAP_SMXY) {
                //时髦新衣
                await this.removePrivilegeByAction(record, CURRENCY_ACTION.IAP_SMXY);
            }
        } catch (e) {
            console.log(e);
        }

        // 更新存档 并且强制更新
        await recordMod.setRecord(uid, record);
        await userMod.updateOne(uid, { forceDownload: true });
    },
    /**
     * 充值礼包退款后，尝试移除玩家已经获取到的礼包里面的其他道具
     * 可能是家具、转转券、饼干
     * 可能礼包里面没有其他道具 则什么都不会做
     * @param data
     * @param record
     * @returns {Promise<void>}
     */
    async tryRemoveItemByAction(data, record) {
        let world = recordMod.getFromRecord('world', record),
            global = recordMod.getFromRecord('global', record);
        let deal_furn = async (type, id, count) => {
            // 先尝试从背包移除
            let removed_count = await this.removeItemFromBagById(record, id, count)
            // 没移除成功 说明家具被放在某个房间使用了 遍历房间方案
            !removed_count && (removed_count = await this.removeItemFromKeFangById(record, id, count));
            // 特殊家具需要从world里面移除
            let unlockSpeFurns_world = world["unlockSpeFurns"];
            for (let furn of unlockSpeFurns_world) {
                furn.id === id && (furn.count -= count);
                !furn.count && (unlockSpeFurns_world.splice(unlockSpeFurns_world.indexOf(furn), 1));
            }
        };
        let deal_candies = async (type, id, count) => {
            id === -1 && (global.candies = Math.max(global.candies - count, 0));
        };
        let deal_biscuits = async (type, id, count) => {
            id === -1 && (global.biscuits = Math.max(global.biscuits - count, 0));
        }
        let deal_wudong_skin = async (type, id, count) => {
            let main = recordMod.getFromRecord('main', record);
            let skins = main.unlockWudongSkins;
            let index = skins.indexOf(id);
            index > -1 && (skins.splice(index, 1)) && (main.useWudongSkinId = skins[0]);
        }
        let deal_staff = async (type, id, count) => {
            let data = recordMod.getFromRecord('staff', record);
            if (!data) return false;
            data['unlockStaffs'] = data['unlockStaffs'].filter(item => {
                return item.id !== id;
            })
        }
        let deal_meteor_skin = async (type, id, count) => {
            let main = recordMod.getFromRecord('main', record);
            if (!main) return false;
            main.unlockMainSkins = main.unlockMainSkins.filter(item => {
                return item !== id;
            })
            main.useMainSkinId = main.unlockMainSkins[0];
        }
        let rewards = gameHelper.stringToConditions(data.reward)
        for (let { type, id, count } of rewards) {
            switch (type) {
                case 4:
                    return void await deal_furn(type, id, count);
                case 1:
                    return void await deal_biscuits(type, id, count);
                case 2:
                    return void await deal_candies(type, id, count);
                case 10:
                    return void await deal_wudong_skin(type, id, count);
                case 11:
                    return void await deal_meteor_skin(type, id, count);
                case 16:
                case 17:
                    break;
                case 21:
                    return void await deal_staff(type, id, count);
                default:
                    logger.warn(`没有对应的道具扣除逻辑!!!!! type:${type}`);
                    break;
            }
        }
    },
    /**
     * 尝试从客房移除指定id家具
     * 返回值true表示成功
     * @param record
     * @param id
     * @param count
     * @returns {Promise<boolean>}
     */
    async removeItemFromKeFangById(record, id, count = 1) {
        let deal = false;
        let prefix = "hotel_kefang_";
        let keys = Object.keys(record);
        // 只会处理特殊家具
        let deal_spefurn_kefang = async (roomId) => {
            // roomId 可能是 202 或者 202_map
            roomId = parseInt(roomId);
            let room = record[prefix + roomId];
            let kefang = room[`kefang_${roomId}`];
            //let unlockFurns = kefang["unlockFurns"];
            let furnSaves = kefang["furnSaves"];
            let _id = `SPE_${id}`
            for (let plan of furnSaves) {
                for (let furn of plan.furnitures) {
                    if (furn.id === _id) {
                        plan.furnitures.splice(plan.furnitures.indexOf(furn), 1);
                        !deal && (deal = true);
                    }
                }
            }
        }
        for (let key of keys) {
            key.startsWith(prefix) && await deal_spefurn_kefang(key.substring(prefix.length));
        }
        return deal;
    },
    /**
     * 尝试从背包移除指定Id的道具
     * 返回值是成功移除的数量
     * 会保证数量至少剩余0,不能为负数
     * @param record
     * @param id
     * @param count 移除全部填-1
     * @returns {Promise<number>}
     */
    async removeItemFromBagById(record, id, count = 1) {
        let removed_count = 0;
        let bag = recordMod.getFromRecord('bag', record);
        for (let item of bag.props) {
            if (item.id === id) {
                item.count -= count;
                removed_count += count;
            }
            if (item.count <= 0) {
                bag.props.splice(bag.props.indexOf(item), 1);
            }
        }
        return removed_count;
    },

    /**
     * 移除shop的purchasedItems里面的特权卡
     *
     * @param record
     * @param action
     * @returns 返回true表示至少移除1条记录，反之false
     */
    async removePrivilegeByAction(record, action) {
        let data = recordMod.getFromRecord('shop', record);
        if (!data) return false;
        let len = data.purchasedItems.length;
        data.purchasedItems = data.purchasedItems.filter(item => {
            return item.action !== action;
        })
        return len - data.purchasedItems.length > 0;
    },
    /**
     * 使用订单号获取db订单记录
     * @param order
     * @returns {Promise<void>}
     */
    async getSystemPayOrderInfo(order) {
        if (!order) throw new Error(`获取订单记录失败,order:${order}`)
        return await this.currencyOrderCol.findOne({ order })
    },
    /**
     * 获取已经处理完成的退款订单列表
     * @returns
     */
    async getRefunds(page, uid) {
        let where = {};
        uid && (where.uid = uid);
        let count = await this.refundCol.countDocuments(where);
        let pageCount = Math.ceil(count / maxLinesOfOnePage);
        page > pageCount && (page = pageCount);
        page < 1 && (page = 1);
        let docs = await this.refundCol.find(where).sort({ time: 'desc' }).skip((page - 1) * maxLinesOfOnePage).limit(maxLinesOfOnePage);
        docs = await util.promiseMap(docs, async (doc) => {
            let line = {};
            line.uid = doc.uid;
            line.time = doc.time;
            line.reason = doc.reason;
            line.orderId = doc.orderId;
            line.productId = doc.productId;
            line.platform = doc.platform;
            return line;
        })
        return { data: docs, pageCount: pageCount };
    }


}

module.exports = storeMod;
