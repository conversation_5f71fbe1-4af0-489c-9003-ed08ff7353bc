/**
 * 性能统计模块
 */

const util = require("../common/util").default;

const logger = require("../common/log").getLogger("performanceMod");

const SAVE_TIME = 10 * util.Time.Minute;

// 全局存储
const reqInfo = {}; //统计请求

let performanceMod = {
    redis: null,
    performanceCol: null,

    init() {
        let db = require("../db/db");
        this.redis = db.redis;
        this.performanceCol = db.createPerformanceModel()

        this.checkSave()
    },

    req(name, cosTime, reqSize, repSize) {
        let info = reqInfo[name]
        if (!info) info = {count: 0, totalTime: 0, maxTime: 0, maxAvgTime: 0, reqTotalSize: 0, repTotalSize: 0}
        // 次数
        info.count++
        // 总耗时
        info.totalTime += cosTime
        if (cosTime > info.maxTime) {
            // 单次最长耗时
            info.maxTime = cosTime
        }
        // 平均耗时 = 总耗时/次数  不存,计算
        // let avgTime = info.totalTime / info.count
        // if (avgTime > info.maxAvgTime) {
        //     info.maxAvgTime = avgTime
        // }
        // 请求包总长
        info.reqTotalSize += reqSize;
        // 响应包总长
        info.repTotalSize += repSize;
        reqInfo[name] = info
    },

    async checkSave() {
        if (this.performanceCol) {
            try {
                let now = Date.now()
                let date = now - (now % util.Time.Day)

                for (let name in reqInfo) {
                    let info = reqInfo[name]
                    let update = {
                        $inc: {
                            count: info.count,
                            totalTime: info.totalTime,
                        }
                    }
                    delete reqInfo[name]
                    let doc = await this.performanceCol.findOneAndUpdate({date, name}, update, {
                        upsert: true,
                        new: true
                    })
                    let maxInfo = {}
                    if (!doc.maxTime || doc.maxTime < info.maxTime) {
                        maxInfo.maxTime = info.maxTime
                    }
                    if (!doc.maxAvgTime || doc.maxAvgTime < info.maxAvgTime) {
                        maxInfo.maxAvgTime = info.maxAvgTime
                    }
                    if (!doc.reqTotalSize || doc.reqTotalSize < info.reqTotalSize) {
                        maxInfo.reqTotalSize = info.reqTotalSize;
                    }
                    if (!doc.repTotalSize || doc.repTotalSize < info.repTotalSize) {
                        maxInfo.repTotalSize = info.repTotalSize;
                    }
                    if (Object.keys(maxInfo).length > 0) {
                        await this.performanceCol.updateOne({date, name}, maxInfo)
                    }

                }
            } catch (error) {
                logger.error("checkSave", error)
            }
        }
        await util.wait(SAVE_TIME)
        this.checkSave()
    },

    async getInterfaceData(start, end, path) {
        if (await this.tryParseToInt(end, 0) === 0) {
            end = Date.now();
        }
        start = await this.tryParseToInt(start, 0);
        let conditionTemp1 = {
            date: {
                $gte: start,
                $lt: end
            }
        };
        // 已经在客户端处理过 可以省略
        if (path && path.startsWith("/")) {
            path = path.replace("/", "");
        }
        let conditionTemp2 = {
            date: {
                $gte: start,
                $lt: end,
            },
            name: {
                $regex: path
            }
        };
        let conditions = path ? conditionTemp2 : conditionTemp1;
        return await this.performanceCol.find(conditions);
    },

    async tryParseToInt(val, defaultVal) {
        let res = parseInt(val);
        if (!res) {
            res = defaultVal;
        }
        return res;
    }
}

module.exports = performanceMod;