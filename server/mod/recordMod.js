const logger = require("../common/log").getLogger("recordMod");
let userMod, idMod, verfiyRecordMod, loginMod, rankMod, accountMod, verifyMod;
let recordCol, inRecordCol, recordBlackCol;
const verror = require("verror");
const lzw = require("../common/lzw")
const zlib = require("zlib");
const util = require("../common/util").default;
const redisKey = require("../db/redisKey");
const config = require("../config")
const got = require("got");
const { isNumber } = require("../common/util").default;
const { ADD_IP_SCORE } = require("../common/constant");
const db = require("../db/db");
const activityMod = require("./activityMod");


const RecordState = {
    SAME: 0, //和线上一样
    DOWNLOAD: 1, //需要下载
    UPLOAD: 2, //需要上传
    CD: 3, //上传时间冷却中
    RECHECK: 4, //前端上传存档重新检测
    NONE: 5, //账号正在注销流程中,无法读取存档
}

const ORG_KEY_PREFIX = "__@"
const NEED_CACHE_COUNT = 10

const NAMESPACE_LEN = '__data_len'
const NAMESPACE_VER = '__data_ver'
const NAMESPACE_DATE = '__data_date'
const DATA_CLEAR = '__@clearAll__'
const DATA_VERSION = '__@version__'
const KEY_COUNT = 5

let recordMod = {

    redis: null,
    recordCache: null,
    recordDiff: null,
    recordSnapshootCol: null,
    loginGameVersionCol: null,

    init() {
        let db = require("../db/db")
        recordCol = db.createGameRecordModel()
        recordDiff = db.createGameRecordDiffModel()
        inRecordCol = db.createInActiveGameRecordModel()
        this.recordCache = db.createGameRecordCacheModel()
        this.redis = db.redis
        userMod = require("./userMod");
        idMod = require("./idMod");
        loginMod = require("./loginMod")
        verfiyRecordMod = require("./verfiyRecordMod").verfiyRecordMod
        this.recordSnapshootCol = db.createRecordSnapshootModel()
        rankMod = require("./rankMod");
        this.userHeartCol = db.createStatUserHeartModel()
        this.loginInfoCol = db.createLoginInfoModel()
        this.userCancellationCol = db.createUserCancellationModel();
        this.idMapCol = db.createIdMapModel();
        accountMod = require("./accountMod");
        verifyMod = require('./verifyMod');
        recordBlackCol = db.createGameRecordBlackListTempModel();
        this.specialActivityCol = db.createSpecialActivityModel()
    },

    getStarSum(record) {
        return this.getFromRecord("global.heart", record) || 0;
    },

    getPlayTime(record) {
        return this.getFromRecord("user.playTime", record) || 0;
    },

    getLoginDay(record) {
        let day = this.getFromRecord("loginDayCounts", record, false)
        if (day) return parseInt(day) || 1
        return 1
    },

    getWudongSkin(record) {
        return this.getFromRecord("main.useWudongSkinId", record) || 1;
    },

    /**
     *
     * @param {string} keys 如"global.heart"
     * @param {object} record 解析好的存档，和前端本地一致
     */
    getFromRecord(keys, record) {
        keys = keys.split(".");
        for (let dataKey in record) {
            if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
            if (!this.isGameNameSpace(dataKey)) continue
            let data = record[dataKey];//数据块
            let curValue = data;
            let found = false;
            for (let i = 0; i < keys.length; i++) {
                let key = keys[i];
                if (curValue[key] !== undefined) {
                    found = true;
                    if (i == 0 && typeof curValue[key] == 'string') {
                        try {
                            curValue = JSON.parse(curValue[key]);
                        } catch (error) {
                            curValue = curValue[key]
                        }
                    } else {
                        curValue = curValue[key];
                    }
                } else {
                    found = false;
                    break;
                }
            }
            if (found) {
                return curValue;
            }
        }
    },

    setToRecord(key, value, record, stringfy = false) {
        for (let dataKey in record) {
            if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
            if (!this.isGameNameSpace(dataKey)) continue
            let data = record[dataKey];//数据块
            if (data[key] !== undefined) {
                if (stringfy) {
                    value = JSON.stringify(value)
                }
                record[dataKey][key] = value;
                return true
            }
        }
        return false
    },

    checkRecord(newInfo, oldInfo) {
        let updateTime = oldInfo.updateTime;
        if (!updateTime) {
            updateTime = oldInfo.signupTime;
        }

        let newStarSum = newInfo.starSum;
        let newPlayTime = newInfo.playTime;

        let oldStarSum = oldInfo.starSum;
        let oldPlayTime = oldInfo.playTime;

        let nowTime = new Date().getTime();
        let passTime = (nowTime - updateTime);

        let state;

        if (oldInfo.forceDownload || oldStarSum > newStarSum) {
            //需要下载存档
            state = RecordState.DOWNLOAD;
        } else if (oldStarSum < newStarSum) {
            state = RecordState.UPLOAD;
        } else {
            if (newPlayTime > oldPlayTime + 10) {
                if (passTime < 1 * util.Time.Minute) {
                    //最多每1分钟同步一次，暂时不同步
                    state = RecordState.CD;
                } else {
                    //需要上传
                    state = RecordState.UPLOAD;
                }
            } else if (newPlayTime < oldPlayTime) {
                //需要下载存档
                state = RecordState.DOWNLOAD;
            } else {
                //客户端存档一致，不需要同步
                state = RecordState.SAME;
            }
        }

        return {
            state: state,
            starSum: oldStarSum,
            playTime: oldPlayTime,
            updateTime: updateTime,
        }
    },

    async checkRecordByUid(uid, newInfo, record, isBlack = false) {
        let userDoc = await userMod.findOne(uid);
        if (!userDoc) {
            throw verror("user not found");
        }
        if (!record) {
            let isGm = await verifyMod.isGM(uid);
            if (!isGm) {
                record = isBlack ? await this.getRecordBlack(uid) : await this.getRecord(uid);
            } else {
                record = await this.getRecord(uid);
            }
        }
        if (!record) {
            return {
                state: RecordState.UPLOAD,
                starSum: 0,
                updateTime: userDoc.updateTime,
            };
        }
        return this.checkRecord(newInfo, {
            forceDownload: userDoc.forceDownload,
            updateTime: userDoc.updateTime,
            signupTime: userDoc.signupTime,
            starSum: this.getStarSum(record),
            playTime: this.getPlayTime(record),
        })
    },

    tempCheck(uid, oldRecord, newRecord) {
        try {
            if (!oldRecord || !newRecord) return
            let getMain = (r) => {
                for (let key in r) {
                    if (key == "hotel_main") {
                        return r[key]
                    }
                }
            }
            let oldMain = getMain(oldRecord)
            let oldUnlockMainSkins = this.getFromRecord("main.unlockMainSkins", oldRecord) || []
            let oldUnlockWudongSkins = this.getFromRecord("main.unlockWudongSkins", oldRecord) || []
            let oldChanges = this.getFromRecord("main.mainAttr.changes", oldRecord) || []

            let newUnlockMainSkins = this.getFromRecord("main.unlockMainSkins", newRecord) || []
            let newUnlockWudongSkins = this.getFromRecord("main.unlockWudongSkins", newRecord) || []
            let newChanges = this.getFromRecord("main.mainAttr.changes", newRecord) || []
            let newMain = getMain(newRecord)
            let flag = 0
            if (newUnlockMainSkins.length < oldUnlockMainSkins.length) {
                logger.error("mainSkin reduce", uid, newUnlockMainSkins, oldUnlockMainSkins)
                flag = 1
            }
            if (newUnlockWudongSkins.length < oldUnlockWudongSkins.length) {
                logger.error("wudongSkin reduce", uid, newUnlockWudongSkins, oldUnlockWudongSkins)
                flag = 1
            }
            if (newChanges.length < oldChanges.length) {
                logger.error("changes reduce", uid, newChanges, oldChanges)
                flag = 1
            }
            if (flag) {
                logger.error("main ", uid, newMain, oldMain)
            }

            let gameLv1 = this.getFromRecord("newyear.gameLv", oldRecord) || 0
            let gameLv2 = this.getFromRecord("newyear.gameLv", newRecord) || 0
            if (gameLv2 < gameLv1) {
                logger.error("gameLv_v3 ", uid, newRecord["hotel_newyear"], oldRecord["hotel_newyear"])
            }

            let kefangKey = "kefang_"
            let list = [201, 202, 301, 302, 401, 402]
            for (let id of list) {
                let key = kefangKey + id
                let unlockFurns1 = this.getFromRecord(`${key}.unlockFurns`, oldRecord) || []
                let unlockFurns2 = this.getFromRecord(`${key}.unlockFurns`, newRecord) || []
                if (unlockFurns1.length > unlockFurns2.length) {
                    logger.error(key + "_v3", uid, newRecord["hotel_" + key], oldRecord["hotel_" + key])
                }
            }
            list = ["dorm", "dining", "cinema", "shower"]
            for (let key of list) {
                let builds1 = this.getFromRecord(`${key}.builds`, oldRecord) || []
                let builds2 = this.getFromRecord(`${key}.builds`, newRecord) || []
                if (builds1.length > builds2.length) {
                    logger.error(key + "_v3", uid, newRecord["hotel_" + key], oldRecord["hotel_" + key])
                }
            }

        } catch (error) {
            logger.error(error)
        }
    },


    async uploadRecord(uid, record, gameVer, ip, deviceId, checkType) {
        record = this.parseRecord(record);
        record = this.fixRecord(record, uid) //根据业务调整

        let [oldRecord, level] = await Promise.all([
            this.getRecord(uid),
            this.verfityRecord(uid, record, ip, deviceId, gameVer),
        ]), isBlack = !1;
        // 仍然可以上传存档 只是存档是另一份了
        if (level >= 3) {
            logger.warn("uploadRecord block", uid, level), isBlack = !0;
            oldRecord = await this.getRecordBlack(uid);
            //return RecordState.DOWNLOAD
        }

        oldRecord = oldRecord || {}

        let checkRecordPass
        if (!checkType) {
            checkRecordPass = this.checkRecord2(record, oldRecord, uid)
        } else if (checkType == 1 || checkType == 2) {
            checkRecordPass = this.checkRecordByVerSum(record, oldRecord, uid)
        }

        let starSum = this.getStarSum(record);
        let playTime = this.getPlayTime(record);

        if (!checkRecordPass) {
            return RecordState.DOWNLOAD
        }

        this.tempCheck(uid, oldRecord, record)

        let info = await this.checkRecordByUid(uid, { starSum, playTime }, oldRecord, isBlack);
        if (info.state == RecordState.DOWNLOAD) {
            logger.warn("uploadRecord check fail", uid, `heart [${starSum} ${info.starSum}]`, `playTime [${playTime} ${info.playTime}]`, info.state);
            return info.state
        }

        // if (checkType != 2 && util.cmpVersion(gameVer, "2.9.5") >= 0) {
        //     let diff = await userMod.isDeviceDiff(uid, deviceId)
        //     if (diff) {
        //         logger.warn("uploadRecord device diff", uid, deviceId)
        //         return RecordState.DOWNLOAD
        //     }
        // }

        let checkInfo = this.getCheckInfoFromRecord(record)

        let recordBuff = await this.zipRecord(record);
        let recordUpdateInfo = {
            uid, recordBuff, gameVer,
            checkInfo: JSON.stringify(checkInfo)
        }
        isBlack ? await recordBlackCol.updateOne({ uid }, recordUpdateInfo, { upsert: true }) : await this.updateOne(uid, recordUpdateInfo, true);

        this.needCache(uid).then((need) => {
            if (need) {
                this.updateRecordCache(recordUpdateInfo)
            }
        })

        let nowTime = new Date().getTime();
        let updatestrUser = { updateTime: nowTime, playTime: playTime };
        // 非黑才更新排行榜
        !isBlack && await userMod.updateOne(uid, updatestrUser, true);
        !isBlack && !level && rankMod.updateScoreByUid(uid, starSum);

        if (checkType == 1 && config.type != "global") { //游玩状态下的上传
            userMod.checkGuessAdult(uid, oldRecord, record)
        }

        if (checkType == 2) { //切换设备
            await this.switchDevice(uid)
        }

        logger.info("uploadRecord success", uid, `heart [${starSum} ${info.starSum}]`, `playTime [${playTime} ${info.playTime}]`, info.state, `isBlack:${isBlack}`);
        //await loginMod.addIpScore(uid, ip, ADD_IP_SCORE.UPLOAD_RECORD);
        return RecordState.UPLOAD;
    },

    async switchDevice(uid) {
        logger.info("switchDevice", uid)
        await this.loginInfoCol.updateOne({ uid }, { deviceId: null })
    },

    // 获取差异存档
    async getDiffRecordList(uid, antistop) {
        return await recordDiff.find({ uid }).sort({ timestamp: -1 })
    },

    // 上传差异存档
    async uploadDiffRecord(uid, diff, timestamp) {
        await recordDiff.updateOne({ uid, timestamp }, { diff }, { upsert: true })

        let count = await recordDiff.countDocuments({ uid })
        if (count > 100) {
            await recordDiff.deleteOne({ uid })
        }
    },

    checkRecordWithCheckInfo(checkInfo, oldCheckInfo, uid, gameVer) {
        let dataMap = checkInfo.dataMap
        let oldDataMap = oldCheckInfo.dataMap || {}
        // hotel_homeFurnMall2 hotel_debug_v1_homeFurnMall2 新的
        // hotel_homeFurnMall hotel_debug_v1_homeFurnMall 旧的

        if (util.cmpVersion(gameVer, "10.5.2") >= 0) {
            const key1 = config.debug ? "hotel_debug_v1_homeFurnMall2" : "hotel_homeFurnMall2"
            const key2 = config.debug ? "hotel_debug_v1_homeFurnMall" : "hotel_homeFurnMall"
            const oldHasNew = oldDataMap[key1]
            const oldHasOld = oldDataMap[key2]
            if (oldHasNew && !oldHasOld) {

            }
            else {
                const newhasNew = dataMap[key1]
                const newhasOld = dataMap[key2]
                if (newhasNew && !newhasOld) {
                    return RecordState.DOWNLOAD
                }
            }
        }


        for (let dataKey in oldDataMap) {
            let newData = dataMap[dataKey]
            let oldData = oldDataMap[dataKey]
            if (dataKey == "hotel_net") continue
            if (!this.isGameNameSpace(dataKey)) continue
            if (this.isEmptyData(newData) && !this.isEmptyData(oldData)) { //如果本地是空的，线上非空，回档
                logger.warn("checkRecordWithCheckInfo back empty", uid, dataKey, newData, oldData)
                return RecordState.DOWNLOAD
            }
            if (this.isEmptyData(oldData)) { //线上是空的，无论本地的数据如何都没影响
                continue
            }
            if (!this.checkByDate(newData, oldData)) {
                logger.warn("checkRecordWithCheckInfo back date", uid, dataKey, newData, oldData)
                return RecordState.DOWNLOAD
            }

            if (!this.checkByVersion(newData, oldData)) {
                logger.warn("checkRecordWithCheckInfo back version", uid, dataKey, newData, oldData)
                return RecordState.DOWNLOAD
            }
        }
        if (checkInfo.heart < oldCheckInfo.heart) {
            return RecordState.DOWNLOAD
        } else if (checkInfo.heart == oldCheckInfo.heart) {
            if (checkInfo.playTime < oldCheckInfo.playTime - 10) return RecordState.DOWNLOAD
            return RecordState.UPLOAD
        } else {
            return RecordState.UPLOAD
        }
    },

    async checkLogin(uid, checkInfo, gameVer) {
        let cancelDoc = await this.userCancellationCol.findOne({ uid });
        if (cancelDoc && cancelDoc.timestamp && cancelDoc.timestamp > 0) {
            if (cancelDoc.timestamp < Date.now()) {
                await accountMod.logic(cancelDoc);
            }
            return { recordState: RecordState.NONE, timeLeft: cancelDoc.timestamp - Date.now() };
        }
        let userPromise = userMod.findOne(uid, { forceDownload: 1 })
        let recordDoc = await this.findOne(uid)
        if (!recordDoc || !recordDoc.checkInfo) {
            return { recordState: RecordState.RECHECK }
        }

        try {
            let record = await this.unzipRecord(recordDoc.recordBuff, true)
            if (this.isLowVerRecord(record, uid, "checkLogin")) {
                return { recordState: RecordState.UPLOAD }
            }

            let oldCheckInfo = JSON.parse(recordDoc.checkInfo)
            let recordState = this.checkRecordWithCheckInfo(checkInfo, oldCheckInfo, uid, gameVer)
            let userDoc = await userPromise
            if (userDoc.forceDownload) {
                recordState = RecordState.DOWNLOAD
            }
            return { recordState, oldCheckInfo }
        } catch (error) {
            logger.error("checkLogin error", error, uid, checkInfo, recordDoc.checkInfo)
            return { recordState: RecordState.RECHECK }
        }
    },

    /*
    同一个设备
    丢失整个key->createTime变化
    key版本回退->version变化
    不同设备
    1.登录时，整体回档
    2.在线时弹toast
    */
    checkRecord2(newRecord, oldRecord, uid) {
        // if (!this.checkByDeviceID(newRecord, oldRecord)) {
        //     return false
        // }
        if (this.isLowVerRecord(oldRecord, uid, "checkRecord2")) {
            return true
        }

        for (let dataKey in oldRecord) {
            if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
            let newData = newRecord[dataKey]
            let oldData = oldRecord[dataKey]
            if (dataKey == "hotel_net") continue
            if (!this.isGameNameSpace(dataKey)) continue
            if (this.isEmptyData(newData) && !this.isEmptyData(oldData)) { //如果本地是空的，线上非空，回档
                logger.warn("checkRecord2_v2 back empty", uid, newData, oldData)
                return false
            }
            if (this.isEmptyData(oldData)) { //线上是空的，无论本地的数据如何都没影响
                continue
            }

            if (!this.checkByDate(newData, oldData)) {
                logger.warn("checkRecord2_v2 back date", uid, newData, oldData)
                return false
            }

            if (!this.checkByVersion(newData, oldData)) {
                logger.warn("checkRecord2_v2 back version", uid, newData, oldData)
                return false
            }

            // if (!this.checkByKeys(newData, oldData)) {
            //     logger.warn("checkRecord2_v2 back keys", uid, newData, oldData)
            //     return false
            // }
        }
        return true
    },

    checkByDate(newData, oldData) {
        let newDate = newData.__data_date
        let oldDate = oldData.__data_date || 0
        if (oldDate == 0) { //线上数据没有date字段
            return true
        }
        return newDate === oldDate
    },

    checkByVersion(newData, oldData) {
        let newVersion = newData.__data_ver
        let oldVersion = oldData.__data_ver || -1
        if (oldVersion == -1) { //线上数据没有ver字段
            return true
        } else {
            let diff = newVersion - oldVersion
            return diff >= 0
        }
    },

    checkByKeys(newData, oldData) {//如果newData包含了oldData的所有key，那么可以认为newData是更新的
        for (let key in oldData) {
            if (newData[key] == undefined) {
                return false
            }
        }
        return true
    },

    checkByDeviceID(newRecord, oldRecord) {
        let dataKey = "__@device_id"
        let newDeviceId = newRecord[dataKey]
        let oldDeviceId = oldRecord[dataKey]
        if (oldDeviceId == null) {
            return true
        }
        return newDeviceId === oldDeviceId
    },

    isEmptyData(data) {
        if (!data) return true
        return false
        // if (!data.__data_len && !data.__data_ver) return true
        // for (let key in data) {
        //     if (!key.startsWith("__")) {
        //         return false
        //     }
        // }
        // return true
    },

    checkRecordByVerSum(newRecord, oldRecord, uid) {
        if (this.isLowVerRecord(oldRecord, uid, "checkRecordByVerSum")) {
            return true
        }

        let newVerSum = this.getRecordVerSum(newRecord)
        let oldVerSum = this.getRecordVerSum(oldRecord)
        logger.debug("checkRecordByVerSum", newVerSum, oldVerSum)
        if (newVerSum < oldVerSum) {
            logger.warn("checkRecordByVerSum fail", uid, newVerSum, oldVerSum)
            return false
        }
        return this.checkRecordByKey(newRecord, oldRecord, uid)
    },


    //对一些key进行单独校验，配合checkRecordByVerSum
    checkRecordByKey(newRecord, oldRecord, uid) {
        if (this.isLowVerRecord(oldRecord, uid, "checkRecord2")) {
            return true
        }

        let dataKeys = ["dorm", "beauty", "cinema", "dining", "shower", "kefang_201", "kefang_202", "kefang_301", "kefang_302", "kefang_401", "kefang_402"]

        for (let dataKey in oldRecord) {
            if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
            let newData = newRecord[dataKey]
            let oldData = oldRecord[dataKey]
            if (!this.isGameNameSpace(dataKey)) continue
            if (!dataKeys.find(key => dataKey.endsWith(key))) continue
            if (this.isEmptyData(newData) && !this.isEmptyData(oldData)) { //如果本地是空的，线上非空，回档
                logger.warn("checkRecordByKey back empty", uid, newData, oldData)
                return false
            }
            if (this.isEmptyData(oldData)) { //线上是空的，无论本地的数据如何都没影响
                continue
            }

            let newVersion = newData.__data_ver
            let oldVersion = oldData.__data_ver || -1
            if (oldVersion == -1) { //线上数据没有ver字段
                continue
            } else {
                if (newVersion / oldVersion < 0.8) { //两个相差太多也不让上传
                    logger.warn("checkRecordByKey back version", uid, newData, oldData)
                    return false
                }
                logger.debug("checkRecordByKey", dataKey, newVersion, oldVersion)
            }
        }
        return true
    },

    getRecordVerSum(record) {
        let sum = 0
        for (let dataKey in record) {
            if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
            if (!this.isGameNameSpace(dataKey)) continue
            let data = record[dataKey]
            sum += data[NAMESPACE_VER] || 0
        }
        return sum
    },

    fixRecord(record, uid) {
        let delteList = []
        if (config.debug) {
            for (let dataKey in record) {
                if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
                if (!this.isGameNameSpace(dataKey)) {
                    delteList.push(dataKey)
                }
            }
            if (delteList.length > 0) {
                logger.warn("debug fixRecord", uid)
            }
        } else { //正式版
            for (let dataKey in record) {
                if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
                let gameNameSpace = config.gameNameSpace
                if (gameNameSpace && dataKey.startsWith(`${gameNameSpace}_debug`)) {
                    delteList.push(dataKey)
                }
            }
            if (delteList.length > 0) {
                logger.warn("offical fixRecord", uid)
            }
        }
        for (let key of delteList) {
            delete record[key]
        }

        return record
    },

    fixRecordOnDownload_2(record, uid) {
        let prefix = "hotel_"
        let isOldKey = (dataKey) => {
            if (dataKey.startsWith("__")) return false
            if (dataKey.startsWith(prefix)) {
                let [_, suffix] = dataKey.split(prefix)
                return isNumber(suffix)
            }
            return false
        }
        let fix = false
        for (let dataKey in record) {
            if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
            if (isOldKey(dataKey)) {
                let data = record[dataKey]
                for (let key in data) {
                    let subData = data[key]
                    if (typeof subData == "string") {
                        try {
                            let val = JSON.parse(subData)
                            if (val && typeof val == "object") {
                                record[dataKey][key] = val
                                fix = true
                            }
                        } catch (error) {
                        }
                    }
                }
            }
        }
        if (fix) {
            logger.warn("fixRecordOnDownload", uid)
        }
    },

    fixRecordOnDownload(record, uid) {
        let fix = 0
        let prefix = "hotel_"
        let isOldKey = (dataKey) => {
            if (dataKey.startsWith("__")) return false
            if (dataKey.startsWith(prefix)) {
                let [_, suffix] = dataKey.split(prefix)
                return isNumber(suffix)
            }
            return false
        }

        let isNewKey = (dataKey, data) => {
            if (dataKey.startsWith(prefix)) {
                let [_, suffix] = dataKey.split(prefix)
                if (isNaN(Number(suffix))) {
                    if (typeof data == "object") return true
                }
            }
            return false
        }
        let oldGlobal = null
        for (let dataKey in record) {
            if (isOldKey(dataKey)) {
                let data = record[dataKey]
                if (data.global) {
                    if (typeof data.global == 'string') {
                        oldGlobal = JSON.parse(data.global)
                    } else {
                        oldGlobal = data.global
                    }
                }
            }
        }
        let newGlobal = null
        if (record[prefix + "global"]) {
            newGlobal = record[prefix + "global"].global
        }
        if (oldGlobal && newGlobal) {
            if (oldGlobal.heart <= newGlobal.heart) { //删除自动桶里的key
                for (let dataKey in record) {
                    if (isOldKey(dataKey)) {
                        let data = record[dataKey]
                        for (let key in data) {
                            if (record[prefix + key]) {
                                delete data[key]
                                fix |= (1 << 1)
                            }
                        }
                        record[dataKey] = data
                    }
                }
            } else { //删除手动桶的key
                // for (let dataKey in record) {
                //     if (isNewKey(dataKey, record[dataKey])) {
                //         fix |= (1 << 2)
                //         delete record[dataKey]
                //     }
                // }
            }
        }

        for (let dataKey in record) {
            if (isOldKey(dataKey)) { //如果是自动桶
                let data = record[dataKey]
                let [_, suffix] = dataKey.split(prefix)
                for (let key in data) {
                    let subData = data[key]
                    let type = typeof subData
                    if (type == "string") {
                        try {
                            let val = JSON.parse(subData)
                            if (val && typeof val == "object") {
                                subData = data[key] = val
                                type = "object"
                                fix |= (1 << 3)
                            }
                        } catch (error) {
                        }
                    }

                    if (type == 'object') {
                        let info = {}
                        info[NAMESPACE_VER] = data[NAMESPACE_VER] || 0
                        info[NAMESPACE_DATE] = data[NAMESPACE_DATE] || new Date().getTime()
                        info[key] = subData
                        record[prefix + key] = info

                        delete record[dataKey][key]
                        fix |= (1 << 4)
                    }
                }
                let index = Number(suffix)
                if (index >= KEY_COUNT) {
                    fix |= (1 << 5)
                    for (let key in data) {
                        if (key.startsWith("__")) continue;
                        let k = util.randomInt(0, KEY_COUNT - 1)
                        record["hotel_" + k][key] = data[key]
                    }
                    delete record[dataKey]
                } else {
                    record[dataKey][NAMESPACE_LEN] = 0
                }
            }
        }
        for (let dataKey in record) {
            let obj = record[dataKey]
            if (isNewKey(dataKey, obj)) {
                let [_, key] = dataKey.split("hotel_")
                let data = obj[key]
                if (data.data && data.ver != undefined) {
                    record[dataKey][key] = data.data
                }
            }
        }
        if (!record[DATA_VERSION] || Number(record[DATA_VERSION]) < 2) {
            record[DATA_VERSION] = 2
        }
        if (fix > 0) {
            logger.warn("fixRecordOnDownload_v3", uid, parseInt(fix).toString(2))
        }
    },

    async download(uid, clientRecord, gameVer) {
        let blackDoc = await verifyMod.getBlackById(uid), isBlack = !1;
        if (blackDoc && blackDoc.level && blackDoc.level >= 3) {
            logger.warn(`user is in the blacklist, download record -> black. ${uid},${blackDoc.level}`);
            isBlack = !0;
        }
        let record
        // 非手动封的 不可以再利用存档下载解封
        if (isBlack && blackDoc.types.includes(10)) {
            return { stop: true }
        }
        // 否则去下黑名单数据
        if (isBlack) {
            record = await recordMod.getRecordBlack(uid)
            // 可能没有，加一个容错保证
            if (!record) record = await recordMod.getRecord(uid);
        } else
            record = await recordMod.getRecord(uid);
        if (!record) {
            return
        }

        let userDoc = await userMod.findOne(uid);
        if (clientRecord) {
            clientRecord = this.parseRecord(clientRecord);
            if (!userDoc) {
                throw verror("user not found");
            }
            let starSum = this.getStarSum(clientRecord);
            let playTime = this.getPlayTime(clientRecord)

            let info = await recordMod.checkRecord({
                starSum,
                playTime,
            }, {
                forceDownload: userDoc.forceDownload,
                updateTime: userDoc.updateTime,
                signupTime: userDoc.signupTime,
                starSum: this.getStarSum(record),
                playTime: this.getPlayTime(record),
            })
            // logger.debug("downloadRecord check result", `heart [${starSum} ${info.starSum}]`, `playTime [${playTime} ${info.playTime}]`, info.state)
            if (info.state == RecordState.UPLOAD) {
                logger.warn("downloadRecord check fail", `heart [${starSum} ${info.starSum}]`, `playTime [${playTime} ${info.playTime}]`, info.state)
                return
            }
        }

        let globalStr, userStr
        try {
            globalStr = recordMod.getFromRecord("global", record, false);
            userStr = recordMod.getFromRecord("user", record, false);
        } catch (error) {
            logger.error(error)
        }

        if (util.cmpVersion(gameVer, "1.8.5") >= 0) {
            this.fixRecordOnDownload_2(record, uid)
        }
        //临时fix
        let prefix = "hotel_"
        if (record[prefix + "global"] && Number(record["__@version__"]) < 2) {
            logger.warn("fix record", uid, record["__@version__"])
            record["__@version__"] = "2"
        }

        let version = 0
        if (gameVer && util.cmpVersion(gameVer, "1.5.1") >= 0) {
            record = JSON.stringify(record)
            version = 1
        } else {
            record = lzw.oldCompress(JSON.stringify(record));
            version = 0
        }

        if (!globalStr || !userStr) {
            logger.info("downloadRecord success", uid, record, version);
        } else {
            logger.info("downloadRecord success", uid, globalStr, userStr, version);
        }

        if (userDoc.forceDownload) {
            await userMod.updateOne(uid, { forceDownload: false })
        }
        isBlack && (await verfiyRecordMod.deblock(uid, false));
        return { version, record };
    },

    async downloadByUid(uid, fromGameVer, fromUid) {
        let recordDoc = await this.getRecordCache(uid) || await this.findOne(uid)
        if (!recordDoc) return

        logger.info("downloadByUid", fromUid, fromGameVer, uid, recordDoc.gameVer)
        let versionLimit = await loginMod.versionLimit(fromGameVer, recordDoc.gameVer)
        if (versionLimit) {
            return
        }

        let record = await this.unzipRecord(recordDoc.recordBuff, false);
        return record
    },

    async needCache(uid) {
        let key = redisKey.recordCacheFlag(uid)
        let count = await this.redis.get(key) || 0
        return parseInt(count) > NEED_CACHE_COUNT
    },

    async getRecordCache(uid) {
        let cacheDoc
        let key = redisKey.recordCacheFlag(uid)
        let count = await this.redis.incrby(key, 1)
        if (count > NEED_CACHE_COUNT) {
            cacheDoc = await this.recordCache.findOne({ uid })
            if (!cacheDoc) {
                let recordDoc = await this.findOne(uid)
                if (!recordDoc) {
                    return
                }
                cacheDoc = await this.recordCache.findOneAndUpdate({ uid }, recordDoc, { upsert: true, new: true })
            }
        }
        this.redis.pexpire(key, 1 * util.Time.Day)
        return cacheDoc
    },

    async updateRecordCache(recordDoc) {
        await this.recordCache.updateOne({ uid: recordDoc.uid }, recordDoc, { upsert: true })
    },

    async clearRecordCache(_id) {
        let where = {}
        if (_id) {
            where = { _id: { $lt: _id } }
        }
        let docs = await this.recordCache.find(where, { uid: 1 }).sort("-_id").limit(1000)
        for (let doc of docs) {
            let uid = doc.uid
            let need = await this.needCache(uid)
            if (!need) {
                await this.recordCache.deleteOne({ uid })
            }
        }
        if (docs.length > 0) {
            return this.clearRecordCache(docs[docs.length - 1]._id)
        }
    },

    async findOne(uid, project) {
        let where = { uid: uid };
        let doc = await recordCol.findOne(where, project);
        if (!doc) {
            doc = await inRecordCol.findOne(where, project);
            if (!doc) {
                // throw verror("record not found: ", uid)
            }
        }
        return doc;
    },

    /**
     *
     * @param {*} uid
     * @param {*} parse
     * @description 默认返回解码好的存档，parse=fale用于返回给前端本地
     */
    async getRecord(uid, parse = true) {
        if (!uid) return
        let recordDoc = await this.findOne(uid);
        if (recordDoc) {
            return this.unzipRecord(recordDoc.recordBuff, parse);
        }
    },
    async getRecordBlack(uid, parse = true) {
        if (!uid) return
        let recordDoc = await recordBlackCol.findOne({ uid });
        if (recordDoc) {
            return this.unzipRecord(recordDoc.recordBuff, parse);
        }
    },
    getCheckInfoFromRecord(record) {
        let checkInfo = {}
        let dataMap = {}
        for (let dataKey in record) {
            let data = record[dataKey]
            if (dataKey.startsWith(ORG_KEY_PREFIX) && typeof data != 'object') continue
            if (!this.isGameNameSpace(dataKey)) continue
            dataMap[dataKey] = {}
            dataMap[dataKey][NAMESPACE_VER] = data[NAMESPACE_VER]
            dataMap[dataKey][NAMESPACE_DATE] = data[NAMESPACE_DATE]
        }
        checkInfo.heart = this.getStarSum(record)
        checkInfo.playTime = this.getPlayTime(record)
        checkInfo.dataMap = dataMap
        return checkInfo
    },

    /**
     *
     * @param {*} uid
     * @param {*} record
     * @description record为解码好的存档
     */
    async setRecord(uid, record) {
        let checkInfo = this.getCheckInfoFromRecord(record)
        let recordBuff = await this.zipRecord(record);
        await this.updateOne(uid, { recordBuff: recordBuff, checkInfo: JSON.stringify(checkInfo) }, true);
    },

    async changeRecord(uid, onChange, forceDownload) {
        let recordMap = await this.getRecord(uid);
        if (!recordMap) {
            throw verror({ name: "record not found", info: uid });
        }
        await onChange(recordMap);
        await this.setRecord(uid, recordMap);

        if (forceDownload) {
            await userMod.updateOne({ uid: uid }, { forceDownload: true });
        }
        return recordMap;
    },

    async zipRecord(record) {
        record = this.stringifyRecord(record);
        let buf = Buffer.from(record);
        return await new Promise((resolve, reject) => {
            zlib.gzip(buf, (error, zipBuff) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(zipBuff);
                }
            });
        }).catch((error) => {
            throw verror({ name: "zip error", info: error });
        })
    },

    async unzipRecord(record, parse) {
        let resUnzip = await new Promise((resolve, reject) => {
            zlib.unzip(record, (error, unzipBuff) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(unzipBuff);
                }
            });
        }).catch((error) => {
            throw verror({ name: "unzip error", info: error });
        })
        let result = resUnzip.toString()
        if (parse) {
            result = this.parseRecord(result);
        }
        return result;
    },

    stringifyRecord(record) {
        return JSON.stringify(record);
    },

    parseRecord(record) {
        record = JSON.parse(record);
        return record;
    },

    async updateOne(uid, info, upsert) {
        let where = { uid: uid };
        let doc = await recordCol.findOneAndUpdate(where, info, { new: true, upsert: upsert });
        if (!doc) {
            doc = await inRecordCol.findOneAndUpdate(where, info, { new: true });
        }
        return doc;
    },

    async copy(sourceId, targetId) {
        let record = await this.getRecord(sourceId);
        if (!record) {
            return false
        }
        await this.setRecordByCopy(targetId, record)
        return true
    },

    async copyBySnapshoot(snapshootId, targetId) {
        let snapshootDoc = await this.recordSnapshootCol.findOne({ uid: snapshootId });
        if (!snapshootDoc) {
            return false
        }
        let record = await recordMod.unzipRecord(snapshootDoc.recordBuff, true)
        await this.setRecordByCopy(targetId, record)
        return true
    },

    async setRecordByCopy(uid, record) {
        let userKey = 'user';
        let user = this.getFromRecord(userKey, record);
        user.uid = uid;
        this.setToRecord(userKey, user, record);
        await this.setRecord(uid, record)
    },

    async debugCopy(targetId, needRecord = true) {
        let info = config.releaseNode
        if (!info) return//正式服直接返回null
        let { ip, port } = info;
        const url = `http://${ip}:${port}/debug_copy?uid=${targetId}`
        console.log(url)
        let { status, data } = await got(url, {
            timeout: 5 * util.Time.Second,
        }).json();
        if (data) {
            let userDoc, recordDoc, idDocs, activityDoc
            data = JSON.parse(data)
            userDoc = data.userDoc
            recordDoc = data.recordDoc
            activityDoc = data.activityDoc
            idDocs = data.idDocs
            // 分配新的uid
            let uid = idMod.genUID();
            let promiseList = []
            if (userDoc) {
                delete userDoc._id
                userDoc.uid = uid;
                promiseList.push(userMod.updateOne(userDoc.uid, userDoc, true))
            }
            if (recordDoc) {
                delete recordDoc._id
                recordDoc.uid = uid;
                promiseList.push(recordMod.updateOne(recordDoc.uid, recordDoc, true))
            }
            if (activityDoc) {
                delete activityDoc._id
                activityDoc.uid = uid;
                if (activityDoc.limit) {
                    activityDoc.limit.forEach(l => delete l._id)
                }
                if (activityDoc.record) {
                    activityDoc.record.forEach(l => delete l._id)
                }
                promiseList.push(this.specialActivityCol.updateOne({ uid }, activityDoc, { upsert: true }))
            }
            if (idDocs) {
                for (let doc of idDocs) {
                    if (doc) {
                        doc.uid = uid;
                        promiseList.push(idMod.reBindID(doc.tpid, doc.uid, true))
                    }
                }
            }
            await Promise.all(promiseList)
            // if (recordDoc) {
            //     let record = await recordMod.getRecord(recordDoc.uid)
            //     record = this.fixGameNameSpace(record)
            //     await recordMod.setRecord(recordDoc.uid, record)
            // }
            if (needRecord && userDoc) {
                return recordMod.getRecord(userDoc.uid, false)
            }
        }
    },

    async debugCopyByTpid(tpid, tpid2, needRecord = true) {
        let info = config.releaseNode
        if (!info) return//正式服直接返回null
        let { ip, port } = info;
        const url = `http://${ip}:${port}/debug_getUidByTpid?tpid=${tpid}&tpid2=${tpid2}`
        let { status, uid } = await got(url, {
            timeout: 5 * util.Time.Second,
        }).json();
        return { uid, record: await this.debugCopy(uid, needRecord) }
    },

    getRecordVersion(record) {
        return record[DATA_VERSION]
    },

    isGameNameSpace(key) {
        let gameNameSpace = config.gameNameSpace
        if (gameNameSpace) {
            return key.startsWith(gameNameSpace)
        }
        return true
    },

    async verfityRecord(uid, record, clientIp, deviceId, gameVer) {
        try {
            let computeServer = config.computeServer
            if (!computeServer) {
                return await verfiyRecordMod.check(uid, record, false, clientIp, deviceId, gameVer)
            }
            let { ip, port } = computeServer
            let url = `http://${ip}:${port}/compute/verfityRecord`
            let { level } = await got.post(url, {
                json: {
                    uid, record, ip: clientIp, deviceId, gameVer
                },
                timeout: 5 * util.Time.Second, //5s都算不完直接放过
            }).json()
            return level
        } catch (error) {
            logger.error('verfityRecord', uid, error)
            return 0
        }
    },

    fixGameNameSpace(record) {
        let officalNameSpace = "hotel"
        if (!record || config.gameNameSpace == officalNameSpace) return
        let newRecord = {}
        for (let key in record) {
            if (!this.isGameNameSpace(key) && key.startsWith(officalNameSpace)) {
                let [_, suffix] = key.split(officalNameSpace)
                let newKey = config.gameNameSpace + suffix
                newRecord[newKey] = record[key]
            } else {
                newRecord[key] = record[key]
            }
        }
        return newRecord
    },

    async gacha(uid, type, data, gameVer) {
        if (type == 1) {
            let [userDoc, record] = await Promise.all([userMod.findOne(uid), this.getRecord(uid)])
            if (!userDoc || !record) {
                return
            }
            if (userDoc.forceDownload) {
                return
            }
            if (util.cmpVersion(gameVer, '2.2.10') < 0) {
                for (let key in data) {
                    let info = data[key]
                    recordMod.setToRecord(key, info, record)
                }
            } else {
                for (let key in data) {
                    let info = data[key]
                    for (let dataKey in record) {
                        if (dataKey.startsWith(ORG_KEY_PREFIX)) continue
                        if (!this.isGameNameSpace(dataKey)) continue
                        let data = record[dataKey];//数据块
                        if (data[key] !== undefined) {
                            record[dataKey][key] = info.data;
                            record[dataKey][NAMESPACE_VER] = Math.max(record[dataKey][NAMESPACE_VER], info.ver)
                            break
                        }
                    }
                }
            }

            let level = await this.verfityRecord(uid, record)
            if (level == 0) {
                await this.setRecord(uid, record);
            }
        }
    },

    async getHeartByUid(uid, serverId) {
        let heartDoc = await this.userHeartCol.findOne({ uid })
        if (heartDoc) {
            return heartDoc.heart
        }

        let heart = -1

        if (serverId !== undefined) {
            let key = rankMod.getRankName(serverId)
            heart = await rankMod.getScore(key, uid) || -1
        }

        if (heart < 0) {
            let recordDoc = await recordMod.findOne(uid, { checkInfo: 1 })
            if (recordDoc) {
                if (recordDoc.checkInfo) {
                    try {
                        let checkInfo = JSON.parse(recordDoc.checkInfo)
                        heart = checkInfo.heart
                    } catch (error) {
                    }
                }

                if (heart < 0) {
                    let record = await recordMod.getRecord(uid)
                    heart = recordMod.getStarSum(record)
                }
            } else {
                heart = 0
            }
        }
        return heart
    },

    isLowVerRecord(recordDoc, uid, from) {
        let curVer = this.getRecordVersion(recordDoc)
        if (!curVer || curVer < 2) {
            logger.warn(`${from} low_record_ver`, uid, curVer)
            return true
        }
        return false
    }
}

module.exports = recordMod;



