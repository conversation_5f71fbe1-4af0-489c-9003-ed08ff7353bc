const db = require("../db/db");
const party_config = require("../client/json/homePartyBase.json");
const util = require("../common/util").default;
const recordMod = require("./recordMod");

const max_join_party_num = 0

let partyPlusMod = {

    init() {
        this.userCol = db.createUserModel()
        this.partyPlusCol = db.createPartyPlusModel()
    },
    async removeSelfFromJoin(uid, docId) {
        // return await this.userCol.findOneAndUpdate({uid}, {
        //     $pull: {
        //         partyPlusJoin: docId
        //     }
        // })
    },
    async testGetPartyDoc(uid) {
        const doc = await this.partyPlusCol.findOne({uid}, {partyId: 1, time: 1, joiner: 1})
        if (!doc) return null
        const config = party_config.find(e => e.id === doc.partyId)
        if (!config) return null
        const durTime = Number(config["dur_time"])
        if (util.Time.Minute * durTime + doc.time > Date.now()) {
            return doc
        }
        return null
    },
    async getPartyDoc(uid) {
        return await this.testGetPartyDoc(uid) == null
    },
    async getTodayCanJoinCnt(uid) {
        const userDoc = await this.userCol.findOne({uid}, {todayPartyPlusJoinCnt: 1})
        if (!userDoc) return 0
        let todayPartyPlusJoinCnt = userDoc.todayPartyPlusJoinCnt
        // 上一次更新时间
        let time = 0
        if (!todayPartyPlusJoinCnt) {
            todayPartyPlusJoinCnt = {
                cnt: 0,
                time: 0
            }
        }
        time = todayPartyPlusJoinCnt.time

        if (time < util.getSixAmTimestamp() || !time) {
            todayPartyPlusJoinCnt.cnt = max_join_party_num
            todayPartyPlusJoinCnt.time = Date.now()
            await this.userCol.updateOne({uid}, {todayPartyPlusJoinCnt})
            return max_join_party_num
        }
        return todayPartyPlusJoinCnt.cnt
    },
    async start(uid, partyId, dressUp) {
        // 容错处理
        const doc = await this.partyPlusCol.findOne({uid})
        if (doc && doc.joiner && doc.joiner.length) {
            for (const id of doc.joiner) {
                await this.removeSelfFromJoin(id, doc._id.toString())
            }
        }
        await this.partyPlusCol.updateOne({uid}, {
            partyId,
            dressUp,
            time: Date.now(),
            joiner: [],
        }, {upsert: true})
        return {status: 0}
    },

    async join(uid, targetId, dressUp) {
        const doc = await this.partyPlusCol.findOne({uid: targetId})
        if (!doc) return {status: -1} //目标没有开派对
        let config = party_config.find(e => e.id === doc.partyId)
        if (doc.joiner.length >= config["max_friends"]) return {status: -2} //参与者已满

        let cnt = await this.getTodayCanJoinCnt(uid)
        // 限制每天两次
        // if (cnt <= 0) return {status: -3} //当天已经参与过两次
        let exists = doc.joiner.find(e => e.uid === uid)
        if (exists) return {status: -4} // 已经参与其中了

        await this.userCol.updateOne({uid}, {$inc: {"todayPartyPlusJoinCnt.cnt": 1}})
        cnt += 1
        await this.partyPlusCol.updateOne({_id: doc._id.toString()}, {
            $push: {
                joiner: {
                    $each: [{
                        uid,
                        time: Date.now(),
                        dressUp,
                    }]
                }
            }
        })
        return {status: 0, partyId: doc.partyId, cnt}
    },
    async getPartyInfo(myUid) {
        const doc = await this.partyPlusCol.findOne({uid: myUid})
        if (!doc) return {status: -1} //本人没有开派对
        const vis = []
        for (let {uid, time, dressUp} of doc.joiner) {
            let [record, user] = await Promise.all([
                recordMod.getRecord(uid),
                this.userCol.findOne({uid})
            ])
            let skin = ""
            if (record) skin = await recordMod.getFromRecord("main.useWudongSkinId", record)
            if (!user) {
                user = {diyName: "", lang: ""}
            }
            let myName = user.diyName || user.nickName || ""
            vis.push({uid, time, dressUp, name: myName, skin})
        }
        return {
            status: 0,
            partyId: doc.partyId,
            time: doc.time,
            dressUp: doc.dressUp,
            joiner: vis,
        }
    }
}

module.exports = partyPlusMod
