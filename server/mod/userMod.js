let inUserCol, userCol, countInfoCol, serverInfoCol, serverLimitUserCol, idMapCol, recordCol, staUserCol, rankMod,
    redis, shareMod, recordMod, textModerationMod, loginMod, backupUserDataCol, verifyMod;
const response = require("../web/ResponseCode");
const SERVER_COUNT = 500;
const TOKEN_KEY = "q1yzMzlVSLtl3zm2"
const SESSION_KEY = "2mz3ltLSVlzMzy1q"

const DEBUG_IDCARD = {
    idNum: "110101199003073677",
    name: "苏大强",
}

let userMod = {

    spAwardCol: null,
    tokenCol: null,
    idCardCol: null,

    init() {
        let db = require("../db/db");
        redis = db.redis
        let logicUserCol = db.createUserModel();
        inUserCol = db.createInActiveUserModel();
        staUserCol = db.createStatUserModel();
        userCol = db.createDouleWriteModel(logicUserCol);
        countInfoCol = db.createCountInfoModel();
        serverInfoCol = db.createServerInfoModel();
        serverLimitUserCol = db.createServerLimitUserModel();
        idMapCol = db.createIdMapModel();
        recordCol = db.createGameRecordModel();
        backupUserDataCol = db.createBackupUserDataModel();
        rankMod = require("./rankMod");
        shareMod = require("./shareMod");
        verifyMod = require('./verifyMod');
        this.spAwardCol = db.createSpecialAwardModel()
        this.tokenCol = db.createUserTokenModel()
        recordMod = require("./recordMod");
        this.idCardCol = db.createUserIDCardModel()
        this.workDayCol = db.createWorkDayModel()
        this.guessAdultCol = db.createGuessAdultModel()
        textModerationMod = require("./textModerationMod")
        subscribeMod = require("./subscribeMod")
        statisticsMod = require("./statisticsMod")
        this.deviceTokenCol = db.createDeviceToken()
        this.deviceInfoCol = db.createDeviceInfoModel()
        loginMod = require("./loginMod")
        this.loginInfoCol = db.createLoginInfoModel()
        this.compensationRewardCol = db.createCompensationReward()
        this.compensationRewardHistoryCol = db.createCompensationRewardHistory()
        this.createCurrencyBalanceCol = db.createCurrencyBalanceModel();
        this.userHeartCol = db.createStatUserHeartModel()
        this.subscribeCol = db.createSubscribeModel();
        this.catchUsersCol = db.createCatchUserModel();
    },

    async newUser(info) {
        let serverId = await this.upadteSeverInfo();
        info.serverId = serverId;
        let countInfo = await countInfoCol.findOneAndUpdate({ name: "userIndex" }, { $inc: { count: 1 } }, {
            upsert: true,
            new: true
        });
        info.index_id = countInfo.count
        let user = await userCol.findOneAndUpdate({ uid: info.uid }, info, { upsert: true, new: true });
        return user;
    },

    async findOne(uidOrWhere, project) {
        if (uidOrWhere == null) throw verror({ name: 'uid is null' })
        let where = uidOrWhere;
        if (typeof uidOrWhere == 'string') {
            where = { uid: uidOrWhere };
        }
        let doc = await userCol.findOne(where, project);
        if (!doc) {
            doc = await inUserCol.findOne(where);
        }
        return doc;
    },

    async updateOne(uidOrWhere, info, upsert) {
        if (uidOrWhere == null) throw verror({ name: 'uid is null' })
        let where = uidOrWhere;
        if (typeof uidOrWhere == 'string') {
            where = { uid: uidOrWhere };
        }
        let doc = await userCol.findOneAndUpdate(where, info, { new: true, upsert: upsert });
        if (!doc) {
            doc = await inUserCol.findOneAndUpdate(where, info, { new: true });
            if (doc) {
                userCol.updateOne(where, doc, { upsert: true }); //转到活跃表
            } else {
                throw verror({ name: "user not found", info: { uidOrWhere } });
            }
        }

        await this.updateUserCacheInfo(doc)
        return doc;
    },

    async upadteSeverInfo() {
        let serverName = "serverCount";
        let countInfo = await countInfoCol.findOneAndUpdate({ name: serverName }, {}, { upsert: true, new: true });
        let [serverLimitDoc, serverInfo] = await util.promiseMap([
            serverLimitUserCol.findOne({}),
            serverInfoCol.findOneAndUpdate({ serverId: countInfo.count }, { $inc: { userCount: 1 } }, {
                upsert: true,
                new: true
            })
        ]);

        let serverLimitCount = SERVER_COUNT;
        if (serverLimitDoc && serverLimitDoc.limitCount) { //看后台有没有填人数，没有用默认的
            serverLimitCount = serverLimitDoc.limitCount;
        }

        if (serverInfo.userCount > serverLimitCount) { //开新服
            try {
                countInfo = await countInfoCol.findOneAndUpdate({ name: serverName }, { $inc: { count: 1 } }, {
                    upsert: true,
                    new: true
                });
            } catch (error) {
                logger.error(error)
            }
        }
        return countInfo.count;
    },

    async deleteUser(uid, reason = '删除用户备份') {
        await this.backupUserData(uid, reason);
        let where = { uid };
        await rankMod.remove(uid)
        await this.deviceTokenCol.deleteOne(where),
            await this.subscribeCol.deleteMany(where);
        await util.promiseMap([
            idMapCol.deleteMany(where),
            userCol.deleteOne(where),
            staUserCol.deleteOne(where),
            inUserCol.deleteOne(where),
            recordCol.deleteOne(where)
        ])
    },

    async MoveUsr(uid, _uid, del, white, moveAct) {
        let userDoc = await userMod.findOne(uid);
        let targetUser = await userMod.findOne({ uid: _uid });
        if (!userDoc) {
            return response.ERROR_WITH_DESC_NO_RESULT('原账号ID不存在');
        }
        if (!targetUser) {
            return response.ERROR_WITH_DESC_NO_RESULT('目标账号ID不存在');
        }
        // 备份用户信息和存档
        await userMod.backupUserData(_uid, `账号迁移备份(from:${uid})`);
        let success = await recordMod.copy(userDoc.uid, _uid);
        if (success) {
            // 同步对应的区服，昵称，头像，注册时间
            let userInfo = {
                serverId: userDoc.serverId,
                nickName: userDoc.nickName,
                avatarUrl: userDoc.avatarUrl,
                signupTime: userDoc.signupTime,
                playTime: userDoc.playTime || 0,
                likeNum: userDoc.likeNum || 0,
            }
            try {
                success = await userMod.updateOne(_uid, userInfo, false);
                // 需要同步货币余额和订单信息
                success = await balanceCol.updateMany({ uid }, { uid: _uid });
                success = await orderCol.updateMany({ uid }, { uid: _uid });
            } catch (err) {
                logger.error("userMod 更新user时会去更新用户redis缓存信息,可能会报错,没影响.");
                success = true;
            }
            if (moveAct) {
                // 同步兔兔商店
                const ada = await activityMod.getActivityData(uid)
                if (ada) {
                    await activityMod.updateActivityData(_uid, {
                        coupon: ada.coupon || 0,
                        skin: ada.skin || [],
                        point: ada.point || 0,
                        login: ada.login || 0,
                        totalLogin: ada.totalLogin || 0,
                        update: ada.update || 0,
                        task: ada.task || [],
                        heart: ada.heart || 0,
                        reward: ada.reward || [],
                        choose: ada.choose || 0,
                        limit: ada.limit || [],
                        record: ada.record || [],
                        bigLogin: ada.bigLogin || "",
                    })
                    // 删除原用户的
                    await activityMod.delActivityData(uid)
                }
            }
            if (success) {
                // 删除旧账号 有备份
                if (del === "true") {
                    await userMod.deleteUser(uid);
                }
                // 新账号加入白名单 / 加GM
                if (white === "true") {
                    //await verifyMod.addWhite(_uid);
                    await verifyMod.addGM(_uid);
                }
            }
            return success ?
                response.SUCCESS_WITH_DESC_NO_RESULT('迁移成功!')
                : response.ERROR_WITH_DESC_NO_RESULT('迁移失败(更新user信息时)');
        }
        return response.ERROR_WITH_DESC_NO_RESULT('迁移失败,原账号ID可能没有存档');
    },

    /**
     * 备份用户数据
     * @param uid
     * @param reason
     * @returns {Promise<void>}
     */
    async backupUserData(uid, reason) {
        let where = { uid };
        let attr = {};
        let userColData = await userCol.findOne(where);
        attr["userColData"] = await this.mergeAttribute(userColData);
        let idMapColData = await idMapCol.find(where);
        attr["idMapColData"] = await this.mergeAttribute(idMapColData);
        let recordColData = await recordCol.findOne(where);
        attr["recordColData"] = await this.mergeAttribute(recordColData);
        let saveInfo = new backupUserDataCol({
            uid,
            timestamp: Date.now(),
            data: JSON.stringify(attr),
            reason: reason || "未知"
        });
        await saveInfo.save();
    },
    async mergeAttribute(colData) {
        if (colData && !colData.length) {
            let doc = colData._doc;
            return JSON.stringify(doc) || "";
        }
        return JSON.stringify(colData) || "";
    },
    /**
     * 恢复上面备份方法备份的玩家数据
     * @param dataId
     * @returns {Promise<boolean>}
     */
    async restoreUserData(dataId) {
        let doc = await backupUserDataCol.findOne({ _id: ObjectId(dataId) });
        if (!doc) {
            return false;
        }
        let uid = doc.uid;
        let data = doc.data;
        if (!data) {
            return false;
        }
        data = JSON.parse(data);
        let userColData = JSON.parse(data.userColData) || {};
        let idMapColData = JSON.parse(data.idMapColData) || {};
        let recordColData = JSON.parse(data.recordColData) || {};
        userColData = util.removeAttrIfExits(userColData, ["__v", "_id"], true);
        idMapColData = util.removeAttrIfExits(idMapColData, ["__v", "_id"], true);
        recordColData = util.removeAttrIfExits(recordColData, ["__v", "_id"], true);
        if (userColData) {
            await userCol.updateOne({ uid }, userColData, { upsert: true });
        }
        if (idMapColData) {
            for (let colData of idMapColData) {
                colData = util.removeAttrIfExits(colData, ["__v", "_id"], true);
                await idMapCol.updateOne({ tpid: colData.tpid }, colData, { upsert: true });
            }
        }
        if (recordColData) {
            await recordCol.updateOne({ uid }, recordColData, { upsert: true });
        }
        return true;
    },

    // async deleteAll() {
    //     let where = {};
    //     await util.promiseMap([
    //         idMapCol.deleteMany(where),
    //         userCol.deleteMany(where),
    //         staUserCol.deleteMany(where),
    //         inUserCol.deleteMany(where),
    //         recordCol.deleteMany(where)
    //     ])
    // }

    //这里拿到的是diy过后的
    async getNameAndAvatar(uid) {
        let cacheInfo = await this.getUserCacheInfo(uid)
        if (cacheInfo) return util.setValue("avatarUrl|nickName", cacheInfo)
        return await userMod.findOne(uid, { nickName: 1, avatarUrl: 1 }) || {}
    },

    //废弃
    // async updateNameAndAvatar(uid, nickName, avatarUrl) {
    //     let update = {}
    //     if (nickName) update.nickName = nickName
    //     if (avatarUrl) update.avatarUrl = avatarUrl
    //     return await userMod.updateOne(uid, update)
    // },

    async mergeUser(uid, oldUid) {
        if (!oldUid) return
        let oldUser = await userMod.findOne(oldUid)
        if (!oldUser) {
            throw verror({ name: "oldUid not found", info: { uid, oldUid } })
        }
        let update = {}
        if (oldUser.openid) {
            update.openid = oldUser.openid
        }
        if (oldUser.wxAppOpenid) {
            update.wxAppOpenid = oldUser.wxAppOpenid
        }
        if (oldUser.unionid) {
            update.unionid = oldUser.unionid
        }
        if (oldUser.userType) {
            update.userType = oldUser.userType
        }
        await userMod.updateOne(uid, update)
    },

    async getRandomUser() {
        let heart = util.randomInt(0, 2000)
        let list = []
        for (let i = 40; i < 60; i++) {
            list.push(i)
        }
        util.randomArray(list)
        for (let serverId of list) {
            let key = rankMod.getRankName(serverId);
            let [uid] = await redis.zrangebyscore(key, heart, heart + 100, "limit", 0, 1)
            if (uid) {
                return uid
            }
        }
    },

    async getUserCacheInfo(uid) {
        let key = redisKey.userCacheInfo(uid)
        let info = await redis.get(key)
        if (!info) {
            let userDoc = await this.findOne(uid)
            if (userDoc) {
                info = await this.updateUserCacheInfo(userDoc)
            }
        } else {
            info = JSON.parse(info)
        }
        return info
    },

    async updateUserCacheInfo(userDoc) {
        let key = redisKey.userCacheInfo(userDoc.uid)
        let info = util.setValue2("avatarUrl|nickName|diyName|diyAvatar", userDoc)
        let cacheInfo = {
            nickName: info.diyName || info.nickName,
            avatarUrl: info.diyAvatar || info.avatarUrl
        }
        await redis.set(key, JSON.stringify(cacheInfo))
        redis.pexpire(key, 1 * util.Time.Day)
        return info
    },

    async moveInactiveUser() {
        // userCol.findOne()
    },

    async isNewUser(uid, signupTime) {
        if (!signupTime) {
            let userDoc = await userMod.findOne(uid)
            signupTime = userDoc.signupTime
        }
        let now = Date.now()
        if (now - signupTime < 24 * util.Time.Hour) {
            let info = await shareMod.getBeInvitedInfo(uid)
            return info == null ? true : false
        }
        return false
    },

    async addSpAward(uid, type) {
        return this.spAwardCol.updateOne({ uid }, { $addToSet: { awards: type } }, { upsert: true })
    },

    async getSpAward(uid, types) {
        let doc = await this.spAwardCol.findOne({ uid })
        let awards = []
        if (!doc) return awards
        for (let type of doc.awards) {
            if (types.indexOf(type) >= 0) {
                awards.push(type)
            }
        }
        return awards
    },

    async copyTop() {
        let info = config.releaseNode
        if (!info) return//正式服直接返回null
        let { ip, port } = info;
        for (let i = 50; i < 60; i++) {
            const url = `http://${ip}:${port}/debug_getTop?serverId=${i}`
            let { rankList } = await got(url, {
                timeout: 5 * util.Time.Second,
            }).json();
            for (let { id } of rankList) {
                logger.debug(id)
                await recordMod.debugCopy(id, false)
            }
            logger.info("copyTop ----------", i)
        }
    },

    async hasToken(uid, token) {
        let key = redisKey.userToken(uid)
        let hasToken = await redis.sismember(key, token)
        if (!hasToken) {
            let doc = await this.tokenCol.findOne({ uid, token })
            if (doc) {
                hasToken = true
                await redis.sadd(key, token)
            }
        }
        if (hasToken) {
            redis.pexpire(key, util.Time.Day)
        }

        return hasToken
    },

    verifyToken(uid, token) {
        let str = token.substring(0, 10) + token.substring(15)
        let random = token.substring(10, 15)
        try {
            str = cryptoHelper.decryptASE(str, TOKEN_KEY)
            let info = JSON.parse(str)
            if (info.uid == uid && info.random == random) {
                return true
            }
        } catch (error) {
            logger.error(error)
            return false
        }
    },

    parseToken(token) {
        let str = token.substring(0, 10) + token.substring(15)
        str = cryptoHelper.decryptASE(str, TOKEN_KEY)
        return JSON.parse(str)
    },

    async addToken(uid, token, type) {
        let now = Date.now()
        await this.tokenCol.updateOne({ uid, token }, { createTime: now, type }, { upsert: true })
        let key = redisKey.userToken(uid)
        await redis.sadd(key, token)
        redis.pexpire(key, util.Time.Day)
    },

    makeToken(uid, type) {
        let random = util.getRandomString(5)
        let payload = {
            uid, type,
            random,
        }
        let str = cryptoHelper.ase(JSON.stringify(payload), TOKEN_KEY)
        return str.substring(0, 10) + random + str.substring(10)
    },

    makeSessionId(uid) {
        let now = Date.now()
        let payload = {
            uid,
            time: now
        }
        let str = cryptoHelper.ase(JSON.stringify(payload), SESSION_KEY)
        return str
    },

    async makeSessionKey(uid, userDoc) {
        userDoc = userDoc || await userMod.findOne(uid)
        let now = Date.now()
        return String(userDoc.index_id) + String(now)
    },

    async updateSessionKey(uid, sessionKey) {
        let key = redisKey.lastSessionKey(uid)
        await redis.set(key, sessionKey)
        redis.pexpire(key, util.Time.Day * 3)
    },

    async getLastSessionKey(uid) {
        let key = redisKey.lastSessionKey(uid)
        return await redis.get(key)
    },

    async delLastSessionKey(uid) {
        let key = redisKey.lastSessionKey(uid)
        return await redis.del(key)
    },

    parseSessionId(sessionId) {
        let str = cryptoHelper.decryptASE(sessionId, SESSION_KEY)
        return JSON.parse(str)
    },

    async genToken(uid, type) {
        let tokenDoc = await this.tokenCol.findOne({ uid, type }) //token重复利用
        let token
        if (tokenDoc) {
            token = tokenDoc.token
        } else {
            token = userMod.makeToken(uid, type)
            await userMod.addToken(uid, token, type)
        }
        return token
    },

    async getSeverCount() {
        let serverName = "serverCount";
        let countInfo = await countInfoCol.findOne({ name: serverName });
        if (!countInfo) return 0
        return countInfo.count
    },

    //status: 0.正确 -4.身份证为空 -2.名字为空 -3.身份证格式不对 -1.身份证名字不匹配
    async authIDCard(uid, idNum, name) {
        let pass = true
        if (!config.debug) {
            if (DEBUG_IDCARD.idNum == idNum && DEBUG_IDCARD.name == name) {  //苹果审核/debug用
                pass = true
            } else {
                pass = await idCardVerify.auth(uid, name, idNum)
            }

            if (!pass) {
                if (!idNum) {
                    return { status: -4 }
                } else {
                    let { status } = idCardVerify.checkID(idNum)
                    if (status != 0) {
                        return { status: -3 }
                    }
                }
                if (!idCardVerify.checkTrueName(name)) {
                    return { status: -2 }
                }
            }
        }
        if (pass) {
            await this.idCardCol.updateOne({ uid }, { idNum, name, timestamp: Date.now() }, { upsert: true })
            this.cardInfoSubmitByUid(uid, name, idNum);
            return { status: 0, age: idCardVerify.getAge(idNum) }
        } else {
            return { status: -1 }
        }
    },
    async cardInfoSubmitByUid(uid, name, idNum, prop) {
        let user = await userCol.findOne({ uid }, { unionid: 1 });
        let id = user ? user.unionid : '';
        if (!id) {
            let idCol = await idMapCol.findOne({ uid });
            id = idCol ? idCol.tpid : id;
        }
        await this.cardInfoSubmit(id, name, idNum, prop);
    },
    async cardInfoSubmit(id, name, idNum, prop = 1) {
        try {
            let { ip, gameId, port } = config.twCc;
            if (!ip || !port || !id) return;
            const url = `http://${ip}:${port}/card/submit?id=${id}&gameId=${gameId}&name=${name}&idNum=${idNum}&prop=${prop}`;
            let { code, data } = await got(url, {
                timeout: 5 * util.Time.Second,
            }).json();
            logger.info(`card info submit :${code}, ${id}, ${name}, ${idNum}, ${prop}`);
        } catch (e) {
            logger.error(`cardInfoSubmit err ${id}, ${e}`);
        }
    },
    async cardInfoVerifyByUid(uid) {
        let user = await userCol.findOne({ uid }, { unionid: 1 });
        let id = user ? user.unionid : '';
        if (!id) {
            let idCol = await idMapCol.findOne({ uid });
            id = idCol ? idCol.tpid : id;
        }
        let ret = await this.cardInfoVerify(id);
        logger.info(`card info verify :${uid}, ${JSON.stringify(ret)}`);
        return ret;
    },
    async cardInfoVerify(id) {
        try {
            if (!id) return !1;
            let { ip, gameId, port } = config.twCc;
            if (!ip || !port) return !1;
            const url = `http://${ip}:${port}/card/verify?id=${id}`;
            let { code, data } = await got(url, {
                timeout: 5 * util.Time.Second,
            }).json();
            if (code === 0) return data;
        } catch (e) {
            logger.error(`cardInfoVerify err ${id}, ${e}`);
        }
        return !1;
    },
    async getAge(uid, userType) {
        if (userType == USER_TYPE.XIAOMI || config.type === 'global' || config.type === 'fb') return 18;
        // 先本地验证 然后去中控请求
        let doc = await this.idCardCol.findOne({ uid })
        if (!doc) {
            let rsp = await this.cardInfoVerifyByUid(uid);
            if (rsp && rsp.pass) return rsp.age;
            return 0;
        }
        return idCardVerify.getAge(doc.idNum);
    },

    //包括法定节假日和周末
    async isHoliday(timestamp) {
        let date = new Date(timestamp)
        let dateNum = Number(util.dateFormat("yyyyMMdd", date.getTime()))
        let doc = await this.workDayCol.findOne({ date: dateNum })
        return doc
    },

    //节假日，周5到周末晚 8点到9点
    async getNonagePlayTime() {
        let date = new Date()
        let day = date.getDay()
        if (day == 5 || await this.isHoliday()) {
            let hour = date.getHours()
            if (20 <= hour && hour < 21) {
                return util.Time.Hour - (date.getMinutes() * util.Time.Minute + date.getSeconds() * util.Time.Second)
            }
            return 0
        }
        return 0
    },

    async isGuessAdult(uid) {
        let doc = await this.guessAdultCol.findOne({ uid })
        if (doc) {
            return doc.prop >= 2
        }
        return false
    },

    async checkGuessAdult(uid, oldRecord, record) {
        let check = () => {
            let oldStarSum = recordMod.getStarSum(oldRecord)
            let newStarSum = recordMod.getStarSum(record)
            // logger.info("debug star", uid, oldStarSum, newStarSum)
            if (newStarSum > oldStarSum) { //蜡烛有变化
                logger.info("checkGuessAdult star", uid, oldStarSum, newStarSum)
                return true
            }
            let oldCandy = recordMod.getFromRecord("global.candies", oldRecord)
            let newCandy = recordMod.getFromRecord("global.candies", record)
            // logger.info("debug candy", uid, oldStarSum, newStarSum)
            if (oldCandy != newCandy) { // 糖果变化
                logger.info("checkGuessAdult candy", uid, oldCandy, newCandy)
                return true
            }
        }

        let isAdultTime = await this.getNonagePlayTime() <= 0
        // logger.info("debug agisAdultTimee", uid, isAdultTime)
        if (isAdultTime) {
            let date = new Date()
            let hour = date.getHours()
            let minute = date.getMinutes()
            if (hour == 21 && minute < 5) { //防止一些请求延迟
                return
            }
            let age = await this.getAge(uid)
            // logger.info("debug age", uid, age)
            if (age == 0) { //没认证过
                if (check()) {
                    await this.guessAdultCol.updateOne({ uid }, { $inc: { prop: 1 } }, { upsert: true })
                    // 可能性上报
                    // this.cardInfoSubmitByUid(uid, '', '', 1);
                }
            }
        }
    },

    async getDHLogInfo(uid, userDoc) {
        if (!config.dhlog || !uid) return
        let doc = userDoc || await userMod.findOne(uid)
        if (!doc) return
        if (!doc.index_id) return
        let userInfo = {
            bundle_id: config.bundle_id,
            server_id: "1",
            user_id: String(doc.index_id),
            session_id: await userMod.getLastSessionKey(uid) || (String(doc.index_id) + doc.loginTime)
        }
        return userInfo
    },

    async isDeviceDiff(uid, deviceId) {
        if (!uid || !deviceId) return false
        let lastLoginInfo = await this.loginInfoCol.findOne({ uid })
        if (!lastLoginInfo || !lastLoginInfo.deviceId) return false
        if (lastLoginInfo.deviceId != deviceId) {
            let userDoc = await userMod.findOne(uid)
            return Date.now() - userDoc.updateTime > util.Time.Minute
        }
        return false
    },

    async getLoginVer(uid) {
        let result;
        !uid && (result = '');
        if (!result) {
            let doc = await this.loginInfoCol.findOne({ uid });
            result = doc && (doc.gameVer) || '';
        }
        return result;
    },

    async isDeviceDiffAndGetInfo(uid, deviceId) {
        if (!uid || !deviceId) return {}
        let lastLoginInfo = await this.loginInfoCol.findOne({ uid })
        if (!lastLoginInfo || !lastLoginInfo.deviceId) return {}
        if (lastLoginInfo.deviceId != deviceId) {
            return { diff: true, platform: lastLoginInfo.platform }
        }
        return {}
    },

    async heartbeat(token, sessionId, path) {
        try {
            let uid
            if (!token && !sessionId) return
            if (!uid) {
                try {
                    let info = {}
                    if (token) {
                        info = this.parseToken(token)
                    } else if (sessionId) {
                        info = this.parseSessionId(sessionId)
                    }
                    uid = info.uid
                } catch (error) {
                    logger.error("heartbeat parse err", error)
                }
            }
            if (!uid) return

            let key = redisKey.onlineUser()
            let time = await redis.zscore(key, uid) || 0
            let now = Date.now()
            if (!time) {
                logger.debug("heartbeat", uid, path, false)
                return false
            }
            await redis.zadd(key, now, uid)
            logger.debug("heartbeat", uid, path, true)
            return true
        } catch (error) {
            logger.error("heartbeat", error)
        }
    },

    async reconnect(uid, userDoc) {
        let now = Date.now()
        let key = redisKey.onlineUser()
        await redis.zadd(key, now, uid)
        this.online(uid, "reconnect")
    },

    async getOnlineUsersCount() {
        let key = redisKey.onlineUser()
        return await redis.zcard(key)
    },

    async removeOfflineUsers() {
        let key = redisKey.onlineUser()
        let now = Date.now()
        let expireTime = now - 2 * util.Time.Minute
        if (config.debug) {
            // expireTime = now - 20 * util.Time.Second
        }
        let uids = await redis.zrangebyscore(key, "-inf", expireTime) || []
        console.log("removeOfflineUsers", uids.length)
        await redis.zremrangebyscore(key, "-inf", expireTime)
        for (let uid of uids) {
            try {
                this.offline(uid);
                this.dealCatchUsers(uid);
            } catch (e) {
            }
        }
    },
    /**
     *  对异常用户扣除数据，如果被封号等级大于等于3(无法上传存档),则不处理,留待后续人工处理
     *  否则扣除对应蜡烛数量
     */
    async dealCatchUsers(uid) {
        let user = await this.catchUsersCol.findOne({ uid });
        if (!user) return;
        if (!user.count) return;
        let block = await verifyMod.getBlackById(uid);
        if (block && block.level >= 3) {
            return void logger.warn(`user can not deal: ${uid}  -- ${block.level}.`);
        }
        let record = await recordMod.getRecord(uid)
        let global = recordMod.getFromRecord("global", record);
        global.heart -= user.count * 5;
        if (global.heart <= 0) {
            return void logger.warn(`change data error: ${uid} -- ${user.count} -- ${global.heart}.`);
        }
        recordMod.setToRecord("global", global);
        await recordMod.setRecord(uid, record);
        await userMod.updateOne(uid, { forceDownload: true });
        await this.catchUsersCol.updateOne({ uid }, {
            count: 0,
            $inc: { editCount: user.count },
            lastEditTimeStamp: Date.now()
        });
        block && await verifyMod.deblockUser(uid);
    },

    async offline(uid, userDoc) {
        logger.info("offline", uid)
        let now = Date.now()
        try {
            userDoc = userDoc || await userMod.findOne(uid)
            subscribeMod.subOfflineAward(uid, userDoc)
            subscribeMod.subLostRecall(uid, userDoc)

            userMod.getLastSessionKey(uid).then((sessionKey) => {
                if (!sessionKey) return
                logger.info("offline session", uid, sessionKey)
                let len = sessionKey.length
                let lastSessionTime = Number(sessionKey.substring(len - 13))
                let now = Date.now()
                if (now - lastSessionTime > util.Time.Second * 10) { //如果跟上次sessiontime离得很近，说明应该是中间重连了
                    this.reportDHLogout(uid, userDoc, sessionKey)
                    this.delLastSessionKey(uid)
                } else {
                    logger.warn("offline duplicate", uid, sessionKey, now - lastSessionTime)
                }
            })

            await userMod.updateOne(uid, { logoutTime: now })
        } catch (error) {
            logger.error("offline err", uid, error)
        }
    },

    async reportDHLogout(uid, userDoc, session_id) {
        try {
            let userInfo = await userMod.getDHLogInfo(uid, userDoc)
            if (userInfo) {
                if (session_id) {
                    userInfo.session_id = session_id
                }
                dhlog.report(DH_LOG_EVENT.LOGOUT, null, userInfo)
            }
        } catch (error) {
            logger.error("offline reportLog err", uid, error)
        }
    },

    async online(uid, path) { //从离线切到在线
        logger.info("online", uid, path)
        try {
            let cancelSub = async () => {
                try {
                    let doc = await this.deviceTokenCol.findOne({ uid }, { _id: 1 })
                    if (doc) {
                        await subscribeMod.cancelOfflineAward(uid)
                        await subscribeMod.cancelLostRecall(uid)
                    }
                } catch (error) {
                    logger.error("online cancelSub err", uid, error)
                }
            }

            let static = async () => {
                try {
                    let info = await redis.get(redisKey.subscribeLast(uid))
                    if (info) {
                        let { msgType, platform } = JSON.parse(info)
                        await statisticsMod.umeng({ eventId: "notify", params: { [msgType]: 1, [platform]: 1 } })
                        await redis.del(redisKey.subscribeLast(uid))
                    }
                } catch (error) {
                    logger.error("online static err", uid, error)
                }
            }

            await Promise.all([
                cancelSub(), static()
            ])

        } catch (error) {
            logger.error("online err", uid, error)
        }
    },

    async getDeviceInfo(uid) {
        let doc = await this.deviceInfoCol.findOne({ uid })
        if (!doc) return
        let deviceInfo = {
            "adid": doc.adid || "",//广告ID,安卓是Google Advertising ID ;ios是IDFA
            "idfv": doc.idfv || "",//idfv（仅ios）
            "imei": doc.imei || "",//国际移动设备识别码（仅安卓）
            "android_id": doc.android_id || "",//安卓id，安卓设备号（仅安卓）
            "appsflyer_id": doc.appsflyer_id || "",//appsflyer sdk得到的id
            "device_token": doc.device_token || "",//推送消息用的token
            "mac_address": doc.mac_address || "",//mac地址
            "device_model": doc.device_model || "",//设备型号
            "device_name": doc.device_name || "",//设备名字
            "os_version": doc.os_version || "",//手机系统版本
            "network_type": doc.network_type || "",//网络类型，1是数据 2是Wi-Fi
            // "oaid":"",///OAID 广告标识符 (Open Advertising Identifier)，仅安卓
            language: doc.language || "",
            app_version: doc.app_version || ""
        }
        let userInfo = {
            bundle_id: doc.bundle_id || "",
            server_id: doc.server_id || -999, //目前按排行榜划分的服在统计上意义不大，先统一都算成一个服
            user_id: doc.user_id || "",
            session_id: doc.session_id || -999,
            user_name: doc.user_name || "",
            lv: doc.lv || -999,
            account: doc.user_id || "",
            platform: doc.platform || "",
        }
        return { deviceInfo, userInfo }
    },

    async isOnline(uid) {
        let key = redisKey.onlineUser()
        let time = await redis.zscore(key, uid) || 0
        return time
    },

    async updateDiyInfo(userDoc) {
        if (!userDoc) return
        let update = {}
        let userType = userDoc.userType
        if (userType != USER_TYPE.FB || userType != USER_TYPE.TWITTER) return //只过滤fb推特
        if (!userDoc.diyName && userDoc.nickName) {
            let nickName = await textModerationMod.checkAndFilter(userDoc.nickName)
            if (nickName) {
                update.diyName = nickName
            }
        }
        if (Object.keys(update).length > 0) {
            await userMod.updateOne(userDoc.uid, update)
        }
    },

    async changeDiyInfo(uid, info) {
        let update = {}
        if (info.diyName) {
            let isVaild = await textModerationMod.isValid(info.diyName)
            if (!isVaild) return { status: -1 }
            update.diyName = info.diyName
        }
        if (info.diyAvatar) {
            update.diyAvatar = info.diyAvatar
        }
        await userMod.updateOne(uid, update)
        return { status: 0 }
    },

    //检查重放请求
    async checkReplay(reqId) {
        let rspKey = redisKey.replayRsp(reqId)
        let rsp = await redis.get(rspKey)
        logger.debug(rspKey, rsp)
        if (rsp) {
            return { isReplay: true, rsp }
        }
        let key = redisKey.replayReqFlag(reqId)
        let isReplay = !!await redis.getset(key, 1)
        redis.pexpire(key, 1 * util.Time.Minute)
        return { isReplay }
    },

    async cacheRsp(reqId, dataStr) {
        let key = redisKey.replayRsp(reqId)
        await redis.set(key, dataStr)
        logger.debug("cachRsp", reqId)
        redis.pexpire(key, 1 * util.Time.Minute)
    },

    async getCompensationRewards(uid, timestamp, gameVer) {
        let rewards = [], text = [];
        if (!uid) return { status: 0, rewards, text };
        let docs = await this.compensationRewardCol.find({ uid })
        for (let doc of docs) {
            try {
                if (doc.limitTime && doc.limitTime !== 0 && doc.limitTime < Date.now()) {
                    await this.compensationRewardCol.deleteOne({ _id: doc._id })
                    continue
                }
                // 时间未到,不允许领取奖励
                if (doc.timestamp > Date.now()) continue
                if (doc.timestamp > timestamp) {
                    if (doc.needGameVer && util.cmpVersion(gameVer, doc.needGameVer) < 0) {
                        //return {status: -101}; //版本号不匹配 不能领取
                        continue;
                    }
                    if (doc.type === 100712 || doc.type === 100713) {
                        rewards.push(doc)
                        text = doc.lang;
                    } else {
                        let array = gameHelper.stringToConditions(doc.reward);
                        await this.sendSpecialPrize(uid, array);
                        rewards.push(doc)
                        doc.lang && (text = JSON.parse(doc.lang));
                    }
                    await this.compensationRewardCol.deleteOne({ _id: doc._id })
                } else {
                    await this.compensationRewardCol.deleteOne({ _id: doc._id })
                    logger.info("clear compend reward", doc);
                }
            } catch (e) {
                await this.compensationRewardCol.deleteOne({ _id: doc._id })
                logger.info(`getCompensationRewards err:${e}, doc:${doc}`)
            }
        }
        return { status: 0, rewards, text }
    },
    // 这个接口同一个uid和type只能存在一条
    async addCompensationReward(uid, reward, type = 10071) {
        let timestamp = Date.now()
        await this.compensationRewardCol.updateOne({ uid, type }, { reward, timestamp }, { upsert: true })
        this.compensationRewardHistoryCol.updateOne({ uid, timestamp }, { reward, type }, { upsert: true }).then()
    },
    // 这个接口同一个uid和type可以存在多条
    // 1228 增加参数timestamp,控制奖励没到时间不能领取,针对派对添加
    async addCompensationRewardMultiple(uid, reward, lang, ver, type = 10071, limit = 0) {
        const timestamp = Date.now()
        let save = new this.compensationRewardCol({
            uid, type, reward, timestamp, lang, needGameVer: ver
        });
        await save.save();
        this.compensationRewardHistoryCol.updateOne({ uid, timestamp }, {
            reward,
            type,
            lang,
            needGameVer: ver,
            limitTime: limit
        }, { upsert: true }).then()
    },

    /**
     * 发送特殊奖励  比如剪刀和风车
     * @returns {Promise<void>}
     */
    async sendSpecialPrize(uid, rewardArray) {
        for (let r of rewardArray) {
            switch (r.id) {
                case -1: {
                    if (r.type === 17) {
                        await currencyModel.changeCurrencyBalance(uid, constants.CURRENCY.SCISSOR, r._count);
                    } else if (r.type === 16) {
                        await currencyModel.changeCurrencyBalance(uid, constants.CURRENCY.WINDMILL, r._count);
                    } else if (r.type === 9102) {
                        // 特殊商店抽奖券
                        await activityMod.changeCoupon(uid, r._count);
                    } else if (r.type === 9103) {
                        await activityMod.updateActivityData(uid, {
                            $inc: { point: r._count }
                        })
                    } else {
                        logger.info("发奖未处理的类型:", r.type);
                    }
                    break;
                }
            }
        }
    },
    // 判断逻辑表的奖励是否被领取了(不存在则是)
    async isCompensationRewardReceived(uid, timestamp) {
        let docs = await this.compensationRewardCol.find({ uid, timestamp })
        return docs && docs.length > 0
    },
    // 清理玩家备份数据 (超过15天的)
    async clearUserBackupData() {
        let max = 15 * 24 * 60 * 60 * 1000;
        let now = Date.now();
        let del = [];
        let docs = await backupUserDataCol.find({});
        for (let doc of docs) {
            if (!doc.timestamp) {
                del.push(doc._id);
                continue;
            }
            if (now - doc.timestamp >= max) {
                del.push(doc._id);
            }
        }
        if (del.length > 0) {
            await backupUserDataCol.deleteMany({ _id: { $in: del } })
        }
    },
    async isUserExits(uid) {
        let doc = await this.findOne({ uid });
        return !!doc;
    },
    async getUserHeart(uid) {
        let doc = await this.userHeartCol.findOne({ uid });
        return doc ? doc.heart : 0
    },
    async addPayNum(uid, num) {
        await userCol.updateOne({ uid }, { $inc: { pay: num } });
    }

}

module.exports = userMod;

const util = require("../common/util").default;
const logger = require("../common/log").getLogger("userMod");
let verror = require("verror");
const redisKey = require("../db/redisKey");
const config = require("../config")
const got = require("got")
const cryptoHelper = require("../cypto/cryptoHelper")
const idCardVerify = require("../common/idCardVerify")
const { dhlog, DH_LOG_EVENT } = require("../common/DHLog");
const { USER_TYPE } = require("../common/constant")
const { ObjectId } = require("mongoose/lib/types");
const { gameHelper } = require("../client/GameHelper");
const currencyModel = require("./currencyModel")
const constants = require("../common/constant");
const db = require("../db/db");
const { retail } = require("googleapis/build/src/apis/retail");
const activityMod = require('./activityMod')
