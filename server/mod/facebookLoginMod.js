const logger = require("../common/log").getLogger("facebookLoginMod");
const util = require("../common/util").default;
const got = require('got');
const {USER_TYPE} = require("../common/constant")
const base64url = require('base64url')
const jwt = require('jsonwebtoken')
const jwkToPem = require('jwk-to-pem')
const JWKSurl = 'https://limited.facebook.com/.well-known/oauth/openid/jwks/'

let facebookLoginMod = {
    async getLoginInfo(fbId, token, jwtToken) {
        let data = await this.getUserInfo(fbId, token)
        if (data && data.id) {
            return {
                avatarUrl: data.picture.data.url,
                nickName: data.name,
                openid: data.id,
                userType: USER_TYPE.FB
            };
        }
        return await this.validateOIDCToken(fbId, token, jwtToken)
    },

    async getUserInfo(fbId, token, retry = 3) {
        //todo retry
        let url = `https://graph.facebook.com/${fbId}?fields=id,name,picture&access_token=${token}`;
        try {
            let data = await got(url, {
                timeout: 10 * util.Time.Second,
            }).json();
            let {error} = data;
            if (!error) {
                return data
            } else {
                //throw verror({name: "fb getUserInfo err", info: {fbId, token, data}});
                return null
            }
        } catch (error) {
            if (retry > 0) {
                await util.wait(1000)
                return this.getUserInfo(fbId, token, retry - 1)
            }
            return null
        }
    },

    // 受限登录
    async validateOIDCToken(clientId, clientToken, clientJWTtoken) {
        if (!clientJWTtoken) return null
        let jwtTokenL = clientJWTtoken.split('.')
        if (jwtTokenL.length !== 3) return null

        let headerJson = JSON.parse(base64url.decode(jwtTokenL[0]))
        let payloadJson = JSON.parse(base64url.decode(jwtTokenL[1]))
        if (payloadJson.sub !== clientId) return null
        let pubKey = await this.getOIDCPublicKey(headerJson.kid, clientId)
        if (!pubKey) return null

        let jwtVerifySync = (token, key) => {
            return new Promise((resolve) => {
                jwt.verify(token, key, {algorithms: ['RS256']}, (verifyErr, verifyRes) => {
                    if (verifyErr) {
                        logger.error("validateOIDCToken verify err: ", verifyErr, clientId)
                        resolve(false)
                    } else {
                        console.log("verifyRes is ", verifyRes)
                        resolve(verifyRes.sub === clientId)
                    }
                })
            })
        }

        let validateRes = await jwtVerifySync(clientJWTtoken, pubKey)
        if (validateRes) return {
            openid: clientId,
            avatarUrl: payloadJson.picture,
            nickName: payloadJson.name,
            accessToken: clientToken,
            userType: USER_TYPE.FB
        }
        return null
    },

    async getOIDCPublicKey(kid, clientId) {
        if (!kid) return null
        try {
            let resData = await got.get(JWKSurl, {
                timeout: 5 * util.Time.Second,
            }).json()
            for (let keyMap of resData.keys) {
                if (keyMap.kid === kid) {
                    return jwkToPem(keyMap)
                }
            }
        } catch (error) {
            logger.error("getOIDCPublicKey catch ", error.message, clientId)
            return null
        }
    }

}

module.exports = facebookLoginMod;
