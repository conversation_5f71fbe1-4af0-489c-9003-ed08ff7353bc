const {verify} = require("jsonwebtoken");
const VError = require("verror");
const util = require("../common/util").default;
const {HOT_UPDATE_RELEASE_NUM, HOT_UPDATE_RELEASE_RANDOM} = require("../db/redisKey");
const db = require("../db/db");
const got = require("got");
const fs = require("fs");
const logger = require("../common/log").getLogger("versionMod");
const config = require('../config');

let hotUpdateCol, bigVersionUpdateCol, redis;

const CheckUpdateStatus = {
    HOT_UPDATE: 0,
    BIG_UPDATE: 1,
    NOT_UPDATE: 2,
}

let versionMod = {
    init() {
        redis = db.redis;
        hotUpdateCol = db.createHotUpdateModel();
        bigVersionUpdateCol = db.createBigVersionUpdateModel();
    },
    async getHotUpdateInfo(platform) {
        let docs = await hotUpdateCol.find({platform}).sort({"_id": -1}).limit(2);
        if (docs.length == 2) {
            let doc = docs[0];
            if (doc.releasePercent >= 100) { //当前最新的是稳定版
                return [doc];
            } else {
                return docs;
            }
        } else {
            return docs;
        }
    },
    async getNearVersionInfo(platform, num) {
        return await hotUpdateCol.find({platform}).sort({"_id": -1}).limit(num);
    },
    async compareVersionSize(platform, versionA, versionB) {
        platform === 'HYKB' && (platform = 'android');
        platform === 'XIAOMI' && (platform = 'android');
        let temp = versionA;
        if (await util.cmpVersion(versionA, versionB) > 0) {
            versionA = versionB, versionB = temp;
        }
        let pre = `https://twgame-hotel-1257666530.file.myqcloud.com/`;
        config.type === 'global' && (pre = `https://twgame-hotel-global-1257666530.cos.ap-singapore.myqcloud.com/`);
        let url = `${pre}hotUpdate/${platform}/${versionA}/project.manifest`;
        let va = await got.get(url, {
            timeout: 5 * util.Time.Second,
        })
        if (!va) return [false, 'versionA load error'];
        url = `${pre}hotUpdate/${platform}/${versionB}/project.manifest`;
        let vb = await got.get(url, {
            timeout: 5 * util.Time.Second,
        })
        if (!vb) return [false, 'versionB load error'];
        let json1 = JSON.parse(va.body) || {}, json2 = JSON.parse(vb.body) || {}, assets1 = json1['assets'],
            assets2 = json2['assets'], list = [], total = 0;
        for (let key in assets2) {
            let item2 = assets2[key]
            let item1 = assets1[key]
            if (!item1 || item1.md5 != item2.md5) {
                let arr = key.split("/")
                arr.splice(arr.length - 1, 0, item2.md5)
                key = arr.join("/")
                list.push({key: key, size: item2.size})
            }
        }
        list.sort((a, b) => {
            return b.size - a.size
        })
        for (let {key, size} of list) {
            total += size;
            console.log(key, size);
        }
        return [true, total];
    },
    async check({uid, version, platform}) {
        let [hotDocs, bigVerDoc] = await util.promiseMap([this.getHotUpdateInfo(platform), bigVersionUpdateCol.findOne({platform})]);

        //判断大版本
        if (bigVerDoc) {
            if (!bigVerDoc.isUpdate) { //不更新
                return {status: CheckUpdateStatus.NOT_UPDATE};
            } else if (util.cmpVersion(bigVerDoc.version, version) > 0) { //去商店下载
                return {
                    status: CheckUpdateStatus.BIG_UPDATE,
                    force: bigVerDoc.force,
                    downloadUrl: bigVerDoc.downloadUrl
                };
            }
        }
        if (hotDocs.length <= 0) {
            return {status: CheckUpdateStatus.NOT_UPDATE};
        }

        //判断热更版本
        let result, doc;
        let [grayRelease, stab] = hotDocs;
        if (hotDocs.length == 2) { //灰度+稳定版
            result = this.checkGrayReleaseVersion(uid, version, grayRelease); // 先看能不能更到灰度版本
            if (result == CheckUpdateStatus.NOT_UPDATE) {
                doc = stab;
                result = this.checkStabVersion(version, stab); //不能则更稳定版
            } else doc = grayRelease
        } else if (grayRelease.releasePercent >= 100) { //只有稳定版
            result = this.checkStabVersion(version, grayRelease)
            doc = grayRelease
        } else { //只有灰度版本
            result = this.checkGrayReleaseVersion(uid, version, grayRelease);
            doc = grayRelease
        }

        if (result == CheckUpdateStatus.HOT_UPDATE) {
            this.recordHotUpdateNum(platform, doc.version); //记录已经灰度的人数，不完全准确，但可以作为参考
            return {status: result, url: doc.manifestUrl, packageUrl: doc.packageUrl, newVer: doc.version}
        }
        return {status: result};
    },

    checkGrayReleaseVersion(uid, version, doc) {
        if (!doc.isUpdate) { //不更新
            return CheckUpdateStatus.NOT_UPDATE;
        } else {
            let result = util.cmpVersion(doc.version, version);
            logger.debug("checkGrayReleaseVersion", uid, version, doc.version, result)
            if (result > 0) { // 线上版本比较新
                if (doc.whiteList && doc.whiteList.includes(uid)) {
                    return CheckUpdateStatus.HOT_UPDATE;
                }
                let percent = this.getGrayPercent(uid, doc.releaseRandom); //判断用户能否被灰度到
                logger.debug("percent: ", uid, version, percent);
                if (doc.releasePercent >= percent) {
                    if (util.cmpVersion(version, doc.preVersion) >= 0) {
                        return CheckUpdateStatus.HOT_UPDATE;
                    }
                    return CheckUpdateStatus.NOT_UPDATE
                }
                return CheckUpdateStatus.NOT_UPDATE;
            } else if (result == 0) {
                return CheckUpdateStatus.NOT_UPDATE;
            } else {
                return CheckUpdateStatus.NOT_UPDATE;
                //TODO 这时候是版本回退了，客户端要不要把热更目录清了
            }
        }
    },

    checkStabVersion(version, doc) {
        if (!doc.isUpdate) { //不更新
            return CheckUpdateStatus.NOT_UPDATE;
        } else {
            let result = util.cmpVersion(doc.version, version);
            logger.debug("cmpVersion: ", doc.version, version, result);
            if (result > 0) { // 线上版本比较新
                if (util.cmpVersion(version, doc.preVersion) >= 0) {
                    return CheckUpdateStatus.HOT_UPDATE;
                }
                return CheckUpdateStatus.NOT_UPDATE
            } else if (result == 0) {
                return CheckUpdateStatus.NOT_UPDATE;
            } else {
                return CheckUpdateStatus.NOT_UPDATE;
            }
        }
    },

    recordHotUpdateNum(platform, version) {
        let key = `${platform}_${version}_${HOT_UPDATE_RELEASE_NUM}`;
        redis.incrby(key, 1); //记录热更人数
        redis.expire(key, 30 * util.Time.Day);
    },

    async getHotUpdateNum(platform, version) {
        let key = `${platform}_${version}_${HOT_UPDATE_RELEASE_NUM}`;
        let num = await redis.get(key);
        return parseInt(num) || 0;
    },

    getGrayPercent(uid, random) {
        if (!uid) return 100;
        let sum = random || 0;
        for (let i = 0; i < uid.length; i++) {
            sum += uid.charCodeAt(i);
        }
        return sum % 100 + 1;
    },

    async releaseHotUpdateVersion(info) {
        let now = Date.now();
        info.releaseRandom = now % 100 + 1;
        info.timestamp = now;
        await hotUpdateCol.create(info);
    },

    async rollbackHotUpdateVersion(platform) {
        await hotUpdateCol.findOneAndDelete({platform}).sort({"_id": -1});
    },
}

module.exports = versionMod;