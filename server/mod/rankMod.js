const userMod = require("./userMod");
const util = require("../common/util").default;
const db = require("../db/db");
const redisKey = require("../db/redisKey");
const verifyMod = require("./verifyMod");
const logger = require("../common/log").getLogger('rankMod');
const { USER_TYPE } = require("../common/constant")

let redis

const TOP_NUM = 50

let rankMod = {
    init() {
        redis = db.redis
    },

    getRankName(serverId, userType) {
        let prefix = 'WORLD_RANK'
        return `${prefix}_${serverId}`
    },

    async getRank(key, uid) {
        let rank = await redis.zrevrank(key, uid)
        if (rank == null) {
            return -1
        }
        return rank
    },

    async getRankByScore(key, score) {
        let [uid] = await redis.zrevrangebyscore(key, 0, score).limit(1)
        if (!uid) {
            return -1
        }
        return this.getRank(key, uid)
    },

    async getScore(key, uid) {
        return redis.zscore(key, uid)
    },

    async getInfo(key, uid) {
        let [rank, score, {nickName, avatarUrl}] = await Promise.all([rankMod.getRank(key, uid), 
            rankMod.getScore(key, uid),
            userMod.getNameAndAvatar(uid)]);
        return {rank, score: parseInt(score), nickName, avatarUrl}
    },

    async getTop(key, num) {
        let result = await redis.zrevrange(key, 0, num - 1, "withscores") || []
        let rankList = []
        for (let i = 0; i < result.length; i += 2) {
            rankList.push({ id: result[i], value: result[i + 1] || 0 })
        }

        return rankList
    },

    async getTopInfo(key, num) {
        let topRanks = await this.getTop(key, num)
        let topRankInfo = await util.promiseMap(topRanks, async ({ id, value }) => {
            let info = {}
            try {
                let { nickName, avatarUrl } = await userMod.getNameAndAvatar(id);
                if (nickName) {
                    info.nickName = nickName
                }
                if (avatarUrl) {
                    info.avatarUrl = avatarUrl
                }
            } catch (error) {
                logger.error(error, id)
            }
            info.uid = id
            info.score = parseInt(value)

            return info
        })

        return topRankInfo;
    },

    async updateScore(key, uid, newScore, nickName, avatarUrl) {
        let isGM = await verifyMod.isGM(uid)
        if (isGM) {
            return true
        }

        let isBlack = await verifyMod.isBlack(uid)
        if (isBlack) {
            return false
        }

        isBlack = await verifyMod.checkBlack(uid, newScore)
        if (isBlack) {
            return false
        }

        let score = await redis.zscore(key, uid) || 0
        if (newScore > score) {
            await redis.zincrby(key, newScore - score, uid)
        }

        return true
    },

    async updateScoreByUid(uid, newScore) {
        let userDoc = await userMod.findOne(uid)
        if (!userDoc) return false
        let {serverId, nickName, avatarUrl, userType} = userDoc
        if (userType == USER_TYPE.GUEST) return false //游客不上排行榜
        let key = this.getRankName(serverId)
        return this.updateScore(key, uid, newScore, nickName, avatarUrl)
    },

    async addCache(key, uid, nickName, avatarUrl) {
        let cacheKey = redisKey.rankCache(key)
        await redis.hset(cacheKey, uid, JSON.stringify({ nickName, avatarUrl }))
    },

    async getCache(key) {
        let cacheKey = redisKey.rankCache(key)
        let cache = await redis.hgetall(cacheKey) || {}
        for (let id in cache) {
            cache[id] = JSON.parse(cache[id])
        }
        return cache
    },

    async setCache(key, data) {
        let cacheKey = redisKey.rankCache(key)
        for (let id in data) {
            data[id] = JSON.stringify(data[id])
        }
        await redis.hmset(cacheKey, data)
    },

    async remove(uid) {
        let userDoc = await userMod.findOne(uid)
        let serverId
        if (userDoc) {
            serverId = userDoc.serverId
        }
        if (!serverId) {
            for (let i = 0; i < 100; i++) {
                let key = rankMod.getRankName(i)
                let rank = await rankMod.getRank(key, uid)
                if (rank > -1) {
                    serverId = i
                    break
                }
            }
        }
        let key = rankMod.getRankName(serverId)
        let cacheKey = redisKey.rankCache(key)
        await Promise.all([redis.zrem(key, uid), redis.hdel(cacheKey, uid)])
    },

    async removeWithServerId(uid, serverId) {
        let key = rankMod.getRankName(serverId)
        let cacheKey = redisKey.rankCache(key)
        await Promise.all([redis.zrem(key, uid), redis.hdel(cacheKey, uid)])
    },

    async removeAll() {
        for (let i = 0; i < 100; i++) {
            let key = rankMod.getRankName(i)
            let cacheKey = redisKey.rankCache(key)
            await Promise.all([redis.del(key), redis.del(cacheKey)])
        }
    }
}

module.exports = rankMod;