const {ObjectId} = require("mongoose/lib/types");
const db = require("../db/db");
let mongoose = require('mongoose');
const response = require("../web/ResponseCode");

let curMod = {
    inited: !1,
    init() {
        if (this.inited) return;
        this.inited = !0;
        this.logStatCol = db.createLogStatModel();
        // 所有的动态生成col都要放到这里面来，避免重复new
        this.cols = {};
        this.event = {};
        // 注册事件和对应的处理方法
        this.event['report_1001'] = this.deal_report_1001;
        this.event['buy_gift'] = this.buyGift;

    },

    async setOpen(_id, open) {
        if (!_id) return response.ERROR_WITH_DESC_NO_RESULT('数据错误!');
        _id = ObjectId(_id);
        let n = await this.logStatCol.findOneAndUpdate({_id}, {open}, {upsert: false, new: true});
        return response.SUCCESS_WITH_RESULT(n);
    },
    async dynamicCreateCol(con, type) {
        let where = {};
        type === 1 && (where._id = ObjectId(con));
        type === 2 && (where.name = con);

        let doc = await this.logStatCol.findOne(where);
        if (!doc) return false; //查不到数据
        if (!doc.config) return false; // 数据是有问题的
        if (this.cols[doc.name]) return this.cols[doc.name];
        let config = JSON.parse(doc.config);
        let sc = {}, haseTs = false;
        for (let {name, type, def, index} of config) {
            if (name === 'ts' || name === 'timestamp') haseTs = true;
            sc[name] = {type, default: def}
            index && (sc[name].index = true);
        }
        !haseTs && (sc['ts'] = {type: Number, index: true});
        let col = new mongoose.Schema(sc);
        this.cols[doc.name] = db.statdb.model(doc.name, col);
        return this.cols[doc.name];
    },
    async loadLogData(_id, start, end) {
        if (!_id) return response.ERROR_WITH_DESC_NO_RESULT('数据错误!');
        let model = await this.dynamicCreateCol(_id, 1);
        let where = {
            ts: {
                $gte: start,
                $lte: end
            }
        };
        return response.SUCCESS_WITH_RESULT(await model.find(where, {_id: 0, __v: 0}));
    },


    async onReport({key, body}) {
        if (!key) return;
        await this.event[key](this, key, body);
    },
    // 礼包主动弹窗数据上报
    async deal_report_1001(ins, key, {storeId, type, point}) {
        let col = ins.cols[key];
        if (!col) {
            col = await ins.dynamicCreateCol(key, 2);
            ins.cols[key] = col;
        }
        if (!col) return;
        let saver = new col({storeId, type, point, ts: Date.now()});
        await saver.save();
    },
    async buyGift(ins, key, {
        giftId,
        price,
        resetNum,
        startDay,
        uid,
        lang,
        heart,
        biscuits,
        candy,
        windmill,
        loginDay,
        registerDay,
        totalPay
    }) {


    }

}

module.exports = curMod;
