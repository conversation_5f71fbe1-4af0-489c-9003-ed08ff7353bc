const userMod = require("./userMod");
const recordMod = require("./recordMod");
const db = require("../db/db");
const rankMod = require("./rankMod");
const { default: util } = require("../common/util");
const { FriendRewardType } = require("../common/constant");
const partyMod = require("./partyMod");
const gardenMod = require("./gardenMod");
const partyPlusMod = require("./partyPlusMod");
const logger = require("../common/log").getLogger('friendMod');

const RECOMMEND_COUNT = 5// 推荐列表数量
const MAX_FRIEND_COUNT = 30// 最大好友数量
const MAX_APPLY_COUNT = 20// 最大申请数量

const SUCCESS = 0
const UID_EQUALITY = 1001// uid 相同
const FRIEND_REAPPLY = 1002// 重复请求
const FRIEND_ALREADY = 1003// 已是好友
const FRIEND_LIMIT = 1004// 好友数量已达上限
const USER_NOT_FOUND = 1005// 用户不存在
const FAVORABILITY_CONFIG = [10, 20, 30, 50, 100]// 陌生 普通 友好 亲密 挚友

let redis
let friendCol
let applyCol
let rewardCol
let favorabilityCol
let friendLikeRecordCol

let friendMod = {

    async init() {
        redis = db.redis
        friendCol = db.createFriendModel()
        applyCol = db.createApplyModel()
        rewardCol = db.createFriendRewardModel()
        favorabilityCol = db.createFavorabilityModel()
        friendLikeRecordCol = db.createFriendLikeRecordModel()

        // -------------------------------- 测试代码-填充本地排行榜数据 --------------------------------
        // let userCol = db.createUserModel()
        // let users = await userCol.find()
        // for (let i = 0; i < users.length; i++) {
        //     let key = rankMod.getRankName(users[i].serverId)
        //     let record = await recordMod.getRecord(users[i].uid)
        //     let success = await rankMod.updateScore(key, users[i].uid, recordMod.getStarSum(record), users[i].nickName, users[i].avatarUrl); //上传分数
        //     if (success) {
        //         logger.info(`serverId:${users[i].serverId} update score success`)
        //     }
        // }

        // for (let i = 0; i < 50; i++) {
        //     let list = await redis.zrevrange(rankMod.getRankName(i), 0, 49)
        //     logger.info(`i=${i} list length:${list.length}`)
        // }
        // -------------------------------- 测试代码-填充本地排行榜数据 --------------------------------

        // userMod.copyTop()
    },

    async findFriendCol(uid) {
        const ary = await friendCol.find({ $or: [{ uid }, { toUid: uid }] })
        // ary去重
        const tmp = []
        for (const doc of ary) {
            const is = tmp.find(m => {
                return m.uid == doc.uid && m.toUid == doc.toUid
            })
            if (!is) tmp.push(doc)
        }
        return tmp
    },

    // 获取好友列表
    async getFriendList(uid, detail = true) {
        let list = await this.findFriendCol(uid)
        let size = list.length
        for (let i = 0; i < size; i++) {
            list[i] = { uid: list[i].uid == uid ? list[i].toUid : list[i].uid }
        }

        return detail ? await Promise.all(list.map(m => friendMod.suppUserData(m.uid, uid))) : list
    },

    // 获取好友数量
    async getFriendCount(uid) {
        let list = await this.findFriendCol(uid)
        return list.length
    },

    // 是否好友关系
    async getIsFriend(uid, toUid) {
        let doc = await friendCol.findOne({ $or: [{ uid, toUid }, { uid: toUid, toUid: uid }] })
        return doc != null
    },

    // 获取推荐列表
    async getRecommendList(uid, heart, serverId) {
        let range = null
        let cout = Math.floor(100 + Math.random() * 200)// 随机推荐范围
        let rank = await rankMod.getRank(rankMod.getRankName(serverId), uid)// 玩家排名
        if (rank == null) {// 无排名
            range = await redis.zrevrange(rankMod.getRankName(serverId), 0, cout) || []// 从 TOP{x} 中随机
        } else {
            range = await redis.zrevrange(rankMod.getRankName(serverId), Math.max(rank - cout, 0), rank + cout) || []// 从前后 {x} 名中随机
        }
        // 好友列表, 已申请列表
        let [friends, applys] = await Promise.all([friendMod.getFriendList(uid, false), applyCol.find({ toUid: uid }, { uid: 1 })])

        let list = []
        let pool = util.randomArray(range)// 预选池
        pool.length = RECOMMEND_COUNT * 2

        await util.promiseMap(pool, async (item) => {
            if (!item) return
            if (item == uid) {// 自己不加入推荐列表
                return
            }
            if (friends.find(friend => friend.uid == item)) return // 已是好友则不加入推荐列表
            if (applys.find(apply => apply.uid == item)) return // 已向Ta申请过则不加入推荐列表

            if (await friendMod.getFriendCount(item) >= MAX_FRIEND_COUNT) {// Ta的好友已达上限则不加入推荐列表
                return
            }

            if (list.length >= RECOMMEND_COUNT) {
                return
            }
            list.push(item)
        })

        return await Promise.all(list.map(m => friendMod.suppUserData(m)))
    },

    // 获取申请列表
    async getApplyList(uid, detail = true) {
        let list = await applyCol.find({ toUid: uid }, { uid: 1 })
        return detail ? await Promise.all(list.map(m => friendMod.suppUserData(m.uid, uid))) : list
    },

    // 申请成为好友
    async applyFriend(uid, toUid) {
        if (uid == toUid) {
            return UID_EQUALITY
        }

        // let doc = await applyCol.findOne({ uid, toUid })
        // if (doc) {// 重复申请
        //     return SUCCESS
        //     // return FRIEND_REAPPLY
        // }

        if (await friendMod.getIsFriend(uid, toUid)) {// 已是好友
            return FRIEND_ALREADY
        }

        doc = await applyCol.findOne({ uid: toUid, toUid: uid })
        if (doc) {// 互相申请成为好友则直接建立好友关系
            return await friendMod.agreeApplyFriend(uid, toUid)
        } else {
            await applyCol.updateOne({ uid, toUid }, { date: Date.now() }, { upsert: true })
        }

        let list = await applyCol.find({ uid })// 已申请列表
        if (!robotMod.isRobot(uid) && list.length >= MAX_APPLY_COUNT) {// 申请数量超过上限
            await applyCol.deleteOne({ uid: list[0].uid })
        }
        if (robotMod.isRobot(toUid)) {
            // 机器人默认同意
            friendMod.agreeApplyFriend(toUid, uid)
            await robotMod.simulateOnline(toUid)
        }
        // // 测试代码，被申请人默认同意
        // friendMod.agreeApplyFriend(toUid, uid)

        return SUCCESS
    },

    // 一键申请好友
    async applyAllFriend(uid, list) {
        await Promise.all(list.map(m => friendMod.applyFriend(uid, m)))
        return SUCCESS
    },

    // 同意成为好友
    async agreeApplyFriend(uid, toUid) {
        await applyCol.deleteMany({ $or: [{ uid, toUid }, { uid: toUid, toUid: uid }] })
        if ((!robotMod.isRobot(uid) && await friendMod.getFriendCount(uid) >= MAX_FRIEND_COUNT) ||
            (!robotMod.isRobot(toUid) && await friendMod.getFriendCount(toUid) >= MAX_FRIEND_COUNT)) {// 好友数量超过上限
            return FRIEND_LIMIT
        }
        await friendCol.updateOne({ $or: [{ uid, toUid }, { uid: toUid, toUid: uid }] }, {
            uid,
            toUid,
            date: Date.now()
        }, { upsert: true })

        return SUCCESS
    },

    // 拒绝成为好友
    async rejectApplyFriend(uid, toUid) {
        await applyCol.deleteOne({ $or: [{ uid, toUid }, { uid: toUid, toUid: uid }] })
        return SUCCESS
    },

    // 清空好友
    async clearFriends(uid) {
        let refs = await friendCol.find({ uid })
        if (!refs) return void 0;
        for (let { uid, toUid } of refs) {
            await this.deleteFriend(uid, toUid);
        }
    },

    // 删除好友
    async deleteFriend(uid, toUid) {
        await friendCol.deleteOne({ $or: [{ uid, toUid }, { uid: toUid, toUid: uid }] })
        let favor = await friendMod.getFavorability(uid, toUid)
        await friendMod.setFavorability(uid, toUid, Math.max(favor.exp - 5, 0), favor.level)// 删除好友扣除5点好感度

        return SUCCESS
    },

    // 同步好感度配置
    async syncFavorabilityConfig() {
        return { status: SUCCESS, config: FAVORABILITY_CONFIG }
    },

    // 增加好感度
    async addFavorability(uid, toUid, exp) {
        let favor = await friendMod.getFavorability(uid, toUid)
        if (favor.level >= FAVORABILITY_CONFIG.length - 1) {// 已是最高等级
            await friendMod.setFavorability(uid, toUid, favor.exp + exp, favor.level)
        } else {
            favor.exp += exp
            if (favor.exp >= FAVORABILITY_CONFIG[favor.level]) {
                favor.exp -= FAVORABILITY_CONFIG[favor.level]
                favor.level++
            }
            await friendMod.setFavorability(uid, toUid, favor.exp, favor.level)
        }

        return SUCCESS
    },

    // 获取好友互动奖励数量
    async getFriendRewardCount(uid, type = FriendRewardType.LUCKY_BAG) {
        let list = await rewardCol.countDocuments({ toUid: uid, type: { $eq: type } })
        return { status: SUCCESS, count: list.length }
    },

    // 获取好友互动奖励列表
    async getFriendRewardList(uid, type = FriendRewardType.LUCKY_BAG) {
        let list = await rewardCol.find({ toUid: uid, type: { $eq: type } })
        list = await Promise.all(list.map(m => friendMod.suppUserNickName(m)))

        return { status: SUCCESS, list: list }
    },

    // 增加好友互动奖励
    async addFriendReward(uid, toUid, reward, type = FriendRewardType.LUCKY_BAG) {
        let list = await rewardCol.find({ toUid })

        if (type == FriendRewardType.LUCKY_BAG) {
            if (list.length >= 10) {// 累积奖励已达上限
                return SUCCESS
            }
        }

        await rewardCol.updateOne({ uid, toUid, date: Date.now(), type: { $eq: type } }, { reward }, { upsert: true })
        return SUCCESS
    },

    // 领取好友互动奖励
    async receiveFriendReward(uid, date, type = FriendRewardType.LUCKY_BAG) {
        await rewardCol.deleteMany({ toUid: uid, date: { $lte: date }, type: { $eq: type } })
        return SUCCESS
    },

    // 查找用户
    async searchUser(uid, toUid) {
        if (await friendMod.getIsFriend(uid, toUid)) {// 已是好友
            return { status: FRIEND_ALREADY, user: await friendMod.suppUserData(toUid) }
        } else {
            let doc = await userMod.findOne(toUid)
            if (doc) {
                if (!robotMod.isRobot(toUid) && await friendMod.getFriendCount(toUid) >= MAX_FRIEND_COUNT) {// 好友数量已达上限
                    return { status: FRIEND_LIMIT, user: await friendMod.suppUserData(toUid) }
                } else {
                    return { status: SUCCESS, user: await friendMod.suppUserData(toUid) }
                }
            } else {
                return { status: USER_NOT_FOUND, user: null }
            }
        }
    },

    // 获取好感度
    async getFavorability(uid, toUid) {
        let favor = await favorabilityCol.findOne({ $or: [{ uid, toUid }, { uid: toUid, toUid: uid }] })
        if (favor) {
            return { exp: favor.exp, level: favor.level }
        }

        return { exp: 0, level: 0 }
    },

    // 设置好感度
    async setFavorability(uid, toUid, exp, level) {
        await favorabilityCol.updateOne({ $or: [{ uid, toUid }, { uid: toUid, toUid: uid }] }, {
            uid,
            toUid,
            exp,
            level
        }, { upsert: true })
    },

    // 补全用户昵称
    async suppUserNickName(doc) {
        let nickName = null
        let userDoc = await userMod.getUserCacheInfo(doc.uid)
        if (userDoc) {
            nickName = userDoc.nickName
        }

        return { uid: doc.uid, nickName, reward: doc.reward, date: doc.date }
    },

    // 补全用户数据-头像&昵称&蜡烛
    // 派对版本-补全派对举办信息
    async suppUserData(uid, myUid = null) {
        let userDoc = await userMod.findOne(uid)
        if (!userDoc) {
            await this.deleteFriend(myUid, uid)
            return null
        }
        // 好友是不是在筹备派对
        let partyV1 = await partyMod.isPlayerStartParty(uid)
        if (partyV1 && myUid) {
            let mine = await userMod.findOne(myUid)
            let partyJoin = mine.partyJoin || []
            // 如果自己参加的派对数量满了，发回好友的派对状态 false
            if (partyJoin && partyJoin.length === partyMod.max_join_party_num) partyV1 = ""
            // 如果自己已经参加了该派对，发回好友的派对状态 false
            partyJoin.includes(partyV1.toString()) && (partyV1 = "")
        }
        // 好友是不是开启游园会
        let gardenParty = await gardenMod.isPlayerStartParty(uid)
        if (gardenParty && myUid) {
            let mine = await userMod.findOne(myUid)
            let partyJoin = mine.gardenPartyJoin || []
            // 如果自己参加的游园会数量满了，发回好友的游园会状态 false
            if (partyJoin.length === gardenMod.max_join_party_num) gardenParty = ""
            // 如果自己已经参加了该游园会，发回好友的游园会状态 false
            partyJoin.includes(gardenParty.toString()) && (gardenParty = "")
        }
        let partyPlus = await partyPlusMod.testGetPartyDoc(uid)
        if (partyPlus && myUid) {
            let partyJoin = partyPlus.joiner || []
            const arr = partyJoin.filter(p => p.uid === myUid)
            partyPlus = arr.length === 0
        }

        let heart = await recordMod.getHeartByUid(uid, userDoc.serverId)

        let nickName = userDoc.diyName || userDoc.nickName
        let avatarUrl = userDoc.diyAvatar || userDoc.avatarUrl
        let lastLoginTime = userDoc.loginTime || 0

        const likeNum = userDoc.likeNum || 0
        const isParty = !!partyV1
        const isGardenParty = !!gardenParty
        const isPartyV2 = !!partyPlus
        const water = await gardenMod.OnPlayerAction(uid, { uid: myUid, type: 1 }, true)
        const fertilize = await gardenMod.OnPlayerAction(uid, { uid: myUid, type: 2 }, true)
        if (myUid) {
            let faovr = await friendMod.getFavorability(uid, myUid)
            return {
                uid,
                nickName,
                avatarUrl,
                heart,
                likeNum,
                isParty,
                isGardenParty,
                isPartyV2,
                water,
                fertilize,
                exp: faovr.exp,
                level: faovr.level,
                lastLoginTime
            }
        }
        return {
            uid,
            nickName,
            avatarUrl,
            heart,
            likeNum,
            isParty,
            isGardenParty,
            isPartyV2,
            water,
            fertilize,
            lastLoginTime
        }
    },
    // 好友点赞
    async likeUser(uid, target) {
        uid = uid.replace(/\s+/g, "")
        target = target.replace(/\s+/g, "")
        if (!uid || !target) return -1
        const self = await userMod.findOne(uid)
        const to = await userMod.findOne(target)
        if (!self || !to) return -1
        const todaySixTime = util.getTodayZeroTime(6)
        const day = util.getNumberDay(todaySixTime)
        // uid本人今天是否给target赞过 LIKE_19165_A => B|C
        let rdm = await redis.get(`LIKE_${day}_${uid}`) || ""
        if (rdm) {
            const _ = rdm.split("|")
            if (_.length && _.indexOf(target) > -1) return -2
            rdm += `|${target}`
            await redis.set(`LIKE_${day}_${uid}`, rdm)
        } else {
            rdm = `${target}`
            const now = new Date();
            // 获取明天凌晨6:00的时间
            const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 6, 0, 0);
            // 计算当前时间到明天凌晨6:00的毫秒数
            const expirationTime = tomorrow.getTime() - now.getTime();
            await redis.set(`LIKE_${day}_${uid}`, rdm, "PX", expirationTime)
        }
        // 保存点赞信息
        await userMod.updateOne({ uid: target }, { $inc: { likeNum: 1 } })
        await friendLikeRecordCol.updateOne({ uid: target }, {
            $push: {
                record: {
                    $each: [{ uid, time: Date.now() }],
                    $sort: { time: -1 },
                    $slice: 30,
                }
            }
        }, { upsert: true })
        return 0
    },
    async getLikeRecord(uid) {
        uid = uid.replace(/\s+/g, "")
        if (!uid) return []
        const doc = await friendLikeRecordCol.findOne({ uid })
        const arr = []
        if (doc && doc.record) {
            for (const { uid, time } of doc.record) {
                const self = await userMod.findOne(uid)
                if (!self) continue
                let myName = self.diyName || self.nickName || ""
                let avatar = self.diyAvatar || self.avatarUrl || ""
                arr.push({
                    uid,
                    time,
                    name: myName,
                    avatar,
                })
            }
        }
        return arr
    },
}

module.exports = friendMod;

const robotMod = require("./robotMod");
