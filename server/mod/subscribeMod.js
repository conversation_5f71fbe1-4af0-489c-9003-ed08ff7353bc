const util = require("../common/util").default;
const userMod = require("./userMod");
const logger = require("../common/log").getLogger("subscribeMod");
const {USER_TYPE, Platform, SubscribeType, SubscribeMsg} = require("../common/constant");
const wxMod = require("./wxMod");
const qqMod = require("./qqMod");
const db = require("../db/db");
const redisKey = require("../db/redisKey")
const googleMod = require("./googleMod")
const appleLoginMod = require("./appleLoginMod")
const recordMod = require("./recordMod")
const {assetsMgr} = require("../client/AssetsMgr");
const config = require("../config")
const {async} = require("regenerator-runtime");

const MAX_CHECK_COUNT = 1000

let subscribeMod = {

    redis: null,
    subscribeCol: null,

    init() {
        this.redis = db.redis
        this.subscribeCol = db.createSubscribeModel()
        this.broadcastCol = db.createBroadcastModel()
        this.deviceTokenCol = db.createDeviceToken()
    },

    async subscribe(info) {
        if (info.type) { //兼容老版本
            info.platform = info.type
            delete info.type
        }
        let where = {
            uid: info.uid,
            msgType: info.msgType,
            platform: info.platform,
        }
        return await this.subscribeCol.updateOne(where, info, {upsert: true})
    },

    async delSub(uid, msgType, platform) {
        if (!uid) {
            logger.error("delSub error uid is null")
            return
        }
        let where = {uid}
        if (msgType) where.msgType = msgType
        if (platform) where.platform = platform
        await this.subscribeCol.deleteMany(where)
    },

    async findSub(uid, msgType, platform) {
        let where = {uid}
        if (msgType) where.msgType = msgType
        if (platform) where.platform = platform
        return await this.subscribeCol.findOne(where)
    },

    async updateSub(uid, msgType, platform, info) {
        let where = {uid}
        if (msgType) where.msgType = msgType
        if (platform) where.platform = platform
        return await this.subscribeCol.updateMany(where, info)
    },

    //把通知时间设为当前，之后几秒会进行通知
    async notifyNow(uid, msgType, platform) {
        await this.updateSub(uid, msgType, platform, {notifyTime: Date.now()})
    },

    async notify({uid, template_id, data, userType, platform, msgType, taskId}) {
        try {
            if (msgType === SubscribeType.BI_INITIATIVE_PUSH) {
                await this.increaseNotifyCount(taskId, 1);
            }
            let succ
            if (taskId) {
                let exist = await this.redis.sismember(redisKey.subscribeTaskFlag(taskId), uid)
                if (exist) {
                    await subscribeMod.delSub(uid, msgType, platform || userType)
                    return
                }
            }
            if (!config.debug || msgType !== SubscribeType.BI_INITIATIVE_PUSH) {
                let lastNotifyTime = await this.redis.get(redisKey.subscribeCD(uid)) //30分钟cd
                if (lastNotifyTime && Date.now() - Number(lastNotifyTime) < 30 * util.Time.Minute) {
                    logger.debug("notify in cd", uid, msgType, Date.now() - Number(lastNotifyTime))
                    await subscribeMod.delSub(uid, msgType, platform || userType)
                    return
                }
            }

            let isOnline = await userMod.isOnline(uid)
            if (isOnline) {
                logger.warn("notify online", uid, msgType)
                await subscribeMod.delSub(uid, msgType, platform || userType)
                return
            }

            if (userType == USER_TYPE.QQ || platform == Platform.QQ) {
                let userDoc = await userMod.findOne(uid)
                if (!userDoc || !userDoc.openid) {
                    logger.error("qq notify uid not found", uid)
                    await subscribeMod.delSub(uid, msgType, platform || userType)
                    return
                }
                succ = await qqMod.sendSubscribeMessage(userDoc.openid, template_id, JSON.parse(data), uid)
            } else if ((userType == USER_TYPE.WX && !platform) || platform == Platform.WX) {
                let userDoc = await userMod.findOne(uid)
                if (!userDoc || !userDoc.openid) {
                    logger.error("wx notify uid not found", uid)
                    await subscribeMod.delSub(uid, msgType, platform || userType)
                    return
                }
                if (msgType == SubscribeType.FILM_AWARD) {
                    let {title, msg} = this.getMsgFromMsgType("film_rowpiece", "cn")
                    data = {
                        "thing1": {
                            "value": title,
                        },
                        "thing3": {
                            "value": msg,
                        },
                    }
                    data = JSON.stringify(data)
                }
                succ = await wxMod.sendSubscribeMessage(userDoc.openid, template_id, JSON.parse(data), uid)
            } else if (platform == Platform.IOS) {
                succ = await appleLoginMod.sendSubscribeMessage(data, msgType, uid)
            } else if (platform == Platform.ANDROID) {
                succ = await googleMod.sendSubscribeMessage(data, msgType, uid)
            } else {
                logger.error("notify platform not found", uid, msgType, platform)
            }
            if (succ) {
                logger.info("notify succ", uid, msgType, platform)
                if (taskId) {
                    await this.redis.sadd(redisKey.subscribeTaskFlag(taskId), uid)
                    this.redis.pexpire(redisKey.subscribeTaskFlag(taskId), util.Time.Day * 15)
                }
                // BI 推送不限制CD
                if (msgType !== SubscribeType.BI_INITIATIVE_PUSH) {
                    await this.redis.set(redisKey.subscribeCD(uid), Date.now())
                    this.redis.pexpire(redisKey.subscribeCD(uid), 30 * util.Time.Minute)

                    await this.redis.set(redisKey.subscribeLast(uid), JSON.stringify({msgType, platform}))
                    this.redis.pexpire(redisKey.subscribeLast(uid), 10 * util.Time.Minute)
                } else {
                    await this.increaseFinishCount(taskId);
                }
            }

        } catch (error) {
            logger.error("notify fail", uid, template_id, error)
        }

        await subscribeMod.delSub(uid, msgType, platform || userType)
    },

    async check() {
        let now = Date.now()
        let docs = await this.subscribeCol.find({notifyTime: {$lte: now}}).sort("-notifyTime").limit(MAX_CHECK_COUNT)
        await util.promiseMap(docs, async (doc) => {
            let {uid, msgType, expireTime, platform} = doc
            expireTime = expireTime || 10 * util.Time.Minute
            doc.msgType && doc.msgType === SubscribeType.BI_INITIATIVE_PUSH && (expireTime = expireTime * 200);
            if (now - doc.notifyTime < expireTime) {
                await this.notify(doc)
                if (doc.msgType === SubscribeType.BI_INITIATIVE_PUSH) {
                    // 推送完成检查
                    // let _c = await this.getBroadcastRemainingCount(doc.taskId);
                    // _c === 0 && (await this.broadcastCol.updateOne({taskId: doc.taskId}, {state: 3}));
                }
            } else {
                logger.warn("sub expire", uid, msgType)
                await subscribeMod.delSub(uid, msgType, platform)
            }
        })

        if (docs.length >= MAX_CHECK_COUNT) {
            return this.check()
        } else {
            await util.wait(5 * util.Time.Second)
            return this.check()
        }
    },
    async dealData(uid, token, platform, title, msgType, notifyTime, taskId, getMsgByLang, msg = '', verLimit = {}, loginTimeLimit = {}) {
        // let exist = await this.redis.sismember(redisKey.subscribeTaskFlag(taskId), uid);
        // if (exist) return
        if (msgType === SubscribeType.BI_INITIATIVE_PUSH) {
            await this.increaseNotifyDealCount(taskId, 1);
        }
        let userDoc = await userMod.findOne(uid)
        if (!userDoc) {
            logger.warn("user not found", uid);
            await this.deviceTokenCol.deleteOne({uid});
            return
        }
        // 登录时间限制
        if (Object.keys(loginTimeLimit).length) {
            if (loginTimeLimit.min && loginTimeLimit.min >= userDoc.loginTime) {
                return;
            }
            if (loginTimeLimit.max && loginTimeLimit.max <= userDoc.loginTime) {
                return;
            }
        }
        let gameVer = '';
        Object.keys(verLimit).length && (gameVer = await userMod.getLoginVer(uid));
        // 版本限制
        if (gameVer && !util.str_checker(gameVer, verLimit)) {
            return;
        }
        let lang = userDoc.lang
        let _l = lang && lang !== 'none' && getMsgByLang && getMsgByLang(lang);
        msg = msg || (_l && _l.content);
        title = title || (_l && _l.title);
        let data = JSON.stringify({
            title: title,
            msg,
            token
        });
        let expireTime = 0;
        await subscribeMod.subscribe({uid, data, platform, msgType, notifyTime, expireTime, taskId})
        //TODO 和动餐的细节上不一样，要注意msgType openid taskId 在两个后台上字段不一样
        await this.increaseReadyCount(taskId);
    },
    async broadcastBiForOne(taskId, msgType, title, uid, content, notifyTime) {
        try {
            let doc = await this.deviceTokenCol.findOne({uid});
            if (!doc) return;
            await this.recordTokenLine(taskId, 1);
            await this.dealData(doc.uid, doc.token, doc.platform, title, msgType, notifyTime, taskId, null, content);
            await this.changeBroadcastState(taskId, 1);
        } catch (error) {
            logger.warn(`broadcastBiForOne was happened error: ${error}`);
        }
    },
    async broadcastBiForAll(taskId, msgType, info, notifyTime, verLimit, loginTimeLimit) {
        let len = 0, tot = 0
        let getMsgByLang = (langType = 'en') => {
            for (let _i of info) {
                if (_i.lang === langType) {
                    return _i;
                }
            }
            return '';
        };
        let count = await this.deviceTokenCol.countDocuments();
        // 记录可能要处理的用户总数
        await this.recordTokenLine(taskId, count);
        let work = async (_id) => {
            let where = {};
            _id && (where._id = {$lt: _id});
            let docs = await this.deviceTokenCol.find(where).sort("-_id").limit(10000)
            tot += docs.length
            await util.promiseMap(docs, async ({uid, token, platform}) => {
                try {
                    await this.dealData(uid, token, platform, "", msgType, notifyTime, taskId, getMsgByLang, "", verLimit, loginTimeLimit);
                    len++
                } catch (error) {
                    logger.error("broadcast error", error)
                }
            })
            if (docs.length > 0) {
                return work(docs[docs.length - 1]._id)
            }
        }
        await work()
        await this.changeBroadcastState(taskId, 1);
    },

    //目前只用于native, 要用到的时候直接跑
    async broadcast(taskId, msgType, delayTime = 0) {
        if (!taskId || !msgType) {
            console.warn("broadcast fail", taskId, msgType)
            return
        }
        let len = 0, tot = 0
        let work = async (_id) => {
            let where = {}
            if (_id) {
                where = {_id: {$lt: _id}}
            }
            let docs = await this.deviceTokenCol.find(where).sort("-_id").limit(10000)
            tot += docs.length
            await util.promiseMap(docs, async ({uid, token, platform}) => {
                try {
                    let exist = await this.redis.sismember(redisKey.subscribeTaskFlag(taskId), uid)
                    if (exist) return
                    let userDoc = await userMod.findOne(uid)
                    if (!userDoc) {
                        console.warn("user not found", uid)
                        await this.deviceTokenCol.deleteOne({uid})
                        return
                    }
                    len++
                    let lang = userDoc.lang
                    let {msg, title} = this.getMsgFromMsgType(msgType, lang)
                    let data = JSON.stringify({title, msg, token})
                    let notifyTime = Date.now() + delayTime
                    let expireTime = util.DATE_TIME
                    await subscribeMod.subscribe({uid, data, platform, msgType, notifyTime, expireTime, taskId})
                } catch (error) {
                    console.error("broadcast error", error)
                }
            })

            console.log("broadcast...", len, tot)

            if (docs.length > 0) {
                return work(docs[docs.length - 1]._id)
            }
        }
        await work()
        console.log("broadcast done")
    },

    async cancelBroadcast(taskId) {
        await this.subscribeCol.deleteMany({taskId})
        await this.redis.del(redisKey.subscribeTaskFlag(taskId))
    },

    getMsgFromMsgType(msgType, lang, params) {
        let key = msgType.toLowerCase()
        let msg = assetsMgr.lang(`share.${key}_desc`, lang)
        let title = assetsMgr.lang(`share.${key}`, lang)
        return {msg, title}
    },

    async subOfflineAward(uid, userDoc) {
        try {
            let docs = await this.deviceTokenCol.find({uid})
            if (docs.length <= 0) return
            let record = await recordMod.getRecord(uid)
            let offlineTimeAttr = recordMod.getFromRecord("main.mainAttr.offlineTimeAttr", record)
            if (offlineTimeAttr) {
                let attrId = offlineTimeAttr.id * 1000 + offlineTimeAttr.lv
                let json = assetsMgr.getJsonData('hostelAttr', attrId)
                const arr = json ? util.stringToNumbers(json.value, ',') : [0, 0]
                let offlineTime = arr[0] || 0
                if (offlineTime) {
                    userDoc = userDoc || await userMod.findOne(uid)
                    if (!userDoc) return
                    let msgType = SubscribeType.OFFLINE_AWARD
                    let list = [
                        {key: "offline_award", weight: 10},
                        {key: "wudong_stamina", weight: 2},
                    ]
                    let heart = recordMod.getStarSum(record)
                    if (heart >= 60) {
                        list.push({key: "taitan_stamina", weight: 2})
                    }
                    if (heart >= 280) {
                        list.push({key: "paopao_stamina", weight: 2})
                    }
                    let key = list[util.randomByWeight(list)].key
                    let lang = userDoc.lang
                    let msg = assetsMgr.lang(`share.${key}_desc`, lang)
                    let title = assetsMgr.lang(`share.${key}`, lang)
                    for (let {platform, token} of docs) {
                        let data = JSON.stringify({title, msg, token})
                        let notifyTime = Date.now() + offlineTime * util.Time.Hour
                        if (config.debug) {
                            notifyTime = Date.now() + offlineTime * util.Time.Minute
                        }
                        await this.subscribe({uid, msgType, platform, notifyTime, data})
                    }
                }
            }
        } catch (error) {
            logger.error("subOfflineAward", uid, error)
        }
    },

    async cancelOfflineAward(uid) {
        await this.delSub(uid, SubscribeType.OFFLINE_AWARD)
    },

    lostTimes: [
        {type: SubscribeType.LOST_12_HOUR, time: 12},
        {type: SubscribeType.LOST_28_HOUR, time: 28},
        {type: SubscribeType.LOST_72_HOUR, time: 72},
        {type: SubscribeType.LOST_168_HOUR, time: 168},
    ],

    async subLostRecall(uid, userDoc) {
        try {
            let docs = await this.deviceTokenCol.find({uid})
            if (docs.length <= 0) return
            userDoc = userDoc || await userMod.findOne(uid)
            if (!userDoc) return
            let now = Date.now()
            let list = [
                {key: "lost_12_hour", weight: 1},
                {key: "lost_28_hour", weight: 1},
                {key: "lost_72_hour", weight: 1},
                {key: "lost_168_hour", weight: 1},
            ]
            list = util.randomArray(list)
            for (let {platform, token} of docs) {
                await util.promiseMap(this.lostTimes, async ({type, time}, index) => {
                    let waitTime = time * util.Time.Hour
                    if (config.debug) {
                        waitTime = time * util.Time.Second * 10
                    }
                    let notifyTime = now + waitTime

                    let key = list[index].key
                    let lang = userDoc.lang
                    let msg = assetsMgr.lang(`share.${key}_desc`, lang)
                    let title = assetsMgr.lang(`share.${key}`, lang)
                    let data = JSON.stringify({title, msg, token})
                    await this.subscribe({uid, msgType: type, platform, notifyTime, data})
                })
            }
        } catch (error) {
            logger.error("subLostRecall", uid, error)
        }
    },

    async cancelLostRecall(uid) {
        await util.promiseMap(this.lostTimes, async ({type, time}) => {
            await this.delSub(uid, type)
        })
    },
    // 保存BI添加的推送信息
    async saveBroadcast_BI(taskId, type, notifyTime, msgType, verLimit, loginTimeLimit, uid, title, content,) {
        let timestamp = Date.now();
        let broadcast = {
            taskId,
            type,
            notifyTime,
            msgType,
            timestamp,
            state: 0,
            readyCount: 0,
            finishCount: 0,
            notifyCount: 0,
        }
        type === "all" && (
            broadcast.verLimit = JSON.stringify(verLimit),
                broadcast.loginTimeLimit = JSON.stringify(loginTimeLimit),
                broadcast.content = JSON.stringify(content)
        );
        type === "one" && (
            broadcast.uid = uid,
                broadcast.title = title,
                broadcast.content = content
        );
        let dbCol = new this.broadcastCol(broadcast);
        await dbCol.save();
    },
    // 改变推送任务状态
    async changeBroadcastState(taskId, state = 1) {
        await this.broadcastCol.updateOne({taskId}, {state}, {upsert: false});
    },
    // 记录拥有token的用户数
    async recordTokenLine(taskId, line = 1) {
        await this.broadcastCol.updateOne({taskId}, {docTotalCount: line});
    },
    // 增加readyCount 1
    async increaseReadyCount(taskId, count = 1) {
        await this.broadcastCol.updateOne({taskId}, {$inc: {readyCount: count}}, {upsert: true});
    },
    // 增加notifyDealCount 1
    async increaseNotifyDealCount(taskId, count = 1) {
        await this.broadcastCol.updateOne({taskId}, {$inc: {notifyDealCount: count}}, {upsert: true});
    },
    async increaseNotifyCount(taskId, count = 1) {
        await this.broadcastCol.updateOne({taskId}, {$inc: {notifyCount: count}}, {upsert: true});
    },
    // 增加finishCount 1
    async increaseFinishCount(taskId, count = 1) {
        await this.broadcastCol.updateOne({taskId}, {$inc: {finishCount: count}, state: 2}, {upsert: true});
    },
    // 指定 taskid 剩余多少条还未推送
    async getBroadcastRemainingCount(taskId) {
        let docs = await this.subscribeCol.find({taskId});
        return docs && docs.length || 0;
    },
    // 获取后台推送的列表数据
    async getBroadcastList() {
        // check时检查一下是不是完成了  顺便更新状态
        let docs = await this.broadcastCol.find({});
        await util.promiseMap(docs, async (doc) => {
            if (doc.state === 2) {
                doc.notifyCount && doc.notifyCount >= doc.readyCount - 1
                && (doc.state = 3, await this.broadcastCol.updateOne({taskId: doc.taskId}, {state: 3}));
            }
            if (doc.state === 2) {
                let count = await this.subscribeCol.find({taskId: doc.taskId}).count();
                count == 0 && (doc.state = 3, await this.broadcastCol.updateOne({taskId: doc.taskId}, {state: 3}));
            }
        })
        return docs;
    },
    // 撤销任务  用task来撤销，可能不够精确
    async removeTargetTaskIdSubscribe(taskId) {
        await this.changeBroadcastState(taskId, 4);
        await this.subscribeCol.deleteMany({taskId});
        await this.changeBroadcastState(taskId, 5);
    },
}

module.exports = subscribeMod;