/**
 * 不管玩家用什么账号登录，游戏中逻辑始终使用自建id
 * 本模块提供三方id到自建id的映射
 */

const logger = require("../common/log").getLogger("idMod");
let uuid = require("uuid");
const userMod = require("./userMod");
let idMapCol, redis;

let idMod = {

    init() {
        let db = require("../db/db");
        idMapCol = db.createIdMapModel();
        redis = db.redis;
    },

    async getID(tpid) {
        if (tpid == null) return;
        let doc = await this.getFromDB(tpid);
        if (doc) {
            return doc.uid
        }
    },

    async bindID(tpid, uid) {
        if (!await userMod.isUserExits(uid)){
            logger.warn('user is not exits.');
            return;
        }
        await idMapCol.create({tpid, uid});
    },

    async reBindID(tpid, uid, upsert = false) {
        if (!await userMod.isUserExits(uid)){
            logger.warn('user is not exits.');
            return
        }
        await idMapCol.updateOne({tpid}, {uid}, {upsert: upsert});
    },

    genUID() {
        return uuid.v4();
    },

    async getFromDB(tpid) {
        let doc = await idMapCol.findOne({ tpid });
        return doc
    },

    async removeByTpid(tpid) {
        if (tpid == null) return
        await idMapCol.deleteOne({tpid})
    }
}

module.exports = idMod;