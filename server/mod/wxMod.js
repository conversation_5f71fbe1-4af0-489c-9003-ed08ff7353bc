const got = require('got');
const util = require("../common/util").default;
const config = require("../config");
const logger = require("../common/log").getLogger("wxMod");
const verror = require("verror")
const WXBizDataCrypt = require("../cypto/WXBizDataCrypt");
const db = require('../db/db');
const redisKey = require('../db/redisKey');
const global = require("../common/global")
const LABEL = {
    100: "正常",
    10001: "广告",
    20001: "时政",
    20002: "色情",
    20003: "辱骂",
    20006: "违法犯罪",
    20008: "欺诈",
    20012: "低俗",
    20013: "版权",
    21000: "其他"
}
let wxMod = {
    access_token: null,
    tokenQueue: [],
    refreshTokenHandle: null,

    getTokenCount: 0,
    lastRefreshTime: 0,

    async getLoginInfo(code, encryptedData, iv) {
        let info = await wxMod.code2Session(code);
        let {session_key, unionid, openid} = info;
        await db.redis.set(redisKey.wxSessionKey(openid), session_key)
        if (!unionid) {
            if (session_key && encryptedData && iv) {
                try {
                    let data = wxMod.decryptData(encryptedData, iv, session_key);
                    info.nickName = data.nickName;
                    info.avatarUrl = data.avatarUrl;
                    info.unionid = data.unionId;
                } catch (error) {
                    logger.warn("decryptData fail: ", openid, encryptedData, iv, session_key);
                }
            }
        }

        return info;
    },

    getConfig() {
        return config.wx;
    },

    //这个接口必须只能中控服来调用
    async getAccessToken(retry = 10) {
        console.log("getAccessToken")
        let passTime = Date.now() - this.lastRefreshTime
        if (passTime < 30 * util.Time.Minute) {
            console.log("getAccessToken too fast", passTime)
            return
        }
        //for test
        // await util.wait(500)
        // return {access_token: this.testToken, expires_in: 3000}
        const {appid, secret} = this.getConfig();
        const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`;
        const data = await got(url, {
            timeout: 10 * util.Time.Second,
        }).json();
        const {errcode, access_token, expires_in} = data;
        if (!errcode) {
            return data
        } else if (errcode == -1 && retry > 0) { //需要重试
            await util.wait(1000);
            return await this.getAccessToken(retry - 1);
        } else {
            logger.error("getToken error", data)
        }
    },

    //这个接口必须只能中控服来调用
    async updateTokenCache() {
        let data = await this.getAccessToken()
        if (data) {
            this.getTokenCount++
            this.lastRefreshTime = Date.now()
            console.log("updateTokenCache", data, this.getTokenCount)
            const {access_token, expires_in} = data;
            let key = redisKey.wxToken()
            await db.redis.set(key, access_token)
            this.access_token = access_token
            if (this.refreshTokenHandle) {
                clearTimeout(this.refreshTokenHandle)
            }
            this.refreshTokenHandle = setTimeout(() => {
                this.refreshToken(this.access_token)
            }, expires_in * util.Time.Second)
            return access_token;
        }
    },

    async getTokenFromCache() {
        if (this.access_token) return this.access_token //先查看本地内存

        let key = redisKey.wxToken() //查看redis
        let redis = db.redis;
        let token = await redis.get(key)
        if (token) {
            this.access_token = token
            return token
        }
    },

    async refreshToken(expireToken) {
        let access_token = await this.getTokenFromCache()
        // console.log("refreshToken", expireToken, access_token)
        if (access_token && access_token != expireToken) {
            return access_token
        }
        let tokenQueue = this.tokenQueue
        let promise = new Promise((resolve) => {
            tokenQueue.push({resolve})
        })
        if (tokenQueue.length > 1) {
            return promise
        }

        let handleQueue = async () => {
            const result = await this.updateTokenCache();
            for (let i = 0; i < tokenQueue.length; i++) {
                tokenQueue[i].resolve(result)
            }
            this.tokenQueue = []
        }

        handleQueue()
        return promise
    },

    //业务逻辑调用,用来通知token过期
    async setTokenExpire(token) {
        let res = await global.requestJobServer("setWxTokenExpire", {token})
        // console.log("setTokenExpire", res)
        if (res) {
            return res.token
        }
    },

    //业务逻辑调用，获取token
    async getToken() {
        let key = redisKey.wxToken()
        let redis = db.redis;
        let token = await redis.get(key)
        if (token) {
            return token
        }

        let res = await global.requestJobServer("getWxToken")
        if (res) {
            return res.token
        }
    },

    decryptData(encryptedData, iv, session_key) {
        let {appid} = this.getConfig();
        let pc = new WXBizDataCrypt(appid, session_key);
        let decryptData = pc.decryptData(encryptedData, iv);
        return decryptData;
    },

    async code2Session(code, retry = 5, useCache = true) {
        let key = redisKey.wxCode2Session(code);
        let data
        if (useCache) {
            data = await db.redis.hgetall(key)
            if (data) {
                return data
            }
        }

        let {appid, secret} = this.getConfig();
        let url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appid}&secret=${secret}&js_code=${code}&grant_type=authorization_code`;
        data = await got(url, {
            timeout: 10 * 1000,
        }).json();
        const {errcode} = data;
        if (!errcode) {
            await db.redis.hmset(key, data)
            db.redis.expire(key, 1 * util.Time.Minute / 1000)
            return data;
        } else if (errcode == -1 && retry > 0) { //需要重试
            await util.wait(1000);
            return await this.code2Session(code, retry - 1, false);
        } else if (errcode == 40163) { //已被使用
            let cache = await db.redis.hgetall(key)
            if (cache) return cache
            throw verror({name: "code2Session error", info: data});
        } else {
            throw verror({name: "code2Session error", info: data});
        }
    },

    async setShareStatus(uid) {
        let key = 'wx_share_' + uid
        let redis = db.redis;
        await redis.set(key, Date.now());
        redis.expire(key, 2.5 * 60);
    },

    async getShareStatus(uid) {
        let key = 'wx_share_' + uid;
        let redis = db.redis;
        let time = await redis.get(key);
        if (time) {
            await redis.del(key)
            return true;
        }
        return false;
    },

    async sendSubscribeMessage(openid, template_id, tpnlData, uid, isRetry, access_token) {
        access_token = access_token || await this.getToken()
        const url = `https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${access_token}`;
        const data = await got.post(url, {
            json: {
                touser: openid,
                template_id: template_id,
                data: tpnlData,
            },
            timeout: 10 * util.Time.Second,
        }).json();
        const {errcode, errmsg} = data
        if (errcode == 0) {
            logger.info("sendSubMsg succ", uid, template_id)
            return true
        } else if (errcode == 43101) { //用户拒绝接受消息，如果用户之前曾经订阅过，则表示用户取消了订阅关系
            logger.warn("sendSubMsg cancel", uid, openid, template_id)
        } else if (errcode == 40001 || errcode == 42001) {
            if (!isRetry) {
                logger.warn("sendSubMsg token expire", uid, openid, template_id)
                access_token = await this.setTokenExpire(access_token)
                return this.sendSubscribeMessage(openid, template_id, tpnlData, uid, true, access_token)
            } else {
                logger.error("sendSubMsg token error", uid, openid, template_id, access_token)
            }
        } else {
            logger.error("sendSubMsg err", uid, openid, template_id, tpnlData, data)
        }
    },
    // 文本内容检测 通过则返回null
    async msgSecCheck(uid, openid, content, access_token, retry = true) {
        content = encodeURI(content);
        access_token = access_token || await this.getToken();
        const url = `https://api.weixin.qq.com/wxa/msg_sec_check?access_token=${access_token}`;
        const data = await got.post(url, {
            json: {
                openid,
                content,
                version: 2,
                scene: 1
            },
            timeout: 3 * util.Time.Second,
        }).json();
        const {errcode, errmsg, result, detail} = data;
        if (errcode == 0) {
            if (result && result.label !== 100) return LABEL[result.label] || '敏感词';
            return null;
        }
        if (errcode == 40001 || errcode == 42001) {
            logger.error(`access_token expired.${uid},${retry}`);
            if (retry) return access_token = await this.setTokenExpire(access_token), await this.msgSecCheck(uid, openid, content, access_token, false);
        }
        logger.error(`wx:msg_sec_check err,${openid},${errcode},${errmsg}`);
        return errmsg;
    },

    //for test
    // testVerfiyToken(token) {
    //     return this.testToken == token || this.testToken2 == token
    // },

    // updateTestToken() {
    //     this.testToken2 = this.testToken
    //     this.testToken = util.getRandomString()

    //     if (!this.testToken2) {
    //         this.testToken2 = this.testToken
    //     }
    //     setTimeout(()=>{
    //         this.updateTestToken()
    //     }, 3000)
    // },

    // TestToken() {
    //     this.updateTestToken()

    //     setInterval(()=>{
    //         for (let i = 0; i < 10; i++) {
    //             let handle = async()=>{
    //                 await util.wait(Math.random() * 100)
    //                 let token = await this.getToken()
    //                 if (!this.testVerfiyToken(token)) {
    //                     token = await this.setTokenExpire(token)
    //                     if (!this.testVerfiyToken(token)) {
    //                         logger.error("verfiy fail", token)
    //                     }
    //                     else {
    //                         logger.info("good2", i, token)
    //                     }
    //                 }
    //                 else {
    //                     logger.info("good1", i, token)
    //                 }
    //             }
    //             handle()
    //         }
    //     }, 1000)
    // }
}

module.exports = wxMod;