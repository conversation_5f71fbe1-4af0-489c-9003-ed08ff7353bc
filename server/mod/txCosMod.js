var COS = require('cos-nodejs-sdk-v5');
const util = require('../common/util').default;
const config = require("../config")
const {secretId, secretKey, cos} = config.txCloud || {}
const logger = require("../common/log").getLogger("txCosMod");

let txCosMod = {
    cos: null,

    init() {
        this.cos = new COS({
            SecretId: secretId,
            SecretKey: secretKey
        });
    },

    async put(key, value) {
        let _put = (key, value)=>{
            return new Promise((resolve)=>{
                this.cos.putObject({
                    Bucket: cos.bucket, /* 必须 */
                    Region: cos.region,    /* 必须 */
                    Key: key,              /* 必须 */
                    StorageClass: 'STANDARD',
                    Body: value, // 上传文件对象
                }, function(err, data) {
                    if (!err) {
                        resolve(true)
                    }
                    else {
                        resolve({err, data})
                    }
                });
            })
        }
        return this.retry(_put, key, value)
    },

    async del(key) {
        let _del = (key)=>{
            return new Promise((resolve)=>{
                this.cos.deleteObject({
                    Bucket: cos.bucket, /* 必须 */
                    Region: cos.region,    /* 必须 */
                    Key: key,              /* 必须 */
                }, function(err, data) {
                    if (!err) {
                        resolve(true)
                    }
                    else {
                        resolve({err, data})
                    }
                });
            })
        }
       
        return this.retry(_del, key)
    },

    async retry(func, ...param) {
        let retry = 3
        while (retry > 0) {
            retry--
            let res = await func(...param)
            if (res === true) return true
            else {
                let err = res.err
                if (retry > 0 && err && err.statusCode == 503) {//需要重试 
                    await util.wait(1000)
                } 
                else {
                    logger.error(func.name, param, res);
                    return false
                }
            }
        }
    }
}

module.exports = txCosMod;