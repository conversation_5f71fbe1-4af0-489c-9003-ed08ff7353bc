const userMod = require("./userMod");
const idMod = require("./idMod");
const logger = require("../common/log").getLogger("accountMod");
const db = require("../db/db");
const {USER_TYPE} = require("../common/constant");
const util = require("../common/util").default;
const config = require("../config");
const rankMod = require("./rankMod");
const friendMod = require("./friendMod");
const appleLoginMod = require("./appleLoginMod");

const CODE = {
    SUCCESS: 0,
    PARAMS_HAS_ERROR: -1, //参数有问题
    USER_NOT_FOUND: 1000,//用户不存在
    USER_IS_GUEST: 1001,//用户是游客 不能注销
    ACCOUNT_NOT_AUTHENTICATION: 1002,//账号认证信息不存在
    ACCOUNT_OWNER_ERROR: 1003,//归属权确认失败
    ACCOUNT_ALREADY_IN_CANCEL: 1004,//该账号正在注销冷静期
    ACCOUNT_ALREADY_CANCEL: 1005,//该账号已经被注销
}

let accountMod = {

    init() {
        this.redis = db.redis;
        this.userIdCardCol = db.createUserIDCardModel();
        this.userCancellationCol = db.createUserCancellationModel();
        this.deviceTokenCol = db.createDeviceToken();
        this.subscribeCol = db.createSubscribeModel();
        this.idMapCol = db.createIdMapModel();
        this.statUserHeartCol = db.createStatUserHeartModel();
        this.loginInfoCol = db.createLoginInfoModel();
        this.userCol = db.createUserModel();
    },
    async beforeCancelAccount(user) {
        if (!user) return {status: CODE.USER_NOT_FOUND};
        //TODO skip.
        //if (user.userType == USER_TYPE.GUEST) return {status: CODE.USER_IS_GUEST};
        return void 0;
    },
    /**
     * 海外需要传递姓名和邮箱
     * 国内需要传递姓名和身份证
     */
    async sureAccountOwner(user, name, code, type = 1) {
        name = name.trim(), code = code.trim();
        if (!config.type || (config.type && config.type !== 'global'))
            return await this.sureAccountOwnerInland(user, name, code);
        return await this.sureAccountOwnerGlobal(user, name, code, type)
    },
    async sureAccountOwnerInland(user, name, code) {
        let idCardDoc = await this.userIdCardCol.findOne({uid: user.uid});
        if (!idCardDoc) {
            let _s = await userMod.authIDCard(user.uid, code, name);
            if (_s.status !== 0) {
                return {status: CODE.ACCOUNT_OWNER_ERROR};
            }
            return void 0;
        }
        if (idCardDoc.name === name && idCardDoc.idNum === code)
            return void 0;
        return {status: CODE.ACCOUNT_OWNER_ERROR};
    },
    async sureAccountOwnerGlobal(user, name, code, type = 1) {
        let uc = await this.userCancellationCol.findOne({uid: user.uid});
        let info = {};
        uc && uc.extends && (info = JSON.parse(uc.extends));
        if (type === 1) {
            info.name = name, info.email = code;
            // 海外只对输入的信息做一个保存,方便找回
            await this.userCancellationCol.updateOne({uid: user.uid}, {
                state: 0,
                extends: JSON.stringify(info)
            }, {upsert: true})
            return {status: CODE.SUCCESS};
        }
        logger.info(name, code, JSON.stringify(info));
        if (info.name === name && info.email === code) return void 0;
        return {status: CODE.ACCOUNT_OWNER_ERROR};
    },
    async reqCancel(uid, name, code) {
        if (!uid) return {status: CODE.PARAMS_HAS_ERROR};
        let user = await userMod.findOne({uid});
        if (!user) return {status: CODE.USER_NOT_FOUND};
        // 基本检查
        let _result = await this.beforeCancelAccount(user);
        if (_result) return _result;
        // 再次确认归属
        _result = await this.sureAccountOwner(user, name, code, 2);
        if (_result) return _result;
        // 是不是已经处于冷静期
        let exist = await this.userCancellationCol.findOne({uid: user.uid});
        if (exist) {
            if (exist.timestamp - Date.now() < 0) {
                // 账号已经被注销(可能定时任务还没跑到, 实际上还没注销, 但是不需要让玩家操作了.)
                return {status: CODE.ACCOUNT_ALREADY_CANCEL};
            }
        }
        return await this.save(user);
    },
    async createSpecialCode(uid, timestamp) {
        timestamp = timestamp.toString();
        let _arr = uid.split('-'),
            xor = timestamp.split(''),
            max = xor.length,
            i = 0,
            encrypted = '';
        for (let str of _arr) {
            let chars = str.split('');
            for (let char of chars) {
                char = char.charCodeAt(0);
                let x = parseInt(xor[i++]),
                    r = char ^ x;
                r < 0 && (r = char | x);
                r < 0 && (r = 0);
                encrypted += `${r}`;
                i >= max && (i = 0);
            }
        }
        return encrypted;
    },
    async save(user, day = 7) {
        let uid = user.uid
            , timestamp = Date.now() + util.Time.Day * day;
        await this.userCancellationCol.updateOne({uid}, {timestamp, state: 1}, {upsert: true})
        return {status: CODE.SUCCESS};
    },
    async deal() {
        let _self = this;
        let docs = await this.userCancellationCol.find({state: 1}).limit(1000);
        await util.promiseMap(docs, async (u) => {
            await _self.logic(u);
        })
    },
    /**
     * 正式的注销逻辑
     */
    async logic(exist) {
        // 不存在注销数据 或者状态不正确
        if (!exist || (exist && exist.state !== 1)) return void 0;
        if (exist.timestamp - Date.now() > 0) return void 0;
        let uid = exist.uid, user = await userMod.findOne({uid});
        if (!user) return void 0;
        // 取消id关联 并保存
        let iDoc = await this.idMapCol.find({uid}), info = {};
        exist.extends && (info = JSON.parse(exist.extends));
        info.idArray = info.idArray ? info.idArray : [];
        if (iDoc) {
            for (let {tpid} of iDoc) {
                info.idArray.push(tpid);
            }
            await this.idMapCol.deleteMany({uid});
        }
        // 排行榜屏蔽
        await rankMod.remove(uid);
        // 删除登录信息
        await this.loginInfoCol.deleteOne({uid});
        // 移除推送
        await this.subscribeCol.deleteMany({uid});
        // 移除好友
        await friendMod.clearFriends(uid);
        // 删除人气统计数据
        await this.statUserHeartCol.deleteOne({uid});
        await this.userCol.deleteOne({uid});
        let dt = await this.deviceTokenCol.findOne({uid});
        // 取消令牌相关数据 apple ?
        if (dt && dt.token && user.userType === USER_TYPE.APPLE) {
            let appleToken = await appleLoginMod.createToken();
            await appleLoginMod.revoke(appleToken, dt.token);
        }
        // 更新注销数据
        await this.userCancellationCol.updateOne({uid},
            {state: 2, extends: JSON.stringify(info)}, {upsert: true});
        return 1;
    },
    /**
     * 恢复账号
     */
    async reqRestore(uid) {
        await this.userCancellationCol.deleteOne({uid});
        return {status: CODE.SUCCESS};
    }


}

module.exports = accountMod;