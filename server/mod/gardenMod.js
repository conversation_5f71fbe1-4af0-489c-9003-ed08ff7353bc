const db = require("../db/db");
const util = require("../common/util").default;
const logger = require("../common/log").getLogger("gardenMod");
const party_config = require("../client/json/gardenPartyBase.json");
const recordMod = require("./recordMod");
const userMod = require("./userMod");
const { ObjectId } = require("mongoose/lib/types");

const Action_Water = 1 // 浇水
const Action_Fertilize = 2 // 施肥

const MAX_Action_Water = 6 // 浇水
const MAX_Action_Fertilize = 2 // 施肥

const DEL_TIME = 48

const gardenMod = {
    max_join_party_num: 2,
    //default_wait_time: 8 * util.Time.Hour, // 自动开启派对的等待时间
    init() {
        this.gardenCol = db.createGardenModel()
        this.partyCol = db.createGardenPartyModel()
        this.userCol = db.createUserModel()
    },
    /**
     * @param target 给谁做事
     * @param action 做了什么事
     * @param onlyCheck 只是检查 而不进行 操作
     * @return {Promise<number>}
     * @constructor
     */
    async OnPlayerAction(target, action, onlyCheck = false) {
        if (!target) return -1 // 格式不对
        if (!action) return -2 // 格式不对
        if (!action.uid || !action.type) return -3 // 格式不对
        let at = util.getSixAmTimestamp()
        // 检查限制: 每个玩家，浇水每天累计可以接受6次该类型，施肥每天累计能接受2次该类型 6点刷新
        let doc = await this.gardenCol.findOne({
            uid: target,
        })
        let arr = doc ? doc.list : []
        arr = arr.filter(e => e.type === action.type && e.time >= at)
        if (action.type === Action_Water && arr.length >= MAX_Action_Water) return -4 // 好友接受次数上限
        if (action.type === Action_Fertilize && arr.length >= MAX_Action_Fertilize) return -4 // 好友接受次数上限
        // 检查限制: 每天只能给同一个好友，操作1次该类型  6点刷新
        arr = arr.filter(e => e.uid === action.uid)
        if (arr.length > 0) return -5 // 自己次数上限
        if (onlyCheck) return 0
        let user = await this.userCol.findOne({ uid: action.uid }, {
            avatarUrl: 1,
            nickName: 1,
            diyName: 1,
            diyAvatar: 1
        })
        if (!user) return -6 // 找不到被do的目标用户
        // 填充基本信息
        action.icon = user.diyAvatar || user.avatarUrl
        action.name = user.diyName || user.nickName
        action.time = Date.now()

        await this.gardenCol.updateOne({ uid: target }, {
            $addToSet: {
                list: action
            }
        }, { upsert: true })
        return 0
    },
    /**
     * @param uid
     * @param lastRequestTime 上次获取数据时服务器返回的时间
     * @return {Promise<any>}
     * @constructor
     */
    async GetPlayerAction(uid, lastRequestTime) {
        let at = Date.now()
        if (!uid) return { time: at, data: [] }
        if (!lastRequestTime) return { time: at, data: [] }
        if (lastRequestTime >= at) return { time: at, data: [] }
        let docs = await this.gardenCol.findOne({ uid })
        // "list.time": {
        //     $gte: lastRequestTime
        // }
        if (!docs || (docs && !docs.list.length)) return { time: at, data: [] }
        let save = []
        let temp = []
        const today = util.getTodayZeroTime(6)
        docs.list.forEach(action => {
            // 当天的记录需要保持
            if (action.time >= today) save.push(action)
            if (action.time >= lastRequestTime) temp.push(action)
        })
        if (save.length !== docs.list.length) {
            // 删除
            await this.gardenCol.updateOne(
                { uid },
                { list: save },
            )
        }
        return { time: at, data: temp }
    },
    /**
     * 筹备游园会
     * @param uid
     * @param partyId 派对id
     * @param step // 阶段
     * @return {Promise<any>}
     */
    async startParty(uid, partyId, step) {
        let line = await this.partyCol.findOne({ uid })
        if (line && (line.status === 0 || line.status === 1)) {
            return { status: -1, partyId: line.partyId, time: line.time } //已经有一个派对筹备中或者已经筹备完成,无法开始一个新的
        }
        let config = party_config.find(e => e.id === partyId)
        if (!config) return -2 // 派对配置id不存在
        let now = Date.now()
        // 直接保存
        await this.partyCol.updateOne({ uid }, {
            partyId,
            step,
            time: now,
            status: 0,
            joiner: []
        }, { upsert: true })
        return { status: 0 }
    },
    /**
     * 参加游园会
     * @param uid
     * @param targetId
     * @return {Promise<number>}
     */
    async joinParty(uid, targetId) {
        let user = await this.userCol.findOne({ uid })
        if (!user) return -1 // 有问题，找不到uid对应的用户
        if (user.gardenPartyJoin && user.gardenPartyJoin.length === this.max_join_party_num)
            return -2 // 自己参与游园会上限

        let party = await this.partyCol.findOne({ uid: targetId })
        if (!party) return -3 // targetId没有游园会记录
        if (party.status !== 0) return -4 // 游园会状态已经不是筹备中
        if (party.joiner) {
            if (party.joiner.find(e => e.indexOf(uid) > -1)) return -5 //已经参加了这个游园会
            let config = party_config.find(e => e.id === party.partyId)
            let max = config["max_friends"]
            max = max.split("|")
            if (!max) return -6
            if (max && max.length < party.step) return -6 // 前端可忽略的错误
            max = max[party.step - 1]
            max = Number(max)
            if (party.joiner.length >= max) return -7 // 参加者列表满了
        }
        await this.userCol.updateOne({ uid }, {
            $push: {
                gardenPartyJoin: {
                    $each: [party._id.toString()]
                }
            }
        })
        let record = await recordMod.getRecord(uid)
        let skin = ""
        if (record) skin = await recordMod.getFromRecord("main.useWudongSkinId", record)
        await this.partyCol.updateOne({ _id: party._id.toString() }, {
            $push: {
                joiner: {
                    $each: [`${uid},${skin}`]
                }
            }
        })
        return 0
    },
    async isPlayerStartParty(uid) {
        let doc = await this.partyCol.findOne({ uid, status: 0 })
        if (doc) {
            let config = party_config.find(e => e.id === doc.partyId)
            let max = config["max_friends"]
            max = max.split("|")
            if (!max) return -6
            if (max && max.length < doc.step) return ""
            max = max[doc.step - 1]
            max = Number(max)
            if (doc.joiner.length >= max) return "" // 参加者列表满了
        }
        return doc ? doc._id : ""
    },
    async getCanJoinPartyNum(uid) {
        let user = await this.userCol.findOne({ uid })
        if (!user) return this.max_join_party_num
        if (user.gardenPartyJoin && user.gardenPartyJoin.length) {
            const info = {
                gardenPartyJoin: []
            }
            for (const id of user.gardenPartyJoin) {
                const doc = await this.partyCol.findOne({ _id: ObjectId(id) })
                if (doc) {
                    info.gardenPartyJoin.push(id)
                }
            }
            if (user.gardenPartyJoin.length != info.gardenPartyJoin.length) {
                await this.userCol.updateOne({ uid }, info)
                user.gardenPartyJoin = info.gardenPartyJoin
            }
        }
        return user.gardenPartyJoin ? this.max_join_party_num - user.gardenPartyJoin.length : this.max_join_party_num
    },
    /**
     * 开始派对
     * @param uid
     * @param force
     * @return {Promise<any>}
     */
    async finishMyParty(uid, force = false) {
        let party = await this.partyCol.findOne({ uid })
        if (!party)
            return { status: 1, vis: [] }
        // 有已知的筹备完成派对
        if (party) {
            await this.partyCol.updateOne({ uid }, {
                status: 2
            })
        }
        let vis = [], master = await this.userCol.findOne({ uid })
        if (!master) {
            master = {
                diyName: "",
                lang: ""
            }
        }
        let masterName = master.diyName || master.nickName || ""
        for (let u of party.joiner) {
            try {
                let par = u.split(","), skin = "", _uid = ""
                par.length && (_uid = par.shift())
                par.length && (skin = par.shift())
                let user = await this.userCol.findOneAndUpdate({ uid: _uid }, {
                    $pull: {
                        gardenPartyJoin: party._id.toString()
                    }
                })
                if (!user) {
                    user = {
                        diyName: "",
                        lang: ""
                    }
                }
                let myName = user.diyName || user.nickName || ""
                await this.recordPartyReward(_uid, masterName, party.partyId, user.lang)
                vis.push({
                    uid: _uid,
                    name: myName,
                    skin
                })
            } catch (e) { }
        }
        // 需要给主人发奖
        if (force) {
            await this.recordPartyReward(uid, `|${vis.length}`, party.partyId, master.lang, true)
        }
        return { status: 1, partyId: party.partyId, vis }
    },
    // 走发奖接口，type固定是 100713
    async recordPartyReward(uid, name = "", partyId, lang = "", self = false) {
        let config = party_config.find(e => e.id === partyId)
        let time = Date.now() + config.dur_time * util.Time.Minute
        let reward = `${name},${partyId}`
        self && (reward = `${reward}|${uid}${name}`)
        // 如果是自己 " |派对人数,派对id|派对主人uid|派对人数 "
        // 如果是别人 " 派对主人name,派对id "
        await userMod.addCompensationRewardMultiple(uid, reward, lang, "", 100713)
    },
    async checkAndRunt2() {
        logger.info("checkAndRunt2....")
        try {
            let limit = 10000
            let logic = async (lastId) => {
                let where = { status: { $in: [0, 1, 2] } };
                lastId && (where._id = { $gt: lastId });
                let docs = await this.partyCol.find(where).sort({ _id: 1 }).limit(limit)
                await util.promiseMap(docs, async ({ _id, uid, partyId, time, status, joiner }) => {
                    let config = party_config.find(e => e.id === partyId)
                    if (!config) return void await this.partyCol.deleteOne({ _id })
                    if (Date.now() - time < DEL_TIME * util.Time.Hour) {
                        return null
                    }
                    // 超过48小时 处理参加者
                    if (joiner && joiner.length) {
                        for (let id of joiner) {
                            id = id.split(",")
                            id = id[0]
                            await this.userCol.updateOne({ uid: `${id}` }, {
                                $pull: {
                                    gardenPartyJoin: _id.toString(),
                                }
                            })
                        }
                    }
                    await this.partyCol.deleteOne({ _id })
                })
                if (docs.length > 0) {
                    return docs[docs.length - 1]._id;
                }
            }
            let exec_ = true
            let lastId = 0;
            while (exec_) {
                lastId = await logic(lastId);
                exec_ = !!lastId;
            }
            await util.wait(3 * 60 * util.Time.Minute)
        } catch (e) {
            logger.info("checkAndRun err:", e)
        }
        logger.info("checkAndRun2 end")
        return this.checkAndRunt2()
    },
}
module.exports = gardenMod
