const { default: util } = require("../common/util");
const wxMod = require("./wxMod");
const logger = require("../common/log").getLogger('adSkipCardMod');
const got = require('got')
const config = require("../config");
const crypto = require('crypto')
const db = require("../db/db");
const redisKey = require('../db/redisKey');

const GENERAL_ERROR = -1// 通用错误码
const SESSION_KEY_ERROR = -2// 无法获取session_key
const ACCESS_TOKEN_ERROR = -3// 无法获取access_token
const INITIATIVE_CANCEL = -4// 主动取消
const PERMISSION_DENIED = -5// 没有权限

let redis

let adSkipCardMod = {

    async init() {
        redis = db.redis
    },

    // 创建广告跳过卡订单
    async createAdSkipCardOrder(uid, openid, amount, code) {
        if (!uid || !openid) {
            return { status: GENERAL_ERROR }
        }

        if (code) {
            let info = await wxMod.code2Session(code)
            let { session_key, unionid, openid } = info;
            await redis.set(redisKey.wxSessionKey(openid), session_key)
        }

        const query = {
            amount,
            out_trade_no: util.getRandomString(32),
            attach: uid
        }
        const session_key = await redis.get(redisKey.wxSessionKey(openid))
        if (!session_key) {
            return { status: SESSION_KEY_ERROR }
        }
        const signature = crypto.createHmac('sha256', session_key).update(JSON.stringify(query)).digest('hex').toLowerCase()

        // logger.info('------ createAdSkipCardOrder query:' + JSON.stringify(query))
        // logger.info('------ createAdSkipCardOrder session_key:' + session_key)
        // logger.info('------ createAdSkipCardOrder signature:' + signature)

        const access_token = await wxMod.getToken()
        if (!access_token) {
            return { status: ACCESS_TOKEN_ERROR }
        }

        let result = await adSkipCardMod.requestAdSkipCardOrder('https://api.weixin.qq.com/wxa/business/adcard/createcostorder?access_token=' + access_token + '&openid=' + openid + '&signature=' + signature + '&sig_method=hmac_sha256', query)
        if (result.status == 42001) {// access_token过期 需要重新拉取一次
            access_token = await wxMod.setTokenExpire(access_token)
            if (!access_token) {
                return { status: ACCESS_TOKEN_ERROR }
            }

            result = await adSkipCardMod.requestAdSkipCardOrder('https://api.weixin.qq.com/wxa/business/adcard/createcostorder?access_token=' + access_token + '&openid=' + openid + '&signature=' + signature + '&sig_method=hmac_sha256', query)
        }

        return result
    },

    async requestAdSkipCardOrder(url, query) {
        let data = await got.post(url, {
            json: query
        }).json();

        // logger.info('------ createAdSkipCardOrder url:' + url)
        // logger.info('------ createAdSkipCardOrder query:' + JSON.stringify(query))
        // logger.info('------ createAdSkipCardOrder data:' + JSON.stringify(data))

        return { status: data.errcode, message: data.errmsg, order: data.order_no }
    }
}

module.exports = adSkipCardMod;