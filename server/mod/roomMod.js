const util = require("../common/util").default;
const userMod = require("./userMod");
const recordMod = require("./recordMod");
const logger = require("../common/log").getLogger("roomMod");
const cryptoHelper = require("../cypto/cryptoHelper");
const {async} = require("regenerator-runtime");
const txCosMod = require("./txCosMod");
const redisKey = require("../db/redisKey")
const {ObjectId} = require("mongoose/lib/types");
const db = require("../db/db");
const room_201 = require("../client/json/roomFurnitureUnlock_1.json");
const room_202 = require("../client/json/roomFurnitureUnlock_2.json");
const room_301 = require("../client/json/roomFurnitureUnlock_3.json");
const room_302 = require("../client/json/roomFurnitureUnlock_4.json");
const room_401 = require("../client/json/roomFurnitureUnlock_5.json");
const room_402 = require("../client/json/roomFurnitureUnlock_6.json");
const {default: ModelMgr} = require("../client/ModelMgr");


const parentDir = "roomSavePlan";

const buildGuideInfoState = {
    FQ: 0x1,//废弃库- 表示审核不通过的方案
    BY: 0x2,//备用库- 表示服务器存放的方案
    RX: 0x3,//入选库- 表示会刷新给玩家的方案
    DX: 0x4,//待选库- 表示已经投稿但还未过审核
};

const BUILD_PLAN_STATE = {
    _DEFAULT: 0, //默认
    _GOOD: 1, // 精选 但是用户没确认的
    _SURE: 2, // 可以被刷出来的数据
    _NOT_SURE: 3, // 废弃
}
const HEART_ADD = 20;// 随机装修方案时 蜡烛正向补正值
const HEART_MIN = 0.2; // 蜡烛最低限制 = 当前hear * HEART_MIN

const add_like = 5;

const planNum = 4; // 方案数量

let roomMod = {
    roomLikeCol: null,
    redis: null,
    buildGuideInfoCol: null,

    cosMod: null,
    init() {
        let db = require("../db/db");
        this.roomLikeCol = db.createRoomLikeModel()
        this.buildGuideInfoCol = db.createBuildGuideInfoModel();
        this.recommendCol = db.createRecommendModel();
        this.buildPlanCol = db.createBuildPlanModel();
        this.userHeartCol = db.createStatUserHeartModel()
        this.roomFavCol = db.createRoomFavModel();
        this.redis = db.redis
        this.cosMod = require("./txCosMod")
    },

    async like(uid, toUid, roomId) {
        let now = Date.now()
        let pushInfo = {
            likeUsers: {
                $each: [{uid: uid, timestamp: now}],
                $sort: {timestamp: -1},
                $slice: 20,
            }
        }
        await this.roomLikeCol.updateOne({uid: toUid, roomId}, {'$inc': {like: 1}, '$push': pushInfo}, {upsert: true})
    },

    async getLike(uid, roomId) {
        let doc = await this.roomLikeCol.findOne({uid, roomId})
        if (!doc) {
            return 0
        }

        return doc.like
    },

    async getLikeInfo(uid, roomId) {
        let doc = await this.roomLikeCol.findOne({uid, roomId})
        let likeUsers = [], like = 0
        if (!doc) {
            return {like, likeUsers}
        }

        likeUsers = await util.promiseMap(doc.likeUsers, async (user) => {
            let info = await userMod.getNameAndAvatar(user.uid)
            let res = {timestamp: user.timestamp}
            if (info) {
                res.nickName = info.nickName
                res.avatarUrl = info.avatarUrl
            }
            return res
        })
        return {like: doc.like, likeUsers}
    },
    // 2.0点赞
    //
    async fav(_self, _pid, num) {
        if (!_self || !_pid) return util.Code.ERROR;
        let time = Date.now();
        let info = {
            likeUsers: {
                $each: [{uid: _self, timestamp: time}],
                $sort: {timestamp: -1},
                $slice: 50
            }
        };
        await this.roomFavCol.updateOne({_pid}, {'$inc': {like: num}, '$push': info}, {upsert: true});
        return util.Code.SUCCESS;
    },
    // 获取方案的点赞信息
    async getFavInfo(_pid) {
        let doc = await this.roomFavCol.findOne({_pid});
        let likeUsers = [], like = 0
        if (!doc) {
            return {like, likeUsers}
        }

        likeUsers = await util.promiseMap(doc.likeUsers, async (user) => {
            let info = await userMod.getNameAndAvatar(user.uid)
            let res = {timestamp: user.timestamp}
            if (info) {
                res.nickName = info.nickName
                res.avatarUrl = info.avatarUrl
            }
            return res
        })
        return {like: doc.like, likeUsers}
    },
    async savePlan(uid, content, md5, oldMD5) {
        let currentMD5 = cryptoHelper.md5(content)
        if (md5 && md5 != currentMD5) {
            return false
        }
        let dir = "roomSavePlan"
        let newKey = `${dir}/${uid}/${currentMD5}.jpg`
        let res = await this.cosMod.put(newKey, util.base64ToImg(content))
        if (res && oldMD5 && md5 != oldMD5) { //删掉老图
            let oldKey = `${dir}/${uid}/${oldMD5}.jpg`
            this.cosMod.del(oldKey)
        }
        return res
    },
    /**
     * 装修指南投稿
     * @param uid
     * @param roomId
     * @param saveInfo
     * @param heartLimit
     * @param base64ToImg
     * @returns {Promise<number>}
     */
    async saveBuildGuideInfo(uid, roomId, saveInfo, heartLimit, base64ToImg) {
        if (!uid || !roomId || !saveInfo || !base64ToImg) return util.Code.ERROR;
        let now = new Date().getTime();
        let key = await this.getPreviewUrlKey(roomId, uid, now);
        let data = {
            uid: uid,
            roomId: roomId,
            heartLimit: heartLimit,
            state: buildGuideInfoState.DX,
            date: now,
            saveInfo: saveInfo,
            previewUrl: key,
        };

        let uploadResult = await this.uploadPreviewImage(key, base64ToImg);
        if (!uploadResult) {
            return util.Code.ERROR;
        }
        let ret = await this.buildGuideInfoCol.insertMany(data);
        let _id = ret[0]._doc._id.toString();

        //模拟审核通过
        //await this.changeSaveInfo(_id, buildGuideInfoState.RX, '');
        // 模拟获取数据
        //let uids = await this.reqGuideList(uid, roomId, heartLimit, 1);
        //let pp = await this.getRoomInfo(roomId, uids);

        return util.Code.SUCCESS;
    },
    /**
     * 获取装修指南列表
     * @param uid
     * @param roomId
     * @param playerHeart 玩家蜡烛数量
     * @param flushCount 刷新次数
     * @returns {Promise<*|number>}
     */
    async reqGuideList(uid, roomId, playerHeart, flushCount) {
        if (!roomId) {
            return util.Code.ERROR;
        }
        // 总数量
        let max = await this.redis.zcard(redisKey.buildGuideRank(roomId));
        if (!max) {
            return null;
        }
        if (max <= planNum) {
            return await this.redis.zrange(redisKey.buildGuideRank(roomId), 0, -1);
        }
        // 低于玩家当前蜡烛的方案数量
        let count = await this.redis.zcount(redisKey.buildGuideRank(roomId), 0, playerHeart);
        let index;
        if (count === 0) {
            index = max;
        } else {
            index = count;
        }
        let mark = {
            S: Math.max(0, index - 20 - flushCount),
            E: Math.min(index + flushCount / 2, max)
        };
        if (mark.E - mark.S < 4) {
            logger.error("Plan number is not enough for random operation!");
            return null;
        }
        let data = [];
        data = await this.randomData(mark.S, mark.E, planNum);
        let uids = [];
        for (let i = 0; i < data.length; i++) {
            let val = await this.redis.zrange(redisKey.buildGuideRank(roomId), data[i], data[i]);
            uids.add(val[0]);
        }
        return uids;
        //return await this.buildGuideInfoCol.find({state: state});
    },
    async getPreviewUrlKey(roomId, uid, now) {
        let imageName = roomId + '_' + now;
        return `${parentDir}/${uid}/${imageName}.jpg`
    },
    /**
     * 路径暂定为/uid/roomId_date.jpg
     * @param key
     * @param base64ToImg
     * @returns {Promise<number>}
     */
    async uploadPreviewImage(key, base64ToImg) {
        // 上传图片到cos
        return await txCosMod.put(key, util.base64ToImg(base64ToImg));
    },
    async get() {

    },
    /**
     * 审核操作 改变状态
     * 有可能这条数据本身没有图片，所以会涉及到图片上传
     * @param _id
     * @param state
     * @param base64ToImg
     * @returns {Promise<number>}
     */
    async changeSaveInfo(_id, state, base64ToImg) {
        if (await this.isStateRight(state)) {
            let _doc = await this.buildGuideInfoCol.findOneAndUpdate({_id}, {state: state}, {
                upsert: true,
                new: true
            })
            if (!_doc) {
                return util.Code.ERROR;
            }
            switch (state) {
                case buildGuideInfoState.FQ: {// 废弃
                    return await this.changeState_FQ(_doc);
                }
                case buildGuideInfoState.RX: {// 入选
                    return await this.changeState_RX(_doc, base64ToImg);
                }
                case buildGuideInfoState.DX: {// 待选
                    return await this.changeState_DX(_doc);
                }
                case buildGuideInfoState.BY: {// 备用
                    return await this.changeState_BY(_doc);
                }
            }
        }
    },
    // 废弃操作
    async changeState_FQ(doc) {
        let document = doc._doc;
        await this.buildGuideInfoCol.updateOne({_id: document._id.toString()}, {state: buildGuideInfoState.FQ});
        await this.removeKeyFromRedis(document.roomId, document._id.toString());
        return util.Code.SUCCESS;
    },
    // 备用操作
    async changeState_BY(doc) {
        let document = doc._doc;
        await this.buildGuideInfoCol.updateOne({_id: document._id.toString()}, {state: buildGuideInfoState.BY});
        await this.removeKeyFromRedis(document.roomId, document._id.toString());
        return util.Code.SUCCESS;
    },
    // 待选操作
    async changeState_DX(doc) {
        let document = doc._doc;
        await this.buildGuideInfoCol.updateOne({_id: document._id.toString()}, {state: buildGuideInfoState.DX});
        await this.removeKeyFromRedis(document.roomId, document._id.toString());
        return util.Code.SUCCESS;
    },
    /**
     * 入选操作
     * @param doc
     * @param base64ToImg
     * @returns {Promise<number>}
     */
    async changeState_RX(doc, base64ToImg) {
        let needUpload = false;
        let document = doc._doc;
        let now = new Date().getTime();
        let key = await this.getPreviewUrlKey(document.roomId, document.uid, now);
        if (document.previewUrl) {
            // 可能是需要更新预览图 删除旧图
            await txCosMod.del(document.previewUrl);
            needUpload = true;
        } else {
            // saveInfo有变化也应该更新预览图，暂时无法判断
        }
        if (needUpload) {
            let uploadResult = await this.uploadPreviewImage(key, base64ToImg);
            if (!uploadResult) {
                logger.error("upload image failed, cancel operation.")
                return util.Code.ERROR;
            }
            await this.buildGuideInfoCol.updateOne({_id: document._id.toString()}, {previewUrl: key});
        }
        // 刷新投稿排行 按照蜡烛消耗数量, 状态为RX的才会上榜
        await this.redis.zadd(redisKey.buildGuideRank(document.roomId), document.heartLimit, document._id.toString());
        return util.Code.SUCCESS;
    },
    // 从redis集合中移除一条记录
    async removeKeyFromRedis(roomId, _id) {
        await this.redis.zrem(redisKey.buildGuideRank(roomId), _id);
    },
    async getGuideInfoByStateAndRoomId(roomId, state, page, size) {
        if (!page || page < 0) page = 1;
        if (!size || size > 100 || size < 1) size = 20;
        if (!await this.isStateRight(state)) {
            return null;
        }
        let data = await this.buildGuideInfoCol.find({
            roomId: roomId,
            state: state
        }, {saveInfo: 0}).limit(size).skip((page - 1) * size);
        let result = [];
        data.forEach((item, index) => {
            let doc = item._doc;
            result[index] = doc;
        })
        return result;
    },
    // 根据房间id和当前标签 获取总数量
    async getTotalGuideInfoByStateAndRoomId(roomId, state) {
        return await this.buildGuideInfoCol.find({roomId: roomId, state: state}).count();
    },
    // 玩家购买推荐方案 增加点赞数
    async buySaveInfo(uid, _id) {
        return await this.fav(uid, _id, add_like);
    },
    async getSaveInfo(saveId) {
        if (!saveId) return null;
        return this.buildGuideInfoCol.findOne({_id: saveId});
    },
    async randomData(min, max, count) {
        let data = [];
        for (let i = 0; i < count;) {
            let random = Math.floor(Math.random() * (max - min)) + min;
            if (data.indexOf(random) < 0) {
                data.push(random);
                i++;
            }
        }
        return data;
    },
    async isStateRight(state) {
        let rightState = false;
        Object.keys(buildGuideInfoState).forEach((k, v) => {
            if (state === buildGuideInfoState[k]) rightState = true;
        })
        return rightState;
    },
    async getRoomInfo(roomId, _ids) {
        if (!roomId || !_ids)
            return null;
        let queryUid = [];
        _ids.forEach((_id, index) => {
            queryUid[index] = ObjectId(_id);
        });
        let data = await this.buildGuideInfoCol.find({roomId, _id: {$in: queryUid}});
        let res = [];
        for (let i = 0; i < data.length; i++) {
            res[i] = res[i] || {};
            let line = data[i];
            res[i].uid = line._doc.uid;
            let info = await userMod.getNameAndAvatar(line._doc.uid);
            res[i].nickName = info.nickName;
            res[i].avatarUrl = info.avatarUrl;
            res[i].previewUrl = line._doc.previewUrl;
            res[i].heartLimit = line._doc.heartLimit;
            res[i]._id = line._doc._id;
        }
        return res;
    },
    // 3.0精选方案 相关接口（内部审核用）
    async getRoomList3_0(rooms, state, limit, page, pageSize, uid, _id, st, et) {
        state = parseInt(state);
        !page || page < 0 && (page = 1);
        !pageSize || pageSize < 0 && (page = 10);
        let docs = [], _ = {}, where = {};
        _.status = 0;
        _.saveInfos = [];
        if (!_id) {
            where = {heart: {$gte: limit.min, $lte: limit.max}};
            st && (where.ts = {}, where.ts.$gte = st);
            et && (where.ts = where.ts || {}, where.ts.$lte = et);
            state !== -1 && (where.state = state);
            rooms && rooms.length && (where.roomId = {$in: rooms});
            uid && (where.uid = uid);
            // 计算total
            _.total = await this.buildPlanCol.find(where).count();
            docs = _.total ? await this.buildPlanCol
                .find(where)
                .sort({_id: 1})
                .limit(pageSize).skip((page - 1) * pageSize) : [];
            if (!_.total) {
                return _;
            }
        } else {
            let line = await this.buildPlanCol.findOne({_id: ObjectId(_id)});
            line && docs.push(line);
        }
        let userHeartCol = this.userHeartCol;
        await util.promiseMap(docs, async ({_id, uid, roomId, roomlike, saveInfo, heart, state}) => {
            if (!uid || !roomId) {
                return;
            }
            let doc = await userHeartCol.findOne({uid});
            let user = {};//获取用户基本信息
            let _d = await userMod.findOne({uid});
            if (_d) {
                // 头像
                user['headIcon'] = _d.avatarUrl || "";
                user.nickName = _d.nickName || `玩家`;
                user.serverId = _d.serverId;
            }
            user._id = _id;
            user.uid = uid;
            user.state = state;
            user.userHeart = doc && doc.heart ? doc.heart : 0;// 玩家当前蜡烛
            user.planHeart = heart; //当前方案蜡烛
            user.like = roomlike || 0;
            user.roomId = roomId;
            user.saveInfo = saveInfo;
            _.saveInfos.push(user);
        });
        where.state = {$in: [1, 2]};
        _.good = await this.buildPlanCol.find(where).countDocuments() || 0;
        return _;
    },
    //改变方案状态
    async changePlanState3_0(uid, roomId, state = BUILD_PLAN_STATE._SURE) {
        let err = 0;
        !err && state && (state = parseInt(state));
        !err && roomId && (roomId = roomId.toString());
        (!uid || !roomId) && (err = -1);
        if (err) return {status: -1};
        let doc = await this.buildPlanCol.findOneAndUpdate({uid, roomId}, {state}, {new: false});
        if (!doc) return {status: -1};
        switch (state) {
            case BUILD_PLAN_STATE._GOOD:// 精选加入数据池
                if (doc.state !== BUILD_PLAN_STATE._GOOD) {
                    state && state === BUILD_PLAN_STATE._GOOD && (await this.sendPrize(uid));
                    await this.redis.zadd(redisKey.buildPlansRankInRoom(roomId), doc.heart, doc._id.toString());
                }
                break;
            case BUILD_PLAN_STATE._DEFAULT://撤销精选 前提是之前是精选
            case BUILD_PLAN_STATE._NOT_SURE:
                if (doc.state === BUILD_PLAN_STATE._GOOD) {
                    await this.redis.zrem(redisKey.buildPlansRankInRoom(roomId), doc._id.toString());
                }
                break;
            case BUILD_PLAN_STATE._SURE://接受精选
                if (doc.state === BUILD_PLAN_STATE._GOOD) {
                }
                break;
        }
        return {status: 0};
    },
    async sendPrize(uid) {
        // 根据配置 发送奖励
        //await userMod.addCompensationRewardMultiple(uid,);
    },
    // 方案点赞
    async doLikeBuildPlan3_0(uid, planId) {
        if (!uid || !planId) return {status: -1};
        let time = Date.now();
        let info = {
            likeUsers: {
                $each: [{uid: uid, timestamp: time}],
                $sort: {timestamp: -1},
                $slice: 50
            }
        };
        await this.roomFavCol.updateOne({planId}, {'$inc': {like: 1}, '$push': info}, {upsert: true});
        return {status: 0};
    },
    // 根据唯一id列表  获取方案具体数据
    async getBuildPlanInfo(ids) {
        if (!ids) return {status: -1};
        if (ids && !ids.length) return {status: -1};
        let objArr = [];
        ids.forEach(id => {
            objArr.push(ObjectId(id));
        })
        let docs = await this.buildPlanCol.find({_id: {$in: objArr}}, {
            _id: 1,
            roomId: 1,
            roomlike: 1,
            heart: 1,
            saveInfo: 1
        });
        return {status: 0, docs};
    },
    // 客户端获取方案
    async tryGetBuildPlanData(type, rooms, num, heart) {
        if (this.doCheckBeforeGetBuildPlanData(type, rooms, num, heart)) return {status: -1, desc: '参数错误'};
        //heart = heart + HEART_ADD;
        let result_arr = [];
        type === 1 && (result_arr = await this.do_WAN_BAO(rooms, num, heart));
        type === 2 && (result_arr = await this.doBuildPlansRandom(rooms, heart, num));

        // 去重
        let set = new Set();
        result_arr.forEach(_d => set.add(_d));
        let data = [], docs = [];
        data = Array.from(set);
        data && data.length && (docs = await this.getBuildPlanInfo(data));
        return {status: 0, data, docs};
    },
    // 晚报先随房间，再随方案
    // 如果房间没得方案，就换房间,有方案就随机一个出来,然后换房间,如果房间随完还不够，就重复进行房间随机-方案随机 直到方案数量足够
    // 在方案数和可选房间数太少的情况下 会出现重复方案,返回前端时做个去重
    // 理论上来说不会出现重复,因为是优先取不同房间的1个方案出来,除非玩家只解锁了一个房间，并且这个房间方案很少,或者玩家的其他房间没有任何匹配的方案
    // 房间数量最多为6个,最差效率的情况是只有一个房间有匹配的方案，那么需要至多循环12次(前端每天需要两个方案)。
    async do_WAN_BAO(rooms, num, heart) {
        let emptyRooms = [];
        let ignore = [];
        let data = [];
        while (data.length < num) {
            if (emptyRooms.length === rooms.length) break;
            let _r = 0;
            if (rooms.length > 1) {
                _r = await util.randomIndex(rooms.length, 1, ignore);
                if (_r === -1) throw new Error("random result err !!!");
                ignore.push(_r);
            }
            let targetRoomId = rooms[_r];
            targetRoomId && (targetRoomId = parseInt(targetRoomId));
            let res = await this.doBuildPlansRandom(targetRoomId, heart, 1);
            if (!res.length) {
                emptyRooms.push(targetRoomId);
                continue// 该房间没得方案
            }
            if (ignore.length === rooms.length) ignore = [];// 随机时可能方案凑不够，清空ignore再来随机一次
            data = data.concat(res);
        }
        return data;
    },

    async doBuildPlansRandom(roomId, heart, num = 1) {
        let count = await this.redis.zcount(redisKey.buildPlansRankInRoom(roomId), Math.ceil(heart * HEART_MIN), heart + HEART_ADD);
        if (count === 0) return [];//没得数据
        if (num !== 1 && count <= num) {// 数据不够
            return await this.redis.zrange(redisKey.buildPlansRankInRoom(roomId), 0, count);
        }
        let _arr = [];
        let ignore = [];
        for (let i = 0; i < num; i++) {
            let __r = await util.randomIndex(count, 1, ignore);
            ignore.push(__r);
            let _data = await this.redis.zrange(redisKey.buildPlansRankInRoom(roomId), __r, __r);
            _arr = _arr.concat(_data);
        }
        return _arr;
    },
    // 客户端获取方案之前的检查
    doCheckBeforeGetBuildPlanData(type, rooms, num, heart) {
        let err = false;
        heart <= 0 && (err = true);
        !err && num <= 0 && (err = true);
        !err && !rooms && (err = true);
        !err && !rooms.length && (err = true);
        return err;
    },
    // 获取需要用户确认的信息列表
    async doGetAboutSelf(uid, state = BUILD_PLAN_STATE._GOOD) {
        let data = await this.buildPlanCol.find({uid, state}, {roomId: 1})
        return {status: 0, data};
    },
    // fix.js 主动遍历一次精选列表 然后添加到redis中
    async checkAndAddToRedis() {
        let docs = await this.buildPlanCol.find({state: {$in: [1, 2]}}, {roomId: 1, heart: 1});
        await util.promiseMap(docs, async ({_id, roomId, heart}) => {
            roomId = parseInt(roomId);
            await this.redis.zadd(redisKey.buildPlansRankInRoom(roomId), heart, _id.toString());
        })
    },
    async clientSavePlan(uid, roomId, furnitures, heart, saveInfo) {
        let save = {uid, roomId, furnitures, heart, saveInfo, state: 0};
        await this.buildPlanCol.updateOne({uid, roomId}, save, {upsert: true});
    },
    async getRoomBuildPlanInfo(uid, roomId) {
        let doc = await this.buildPlanCol.findOne({uid, roomId});
        let code = -1;
        if (doc) {
            code = doc.state || code;
        }
        return {status: 0, code};
    },

    async transfer_data(spePlayers) {
        let buildPlanCol = this.buildPlanCol;
        let recommendCol = this.recommendCol;
        // 指定用户id和房间数据移动
        // let spePlayers = {
        //     //"e78c5864-d3ea-4956-b392-ad58d500bfdd": [201, 301, 401],
        //     "2aa887df-2d02-4234-9d76-454a5f15a05c": [201, 202, 301, 302, 401, 402],
        //     //"eb03b30e-1cc7-47e3-b5b6-a381704873c6": [201, 202, 301, 302, 401, 402],
        // }
        let furnMaxHeart = 0; //家具总最大蜡烛大于等于该值才恢赋值


        let minRoomLike = 50;// 点赞数大于该值的才复制过去
        let maxRoomLike = 100;// 点赞数小于该值的才复制过去

        let getRoomFurnData = async (roomId) => {
            roomId && (roomId = parseInt(roomId));
            let data = [];
            roomId === 201 && (data = room_201);
            roomId === 202 && (data = room_202);
            roomId === 301 && (data = room_301);
            roomId === 302 && (data = room_302);
            roomId === 401 && (data = room_401);
            roomId === 402 && (data = room_402);
            return data;
        }
        let park = async (uid, roomId, activation, furnitures, recommended, roomlike) => {
            let record = await recordMod.getRecord(uid);
            if (!record) return;
            let modelMgr = new ModelMgr();
            try {
                modelMgr.init(record);
                // 获取玩家的客房
                let keFangs = modelMgr.world.getKefangs()
                if (!keFangs) return;
                let _t = keFangs.filter(kefang => {
                    return kefang.no == roomId;
                })
                _t && _t.length === 1 && (_t = _t[0]);
                let roomFurnData = await getRoomFurnData(roomId);
                let curData;
                let curId = _t.currFurnitureInfoId;
                if (!curId || !roomFurnData) return;
                curData = _t.furnSaves.filter(_f => {
                    return _f.id === curId;
                })
                if (!curData || (curData && !curData.length)) return;
                curData.length >= 1 && (curData = curData[0]);
                // 计算方案蜡烛
                let heart = 0;
                for (let furniture of curData.furnitures) {
                    !furniture.isSpecialItem() && (roomFurnData.filter(_dt => {
                        if (_dt.id === furniture.id) {
                            let _arr = _dt['unlock_limit'].split(',');
                            _arr.length === 3 && heart < parseInt(_arr[2]) && (heart = parseInt(_arr[2]));
                            return false;
                        }
                    }));
                }
                if (furnMaxHeart && heart < furnMaxHeart) return;
                // 提取存档里的方案信息
                let __kefang = record[`hotel_debug_v1_kefang_${roomId}`] || record[`hotel_kefang_${roomId}`]
                let __room = __kefang[`kefang_${roomId}`];
                let __index = 0;
                if (__room.furnSaves && __room.furnSaves.length > 1 && __room.currFurnitureInfoId) {
                    __room.furnSaves.filter((item, idx) => {
                        if (item.id === __room.currFurnitureInfoId) {
                            __index = idx;
                        }
                    })
                }
                let saveInfo = "";
                __room && __room.furnSaves && __room.furnSaves.length && (saveInfo = JSON.stringify(__room.furnSaves[__index]));
                let save = {
                    uid,
                    roomId,
                    activation, furnitures,
                    recommended, roomlike,
                    heart,
                    saveInfo: saveInfo,
                    state: 0,
                };
                await buildPlanCol.updateOne({uid, roomId}, save, {upsert: true});
            } catch (e) {
                console.log(e);
                return false;
            }
            return true;
        }
        let logic = async (lastId) => {
            let where = {};
            where.roomlike = {};
            minRoomLike && (where.roomlike.$gte = minRoomLike);
            maxRoomLike && (where.roomlike.$lte = maxRoomLike);
            lastId && (where._id = {$gt: lastId});
            let docs = await recommendCol.find(where).sort({_id: 1}).limit(1000);
            await util.promiseMap(docs, async ({uid, roomId, activation, furnitures, recommended, roomlike}) => {
                if (!uid) return;
                await park(uid, roomId, activation, furnitures, recommended, roomlike);
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            } else {
                return 0;
            }
        }
        let exec_ = true;
        let lastId = 0;
        let keys = Object.keys(spePlayers);
        while (exec_) {
            try {
                if (keys && keys.length) {
                    for (let key of keys) {
                        let rooms = spePlayers[key];
                        if (!rooms) continue;
                        for (let roomId of rooms) {
                            await park(key, roomId, [], 0, 0, 0);
                        }
                    }
                    break;
                }
                lastId = await logic(lastId);
            } catch (e) {
            }
            exec_ = !!lastId;
        }
    },
    async submitBuildPlanInfo(uid, roomId, furnNum, heart, saveInfo, tk) {
        if (!saveInfo) return {status: -21};
        saveInfo = JSON.stringify(saveInfo);
        // 查询是否存在投稿记录
        let hasLine = await this.buildPlanCol.findOne({uid, roomId}),
            now = Date.now();
        if (hasLine) {
            //无code  执行check
            if (!tk) {
                if (hasLine.state === 0 && hasLine.ts && hasLine.ts > (now - util.Time.Day)) return {status: -21} // 24小时内只能投一次
                if (hasLine.saveInfo === saveInfo) return {status: -22} // 方案数据雷同,可能该方案已经被精选
                if (hasLine.state === 1 || hasLine.state === 2) {
                    // 方案已经被精选,客户端确认后覆盖
                    return {status: -23, tk: hasLine._id}
                }
                return {status: -25, tk: hasLine._id} // 非精选被覆盖
            }
            let _id = hasLine._id.toString();
            //有code  执行id检查并覆盖
            if (tk && tk !== _id) {
                return {status: -24}// 不应该走到这里
            }
        }
        await this.buildPlanCol.updateOne({uid, roomId}, {
            furnitures: furnNum,
            heart,
            saveInfo,
            state: 0,
            ts: now
        }, {upsert: true});
        // check通过 覆盖保存
        //todo 客户端是每天的数据都存了一次，应该不会每次登录都来拿数据，所以覆盖基本不会出问题
        return {status: 0}
    },
    async clearPlans() {
        logger.info(`clear build plans.`);
        let limit = 5000, where = {}, now = Date.now(), total = 0;
        let logic = async (lastId) => {
            lastId && (where._id = {$gt: lastId});
            let docs = await this.buildPlanCol.find(where).sort({_id: 1}).limit(limit);
            await util.promiseMap(docs, async ({uid, roomId, state, ts}) => {
                if (state === 1 || state === 2) return;
                if (!ts || (ts && now - ts > util.Time.Day * 15)) {
                    await this.buildPlanCol.deleteOne({uid, roomId});
                    total++;
                }
            })
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
        }
        let exec_ = true;
        let lastId = 0;
        while (exec_) {
            lastId = await logic(lastId);
            exec_ = !!lastId;
        }
        logger.info(`clear build plans ===> end, total :${total}`);
    },

}

module.exports = roomMod;
