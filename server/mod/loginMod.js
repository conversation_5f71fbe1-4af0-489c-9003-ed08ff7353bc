const wxMod = require("./wxMod");
const userMod = require("./userMod");
const idMod = require("./idMod");
const logger = require("../common/log").getLogger("loginMod");
const verror = require("verror");
const recordMod = require('./recordMod');
const wxAppMod = require("./wxAppMod");
const appleLoginMod = require("./appleLoginMod");
const verifyMod = require("./verifyMod");
const { USER_TYPE, SYNC_STATE, SpAwardType, SwitchType, Platform, ADD_IP_SCORE } = require("../common/constant");
const db = require("../db/db");
const util = require("../common/util").default;
const shareMod = require("./shareMod");
const redisKey = require("../db/redisKey");
const { expVerMod } = require("./expVerMod")
const config = require("../config");
const qqMod = require("./qqMod");
const facebookLogin = require("./facebookLoginMod");
const statisticsMod = require("./statisticsMod");
const facebookLoginMod = require("./facebookLoginMod");
const { dhlog, DH_LOG_EVENT } = require("../common/DHLog")
const accountMod = require("./accountMod");
const functionMod = require("./functionMod");
const partyMod = require("./partyMod");
const hmsLoginMod = require("./hmsLoginMod");
const robotMod = require("./robotMod");
const activityMod = require("./activityMod");
const currencyModel = require("./currencyModel");
const xiaomiLoginMod = require("./xiaomiLoginMod");

let loginInfoCol, loginGameVersionCol, ipCol, deviceCol, deviceTokenCol, userClientIpCol

// *************
const stop_config = {
    open: false,
    time: *************,//2022-12-06 00:00:00.000
    duration: util.Time.Day,
    notice: "亲爱的各位店长，旅店在2022年12月6日0:00 - 2022年12月7日0:00暂停营业哟~感谢店长们的支持和理解！"
}

const activity_ver = "10.1.0"
const activity_time = [
    *************, *************, *************, *************
]

let loginMod = {
    redis: null,

    init() {
        loginInfoCol = db.createLoginInfoModel()
        loginGameVersionCol = db.createLoginGameVersionModel()
        ipCol = db.createIpModel()
        deviceCol = db.createDeviceModel()
        deviceTokenCol = db.createDeviceToken()
        this.redis = db.redis
        deviceInfoCol = db.createDeviceInfoModel()
        userClientIpCol = db.createUserClientIpModel();
        this.userCancellationCol = db.createUserCancellationModel();
        this.partyCol = db.createGardenPartyModel()
    },

    async wxLogin(reqInfo) {
        let { code, encryptedData, iv } = reqInfo;
        let loginInfo = {};
        if (code) {
            loginInfo = await wxMod.getLoginInfo(code, encryptedData, iv);
            loginInfo.tpid = loginInfo.unionid || loginInfo.openid
            loginInfo.tpid2 = loginInfo.openid
        } else if (!reqInfo.uid) {
            throw verror("uid and code not found", reqInfo)
        }
        let info = {};
        Object.assign(info, loginInfo, reqInfo);
        let newUid = await this.fixWxLogin(info)
        if (info.uid && newUid) {
            info.uid = newUid
        }
        return await this.commonHandle(info, USER_TYPE.WX);
    },

    async qqLogin(reqInfo) {
        let { code, encryptedData, iv } = reqInfo;
        let loginInfo = {};
        if (code) {
            loginInfo = await qqMod.getLoginInfo(code, encryptedData, iv);
            loginInfo.tpid = loginInfo.unionid || loginInfo.openid
            loginInfo.tpid2 = loginInfo.openid
        }
        let info = { userType: USER_TYPE.QQ };
        Object.assign(info, loginInfo, reqInfo);
        return await this.commonHandle(info, USER_TYPE.QQ);
    },

    /*
    code登录微信 -> openid -> 进入游戏后relogin
    code登录微信 -> uninoinid, openid -> check
    code登录app -> 小程序登录无uninoinid -> 小程序走relogin -> app重启  (检测不出来)
    code登录app -> 小程序登录有uninoinid -> check

    小程序uid登录 -> user表无unionid -> 进入游戏后relogin
    小程序uid登录 -> user表有unionid -> check
    app uid登录 ->  小程序无uninoinid -> 小程序走relogin -> app重启
    app uid 登录 -> 小程序有uninoinid -> check
    */
    async fixWxLogin(info) {
        if (info.uid) { //uid登录
            let { unionid, openid, wxAppOpenid } = await userMod.findOne(info.uid, {
                unionid: 1,
                openid: 1,
                wxAppOpenid: 1
            }) || {}
            if (!unionid) return
            let uid = info.uid
            let unionidUID = await idMod.getID(unionid)
            if (uid != unionidUID) { //check 当前uid和unionid
                let result = await this.checkBetter(uid, unionidUID)
                if (result >= 0) { //当前uid的更好
                    await userMod.mergeUser(uid, unionidUID)
                    await idMod.reBindID(unionid, uid, true)
                    logger.info("checkRelogin_uid uid", uid, unionidUID, unionid, result)
                } else {
                    openid = openid || wxAppOpenid
                    await userMod.mergeUser(unionidUID, uid)
                    await idMod.reBindID(openid, unionidUID, true)
                    logger.info("checkRelogin_uid unionid", uid, unionidUID, openid, result)
                    return unionidUID //返回新uid
                }
            }
        } else { //code登录
            let unionid = info.unionid, openid = info.openid
            if (!unionid || !openid) return

            let [unionidUID, openidUID] = await Promise.all([idMod.getID(unionid), idMod.getID(openid)])
            if (unionidUID != openidUID) { //check unioinid和openid
                let result = await this.checkBetter(unionidUID, openidUID)
                if (result >= 0) {
                    logger.info("checkRelogin_code unionid", unionidUID, openidUID, openid, result)
                    await userMod.mergeUser(unionidUID, openidUID)
                    await idMod.reBindID(openid, unionidUID, true)
                } else {
                    await userMod.mergeUser(openidUID, unionidUID)
                    await idMod.reBindID(unionid, openidUID, true)
                    logger.info("checkRelogin_code openid", unionidUID, openidUID, unionid, result)
                }
            }
        }
    },

    async checkBetter(uid1, uid2) {
        if (!uid1) return -1
        if (!uid2) return 1

        let [record1, record2] = await Promise.all([recordMod.getRecord(uid1), recordMod.getRecord(uid2)])
        if (!record1 && !record2) return 0
        if (!record1) return -1
        if (!record2) return 1

        let startSum1 = recordMod.getStarSum(record1)
        let startSum2 = recordMod.getStarSum(record2)
        return startSum1 - startSum2
    },

    async wxRelogin({ uid, code, encryptedData, iv, starSum, nickName, avatarUrl }) {
        let info = await wxMod.getLoginInfo(code, encryptedData, iv);
        if (info.unionid) {
            let tpid = info.unionid;
            uid = await this.reLogin({ tpid, uid, starSum });
            let update = { unionid: info.unionid }
            if (nickName) update.nickName = nickName
            if (avatarUrl) update.avatarUrl = avatarUrl
            await userMod.updateOne(uid, update)
            return { uid }
        } else {
            throw verror({ name: "unionid is null", info });
        }
    },

    async qqRelogin({ uid, code, encryptedData, iv, starSum, nickName, avatarUrl }) {
        let info = await qqMod.getLoginInfo(code, encryptedData, iv);
        if (info.unionid) {
            let tpid = info.unionid;
            uid = await this.reLogin({ tpid, uid, starSum });
            let update = { unionid: info.unionid }
            if (nickName) update.nickName = nickName
            if (avatarUrl) update.avatarUrl = avatarUrl
            await userMod.updateOne(uid, update)
            return { uid }
        } else {
            // qq貌似暂时通过encryptedData无法获取unionId
            // throw verror({name: "unionid is null", info});
            let update = {};
            if (nickName) update.nickName = nickName
            if (avatarUrl) update.avatarUrl = avatarUrl
            await userMod.updateOne(uid, update)
            return { uid }
        }
    },

    async guestLogin(reqInfo) {
        reqInfo.userType = USER_TYPE.GUEST
        let valid = verifyMod.checkPackageSign(reqInfo.sign, reqInfo.gameVer, reqInfo.platform)
        if (!valid) {
            return { status: 0, cheater: true }
        }
        return await this.commonHandle(reqInfo, USER_TYPE.GUEST);
    },

    async fbInstantLogin(reqInfo) {
        reqInfo.userType = USER_TYPE.FBINSTANT
        if (reqInfo.userId) {
            reqInfo.openid = reqInfo.userId
            reqInfo.tpid = reqInfo.userId
        }
        return await this.commonHandle(reqInfo, USER_TYPE.FBINSTANT);
    },

    async webLogin(reqInfo) {
        reqInfo.userType = USER_TYPE.GUEST
        return await this.commonHandle(reqInfo, USER_TYPE.GUEST);
    },

    async wxTestLogin(reqInfo) {
        let result = await this.wxLogin(reqInfo);
        if (result && !reqInfo.uid) {
            if (util.chance(40)) {
                let sourceId = await userMod.getRandomUser()
                if (sourceId) {
                    logger.info("wxTestLogin sourceId", sourceId)
                    await recordMod.copy(sourceId, result.uid)
                }
            }
        }
        return result
    },

    async checkSync({ uid, platform, deviceId, gameVer }, userDoc, lastLoginInfo) {
        if (!deviceId) return { state: SYNC_STATE.SAME }
        let cancelDoc = await this.userCancellationCol.findOne({ uid });
        if (cancelDoc && cancelDoc.timestamp && cancelDoc.timestamp > 0) {
            if (cancelDoc.timestamp < Date.now()) {
                // 账号已被注销的话，无视版本差异，因为要走到checkLogin才能正式注销
                return { state: SYNC_STATE.SAME };
            }
        }
        lastLoginInfo = lastLoginInfo || await loginInfoCol.findOne({ uid })
        let state = SYNC_STATE.SAME
        if (lastLoginInfo) {
            let now = Date.now()

            let usedDevices = lastLoginInfo.usedDevices || []
            let usedPlatforms = lastLoginInfo.usedPlatforms || []
            if (util.cmpVersion(gameVer, "2.9.6") >= 0) {
                if (lastLoginInfo.deviceId && lastLoginInfo.deviceId != deviceId) {
                    let isDiffPlatform = () => {
                        return lastLoginInfo.platform != platform
                    }
                    if (usedDevices.includes(deviceId) || isDiffPlatform()) { //如果不是首次用这个设备号，或者平台不一致
                        let passTime = now - userDoc.updateTime
                        logger.debug("checkSync passTime", now, userDoc.updateTime, passTime)
                        if (passTime < 2.1 * util.Time.Minute || util.cmpVersion(lastLoginInfo.gameVer, "2.9.6") < 0) { //距离上次存档小于3min
                            logger.warn("checkSync pass", uid, passTime, gameVer)
                            state = SYNC_STATE.SYNC
                        } else {
                            let isBlack = await verifyMod.isBlack(uid)
                            if (isBlack) {
                                logger.warn("checkSync black", uid)
                                state = SYNC_STATE.SYNC
                            } else {
                                let isOnline = await userMod.isOnline(uid)
                                if (isOnline) {
                                    logger.warn("checkSync diff online", uid, passTime)
                                    state = SYNC_STATE.DEVICE_DIFF
                                } else {
                                    let logoutTime = userDoc.logoutTime
                                    let updateTime = userDoc.updateTime
                                    if (logoutTime - updateTime < 3 * util.Time.Minute) {
                                        logger.warn("checkSync pass logout", uid, passTime, logoutTime - updateTime)
                                        state = SYNC_STATE.SYNC
                                    } else {
                                        logger.warn("checkSync diff", uid, passTime, logoutTime - updateTime)
                                        state = SYNC_STATE.DEVICE_DIFF
                                    }
                                }
                            }
                        }
                    } else {
                        state = SYNC_STATE.SYNC
                    }
                } else {
                    state = SYNC_STATE.SAME
                }
            } else {
                if (lastLoginInfo.deviceId != deviceId) {
                    let passTime = now - userDoc.updateTime
                    logger.debug("checkSync passTime", now, userDoc.updateTime, passTime)
                    if (passTime < 3 * util.Time.Minute) { //距离上次存档小于3min
                        state = SYNC_STATE.SYNC
                    } else {
                        state = SYNC_STATE.DEVICE_DIFF
                    }
                } else {
                    state = SYNC_STATE.SAME
                }
            }

            let loginGameVersion = await loginGameVersionCol.findOne()
            if (loginGameVersion && loginGameVersion.active) {
                let samePlatformLimit = false
                if (loginGameVersion && loginGameVersion.active) {
                    samePlatformLimit = loginGameVersion.samePlatformLimit
                }
                let needCheckVersiion = true
                if (!samePlatformLimit) { //如果关闭同平台限制，也只看是不是小程序回滚
                    let isWxRollback = lastLoginInfo.platform == platform && (platform == Platform.WX || platform == Platform.QQ)
                    needCheckVersiion = !isWxRollback
                }
                if (needCheckVersiion) {
                    let limit = await this.versionLimitByLogin(lastLoginInfo.gameVer, gameVer, loginGameVersion, uid)
                    if (limit) {
                        logger.warn("VERSION_DIFF ", uid, lastLoginInfo.platform, platform, lastLoginInfo.gameVer, gameVer)
                        state = SYNC_STATE.VERSION_DIFF
                    }
                }
            }

            return { state, platform: lastLoginInfo.platform, updateTime: userDoc.updateTime }
        } else {
            return { state }
        }
    },

    async syncLoginInfo(info) {
        let update = util.setValue2("deviceId|platform|gameVer|ip", info)
        update["$addToSet"] = { usedDevices: update.deviceId, usedPlatforms: update.platform }
        await loginInfoCol.updateOne({ uid: info.uid }, update, { upsert: true })
    },

    async isBlockLogin(uid, loginInfo) {
        if (!config.debug) {
            let [isGood, level] = await Promise.all([verifyMod.isGood(uid), verifyMod.getBlackLevel(uid)])
            if (!isGood && level >= 6) {
                logger.warn("block login", uid, level, loginInfo)
                return true
            }
        }
        return false
    },

    async checkLowVersion({ gameVer, uid, platform }) {
        if (!gameVer || !platform || util.cmpVersion(gameVer, "1.8.0") < 0) {
            logger.warn("low version user", uid, gameVer, platform)
            // return true
        }
        if (util.cmpVersion(gameVer, "3.0.0") < 0) {
            let doc = await loginInfoCol.findOne({ uid })
            if (doc) {
                let limit = await this.versionLimitByLogin(doc.gameVer, gameVer, uid)
                if (limit) {
                    logger.warn("low version user2", uid, doc.gameVer, gameVer, doc.platform, platform)
                    return true
                }
            }
        }
        return false
    },

    // 1.token过期
    // 2.7天内登录过游戏
    // 3.不是常用ip
    async needDoubleCheck(user, loginInfo) {
        let notPassCount = 1;
        Date.now() - user.loginTime < util.Time.Day * 7 && (notPassCount += 1);
        let line = await userClientIpCol.findOne({ uid: user.uid });
        if (!loginInfo.ip) {
            logger.warn(`wrong value with login ip.`);
            loginInfo.ip = '*';// 兼容ip处理错误
        }
        if (line && line.lastLoginIp) {
            if (loginInfo.ip !== '*' && loginInfo && loginInfo.ip !== line.lastLoginIp) {
                // 比较ip得分
                let ipScoreArr = line.ipScore;
                ipScoreArr = ipScoreArr.filter(iis => {
                    return iis.ip === loginInfo.ip && iis.score >= 100;
                })
                !ipScoreArr.length && (notPassCount += 1);
            }
        }
        if (notPassCount === 3) {
            // need return
            logger.warn(`[_TAG_]record as non-regular player：${user.uid}`);
        }
    },

    async addIpScore(uid, ip, num) {
        if (!ip) {
            logger.warn(`wrong value with login ip.`);
            return;
        }
        let doc = await userClientIpCol.findOneAndUpdate({ uid, 'ipScore.ip': ip }, { $inc: { 'ipScore.$.score': num } });
        if (!doc) {
            await userClientIpCol.updateOne({ uid }, {
                lastLoginIp: ip,
                $addToSet: { ipScore: { ip, score: num } }
            }, { upsert: true });
        }
    },
    async commonHandle(loginInfo, type, isRelogin) {
        // 临时停服配置检测
        if (!stop_config.open && stop_config.time <= Date.now() && stop_config.time + stop_config.duration > Date.now()) {
            return {
                status: 17903,
                notice: stop_config.notice
            }
        }
        let uid = loginInfo.uid;

        let result
        if (config.experienceVer) {
            result = await this.loginExpVer(loginInfo)
        } else {
            result = await this.login(loginInfo);
        }

        if (result) {
            if (result.isBlock) {
                let blackDoc = await verifyMod.getBlackById(result.uid)
                if (blackDoc) {
                    let canDeblock = blackDoc.canDeblock
                    return { status: -10087, uid: result.uid, canDeblock }
                }
            }

            let { userDoc, isNew } = result
            let info = {};
            info.uid = userDoc.uid
            info.index_id = userDoc.index_id
            loginInfo.uid = uid = userDoc.uid
            if (loginInfo.userType == USER_TYPE.APP_WX) {
                info.openid = userDoc.wxAppOpenid
            } else {
                info.openid = userDoc.openid;
            }

            if (!isRelogin) {
                if (await this.checkLowVersion(loginInfo)) {
                    return { status: -10088 }
                }
            }

            info.serverId = userDoc.serverId;
            let gm = await verifyMod.isGM(userDoc.uid)
            if (gm) {
                info.gm = gm
            }
            info.userType = userDoc.userType;
            info.signupTime = userDoc.signupTime
            info.updateTime = userDoc.updateTime || userDoc.loginTime
            info.loginTime = userDoc.loginTime
            // 上次游戏登出时间
            info.logoutTime = userDoc.logoutTime

            if (userDoc.avatarUrl) {
                info.avatarUrl = userDoc.avatarUrl
            }
            info.diyAvatar = userDoc.diyAvatar || userDoc.avatarUrl
            if (userDoc.nickName) {
                info.nickName = userDoc.nickName
            }
            info.nickName = userDoc.diyName || userDoc.nickName
            userMod.updateDiyInfo(userDoc)

            let lastLoginInfo = await loginInfoCol.findOne({ uid })

            let syncInfo = await this.checkSync(loginInfo, userDoc, lastLoginInfo)
            if (syncInfo.state == SYNC_STATE.SAME) {
                await this.syncLoginInfo(loginInfo)
            } else {
                if (syncInfo.state == SYNC_STATE.DEVICE_DIFF) {
                    let record = await recordMod.getRecord(uid) || {}
                    syncInfo.biscuits = recordMod.getFromRecord("global.biscuits", record) || 0
                    syncInfo.heart = recordMod.getFromRecord("global.heart", record) || 0
                }
                info.syncInfo = syncInfo
            }

            //app小程序登录奖励
            if (syncInfo.platform && syncInfo.platform != loginInfo.platform) {
                let check = (platform1, platform2) => {
                    if ((platform1 == 'android' || platform1 == "ios") && platform2 == "wx") return true
                    return false
                }
                let addAward = check(syncInfo.platform, loginInfo.platform) || check(loginInfo.platform, syncInfo.platform)
                if (addAward) {
                    await userMod.addSpAward(uid, SpAwardType.APP_WX_LOGIN)
                }
            }

            let [awards, switches, blackDoc, age, nonagePlayTime, isGuessAdult] = await Promise.all([
                userMod.getSpAward(uid, [SpAwardType.APP_WX_LOGIN]),
                this.getSwitches(loginInfo.gameVer, loginInfo.platform),
                verifyMod.getBlackById(uid),
                userMod.getAge(uid, loginInfo.userType),
                userMod.getNonagePlayTime(),
                userMod.isGuessAdult(uid),
            ])

            if (blackDoc && blackDoc.canDeblock) {
                info.canDeblock = true
            }

            if (awards.length > 0 || !switches.includes(SwitchType.APP_WX_LOGIN) || !userDoc.unionid) {
                info.appWxLogined = info.appWxLogin = true
            }
            // if (util.cmpVersion(loginInfo.gameVer, "1.8.5") < 0) {
            //     if (!loginInfo.deviceId || (!loginInfo.deviceId.includes("iPhone") && !loginInfo.deviceId.includes("iPad"))) {
            //         info.appWxLogin = true
            //     }
            // }

            info.spAward = awards

            if (switches.length > 0) {
                info.switchs = switches
            }
            if (isGuessAdult && age < 18) {
                age = 18
            }
            info.age = age
            info.nonagePlayTime = nonagePlayTime

            let launchInfo = await this.handleLaunchInfo(userDoc, loginInfo.launchInfo, true)
            if (launchInfo) info.launchInfo = launchInfo

            let tokenExpire = isRelogin ? false : true
            if (loginInfo.token) {
                tokenExpire = !await userMod.hasToken(uid, loginInfo.token)
            }

            if ((loginInfo.code || loginInfo.tpid) && tokenExpire) {
                info.token = await userMod.genToken(uid, type)
                tokenExpire = false

            }

            if (tokenExpire) {
                info.tokenExpire = true;
            }


            if (loginInfo.code && (type == USER_TYPE.APP_WX || type == USER_TYPE.WX) && loginInfo.scope) {
                //"snsapi_userinfo,snsapi_friend,snsapi_message"
                if (loginInfo.scope == "snsapi_userinfo") {
                    let lockTime = 0 * util.Time.Hour
                    if (lastLoginInfo && lastLoginInfo.ip && loginInfo.ip != lastLoginInfo.ip) {
                        lockTime = 5 * util.Time.Hour
                        logger.error("snsapi_userinfo ip diff", uid, loginInfo, lastLoginInfo)
                    } else {
                        logger.error("snsapi_userinfo ip same", uid, loginInfo, lastLoginInfo)
                    }
                    if (lockTime > 0) {
                        let key = redisKey.recordUploadLock(uid)
                        await this.redis.set(key, 1)
                        this.redis.pexpire(key, lockTime)
                    }
                }
            }
            let score = 0;
            if (isNew) {
                info.isNew = true;
                // 注册加分
                type !== USER_TYPE.GUEST ? (score += ADD_IP_SCORE.REGISTER_NOT_TOURISTS) : (score += ADD_IP_SCORE.REGISTER_TOURISTS);
            } else {
                // 登录加分
                score += ADD_IP_SCORE.LOGIN;
            }
            // if (info.tokenExpire) {
            //     await this.needDoubleCheck(userDoc, loginInfo);
            // } else {
            //     await this.addIpScore(uid, loginInfo.ip, score);
            // }

            info.sessionId = userMod.makeSessionId(uid)

            if (!isRelogin && (syncInfo.state == SYNC_STATE.SAME || syncInfo.state == SYNC_STATE.SYNC)) {
                let { sessionKey } = await this.reconnect(loginInfo, userDoc, isNew)
                info.sessionKey = sessionKey
            }

            if (loginInfo.deviceToken) {
                this.updateDeviceToken(uid, loginInfo.deviceToken, loginInfo.platform)
            }
            // 是否有正在筹备 或者 筹备完成的派对
            let party = await this.partyCol.findOne({ uid, status: { $in: [0, 1] } })
            info.party = party ? party.partyId : 0
            info.likeNum = userDoc.likeNum || 0
            // 参加的派对
            if (userDoc && userDoc.gardenPartyJoin) {
                for (const id of userDoc.gardenPartyJoin) {
                    let party = await this.partyCol.findOne({ _id: id })
                    if (!party) {
                        await userMod.updateOne({ uid }, { $pull: { gardenPartyJoin: id } })
                        continue
                    }
                    const joiner = party.joiner
                    if (!joiner || !joiner.length) {
                        await userMod.updateOne({ uid }, { $pull: { gardenPartyJoin: id } })
                        continue
                    }
                    if (joiner.find(j => j.includes(uid)) == null) {
                        await this.partyCol.updateOne({ _id: id }, { $pull: { joiner: uid } })
                    }
                }
            }
            activityMod.handleLoginTask(uid)
            // 机器人好友部分逻辑
            await robotMod.dispatchPlayerRobotFriend(uid)
            await robotMod.dispatchPlayerRobotWork(uid)
            info.pay = await currencyModel.getUserTotalPay(uid);
            info.charge = await currencyModel.getUserTotalBal(uid);
            const gameVer = loginInfo.gameVer || ""
            if (util.cmpVersion(gameVer, activity_ver) >= 0) {
                info.act = activity_time
            } else {
                info.act = []
            }
            return info;
        } else {
            return { status: -1 }//兼容账号找不到的情况
        }
    },

    async reportDHLog(info, userDoc, { isNew, lastSessionKey }) {
        if (!config.dhlog) return
        try {
            let uid = userDoc.uid
            let deviceInfo = {
                "adid": "",//广告ID,安卓是Google Advertising ID ;ios是IDFA
                "idfv": "",//idfv（仅ios）
                "imei": "",//国际移动设备识别码（仅安卓）
                "android_id": "",//安卓id，安卓设备号（仅安卓）
                "appsflyer_id": "",//appsflyer sdk得到的id
                "device_token": "",//推送消息用的token
                "mac_address": "",//mac地址
                "device_model": "",//设备型号
                "device_name": "",//设备名字
                "os_version": "",//手机系统版本
                "network_type": "",//网络类型，1是数据 2是Wi-Fi
                language: "",
                app_version: "",
            }
            let userInfo = {
                platform: (info.platform && info.platform.toUpperCase()) || "ANDROID"
            }
            if (info.dhlog) {
                deviceInfo = info.dhlog.deviceInfo
                userInfo = info.dhlog.userInfo
            } else {
                if (info.deviceToken) {
                    deviceInfo.deviceToken = info.deviceToken
                } else {
                    let deviceTokenDoc = await deviceTokenCol.findOne({ uid })
                    if (deviceTokenDoc) {
                        deviceInfo.device_token = deviceTokenDoc.token
                    }
                }
                deviceInfo.language = dhlog.transLang(userDoc.lang)
                deviceInfo.app_version = info.gameVer || ""
            }
            deviceInfo.ip = info.ip
            let user_id = userDoc.index_id
            if (!user_id) return
            user_id = String(user_id)
            userInfo.account = user_id
            userInfo.user_name = userDoc.diyName || userDoc.nickName || ""
            if (deviceInfo.adid) {
                if (deviceInfo.adid.startsWith("did not")) { //兼容
                    deviceInfo.adid = ""
                }
            }
            Object.assign(userInfo, await userMod.getDHLogInfo(userDoc.uid, userDoc))
            if (isNew) {
                userInfo.lv = 0
                await dhlog.report(DH_LOG_EVENT.REGISTER, deviceInfo, userInfo)
            }
            if (!userInfo.lv || userInfo.lv <= 0) {
                let record = await recordMod.getRecord(userDoc.uid)
                let heart = recordMod.getStarSum(record)
                userInfo.lv = heart
            }
            logger.info("lastSessionKey: ", uid, lastSessionKey)
            if (lastSessionKey) {
                userMod.reportDHLogout(uid, userDoc, lastSessionKey)
            }

            await dhlog.report(DH_LOG_EVENT.LOGIN, deviceInfo, userInfo)
        } catch (error) {
            logger.error("reportDHLog", error)
        }
    },

    async updateDeviceToken(uid, token, platform) {
        await deviceTokenCol.updateOne({ uid, platform }, { token }, { upsert: true })
    },

    async getSwitches(gameVer, platform) {
        let types = [
            [SwitchType.APP_WX_LOGIN],
            [SwitchType.SHARE_REWARD, false],
            [SwitchType.APP_WX_LOGIN_BY_PLAYER],
            [SwitchType.ATT],
            [SwitchType.AD_SKIP_CARD, false],
            [SwitchType.MT_LINK, false],
        ]

        let switches = []
        await util.promiseMap(types, async ([type, active]) => {
            if (await verifyMod.isOpen(type, active)) {
                switches.push(type)
            }
        })

        if (platform && gameVer) {
            let ios = await functionMod.get("IOS_AUDIT");
            if (ios && ios.isOpen) {
                let thisVer = ios.extends ? JSON.parse(ios.extends).version : "";
                if (thisVer && platform == Platform.IOS && util.cmpVersion(gameVer, thisVer) >= 0) { //临时
                    let index = switches.indexOf(SwitchType.ATT)
                    if (index >= 0) {
                        switches.splice(index, 1)
                    }
                }
            }
        }

        return switches
    },

    async handleLaunchInfo(userDoc, launchInfo, isLogin) {
        try {
            if (!launchInfo || Object.keys(launchInfo).length <= 0) return
            if (launchInfo.shareMessageToFriendScene) return
            let uid = userDoc.uid
            let sourceUid = launchInfo.uid
            if (launchInfo.type == null) return
            if (sourceUid && uid != sourceUid) {
                let type = Number(launchInfo.type)
                let signupTime = userDoc.signupTime
                let loginTime = userDoc.lastLoginTime
                // if (type < 1000) { //到时候区分大类，如活动
                let info = { type, imageName: launchInfo.imageName, isLogin }
                let isNew = await userMod.isNewUser(userDoc.uid, signupTime)
                if (isNew) {
                    info.isNew = true
                } else if (Date.now() - loginTime >= 14 * util.Time.Day) { //回归用户
                    info.isOld = true
                }
                shareMod.receive(uid, sourceUid, info)
                await activityMod.handleInviteTask(sourceUid, info.isNew, info.isOld, 1)
                return info
                // }
            }
        } catch (error) {
            logger.error("handleLaunchInfo error", userDoc, launchInfo, isLogin, error)
        }
    },

    /**
     * 本地无数据
     * 直接三方登录, tpid
     * 游客登录, tpid = null, uid = null
     *
     * 本地有数据
     * 普通登录 uid
     */
    async login(info) {
        if (info.uid && info.tpid) { //游客转三方，放到relogin
            throw verror({ name: "登录参数错误", info: info })
        }

        let uid = info.uid;

        if (info.tpid) { //直接三方登录
            uid = await idMod.getID(info.tpid);
        }
        if (!uid && info.tpid2 && info.tpid2 != info.tpid) {
            uid = await idMod.getID(info.tpid2);
        }

        if (!uid) {
            // let ip = "*************"
            // let port = ""
            // let tpid = info.tpid || info.open_id
            // if (tpid) {
            //     switch (info.userType) {
            //         case USER_TYPE.TAO:
            //             port = "8040"
            //             break
            //         case USER_TYPE.Alipay:
            //             port = "8052"
            //             break
            //     }
            //     uid = await this.handleCrossCopy(ip, port, tpid)
            // }
        }

        let userDoc;
        let isNew = false
        let lastLoginTime = 0
        if (uid) { //已注册，直接更新登录信息
            let isBlock = await this.isBlockLogin(uid, info)
            if (isBlock) {
                return { isBlock, uid }
            }
            const line = await userMod.findOne(uid, { loginTime: 1 })
            line && (lastLoginTime = line.loginTime)
            info.loginTime = Date.now();
            try {
                userDoc = await userMod.updateOne(uid, info);
                logger.info("login: ", uid, JSON.stringify(info));

            } catch (error) {
                if (error.name == "user not found") {
                    logger.error("login not found", uid, info)
                    if (config.debug) {
                        await idMod.removeByTpid(info.tpid)
                        await idMod.removeByTpid(info.tpid2)
                    }
                    return null
                } else {
                    throw error
                }
            }
        } else {
            userDoc = await this.signup(info);
            isNew = true
        }
        userDoc.lastLoginTime = lastLoginTime || Date.now()
        return { isNew, userDoc };
    },

    async signup(userInfo) {
        let nowTime = new Date().getTime();

        if (!userInfo) {
            userInfo = {};
        }

        userInfo.loginTime = nowTime;
        userInfo.signupTime = nowTime;
        userInfo.uid = userInfo.uid || idMod.genUID()

        let uid = userInfo.uid;
        let tpid = userInfo.tpid;
        let tpid2 = userInfo.tpid2;

        let userDoc = await userMod.newUser(userInfo);

        if (tpid) { //非游客才绑定
            await idMod.bindID(tpid, uid); //建立映射关系，这一步需要保证在newUser之后
        }
        if (tpid2 && tpid2 != tpid) { // 处理微信小程序的openid和app登录wx的openid不一样
            await idMod.bindID(tpid2, uid);
        }

        statisticsMod.updateUserHeart(uid, 0)
        statisticsMod.updateUserGuide(uid, 1, 0)

        logger.info("signup: ", uid, tpid, JSON.stringify(userInfo));

        return userDoc;
    },

    async reLogin({ uid, tpid, starSum }) {
        let orgUid = await idMod.getID(tpid);
        if (orgUid == uid) {
            return uid;
        }
        if (orgUid) {
            let orgRecord = await recordMod.getRecord(orgUid);
            let orgStarSum = recordMod.getStarSum(orgRecord);
            if (starSum >= orgStarSum) {
                await idMod.reBindID(tpid, uid);
                logger.info("reLogin new: ", uid, tpid, orgUid, starSum, orgStarSum);
            } else {
                logger.info("reLogin old: ", uid, tpid, orgUid, starSum, orgStarSum);
                uid = orgUid;
            }
        } else { //游客转三方，openid转unioinid的情况
            // 如果是注销的用户  就不要重登录了
            await idMod.bindID(tpid, uid);
            // 游客绑定 上一次登录是有效的
            //let info = await loginInfoCol.findOne({uid})
            //info && await this.addIpScore(uid, info.ip, ADD_IP_SCORE.BIND_ACCOUNT);
        }
        await userMod.mergeUser(uid, orgUid)
        return uid;
    },

    async wxAppLogin(reqInfo, isRelogin) {
        let { code } = reqInfo;
        let loginInfo = {};
        if (code) {
            let { openid, access_token, scope } = await wxAppMod.code2Session(code);
            loginInfo = await wxAppMod.getUserInfo(access_token, openid);
            loginInfo.tpid = loginInfo.unionid || loginInfo.openid;
            loginInfo.tpid2 = loginInfo.openid;
            loginInfo.nickName = loginInfo.nickname;
            loginInfo.avatarUrl = loginInfo.headimgurl;
            loginInfo.scope = scope
        }
        let info = { userType: USER_TYPE.APP_WX };
        Object.assign(info, loginInfo, reqInfo);

        let newUid = await this.fixWxLogin(info)
        if (info.uid && newUid) {
            info.uid = newUid
        }
        if (!info.nickName && info.nickname) {
            info.nickName = info.nickname;
        }
        if (!info.avatarUrl && info.headimgurl) {
            info.avatarUrl = info.headimgurl;
        }
        if (info.openid) {
            info.wxAppOpenid = info.openid;
            delete info.openid
        }
        return await this.commonHandle(info, USER_TYPE.APP_WX, isRelogin);
    },

    async wxAppRelogin(reqInfo) {
        let { code, uid, starSum } = reqInfo, loginInfo;
        if (code && uid) {
            let { openid, access_token, scope } = await wxAppMod.code2Session(code);
            loginInfo = await wxAppMod.getUserInfo(access_token, openid);
            loginInfo.userType = USER_TYPE.APP_WX
            loginInfo.scope = scope
            if (loginInfo.unionid) {
                let tpid = loginInfo.unionid;
                loginInfo.uid = await this.reLogin({ tpid, uid, starSum });
                let data = await this.wxAppLogin(loginInfo, true);
                data.uid = loginInfo.uid;
                return data;
            } else {
                let errinfo = {};
                Object.assign(errinfo, loginInfo, reqInfo);
                throw verror({ name: "微信应用登录未知错误", info: errinfo });
            }
        } else {
            throw verror({ name: "微信应用登录参数错误", info: reqInfo })
        }
    },

    // 三个地方使用
    async appleLogin(reqInfo, isRelogin) {
        let { code, nickName, userId } = reqInfo, loginInfo = {};
        // if (reqInfo.logined && userId) {
        //     loginInfo.tpid = userId;
        //     loginInfo.openid = userId;
        //     return await this.commonHandle(loginInfo);
        // } else if (code && userId) {
        //     let appleToken =  await appleLoginMod.createToken();
        //     let info = await appleLoginMod.checkCode(appleToken, code);
        //     if (info.sub == userId) {
        //         loginInfo.tpid = info.sub;
        //         loginInfo.openid = info.sub;
        //         loginInfo.nickName = nickName || info.email;
        //         return await this.commonHandle(loginInfo);
        //     } else {
        //         let errinfo = {};
        //         Object.assign(errinfo, info, reqInfo);
        //         throw verror({name: "苹果登录未知错误", info: errinfo});
        //     }
        // } else if (uid) {
        //     reqInfo.userType = USER_TYPE.APPLE;
        //     return await this.commonHandle(reqInfo);
        // } else {
        //     throw verror({name: "苹果登录参数错误", info: reqInfo})
        // }

        if (code) {
            let appleToken = await appleLoginMod.createToken();
            let info = await appleLoginMod.checkCode(appleToken, code);
            if (info.sub == userId) {
                loginInfo.tpid = info.sub;
                loginInfo.openid = info.sub;
                loginInfo.nickName = nickName || info.email;
            } else {
                let errinfo = {};
                Object.assign(errinfo, info, reqInfo);
                throw verror({ name: "苹果登录未知错误", info: errinfo });
            }
        }

        let info = { userType: USER_TYPE.APPLE };
        Object.assign(info, loginInfo, reqInfo);
        if (!info.openid && userId) {
            info.openid = userId;
        }
        if (!info.openid && reqInfo.sub) {
            info.openid = reqInfo.sub;
        }
        if (!info.tpid && userId) {
            info.tpid = userId;
        }
        return await this.commonHandle(info, USER_TYPE.APPLE, isRelogin);
    },

    async appleRelogin(reqInfo) {
        let { code, uid, userId, starSum } = reqInfo, loginInfo = { userType: USER_TYPE.APPLE };
        if (code && uid) {
            if (reqInfo.logined) {
                let tpid = userId;
                loginInfo.uid = await this.reLogin({ tpid, uid, starSum });
                return await this.appleLogin(loginInfo);
            } else {
                let appleToken = await appleLoginMod.createToken();
                loginInfo = await appleLoginMod.checkCode(appleToken, code);
                if (loginInfo.sub == userId) {
                    let tpid = loginInfo.sub;
                    loginInfo.uid = await this.reLogin({ tpid, uid, starSum });
                    let data = await this.appleLogin(loginInfo, true);
                    data.uid = loginInfo.uid;
                    return data;
                } else {
                    let errinfo = {};
                    Object.assign(errinfo, info, reqInfo);
                    throw verror({ name: "苹果登录未知错误", info: errinfo });
                }
            }
        } else {
            throw verror({ name: "登录参数错误", info: reqInfo })
        }
    },

    async appLogin(reqInfo) {
        let { uid, sign, gameVer, platform } = reqInfo;
        let cheater = !verifyMod.checkPackageSign(sign, gameVer, platform)
        if (cheater) {
            return { status: 0, cheater }
        }
        if (!gameVer || !platform || util.cmpVersion(gameVer, "1.8.0") < 0) {
            logger.warn("low version user", reqInfo.uid, gameVer, platform)
            return { status: 0, cheater }
        }

        if (uid) {
            let userInfo = await userMod.findOne(uid);
            if (!userInfo) return { status: -1 }
            let { userType } = userInfo;
            if (userType == USER_TYPE.APP_WX) {
                return this.wxAppLogin(reqInfo);
            } else if (userType == USER_TYPE.APPLE) {
                return this.appleLogin(reqInfo);
            } else if (userType == USER_TYPE.FB) {
                return this.facebookLogin(reqInfo)
            } else if (userType == USER_TYPE.TWITTER) {
                return this.twitterLogin(reqInfo)
            } else if (userType == USER_TYPE.GUEST) {
                return this.guestLogin(reqInfo);
            } else if (userType == USER_TYPE.GOOGLE) {
                return this.googleLogin(reqInfo)
            } else if (userType == USER_TYPE.HMS) {
                return this.hmsLogin(reqInfo)
            } else if (userType == USER_TYPE.XIAOMI) {
                return this.xiaomiLogin(reqInfo)
            } else {
                throw verror({ name: "user数据字段错误", info: userInfo })
            }
        } else {
            throw verror({ name: "applogin参数错误", info: reqInfo })
        }
    },

    async updateLoginGameVersion({ _id, version, active, samePlatformLimit, maxVersion }) {
        let update = {}
        if (version) {
            update.version = version
        }
        if (active !== undefined) {
            update.active = active
        }
        if (samePlatformLimit) {
            update.samePlatformLimit = samePlatformLimit
        }
        if (maxVersion) {
            update.maxVersion = maxVersion
        }
        if (util.cmpVersion(version, maxVersion) > 0) {
            return false
        }
        if (!_id) {
            await loginGameVersionCol.create(update)
        } else {
            await loginGameVersionCol.updateOne({ _id }, update)
        }
        return true
    },

    async getLoginGameVersion() {
        return loginGameVersionCol.findOne({})
    },

    async loginExpVer(info) { //体验版登录
        if (info.uid && info.tpid) { //游客转三方，放到relogin
            throw verror({ name: "登录参数错误", info: info })
        }

        let uid = info.uid;

        if (info.tpid) { //直接三方登录
            uid = await idMod.getID(info.tpid);
        }
        if (!uid && info.tpid2 && info.tpid2 != info.tpid) {
            uid = await idMod.getID(info.tpid2);
        }

        let userDoc;
        let isNew = false
        if (uid) { //已注册，直接更新登录信息
            let needSync = await expVerMod.needSync(uid)
            if (needSync) {
                await expVerMod.syncByUid(uid, info.gameVer)
            }
            info.loginTime = Date.now();
            try {
                userDoc = await userMod.updateOne(uid, info);
                logger.info("login: ", uid, JSON.stringify(info));
            } catch (error) {
                if (error.name == "user not found") {
                    logger.error("login not found", uid, info)
                    if (config.debug) {
                        await idMod.removeByTpid(info.tpid)
                        await idMod.removeByTpid(info.tpid2)
                    }
                    return null
                } else {
                    throw error
                }
            }
        } else {
            userDoc = await expVerMod.syncByTpid(info.tpid, info.tpid2, info.gameVer)
            if (!userDoc) {
                userDoc = await this.signup(info);
                isNew = true
            }
        }

        return { isNew, userDoc };
    },

    async updateIp(data, req, reqInfo) {
        // let uid = data && data.uid
        // if (!uid) return
        // let ip = util.getClientIp(req)
        // await ipCol.updateOne({ uid, ip }, { $inc: { count: 1 } }, { upsert: true })
        // let deviceId = reqInfo && reqInfo.deviceId
        // if (deviceId) {
        //     await deviceCol.updateOne({ uid, deviceId }, { $inc: { count: 1 } }, { upsert: true })
        // }
    },

    async genToken(uid, type, code) {
        let userDoc = await userMod.findOne(uid)
        if (!userDoc) throw verror({ name: 'user not found', info: { uid } })
        let { userType } = userDoc;
        if (userType != type) {
            if (config.debug || userType == USER_TYPE.WX && type == USER_TYPE.APP_WX || //userType区分不出来是微信小程序还是微信app上传的，所以这里跳过校验
                userType == USER_TYPE.APP_WX && type == USER_TYPE.WX) {
                userType = type
            } else {
                logger.error("genToken type error", uid, `[${userType}, ${type}]`, code)
                return { status: -1 }
            }
        }

        if (userType == USER_TYPE.WX) {
            let { unionid, openid } = await wxMod.code2Session(code)
            if (userDoc.unionid != unionid && userDoc.openid != openid) {
                logger.error("genToken wx err", uid, `[${userDoc.unionid}, ${unionid}]`, `[${userDoc.openid}, ${openid}]`)
                let doc = await idMod.getID(unionid)
                if (!doc) {
                    doc = await idMod.getID(openid)
                }
                if (doc && doc.uid != uid) {
                    logger.error("genToken wx 2err", uid, `[${userDoc.unionid}, ${unionid}]`, `[${userDoc.openid}, ${openid}]`)
                    return { status: -2 }
                }
                // return {status: -2}
            }
        } else if (userType == USER_TYPE.QQ) {
            let { unionid, openid } = await qqMod.code2Session(code)
            if (openid != userDoc.openid) {
                logger.error("genToken qq 2err", uid, `[${userDoc.openid}, ${openid}]`)
                return { status: -2 }
            }
        } else if (userType == USER_TYPE.APP_WX) {
            let { openid } = await wxAppMod.code2Session(code)
            if (userDoc.wxAppOpenid != openid) {
                logger.error("genToken wxApp err", uid, `[${userDoc.wxAppOpenid}, ${openid}]`)
                let doc = await idMod.getID(openid)
                if (doc && doc.uid != uid) {
                    logger.error("genToken wxApp 2err", uid, `[${userDoc.wxAppOpenid}, ${openid}]`)
                    return { status: -2 }
                }
                // return {status: -2}
            }

        } else if (userType == USER_TYPE.APPLE) {
            let appleToken = await appleLoginMod.createToken();
            let info = await appleLoginMod.checkCode(appleToken, code);
            if (info.sub != userDoc.openid) {
                logger.error("genToken apple err", uid, userDoc.openid, info)
                let doc = await idMod.getID(info.sub)
                if (doc && doc.uid != uid) {
                    logger.error("genToken wxAapplepp 2err", uid, userDoc.openid, info)
                    return { status: -2 }
                }
                // return {status: -2}
            }
        } else if (userType == USER_TYPE.FB) {
            let { fbId, fbToken } = code
            if (fbId != userDoc.openid) {
                logger.error("genToken fb err", uid, userDoc.openid, info)
                return { status: -2 }
            }
            await facebookLoginMod.getUserInfo(fbId, fbToken)
        } else if (userType == USER_TYPE.TWITTER) {
            if (code != "RJ1qrbdT") {
                logger.error("genToken twitter err", uid, code)
                return { status: -2 }
            }
        } else if (userType == USER_TYPE.GUEST) {
            if (code != "RJ1qrbdP") {
                logger.error("genToken guest err", uid, code)
                return { status: -2 }
            }
        } else if (userType == USER_TYPE.GOOGLE) {
            if (code != "RJ1qrbdG") {
                logger.error("genToken google err", uid, code)
                return { status: -2 }
            }
        } else if (userType == USER_TYPE.HMS) {
            if (code != "RJ1qrbdG") {
                logger.error("genToken hms err", uid, code)
                return { status: -2 }
            }
        } else if (userType == USER_TYPE.FBINSTANT) {
            if (code != "RJ1qrbdG") {
                logger.error("genToken fb instant err", uid, code)
                return { status: -2 }
            }
        } else if (userType == USER_TYPE.XIAOMI) {
            if (code != "RJ1qrbdG") {
                logger.error("genToken xiaomi err", uid, code)
                return { status: -2 }
            }
        } else {
            throw verror({ name: "genToken数据字段错误", info: { uid, type, code, userType } })
        }

        let token = await userMod.genToken(uid, type)
        return { status: 0, token }
    },

    async isFixWudong(uid) {
        //临时fix
        let loginDoc = await loginInfoCol.findOne({ uid })
        if (loginDoc && util.cmpVersion("2.3.5", loginDoc.gameVer) == 0) {
            logger.error("fix getWudongForWork", uid)
            return true
        }
        return false
    },

    async versionLimitByLogin(fromVer, toVer, loginGameVersion, uid) {
        // 排除GM
        if (uid && await verifyMod.isGM(uid)) return false;
        //如果从低版本到高版本，则没有限制
        if (util.cmpVersion(fromVer, toVer) <= 0) return false

        loginGameVersion = loginGameVersion || await loginGameVersionCol.findOne()
        if (loginGameVersion && loginGameVersion.active) {
            let version = loginGameVersion.version
            let maxVersion = loginGameVersion.maxVersion

            let result1 = util.cmpVersion(fromVer, version) >= 0
            let result2 = util.cmpVersion(toVer, version) >= 0
            let result3 = true
            let result4 = true
            if (maxVersion) {
                result3 = util.cmpVersion(fromVer, maxVersion) <= 0
                result4 = util.cmpVersion(toVer, maxVersion) <= 0
            }
            if (result1 && result2 && result3 && result4) { //两个切换的版本都在最低版本和最高版本之间时，不做版本限制
                return false
            }
            return true
        }

        return false
    },

    async versionLimit(fromVer, toVer) {
        let loginGameVersion = await loginGameVersionCol.findOne()
        if (loginGameVersion && loginGameVersion.active) {
            let version = loginGameVersion.version
            let result1 = util.cmpVersion(toVer, version)
            let result2 = util.cmpVersion(version, fromVer)
            if (result1 >= 0 && result2 > 0) {
                return true
            }
        }
        return false
    },

    async facebookLogin(reqInfo, isRelogin) {
        let { fbId, fbToken, jwtToken } = reqInfo;
        if (fbId && fbToken) {
            let info = await facebookLogin.getLoginInfo(fbId, fbToken, jwtToken);
            info.tpid = fbId
            Object.assign(reqInfo, info)
        }
        return this.commonHandle(reqInfo, USER_TYPE.FB, isRelogin);
    },

    async facebookRelogin(reqInfo) {
        let { fbId, fbToken, jwtToken, uid, starSum } = reqInfo
        if (fbId && fbToken && uid) {
            let loginInfo = await facebookLogin.getLoginInfo(fbId, fbToken, jwtToken);
            loginInfo.uid = await this.reLogin({ tpid: fbId, uid, starSum })
            let data = await this.facebookLogin(loginInfo, true)
            data.uid = loginInfo.uid
            return data
        } else {
            throw verror({ name: "facebookRelogin args err", info: reqInfo })
        }
    },

    async hmsLogin(reqInfo, isRelogin) {
        let { userId, code } = reqInfo;
        if (userId && code) {
            let info = await hmsLoginMod.getLoginInfo(userId, code);
            info.tpid = userId
            Object.assign(reqInfo, info)
        }
        return this.commonHandle(reqInfo, USER_TYPE.HMS, isRelogin);
    },
    async hmsReLogin(reqInfo) {
        let { userId, code, uid, starSum } = reqInfo
        if (userId && code && uid) {
            let loginInfo = await hmsLoginMod.getLoginInfo(userId, code);
            loginInfo.uid = await this.reLogin({ tpid: userId, uid, starSum })
            let data = await this.hmsLogin(loginInfo, true)
            data.uid = loginInfo.uid
            return data
        } else {
            throw verror({ name: "hmsReLogin args err", info: reqInfo })
        }
    },

    async xiaomiLogin(reqInfo, isRelogin) {
        let { userId, sessionId, nickName } = reqInfo;
        if (userId && sessionId) {
            let info = await xiaomiLoginMod.getLoginInfo(userId, sessionId, nickName);
            info.tpid = userId
            Object.assign(reqInfo, info)
        }
        reqInfo.userType = USER_TYPE.XIAOMI
        return this.commonHandle(reqInfo, USER_TYPE.XIAOMI, isRelogin);
    },
    async xiaomiRelogin(reqInfo) {
        let { userId, sessionId, nickName, uid, starSum } = reqInfo
        if (userId && sessionId && uid) {
            let loginInfo = await xiaomiLoginMod.getLoginInfo(userId, sessionId, nickName);
            loginInfo.uid = await this.reLogin({ tpid: userId, uid, starSum })
            let data = await this.xiaomiLogin(loginInfo, true)
            data.uid = loginInfo.uid
            return data
        } else {
            throw verror({ name: "xiaomiReLogin args err", info: reqInfo })
        }
    },
    async myLogin(reqInfo, isRelogin) {
        let { open_id, uid } = reqInfo;
        // 因为淘宝客户端始终携带openid，所以优先使用openid去登录
        if (open_id) {
            delete reqInfo.uid
            reqInfo.tpid = open_id
        } else if (uid) {
            delete reqInfo.open_id
        }
        reqInfo.userType = USER_TYPE.TAO

        return this.commonHandle(reqInfo, USER_TYPE.TAO, isRelogin);
    },
    async myReLogin(reqInfo) {
        let { open_id, uid, starSum } = reqInfo
        if (open_id && uid) {
            let loginInfo = {}
            loginInfo.uid = await this.reLogin({ tpid: open_id, uid, starSum })
            let data = await this.myLogin(loginInfo, true)
            data.uid = loginInfo.uid
            return data
        } else {
            throw verror({ name: "myReLogin args err", info: reqInfo })
        }
    },

    async alipayLogin(reqInfo, isRelogin) {
        let { open_id, uid } = reqInfo;
        if (open_id) {
            delete reqInfo.uid
            reqInfo.tpid = open_id
            reqInfo.openid = open_id
        } else if (uid) {
            delete reqInfo.open_id
        }
        reqInfo.userType = USER_TYPE.Alipay
        return this.commonHandle(reqInfo, USER_TYPE.Alipay, isRelogin);
    },
    async alipayReLogin(reqInfo) {
        let { open_id, uid, starSum } = reqInfo
        if (open_id && uid) {
            let loginInfo = {}
            loginInfo.uid = await this.reLogin({ tpid: open_id, uid, starSum })
            let data = await this.alipayLogin(loginInfo, true)
            data.uid = loginInfo.uid
            return data
        } else {
            throw verror({ name: "alipayLogin args err", info: reqInfo })
        }
    },

    async twitterLogin(reqInfo, isRelogin) {
        reqInfo.userType = USER_TYPE.TWITTER
        if (reqInfo.userId) {
            reqInfo.openid = reqInfo.userId
            reqInfo.tpid = reqInfo.userId
        }
        return this.commonHandle(reqInfo, USER_TYPE.TWITTER, isRelogin);
    },

    async twitterRelogin(reqInfo) {
        let { userId, uid, starSum } = reqInfo
        if (userId && uid) {
            let loginInfo = {
                type: USER_TYPE.TWITTER,
                openid: userId,
                avatarUrl: reqInfo.avatarUrl,
                nickName: reqInfo.nickName
            }
            loginInfo.uid = await this.reLogin({ tpid: userId, uid, starSum })
            let data = await this.twitterLogin(loginInfo, true)
            data.uid = loginInfo.uid
            return data
        } else {
            throw verror({ name: "twitterRelogin args err", info: reqInfo })
        }
    },

    async googleLogin(reqInfo, isRelogin) {
        reqInfo.userType = USER_TYPE.GOOGLE
        if (reqInfo.userId) {
            reqInfo.openid = reqInfo.userId
            reqInfo.tpid = reqInfo.userId
        }
        return this.commonHandle(reqInfo, USER_TYPE.GOOGLE, isRelogin);
    },

    async googleRelogin(reqInfo) {
        let { userId, uid, starSum } = reqInfo
        if (userId && uid) {
            let loginInfo = {
                type: USER_TYPE.GOOGLE,
                openid: userId,
                avatarUrl: reqInfo.avatarUrl,
                nickName: reqInfo.nickName
            }
            loginInfo.uid = await this.reLogin({ tpid: userId, uid, starSum })
            let data = await this.googleLogin(loginInfo, true)
            data.uid = loginInfo.uid
            return data
        } else {
            throw verror({ name: "googleRelogin args err", info: reqInfo })
        }
    },

    async fbInstantReLogin(reqInfo) {
        let { userId, uid, starSum } = reqInfo
        if (userId && uid) {
            let loginInfo = {
                type: USER_TYPE.FBINSTANT,
                openid: userId,
                avatarUrl: reqInfo.avatarUrl,
                nickName: reqInfo.nickName
            }
            loginInfo.uid = await this.reLogin({ tpid: userId, uid, starSum })
            let data = await this.fbInstantLogin(loginInfo, true)
            data.uid = loginInfo.uid
            return data
        } else {
            throw verror({ name: "fbInstantReLogin args err", info: reqInfo })
        }
    },

    async reconnect(reqInfo, userDoc, isNew) {
        let { uid, dhlog, gameVer } = reqInfo
        userDoc = userDoc || await userMod.findOne(uid)
        let sessionKey = await userMod.makeSessionKey(uid, userDoc)
        if (util.cmpVersion(gameVer, "2.9.5") < 0) {
            sessionKey = String(userDoc.index_id) + userDoc.loginTime
        }
        let lastSessionKey = await userMod.getLastSessionKey(uid)
        logger.debug("reconnect", lastSessionKey, sessionKey)
        await userMod.updateSessionKey(uid, sessionKey)

        this.reportDHLog(reqInfo, userDoc, { isNew, lastSessionKey })

        await userMod.reconnect(uid, userDoc)
        await activityMod.handleLoginTask(uid)
        return { sessionKey }
    },

    async handleCrossCopy(ip, port, tpid) {
        const url = `http://${ip}:${port}/debug_copy?tpid=${tpid}`
        let { status, userDoc, recordDoc, idDocs, data } = await got(url, {
            timeout: 5 * util.Time.Second,
        }).json()
        const promiseList = []
        let uid = ""
        if (data) {
            data = JSON.parse(data)
            userDoc = data.userDoc
            recordDoc = data.recordDoc
            activityDoc = data.activityDoc
            idDocs = data.idDocs
            // 分配新的uid
            uid = idMod.genUID();
            if (userDoc) {
                delete userDoc._id
                userDoc.uid = uid;
                promiseList.push(userMod.updateOne(userDoc.uid, userDoc, true))
            }
            if (recordDoc) {
                delete recordDoc._id
                recordDoc.uid = uid;
                promiseList.push(recordMod.updateOne(recordDoc.uid, recordDoc, true))
            }
            if (activityDoc) {
                delete activityDoc._id
                activityDoc.uid = uid;
                if (activityDoc.limit) {
                    activityDoc.limit.forEach(l => delete l._id)
                }
                if (activityDoc.record) {
                    activityDoc.record.forEach(l => delete l._id)
                }
                promiseList.push(this.specialActivityCol.updateOne({ uid }, activityDoc, { upsert: true }))
            }
            promiseList.push(idMod.reBindID(tpid, uid, true))
        }
        await Promise.all(promiseList)
        return uid
    }
}

module.exports = loginMod;
