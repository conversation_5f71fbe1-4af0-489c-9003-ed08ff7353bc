const util = require("../common/util").default;
const redisKey = require("../db/redisKey");
const logger = require("../common/log").getLogger("shareMod");
const {INVITE_TYPE} = require("../common/constant");

let shareMod = {

    invitedCol: null,
    dailyShareCol: null,
    shareImageCol: null,
    redis: null,

    init() {
        let db = require("../db/db");
        this.dailyShareCol = db.createDailyShareModel();
        this.shareImageCol = db.createShareImageModel()
        this.invitedCol = db.createInvitedModel()
        this.redis = db.redis;
        this.userMod = require("./userMod")
    },

    async send({uid, type, imageName}) {
        try {
            let now = Date.now()
            let date = (now - now % util.Time.Day)
            await this.dailyShareCol.updateOne({uid, date, type}, {$inc: {shareCount: 1}}, {upsert: true})
            await this.update(uid)

            if (imageName) {
                await this.shareImageCol.updateOne({imageName}, {$inc: {shareCount: 1}}, {upsert: true})
            }
        } catch (error) {
            logger.error("send error", uid, type, error)
        }
    },

    async receive(uid, sourceUid, info = {}) {
        try {
            await this.updateInviteInfo(uid, sourceUid, info)
            await this.statisticsShare(sourceUid, info)
            return true
        } catch (error) {
            logger.error("receive error", uid, sourceUid, info, error)
            return false
        }
    },

    async updateInviteInfo(uid, sourceUid, info) {
        let promiseList = []
        let now = Date.now()
        if (info.isNew) {
            promiseList.push(
                this.invitedCol.updateOne({uid}, {
                    inviter: sourceUid,
                    timestamp: now,
                    type: INVITE_TYPE.NORMAL
                }, {upsert: true})
            )
        }
        if (promiseList.length > 0) {
            await Promise.all(promiseList)
        }
    },

    async getBeInvitedInfo(uid) {
        let doc = await this.invitedCol.findOne({uid})
        return doc
    },

    async delInvite(uid) {
        return this.invitedCol.deleteOne({uid})
    },

    async getInviteInfo(uid, type, withBaseInfo = true) {
        let docs = await this.invitedCol.find({inviter: uid, type: type}, {uid: 1, timestamp: 1})
        let inviteUsers = await util.promiseMap(docs, async (user) => {
            let info = null
            if (withBaseInfo) {
                info = await this.userMod.getNameAndAvatar(user.uid)
            }
            let res = {timestamp: user.timestamp, uid: user.uid}
            if (info) {
                res.nickName = info.nickName
                res.avatarUrl = info.avatarUrl
            }
            return res
        })
        return inviteUsers
    },

    async statisticsShare(uid, info) {
        let now = Date.now()
        let date = now - now % util.Time.Day
        info.type = info.type || 0;
        let where = {
            uid, date, type: info.type
        }
        let update = {}
        if (info.isLogin) {
            update.clickCount = 1
        }
        if (info.isNew) {
            update.newUserCount = 1
        } else if (info.isOld) {
            update.oldUserCount = 1
        }
        if (Object.keys(update).length <= 0) return

        await this.dailyShareCol.updateOne(where, {$inc: update}, {upsert: true})
        await this.update(uid)

        if (info.imageName) {
            await this.shareImageCol.updateOne({imageName: info.imageName}, {$inc: update}, {upsert: true})
        }
    },

    async update(uid) {
        let docs = await this.dailyShareCol.find({uid})
        if (docs.lenth <= 0) return
        if (docs.lenth > 100) {
            docs.sort((a, b) => {
                return b.date - a.date
            })
            await this.dailyShareCol.deleteOne({_id: docs[docs.lenth - 1]._id})
            docs.pop()
        }
        let shareCount = 0, clickCount = 0, newUserCount = 0, oldUserCount = 0;
        for (let doc of docs) {
            shareCount += doc.shareCount || 0;
            clickCount += doc.clickCount || 0;
            newUserCount += doc.newUserCount || 0;
            oldUserCount += doc.oldUserCount || 0;
        }
        let score = this.getScore(clickCount, newUserCount, oldUserCount)
        if (score > 0) {
            let key = redisKey.shareClickRank()
            await this.redis.zadd(key, score, uid)
        }
    },

    getScore(clickCount, newUserCount, oldUserCount) {
        return clickCount + newUserCount * 10 + oldUserCount * 10;
    }
}

module.exports = shareMod;