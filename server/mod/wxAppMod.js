const got = require('got');
const util = require("../common/util").default;
const config = require("../config");
const logger = require("../common/log").getLogger("wxAppMod");
const verror = require("verror");
const userMod = require('./userMod');

let wxAppMod = {
    async getWxAppLoginInfo(code) {
        let {openid, access_token, refresh_token} = await this.code2Session(code);
        let {headimgurl, nickname, unionid, scope} = await this.getUserInfo(access_token, openid);
        return {openid, access_token, refresh_token, headimgurl, nickname, unionid, scope};
    },

    async getUserInfoByUid(uid) {
        let userDoc = await userMod.findOne(uid);
        if (!userDoc) {
            throw verror({name: "user not found", info: {uid}});
        }
        let {access_token, refresh_token, wxAppOpenid} = userDoc;
        if (!access_token) return;
        let info = await this.getUserInfo(access_token, openid);
        if (!info) {
            let tokenInfo = await this.refreshToken(refresh_token);
            if (!tokenInfo) {
                return;
            }
            access_token = tokenInfo.access_token;
            refresh_token = tokenInfo.refresh_token;
            userMod.updateOne(uid, {access_token, refresh_token});
            info = await this.getUserInfo(access_token, wxAppOpenid);
        }
        return info;
    },

    async code2Session(code, retry = 3) {
        let {appid, secret} = this.getConfig();
        let url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appid}&secret=${secret}&code=${code}&grant_type=authorization_code`;

        let data = await got(url, {
            timeout: 10 * util.Time.Second,
        }).json();
        const { errcode } = data;
        if (!errcode) {
            return data;
        }
        else if (errcode == -1 && retry > 0)  { //需要重试
            await util.wait(1000);
            return await this.code2Session(code, retry - 1);
        }
        else {
            throw verror({name: "AppCode2Session", info: data}); 
        }
    },

    async getUserInfo(access_token, openid, retry = 3) {
        let url =  `https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}`;

        let data = await got(url, {
            timeout: 10 * util.Time.Second,
        }).json();
        const { errcode } = data;
        if (!errcode) {
            return data;
        }
        else if (errcode == 40001 || errcode == 42001) { //需要刷新token
            return;
        }
        else if (errcode == -1 && retry > 0) { //需要重试
            await util.wait(1000);
            return await this.getUserInfo(access_token, openid, retry - 1);
        }
        else {
            throw verror(data); 
        }
    },

    async refreshToken(refreshToken, retry = 5) {
        let {appid} = this.getConfig();
        const url = `https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=${appid}&grant_type=refresh_token&refresh_token=${refreshToken}`;
        let data = await got(url, {
            timeout: 10 * util.Time.Second,
        }).json();
        const { errcode } = data;
        if (errcode == 0) {
            return data;
        }
        else if (errcode == 42002 || errcode == 42007 || errcode == 61023 || errcode == 40030) { //需要重新授权
            return;
        }
        else if (errcode == -1 && retry > 0)  { //需要重试
            await util.wait(1000);
            return await this.refreshToken(refreshToken, retry - 1);
        }
        else {
            throw verror(data); 
        }
    },

    getConfig() {
        return config.wxApp;
    }

}   

module.exports = wxAppMod;
