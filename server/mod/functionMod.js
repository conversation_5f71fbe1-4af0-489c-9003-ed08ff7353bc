const db = require("../db/db");
const {FUNCTION_OPEN} = require("../common/constant");

let functionMod = {
    init() {
        this.functionsOpenCol = db.createFunctionOpenModel();
        this.write2db();
    },
    async write2db() {
        let _ = Object.keys(FUNCTION_OPEN);
        for (let key of _) {
            let doc = await this.functionsOpenCol.findOne({functionName: key});
            if (!doc) {
                let saver = new this.functionsOpenCol({
                    functionName: key,
                    isOpen: false
                })
                await saver.save();
            }
        }
    },
    async setOpen(funcName, open, extend = {}) {
        extend = JSON.stringify(extend);
        switch (funcName) {
            case FUNCTION_OPEN.IOS_AUDIT:
                await this.setIosAuditOpen(open, extend);
                break
            default:
                return false;
        }
        return true;
    },
    async isOpen(funcName) {
        switch (funcName) {
            case FUNCTION_OPEN.IOS_AUDIT:
                return await this.isIosAuditOpened();
            default:
                return false;
        }
    },
    async get(func) {
        return await this.functionsOpenCol.findOne({functionName: func});
    },
    async isIosAuditOpened() {
        let _ = await this.get(FUNCTION_OPEN.IOS_AUDIT);
        return _ ? _.isOpen : false;
    },
    async setIosAuditOpen(open = true, extend = {}) {
        await this.functionsOpenCol.updateOne({functionName: FUNCTION_OPEN.IOS_AUDIT}, {isOpen: open, extends: extend});
    }

}

module.exports = functionMod;