// https://console.cloud.tencent.com/api/explorer?Product=cms&Version=2019-03-21&Action=TextModeration&SignVersion=
const tencentcloud = require("tencentcloud-sdk-nodejs");
const config = require("../config")
const db = require("../db/db");
const logger = require("../common/log").getLogger("textModerationMod")

const CmsClient = tencentcloud.tms.v20200713.Client;
const {secretId, secretKey, region} = config.txCloud || {}


let textModerationMod = {
    client: null,
    init() {
        const clientConfig = {
            credential: {
                secretId,
                secretKey,
            },
            region,
            profile: {
                httpProfile: {
                    endpoint: "tms.tencentcloudapi.com",
                },
            },
        };
        this.tdb = db.createTempValidStrCheckModel()
        this.client = new CmsClient(clientConfig);
    },

    async check(content, isBase64 = false) {
        if (!content) {
            return;
        }
        if (!isBase64) {
            content = Buffer.from(content).toString('base64');
        }
        let params = {"Content": content};
        try {
            return await this.client.TextModeration(params)
        } catch (error) {
            logger.error("check err", content, error)
        }
    },

    async checkAndFilter(content, isBase64 = false) {
        let res = await this.check(content, isBase64)
        if (res) {
            if (res.EvilFlag == 0) return content
            return this.filterByRes(content, res.Keywords)
        }
    },

    filterByRes(content, keywords, replace = "*") {
        for (let keyword of keywords) {
            content = content.replace(new RegExp(keyword, 'g'), replace)
            if (keyword.includes("&")) {
                let signleWords = keyword.split("&")
                for (let word of signleWords) {
                    content = content.replace(new RegExp(word, 'g'), replace)
                }
            }
        }
        return content
    },

    async isValid(content) {
        if (config.type === "global") return true
        if (content !== content.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g, '')) {
            return false
        }
        const find = await this.tdb.findOne({str: content})
        if (find) {
            return find.p
        }

        let data = await this.check(content)
        if (!!!(data && data.EvilFlag == 0)) {
            await this.tdb.insertMany({str: content, p: false})
            return false
        }
        await this.tdb.insertMany({str: content, p: true})
        return true;

    }
}

module.exports = textModerationMod;
