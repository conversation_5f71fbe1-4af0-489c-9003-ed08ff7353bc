const got = require("got")

const redisKey = require("../db/redisKey");
const db = require("../db/db");
const {USER_TYPE} = require("../common/constant");

const logger = require("../common/log").getLogger("hmsLoginMod");

const client_id = '*********';
const client_secret = '9c626fd9a119388506ace6716b1d121c79d75551580ad20436deb091f264d14c';
// 获取新token
const REQ_NEW = "authorization_code"
// token刷新
const REFRESH = "refresh_token"

const redirect_uri = `https://game-hotel-wx.twomiles.cn:8000/`
const userinfo_url = ``

let hmsLoginMod = {
    init() {
        this.redis = db.redis
        this.hmsAccountCol = db.createHmsAccountTokenModel()
    },
    async getLoginInfo(hmsId, hmsToken) {
        let data = await this.reqInfo(hmsId, hmsToken)
        if (data) {
            if (hmsId !== data.openID) {
                logger.error("hms login getLoginInfo err. 070")
            }
            return {
                avatarUrl: data.headPictureURL,
                nickName: data.displayName,
                openid: data.openID,
                userType: USER_TYPE.HMS
            };
        }
    },
    // 获取请求域名 会保证重试一次 暂时不用，写死了userinfo_url
    // @see https://developer.huawei.com/consumer/cn/doc/development/hmscore-common-References/obtaining-server-domain-name-****************
    // 可以看aipfox的华为域名请求
    async reqRouter(retry = true) {
        let headers = {
            'Content-Type': `application/json; charset=utf-8`,
        }
        let json = {
            services: ["account.apptouch"]
        }
        let resp = await got.post(`https://openrs-api.cloud.huawei.com/openrs/1.0/router?clientId=${client_id}`, {
            headers,
            json,
        })
        let {resultCode, services, errorList} = resp.json()
        if (errorList) {
            return void logger.error("hms reqRouter err :", JSON.stringify(errorList))
        }


        let cache = resp.headers["cache-control"]
        cache = cache.substring(cache.lastIndexOf("=") + 1)
        await this.redis.set(redisKey.HMS_ROUTER)
        console.log(resp)
    },
    // 解析login token
    async reqInfo(hmsId, code, refresh = false, retry = true) {
        let accessToken = ""
        // 先去db获取如果有access_token就先直接拿出来用，如果没有就去请求新的，如果用的时候token过期那就刷新一下
        let doc = await this.hmsAccountCol.findOne({hmsId}, {access_token: 1, refresh_token: 1, expire: 1})
        if (!refresh && doc && doc.expire) {
            let time = Math.floor(Date.now() / 1000)
            time >= doc.expire && (refresh = true)
        }
        if (!refresh) {
            if (doc && doc.access_token) {
                accessToken = doc.access_token
            } else {
                accessToken = await this.reqToken(hmsId, REQ_NEW, code, "")
            }
        } else if (refresh && doc.refresh_token) {
            accessToken = await this.reqToken(hmsId, REFRESH, code, doc.refresh_token)
        } else {
            logger.error("hms login reqInfo err. 071")
        }
        if (!accessToken) {
            // 获取accessToken失败
            return void 0
        }

        let headers = {
            'Content-Type': `application/x-www-form-urlencoded;charset=utf-8`,
        }
        let form = {
            access_token: accessToken,
            getNickName: 1
        }
        let {
            error,
            openID,
            displayName,
            headPictureURL, email
        } = await got.post(`https://account.cloud.huawei.com/rest.php?nsp_svc=GOpen.User.getInfo`, {
            headers,
            form
        }).json()
        // 华为文档里没有说明这个error是啥玩意儿，也没说明error的可能值, 暂定为token过期，需要刷新重试一次
        if (error && retry) {
            return await this.reqInfo(uid, code, true, false)
        }
        return {
            openID, displayName, headPictureURL
        }
    },

    // 获取login token 会保证重试一次
    async reqToken(hmsId, grant_type, code, _refresh_token, retry = true) {
        let form = {
            "grant_type": grant_type,
            "client_id": client_id,
            "client_secret": client_secret
        }
        if (grant_type === REQ_NEW) {
            if (code === "") return void logger.warn("使用了authorization_code获取，但是没有传入code!!!")
            form.code = code
            form.redirect_uri = redirect_uri
        }
        if (grant_type === REFRESH) {
            if (_refresh_token === "") return void logger.warn("使用了refresh_token刷新，但是没有传入refresh_token!!!")
            form.refresh_token = _refresh_token
        }
        let headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': "*/*"
        }

        try {
            let {
                access_token,
                error,
                sub_error,
                error_description,
                refresh_token,
                expires_in,
            } = await got.post("https://oauth-login.cloud.huawei.com/oauth2/v3/token", {
                headers,
                form,
            }).json()
            // 因为华为的接口很烂，写的和狗屎一样，返回错误码的同时http code是400，直接引起got报错，所以没法儿处理，只能try catch
            if (!access_token && sub_error) {
                logger.error(`hms login get token err:${sub_error},${error_description}.`)
                if (sub_error === 20155 || sub_error === 20156 || sub_error === 20153 || sub_error === 20152) {
                    return
                }
                // if (retry) {
                //     logger.info("hms login get token retry")
                //     return this.reqToken(hmsId, grant_type, code, refresh_token, false)
                // }
            }
            if (access_token) {
                expires_in = expires_in || 0
                let expire = Math.floor(Date.now() / 1000) + expires_in
                // 更新token时间
                await this.hmsAccountCol.updateOne({hmsId}, {access_token, refresh_token, expire})
                return access_token
            }
        } catch (e) {
            // 报错的话就不重试了
            if (e.name === 'HTTPError') {
                logger.error("hms login get token err:", e.response.body)
            }
        }
    }
}

module.exports = hmsLoginMod
