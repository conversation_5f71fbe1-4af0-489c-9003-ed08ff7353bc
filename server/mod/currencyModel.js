let balanceCol, orderCol, statUserHeartCo, userCol;


// 通货模块
let currencyModel = {

    async init() {
        balanceCol = db.createCurrencyBalanceModel()
        orderCol = db.createCurrencyOrderModel()
        this.statUserHeartCol = db.createStatUserHeartModel();
        this.redis = db.redis;
    },

    // 同步通货余额
    async asynCurrencyBalance(uid) {
        let result = {}
        let list = await balanceCol.find({ uid })
        for (let key in CURRENCY) {
            let doc = list.filter(m => m.currency == CURRENCY[key])[0]
            result[CURRENCY[key]] = doc?.balance || 0
        }

        return result
    },
    // 获取玩家总付费 $
    async getUserTotalPay(uid) {
        let doc = await userMod.findOne(uid);
        return doc && doc.pay || 0;
    },

    async getUserTotalBal(uid) {
        let list = await balanceCol.find({
            uid,
            $or: [{ currency: CURRENCY.WINDMILL }, { currency: CURRENCY.SCISSOR }]
        }, { balance: 1, currency: 1, total: 1 })
        return list || []
    },

    // 修改通货余额
    // isUse : 退款由系统扣除为false,不会增加used数量
    async changeCurrencyBalance(uid, currency, val, isUse = true) {
        if (val < 0) {
            let data = await this.asynCurrencyBalance(uid);
            let nowVal = data[currency] || 0;
            // 余额不够扣
            if (nowVal === 0) return;
            val = Math.min(Math.abs(val), nowVal) * -1;
        }

        let inc = { balance: val };
        // 获得货币时增加total
        if (val > 0) {
            inc.total = 0
            // 兼容初始状态下的余额
            const t = await balanceCol.find({ uid, currency })
            if (t && !t.total && t.balance) {
                inc.total += t.balance
            }
            inc.total += val
        }

        if (isUse && val < 0) {
            inc.used = -val
        }

        await balanceCol.updateOne({ uid, currency }, { $inc: inc }, { upsert: true })
    },

    // 创建通货订单
    async createCurrencyOrder(uid, action, order, extra, gameVer) {
        let status = -1

        let orderDoc = await orderCol.findOne({ uid, order })
        if (orderDoc) {
            status = orderDoc.status
        }

        if (status == CURRENCY_ORDER_STATUS.COMPLETED) {
            return { status, extraRes: orderDoc.extraRes }
        }

        if (status == CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS) {
            if (Date.now() - orderDoc.timestamp > util.Time.Hour) { //大于一个小时自动设为完成
                logger.info("auto trans to completed", uid, action, order)
                status = CURRENCY_ORDER_STATUS.COMPLETED
                await orderCol.updateOne({ uid, order }, { status })
            }
            return { status, extraRes: orderDoc.extraRes }
        } else if (status == CURRENCY_ORDER_STATUS.NON_PAYMENT) {
            if (Date.now() - orderDoc.timestamp < util.Time.Second * 30) { //30s还没处理完才重新走处理
                return { status }
            }
        }
        let update = { status: CURRENCY_ORDER_STATUS.NON_PAYMENT, timestamp: Date.now(), action }
        if (extra) {
            update.extra = JSON.stringify(extra)
        }
        let data = this.getCfgByAction(action, gameVer)
        if (!orderDoc) {
            let sTime = Date.now()
            await orderCol.insertMany(Object.assign({ uid, order }, update))
            let diff = Date.now() - sTime
            if (diff > 3000 && !data.action.startsWith("iap_")) {
                logger.error("createCurrencyOrder 3", uid, diff, JSON.stringify(update))
                return { status: -1 }
            }
        }
        let result = {}

        if (!data && (action.endsWith("_ad") || action === CURRENCY_ACTION.WA_SAI_AD || action === CURRENCY_ACTION.FILM_SIGNED)) { //广告跳过剪
            status = await currencyModel.skipAd(uid, action, order, gameVer)
        } else if (action.endsWith("_ad_windmill")) { // 使用风车跳过广告 固定15风车
            status = await currencyModel.skipAdUseWindmill(uid)
        } else if (action === CURRENCY_ACTION.WUDONG_STAMINA_WINDMILL) { // 使用风车增加乌冬体力 固定15风车
            status = await currencyModel.wudong_stamina_windmill(uid)
        } else if (action === CURRENCY_ACTION.RECEIVE_DAILY_MC) {
            result = await currencyModel.receiveDailyMonthCard(uid, action, order, extra, gameVer)
            status = result.status;
        } else if (data.action.startsWith("iap_")) { //内购
            status = await currencyModel.purchase(uid, action, order, extra, gameVer);
            (status === CURRENCY_ORDER_STATUS.COMPLETED || status === CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS) && currencyModel.reportCurrencyChange(uid, data, data.id, { order_no: order }).then();
        } else if (data.action.startsWith("exchange_wsyh") || data.action.startsWith("exchange_bp_exp")) {
            status = await currencyModel.exchangeProp(uid, action, order, gameVer, extra);
        } else if (data.type === ShopItemType.RECEIVE_DAILY_WINDMILL || data.type === ShopItemType.SHOP_FREE) { //免费风车
            status = await currencyModel.receiveDailyFreeGift(uid, action, order, extra, gameVer);
            (status === CURRENCY_ORDER_STATUS.COMPLETED || status === CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS) && currencyModel.reportCurrencyChange(uid, data, CHANNEL_WINDMILL.FREE).then();
        } else if (data.type === ShopItemType.EXCHANGE_SCISSOR) { //兑换剪刀
            status = await currencyModel.exchangeScissor(uid, action, order, gameVer)
            let exLv = util.getLastCharToNumber(data.action) + 1;
            // 剪刀的配表是0 1 2三个档位
            (status === CURRENCY_ORDER_STATUS.COMPLETED || status === CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS) && currencyModel.reportCurrencyChange(uid, data, CHANNEL_WINDMILL.EXCHANGE_AD_SKIP_ITEM, { exLv }).then();
        } else if (data.type === ShopItemType.EXCHANGE_BISCUITS
            || data.type === ShopItemType.EXCHANGE_CANDIES
            || data.type === ShopItemType.PACKAGE
            || data.type === ShopItemType.SHOP_PAY
            || data.action.startsWith("halloween_")
            || data.action.startsWith("exchange_yoyo")
            || data.action.startsWith("exchange_praise")
            || data.action.startsWith("exchange_leaves")
            || data.action.startsWith("exchange_clover")
            || data.action.startsWith("exchange_good")) { //兑换物品
            status = await currencyModel.exchangeProp(uid, action, order, gameVer)
            let channel = 0, exLv = util.getLastCharToNumber(data.action) || 1;
            !channel && data.type === ShopItemType.EXCHANGE_BISCUITS && (channel = CHANNEL_WINDMILL.EXCHANGE_BISCUITS);
            !channel && data.type === ShopItemType.EXCHANGE_CANDIES && (channel = CHANNEL_WINDMILL.EXCHANGE_CANDIES);
            !channel && data.type === ShopItemType.SHOP_PAY && (channel = CHANNEL_WINDMILL.DAILY_GIFT);
            !channel && data.action === 'exchange_good_gift1' && (channel = CHANNEL_WINDMILL.EXCHANGE_BEAUTY_PACKAGE);
            !channel && data.action.startsWith("exchange_yoyo") && (channel = CHANNEL_WINDMILL.EXCHANGE_YO_YO);
            !channel && data.action.startsWith("exchange_staff") && (channel = CHANNEL_WINDMILL.EXCHANGE_STAFF_ITEM);
            !channel && data.action.startsWith("exchange_praise") && (channel = CHANNEL_WINDMILL.EXCHANGE_CINEMA);
            !channel && data.action.startsWith("exchange_good") && (channel = CHANNEL_WINDMILL.EXCHANGE_BEAUTY);
            !channel && data.action.startsWith("halloween_") && (channel = CHANNEL_WINDMILL.HALLOWEEN_EXCHANGE);
            !channel && data.action.startsWith("exchange_leaves") && (channel = CHANNEL_WINDMILL.EXCHANGE_LEAVES);
            (status === CURRENCY_ORDER_STATUS.COMPLETED || status === CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS) &&
                channel && currencyModel.reportCurrencyChange(uid, data, channel, { exLv }).then();
        } else if (data.action.startsWith('black_five_')) {
            // 黑五奖励
            status = await currencyModel.getBlackFridayReward(uid, action, order, gameVer)
        }

        if (status == -1) { //不合法的订单
            await orderCol.deleteOne({ uid, order })
        } else if (status >= CURRENCY_ORDER_STATUS.NON_PAYMENT) { //合法订单
            update.status = status
            if (result.extraRes) {
                update.extraRes = result.extraRes
            }
            await orderCol.updateOne({ uid, order }, update, { upsert: true })
        }

        result.status = result.status || status
        return result
    },
    // // 查询通货订单
    // async queryCurrencyOrder(uid, order) {
    //     let doc = await orderCol.findOne({ uid, order })
    //     if (doc) {
    //         return doc.status
    //     }

    //     return -1
    // },

    // 获取已支付订单
    async getPaidOrder(uid) {
        let list = await orderCol.find({ uid, status: CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS })
        return list
    },

    // 提交订单状态
    async submitCurrencyOrderStatus(uid, order, status) {
        await orderCol.updateOne({ uid, order }, { status: CURRENCY_ORDER_STATUS.COMPLETED })
    },

    async receiveDailyFreeGift(uid, action, order, extra, gameVer) {
        // if (extra && extra.offset) {
        //     let list = await orderCol.find({ uid, action, status: CURRENCY_ORDER_STATUS.COMPLETED }).sort({ timestamp: -1 }).limit(1)// 获取最后一条领取每日风车记录
        //     let doc = list[0]
        //     if (doc) {
        //         let d1 = new Date(doc.timestamp)
        //         let d2 = new Date()
        //         if (d1.getTime() > d2.getTime() || (d1.getMonth() == d2.getMonth() && d1.getDate() && d2.getDate())) {
        //             return -1
        //         }
        //     }
        // }
        // 每日免费风车限制领取次数
        if (action === 'shopFreeExchange_1601') {
        }
        let n = await this.redis.get(redisKey.dailyWelfare(uid)) || "0";
        n = parseInt(n);
        if (n && n >= 2) {
            return CURRENCY_ORDER_STATUS.CANCELED
        }
        n += 1;
        let ttl = await this.redis.ttl(redisKey.dailyWelfare(uid));
        ttl <= 0 && (ttl = util.Time.Day / 1000);
        await this.redis.set(redisKey.dailyWelfare(uid), n, 'EX', ttl);
        let data = this.getCfgByAction(action, gameVer)
        await this.changeCurrencyByData(uid, data)
        return CURRENCY_ORDER_STATUS.COMPLETED
    },

    async receiveDailyMonthCard(uid, action, order, extra, gameVer) {
        let monthCardAction = extra.action
        let days = extra.days
        let data = this.getCfgByAction(monthCardAction, gameVer)
        let balance = await currencyModel.asynCurrencyBalance(uid)
        days = Math.min(balance[monthCardAction], days)
        if (days > 0) {
            await this.changeCurrencyByData(uid, { reward: data.extraReward }, days)
            await this.changeCurrencyBalance(uid, monthCardAction, -days)
        }
        currencyModel.reportCurrencyChange(uid, { reward: data['extraReward'] }, CHANNEL_WINDMILL.MONTH_CARD);
        return { status: CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS, extraRes: JSON.stringify({ days }) }
    },

    // 兑换剪刀
    async exchangeScissor(uid, action, order, gameVer) {
        let data = this.getCfgByAction(action, gameVer)
        let cost = gameHelper.stringToConditions(data.cost)[0].count
        let reward = gameHelper.stringToConditions(data.reward)[0].count
        let balance = await currencyModel.asynCurrencyBalance(uid)
        if (balance[CURRENCY.WINDMILL] < cost) {
            return -1
        }

        await currencyModel.changeCurrencyBalance(uid, CURRENCY.WINDMILL, -cost)
        await currencyModel.changeCurrencyBalance(uid, CURRENCY.SCISSOR, reward)
        return CURRENCY_ORDER_STATUS.COMPLETED
    },
    // 乌冬使用风车增加体力
    async wudong_stamina_windmill(uid) {
        let balance = await currencyModel.asynCurrencyBalance(uid)
        if (balance[CURRENCY.WINDMILL] < 10) {
            return -1
        }
        await currencyModel.changeCurrencyBalance(uid, CURRENCY.WINDMILL, -10)
        return CURRENCY_ORDER_STATUS.COMPLETED
    },

    // 使用风车跳过广告
    async skipAdUseWindmill(uid) {
        let balance = await currencyModel.asynCurrencyBalance(uid)
        if (balance[CURRENCY.WINDMILL] < 15) {
            return -1
        }
        await currencyModel.changeCurrencyBalance(uid, CURRENCY.WINDMILL, -15)
        return CURRENCY_ORDER_STATUS.COMPLETED
    },

    getCfgByAction(action, gameVer) {
        let shopDatas = this.getJson("shop", gameVer).datas;
        let limitOfferGifts = this.getJson("limitOfferGifts", gameVer, action).datas;
        let washaiDatas = this.getJson('waishaiItem', gameVer, action).datas;
        let shopPayExchange = this.getJson('shopPayExchange', gameVer, action).datas;
        let shopFreeExchange = this.getJson('shopFreeExchange', gameVer, action).datas;
        let holidayExchange = this.getJson('holidayExchangeBase', gameVer, action).datas;
        let blackFriday = this.getJson('consumeTimeLimitAward', gameVer, action).datas;
        return shopDatas.find(m => m.action == action)
            || limitOfferGifts.find(m => m.action == action)
            || washaiDatas.find(m => m.action == action)
            || shopPayExchange.find(m => m.action == action)
            || shopFreeExchange.find(m => m.action == action)
            || holidayExchange.find(m => m.action == action)
            || blackFriday.find(m => m.action == action)
    },

    getJson(name, gameVer, action) {
        let cfgName = name
        if (name == "shop") {
            cfgName = "shop_v1"
            if (util.cmpVersion(gameVer, "4.2.2") >= 0) {
                cfgName = "shop"
            } else if (util.cmpVersion(gameVer, "2.11.0") >= 0 || (action && action.startsWith("iap_windmill"))) {
                cfgName = "shop_v2"
            }
        }
        logger.debug("getCfgByAction gameVer: ", cfgName, gameVer)
        return assetsMgr.getJson(cfgName)
    },

    async getBlackFridayReward(uid, action, order, gameVer) {
        let data = this.getCfgByAction(action, gameVer)
        await currencyModel.changeCurrencyByData(uid, data)
        return CURRENCY_ORDER_STATUS.COMPLETED
    },

    // 风车兑换道具
    async exchangeProp(uid, action, order, gameVer, extra) {
        let cost, data = this.getCfgByAction(action, gameVer);
        if (extra && typeof extra == "object") {
            extra = Number(extra['val']) || 1
        } else if (extra && typeof extra == "number") {
            if (extra < 0) return -1;
            let cut_range = data['cut_range'] || '1,1';
            let [a, b] = util.stringToNumbers(cut_range, ',');
            a *= 0.1, b *= 0.1;
            if (extra !== 0 && (extra < a || extra > b)) return -1;
            extra == 0 && (extra = 1);
        } else {
            extra = 1;
        }
        // 哇塞配置表读   cost_global
        // 万圣节配置表读 price_global
        // 默认读 cost
        let costStr = data.cost_global || data.price_global || data.cost
        cost = gameHelper.stringToConditions(costStr)[0].count;
        cost *= extra, cost = Math.floor(cost);
        let balance = await currencyModel.asynCurrencyBalance(uid)
        if (balance[CURRENCY.WINDMILL] < cost) {
            return -1
        }
        await currencyModel.changeCurrencyBalance(uid, CURRENCY.WINDMILL, -cost)
        await this.changeCurrencyByData(uid, data)
        return CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS
    },

    // 跳过广告
    async skipAd(uid, action, order) {
        let balance = await currencyModel.asynCurrencyBalance(uid)
        if (balance[CURRENCY.SCISSOR] < 1) {
            return -1
        }

        await currencyModel.changeCurrencyBalance(uid, CURRENCY.SCISSOR, -1)
        return CURRENCY_ORDER_STATUS.COMPLETED
    },

    async changeCurrencyByData(uid, data, times = 1, isUse = true) {
        let rewards = gameHelper.stringToConditions(data.reward)
        for (let { type, count } of rewards) {
            if (type == 16) { //风车
                await currencyModel.changeCurrencyBalance(uid, CURRENCY.WINDMILL, count * times, isUse);
            } else if (type == 17) { //剪刀
                await currencyModel.changeCurrencyBalance(uid, CURRENCY.SCISSOR, count * times, isUse)
            }
        }
    },

    /**
     *  上报卓杭货币产销
     * @param uid 用户id
     * @param data 礼包数据/道具数据
     * @param channel 产销途径,购买风车的话就是礼包id,否则就是预定义的 CHANNEL_WINDMILL 里面的类型
     * @param order 扩展信息
     * @return {Promise<void>}
     */
    async reportCurrencyChange(uid, data, channel, order = {}) {
        let rewards = gameHelper.stringToConditions(data.reward),
            logs = [],
            cost = [],
            extend = {},
            balanceData = await currencyModel.asynCurrencyBalance(uid);
        if (data && data.action && !data.action.startsWith("iap_")) {
            data.cost && (cost = gameHelper.stringToConditions(data.cost));
        }
        if (Object.keys(order).length && order.order_no) {
            // 内购操作 只用传递订单号
            extend.order_no = order.order_no;
        } else if (channel !== CHANNEL_WINDMILL.MONTH_CARD) {
            // 其他产销 需要传递 heart / exLv
            extend['heart'] = await userMod.getUserHeart(uid);
            // 免费风车不需要lv
            (channel !== CHANNEL_WINDMILL.FREE
                && channel !== CHANNEL_WINDMILL.EXCHANGE_STAFF_ITEM
                && channel !== CHANNEL_WINDMILL.DAILY_GIFT)
                && (extend['exLv'] = order['exLv'] || -1);
        }
        for (let { type, _count } of cost) {
            let balance = 0, changeType = ITEM_CHANGE_TYPE.REDUCE;
            if (type == 16) {
                balance = balanceData[CURRENCY.WINDMILL] || 0;
                let obj = new ItemChangeObj(_count, type, channel, balance, changeType, extend);
                logs.push(obj);
            }
        }
        for (let { type, count } of rewards) {
            let balance = 0, changeType = count > 0 ? ITEM_CHANGE_TYPE.ADD : ITEM_CHANGE_TYPE.REDUCE;
            if (type == 16 || type == 17) {
                type == 16 && (balance = balanceData[CURRENCY.WINDMILL] || 0);
                type == 17 && (balance = balanceData[CURRENCY.SCISSOR] || 0);
                let obj = new ItemChangeObj(count, type, channel, balance, changeType, extend);
                logs.push(obj);
            }
        }
        if (!logs.length) return;
        userMod.getDHLogInfo(uid).then((userInfo) => {
            if (!userInfo) return;
            let serial_no = ItemChangeObj.create_serial_no_by_user_info(userInfo);
            dhlog.report(DH_LOG_EVENT.ITEM_WINDMILL, null, userInfo, { serial_no, logs });
        })
    },

    // 内购
    async purchase(uid, action, order, extra, gameVer) {
        let result = { code: 0 }

        if (!config.debug || extra) {
            result = await payMod.check(Object.assign({ uid }, extra))
        }
        if (result.code == 0) {
            let shopDatas = this.getJson('shop', gameVer).datas
            let limitOfferGifts = this.getJson("limitOfferGifts", gameVer).datas
            let data = shopDatas.find(m => m.action == action)
            let store_id
            if (!data) {
                data = limitOfferGifts.find(m => m.action == action)
                store_id = Number("1" + data.id)
            } else {
                store_id = data.id
            }

            //dh付费统计，不是测试订单才上报
            if (!result.isTest) {
                this.report_purchase(uid, extra, order, data, store_id).then();
            }

            if (data.type == ShopItemType.MONTH_CARD) { //月卡
                await this.changeCurrencyBalance(uid, data.action, 30)
            }

            await this.changeCurrencyByData(uid, data)
            // 记录付费值
            data.cost && await userMod.addPayNum(uid, data.cost);

            if (action.indexOf('iap_windmill') != -1) {// 风车
                return CURRENCY_ORDER_STATUS.COMPLETED
            } else {// 礼包
                return CURRENCY_ORDER_STATUS.PAYMENT_SUCCESS
            }
        } else {
            if (payMod.isInvalidErroCode(result)) { //不合法订单，本地删除
                return -1
            }

            return -2
        }
    },
    async report_purchase(uid, extra, order, data, store_id) {
        // 首次付费
        let notFirst = await orderCol.find({
            uid,
            action: { $regex: 'iap_' },
            status: { $in: [1, 3] }
        }).countDocuments() || 0;
        userMod.getDHLogInfo(uid).then(async (userInfo) => {
            if (!data || !userInfo || !extra) return
            let pay_channel = 999
            if (extra.platform === Platform.WX) {
                pay_channel = 1
            }
            let eventInfo = {
                order_no: order,
                pay_price: data.cost,
                pay_price_rmb: data['cost_CNY'],
                store_id,
                pay_channel,
                currency_pay: extra.currency_pay,
                currency_price: extra.currency_price,
                pay_state: 0,
            };
            if (!notFirst) {
                let record = await recordMod.getRecord(uid);
                let popularity = await this.statUserHeartCol.findOne({ uid });
                eventInfo.popularity = popularity.heart || 0;
                let userDoc = await userMod.findOne(uid);
                eventInfo.total_online_time = userDoc ? userDoc.playTime : 0;
                if (record) {
                    !eventInfo.popularity && (eventInfo.popularity = recordMod.getFromRecord('global.heart', record));
                    eventInfo.operate_days = recordMod.getFromRecord('loginDayCounts', record);
                }
                eventInfo.operate_days = eventInfo.operate_days || 0;
            }
            dhlog.report(DH_LOG_EVENT.PAY, null, userInfo, eventInfo).then();
        })
    },

    // 获取分布式锁
    async acquireLock(uid, order, expireSeconds = 30) {
        const lockKey = `currency_order_lock:${uid}:${order}`;
        const lockValue = `${Date.now()}_${Math.random()}`;
        try {
            // 使用SET命令的NX和EX参数实现原子性锁
            const result = await this.redis.set(lockKey, lockValue, 'EX', expireSeconds, 'NX');
            logger.info(uid, result);
            if (result === 'OK') {
                return { success: true, lockKey, lockValue };
            }
            return { success: false };
        } catch (error) {
            logger.error('获取分布式锁失败:', error);
            return { success: false };
        }
    },

    // 释放分布式锁（使用Lua脚本确保原子性）
    async releaseLock(lockKey, lockValue) {
        const luaScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
    `;
        try {
            await this.redis.eval(luaScript, 1, lockKey, lockValue);
        } catch (error) {
            logger.error('释放分布式锁失败:', error);
        }
    },
}

module.exports = currencyModel;

const db = require("../db/db");
const { default: util } = require("../common/util");
const logger = require('../common/log').getLogger('currencyModel');
const { payMod } = require("../payment/PayModel")
const { CURRENCY, CURRENCY_ACTION, CURRENCY_ORDER_STATUS, ShopItemType } = require("../common/constant");
const { dhlog, DH_LOG_EVENT } = require("../common/DHLog");
const userMod = require("./userMod");
const { assetsMgr } = require("../client/AssetsMgr");
const { Platform } = require("../common/constant")
const config = require("../config");
const { gameHelper } = require("../client/GameHelper");
const ItemChangeObj = require("../common/reportObj/ItemChangeObj").default;
const { CHANNEL_WINDMILL, ITEM_CHANGE_TYPE } = require("../common/reportObj/Enums");
const recordMod = require('../mod/recordMod');
const redisKey = require('../db/redisKey');
const { log } = require("winston");
