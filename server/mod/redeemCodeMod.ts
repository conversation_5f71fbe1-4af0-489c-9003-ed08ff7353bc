const logger = require("../common/log").getLogger("RedeemCodeMod");
let db = require("../db/db");
import util from "../common/util"
import recordMod from "./recordMod"
import userMod from "./userMod"
import config from "../config"
import ModelMgr from "../client/ModelMgr";
import OfflineSysModel from "../client/OfflineSysModel";
import DBModel from "../client/DBModel";
import { ConditionType } from "../client/constant/Enums";
import { assetsMgr } from "../client/AssetsMgr";

class RedeemCodeMod {

    private userRedeemCodeCol: any = null

    private planCol: any = null

    public init() {
        this.userRedeemCodeCol = db.createUserRedeemCodeModel()
        this.planCol = db.createRedeemCodePlanModel()
    }

    public claimCode() {

    }

    public async getCodesByUid(uid) {
        let doc = await this.userRedeemCodeCol.findOne({uid})
        if (doc) {
            return doc.codes
        }
        return []
    }

    public async savePlan(info) {
        await this.planCol.updateOne({name: info.name}, info, {upsert: true})
    }

    public async getPlans() {
        return this.planCol.find({})
    }
}

export const redeemCodeMod = new RedeemCodeMod()