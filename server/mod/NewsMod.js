const db = require("../db/db");
const util = require("../common/util").default;
const redisKey = require("../db/redisKey")
const logger = require("../common/log").getLogger("newsMod");

// 行为类型
const ACTION_CONFIG = require("../client/json/tidbit.json");
const {async} = require("regenerator-runtime");

// 每天随机事件的范围
const MIN_ = 2;
const MAX_ = 5;
const LEN_ = 38;
// 每天每个排行榜取前多少条数据
const RANK_NUM = 20;
// 每天趣闻随机个数范围
const COUNT_MIN_ = 3;
const COUNT_MAX_ = 5;


let NewsMod = {
    __init: false,
    init() {
        if (this.__init) return
        this.newsCol = db.createNewsModel();
        this.userCol = db.createUserModel();
        this.redis = db.redis;
        this.__init = true;
    },
    // 随机出 MIN_ =< n <= MAX_ 个不重复事件
    async doRandomActionType() {
        if (!ACTION_CONFIG || (ACTION_CONFIG && !ACTION_CONFIG.length)) {
            logger.error("config not exits !");
            return
        }
        let num, types = [], ignore = [];
        num = util.random(MIN_, MAX_);
        for (let i = 0; i < num; i++) {
            let randomNumber = util.randomIndex(LEN_, 1, ignore);
            types.push(randomNumber + 1);
            ignore.push(randomNumber);
        }
        return types;
    },
    // 用value去获取actionType
    async getActionTypeByValue(val) {
        val = parseInt(val);
        for (let config of ACTION_CONFIG) {
            if (config.id === val) {
                return config;
            }
        }
    },
    // 时间戳是自 1970 1-1 的8点开始，所以这里百分百可以保证使用0点的时间,都是同一个day
    async getToday() {
        return util.getDateOfDay(util.getTodayZeroTime()) / util.Time.Day;
    },
    // 尝试获取今日份的事件
    async tryGetTodayActionTypes(day = 0) {
        let types = [];
        day = day || await this.getToday()
        // redis有就返回 没有就去mongodb
        types = await this.redis.smembers(redisKey.todayNewsTypes());
        if (!types || (types && !types.length)) {
            let doc = await this.newsCol.findOne({day}, {types: 1});
            if (doc) {
                types = JSON.parse(doc.types);
                for (const _t of types) {//redis丢了今日事件 重新写回去
                    await this.redis.sadd(redisKey.todayNewsTypes(), _t);
                }
            }
        } else { //redis里拿出来的是string
            let temp = types;
            types = [];
            temp.filter(_ => {
                types.push(parseInt(_))
            });
        }
        return types;
    },
    // 获取下一次刷新时间
    async getNextFlushTime() {
        let next = 0, day = await this.getToday();
        let doc = await this.newsCol.findOne({day}, {next: 1});
        if (doc) {
            next = doc.next || 0;
        } else {
            day -= 1;
            doc = await this.newsCol.findOne({day}, {next: 1});
            doc && (next = doc.next || 0);
        }
        return next;
    },
    //移除redis里面 之前的行为事件
    async tryRemoveActionTypes() {
        await this.redis.del(redisKey.todayNewsTypes());
    },
    /**
     * 检查 redis 和mongodb 后 再进行今日事件的随机
     */
    async doTodayRandom() {
        logger.info("doTodayRandom.");
        let types, day;
        types = await this.tryGetTodayActionTypes();
        if (types.length) return types;
        types = await this.doRandomActionType();
        day = await this.getToday();
        //to redis
        await this.redis.sadd(redisKey.todayNewsTypes(), types);
        //to db
        let save = new this.newsCol({
            next: Date.now() + util.Time.Day,
            day: await this.getToday(),
            types: JSON.stringify(types)
        })
        await save.save();
        logger.info(`doTodayRandom result: ${JSON.stringify(types)}`);
        return types;
    },
    // 今日行为上报
    async onReportAction(uid, type, number) {
        if (!uid) return {status: -1, desc: "uid is empty"};
        if (!number) return {status: -1, desc: "number can not be zero"};
        let dbTypes = await this.tryGetTodayActionTypes();
        type = parseInt(type);
        if (dbTypes.indexOf(type) === -1) {
            logger.warn(`type not exits, uid:${uid}, type:${type}, number:${number}, serverTypes:${JSON.stringify(dbTypes)}`);
            return {status: -1, desc: "type not exits!"};
        }
        number = parseInt(number) || 1;
        let day, _key, nowValue;
        day = await this.getToday();
        _key = redisKey.newsRank(day, type);
        let config = await this.getActionTypeByValue(type);
        nowValue = await this.redis.zscore(_key, uid) || 0;
        nowValue = parseInt(nowValue);
        nowValue += number;
        // 检查值是否超过了最大 减少上报次数和处理压力 超出次数的就告诉客户端不上报了
        if (nowValue > config.limit) {
            return {status: -71, full: true, day};
        }
        await this.redis.zadd(_key, nowValue, uid);
        return {status: 0, full: false, day};
    },
    // 每天的数据清理和排行榜数据提取
    async doClear(target = []) {
        let isTest = !!target.length;
        let day = await this.getToday();
        !isTest && (day = day - 1);
        let types = await this.tryGetTodayActionTypes(day);
        let rankInfo = [];
        // 前一天各个类型排行榜数据删除
        for (let type of types) {
            let _key = redisKey.newsRank(day, type), temp = [];
            // 提取数据
            let data = await this.redis.zrevrange(_key, 0, RANK_NUM, "WITHSCORES") || [];
            for (let i = 0; i < data.length; i += 2) {
                let config = await this.getActionTypeByValue(type);
                let name = -1;
                if (config && config.name) {
                    let _ = config.name.split('|');
                    _.length && (name = util.random(0, _.length));
                }
                temp.push({type, name, uid: data[i], score: data[i + 1]});
            }
            !data && (logger.warn(`rank:${type}_${day} loss data.`));
            if (temp.length) {
                let index = util.randomIndex(temp.length, 1);
                rankInfo.push(temp[index]);
            }
            await this.redis.del(_key);
        }
        // 补充玩家昵称区服等信息
        for (let info of rankInfo) {
            let user;
            user = await this.userCol.findOne({uid: info.uid}, {diyName: 1, serverId: 1, nickName: 1});
            user && (info.diyName = user.diyName || user.nickName || '', info.serverId = user.serverId);
        }
        isTest && (day = day - 1);
        let doc = await this.newsCol.findOneAndUpdate({day}, {rankInfo: JSON.stringify(rankInfo)}, {
            new: true,
            upsert: true
        });
        !doc && (logger.warn(`no rank infos ,day: ${day}`));
        !isTest && await this.tryRemoveActionTypes();
    },
    async getTodayNews() {
        let day = await this.getToday(), result = {};
        day -= 1;
        let today = await this.newsCol.findOne({day});
        result.status = 0;
        result.data = [];
        if (today && today.rankInfo) {
            result.data = JSON.parse(today.rankInfo);
        }
        return result;
    },
    async doFlush() {
        let next = await this.getNextFlushTime(), now = Date.now();
        if (now < next) return next - now;
        await this.doClear();
        await this.doTodayRandom();
        return 0;
    },
    /**
     * 提供给测试用来清除当天排行数据
     */
    async debug_clear_redis_data() {
        let types = await this.tryGetTodayActionTypes();
        let day = await this.getToday();
        for (let type of types) {
            let _key = redisKey.newsRank(day, type);
            await this.redis.del(_key);
        }
        return {status: 0};
    },
    /**
     * 提供给测试用来随机指定的类型数据
     * @param types
     */
    async debug_today_random(types = []) {
        await this.tryRemoveActionTypes();
        await this.debug_clear_redis_data();
        if (!types.length) return {status: 0};
        let day = await this.getToday();
        await this.redis.sadd(redisKey.todayNewsTypes(), types);
        let save = {
            next: Date.now() + util.Time.Day,
            types: JSON.stringify(types)
        }
        await this.newsCol.updateOne({day}, save, {upsert: true});
        return {status: 0};
    },
}

module.exports = NewsMod;
