const VError = require("verror");
const util = require("../common/util").default;
const { TXRewardAdFlag } = require("../db/redisKey");
const idMod = require("./idMod");
const recordMod = require("./recordMod");
const userMod = require("./userMod");
const global = require("../common/global")
const config = require("../config")

const logger = require("../common/log").getLogger("verifyMod");
let whiteListCol, gmCol, blackListCol, redis;

let packageSigns = ["3169d253f38147f0f7cb6ba1672e6255", "705ec6a4af3fe7f68294142731267317"]
if (config.type == "global") {
    packageSigns = []
}

let verifyMod = {
    switchCol: null,

    init() {
        let db = require("../db/db");
        redis = db.redis;
        whiteListCol = db.createWhiteListModel()
        blackListCol = db.createBlackListModel()
        this.blackListHistoryCol = db.createBlackListHistoryModel()
        gmCol = db.createGMModel()
        this.switchCol = db.createSwtichModel()
    },

    async isGM(uid, checkActive = true) {
        let doc = await verifyMod.findGM(uid)
        if (doc) {
            if (checkActive) {
                return doc.active
            }
            return true
        }
        return false
    },

    async getAllGM() {
        return gmCol.find({})
    },

    async findGM(uid) {
        let userDoc = await userMod.findOne(uid)
        if (!userDoc) return
        let { openid, unionid } = userDoc
        let orList = [{ uid }]
        if (openid) orList.push({ openid })
        if (unionid) orList.push({ unionid })
        let where = { '$or': orList }
        return gmCol.findOne(where)
    },

    async addGM(uid) {
        let doc = await this.findGM(uid)
        if (doc) return

        doc = await userMod.findOne(uid)
        doc = Object.assign({ active: true }, doc.toObject())
        await gmCol.updateOne({ uid }, doc, { upsert: true })
    },

    async updateGM(uid, info) {
        await gmCol.updateOne({ uid }, info)
    },

    async delGM(uid) {
        await gmCol.deleteOne({ uid })
    },

    async isWhite(uid) {
        let doc = await whiteListCol.findOne({ uid })
        return doc ? true : false
    },

    async getWhiteList() {
        let docs = whiteListCol.find({})
        return docs
    },

    async addWhite(uid) {
        await whiteListCol.updateOne({ uid }, { uid }, { upsert: true })
    },

    async delWhite(uid) {
        return whiteListCol.deleteOne({ uid })
    },

    async checkBlack(uid, starSum) { //返回true为作弊
        let record = await recordMod.getRecord(uid)
        if (!record) return true
        let global = recordMod.getFromRecord("global", record)
        if (!global) return true
        global.heart = starSum
        recordMod.setToRecord("global", record)
        let lv = await recordMod.verfityRecord(uid, record)
        return lv > 0
    },

    async isGood(uid) {
        let [isGM, isWhite] = await Promise.all([verifyMod.isGM(uid, true), verifyMod.isWhite(uid)])
        return isGM || isWhite
    },

    async addBlack(uid, reason, level = 1) {
        reason = reason || {}
        let c = {
            types: [10],
            reason: JSON.stringify(reason),
            timestamp: Date.now(),
            level
        }
        await blackListCol.updateOne({ uid }, c, { upsert: true })
        this.blackListHistoryCol.create(c);
    },

    async addBlackForce(uid) {
        return Promise.all([rankMod.remove(uid), verifyMod.addBlack(uid, "addBlackForce", 4)])
    },

    async isBlack(uid) {
        let doc = await blackListCol.findOne({ uid }, { _id: 1 })
        return doc ? true : false
    },

    async getBlackLevel(uid) {
        let doc = await blackListCol.findOne({ uid }, { level: 1 })
        if (doc) return doc.level || 0
        return 0
    },

    async getBlackById(uid) {
        let count = await this.getBlackCount(uid);
        let doc = await blackListCol.findOne({ uid });
        doc && (doc._doc.count = count || 0);
        return doc;
    },
    async getBlackCount(uid) {
        return await this.blackListHistoryCol.countDocuments({ uid });
    },
    async getBlackList(startTime, endTime, page = 1, pageSize = 30) {
        if (!startTime || !endTime) return []
        page = parseInt(page)
        pageSize = parseInt(pageSize)
        let wherestr = {
            timestamp: { $gte: parseInt(startTime), $lte: parseInt(endTime) }
        }
        let count = await blackListCol.countDocuments(wherestr)
        let data = await blackListCol.find(wherestr).skip((page - 1) * pageSize).limit(pageSize).sort("-timestamp")
        for (let u of data) {
            u._doc.count = await this.getBlackCount(u.uid);
        }
        return { count, data }
    },

    async delBlack(uid) {
        return blackListCol.deleteOne({ uid })
    },

    async addTXRewardAdFlag(uid, transid) {
        let key = TXRewardAdFlag(uid)
        let flag = await redis.hget(key, transid)
        if (flag) {
            if (flag == "remove") {
                await redis.hdel(key, transid)
            }
            return
        }
        await redis.hset(key, transid, "add")
        redis.expire(key, 30 * util.Time.Day / 1000)
    },

    async removeTXRewardAdFlag(uid, transid) {
        let key = TXRewardAdFlag(uid)
        let flag = await redis.hget(key, transid)
        if (flag) {
            if (flag == "add") {
                await redis.hdel(key, transid)
            }
            return
        }
        await redis.hset(key, transid, "remove")
        redis.expire(key, 30 * util.Time.Day / 1000)
    },

    async checkTXRewardAdFlag(uid) {
        let key = TXRewardAdFlag(uid)
        let info = await redis.hgetall(key) || {}
        let count = 0
        for (let key in info) {
            if (info[key] == "add") count++
        }
        return count <= 10
    },

    async clearTXRewardAdFlag(uid) {
        let key = TXRewardAdFlag(uid)
        return redis.del(key)
    },

    checkPackageSign(sign, gameVer, platform) {
        if (platform != 'android') return true
        if (packageSigns.length <= 0) return true
        return packageSigns.some(s => s === sign)
    },

    async isOpen(type, defaultActive = true) {
        let where = { type }
        let doc = await this.switchCol.findOne(where)
        if (!doc) {
            await this.switchCol.updateOne(where, { active: defaultActive }, { upsert: true })
            return defaultActive
        }
        return doc.active
    },

    async setSwtich(type, active) {
        let where = { type }
        await this.switchCol.updateOne(where, { active })
    },

    async setUserCanDeblock(uid, can = true) {
        await blackListCol.updateOne({ uid }, { canDeblock: can })
    },

    async deblockUser(uid) {
        let blackDoc = await this.getBlackById(uid)
        if (!blackDoc) return true
        let canDeblock = blackDoc.canDeblock
        if (!canDeblock) return false
        let data = await global.requestComputeSever("deblockUser", { uid })
        return data && data.succ
    }
}

module.exports = verifyMod;