const ThinkingAnalytics = require("thinkingdata-node");
const {USER_TYPE} = require('../common/constant');

const taConfig = {
    appId: 'd3509bd231d247ecbedb389e156d88e8',
    serverUrl: 'https://receiver-ta.twomiles.cn'
}
const TA = {
    REGISTER: 'ta_register',
}

let thinkingData = {
    init() {
        this.ta = ThinkingAnalytics.initWithBatchMode(taConfig.appId, taConfig.serverUrl, {
            batchSize: 1,
            enableLog: true // 允许打印发送数据. 当开启日志的时候会打印详细的发送日志
        });
    },
    async doReport(uid, time = 0, ta_event = TA.REGISTER) {
        if (!time) return void console.warn(`time err: ${uid}`)
        this.ta.track({
            event: ta_event,
            accountId: uid,
            time: new Date(time),
        })
        this.ta.flush();
        this.ta.userSet({
            accountId: uid,
            properties: {
                device_id: 'test_TOPQOSA',
                os_version: 'android',
                platform: 'android',
                open_id: uid,
                register_time: new Date(time)
            }
        });
    },
    async update_register_info(uid, device_id, platform, os_version, time) {
        let properties = {open_id: uid};
        device_id && (properties.device_id = device_id);
        platform && (properties.platform = platform);
        os_version && (properties.os_version = os_version);
        time && (properties.register_time = new Date(time));

        this.ta.userSet({
            accountId: uid,
            properties
        });
    }

}

module.exports = thinkingData;