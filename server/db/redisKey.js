const util = require("../common/util").default;
module.exports = {
    HOT_UPDATE_RELEASE_RANDOM: "HOT_UPDATE_RELEASE_RANDOM", //用来随机哪些玩家能被灰度到
    HOT_UPDATE_RELEASE_NUM: "HOT_UPDATE_RELEASE_NUM", //已热更人数

    wxCode2Session(code) {
        return 'WX_CODE_2_SESSION_' + code
    },

    qqCode2Session(code) {
        return 'QQ_CODE_2_SESSION_' + code
    },

    rankCache(rankKey) {
        return 'RANK_CAHCE_' + rankKey
    },

    TXRewardAdFlag(uid) {
        return "TXRewardAd_" + uid
    },

    shareClickRank() {
        return "SHARE_CLICK_RANK"
    },

    reddemCode(code) {
        return "REDDEMCODE_" + code
    },

    userCacheInfo(uid) {
        return "USER_CACHE_INFO_" + uid
    },

    roomLikeFlag(uid) {
        return "ROOM_LIKE_FLAG_" + uid
    },

    wxToken() {
        return "WX_TOKEN"
    },

    qqToken() {
        return "QQ_TOKEN"
    },

    recordCacheFlag(uid) {
        return "RECORD_CACHE_FLAG_" + uid
    },

    inviteWorkLock(uid) {
        return "INVITE_WORK_LOCK_" + uid
    },

    userToken(uid) {
        return "USER_TOKEN_" + uid
    },

    recordUploadLock(uid) {
        return "RECORD_UPLOAD_LOCK" + uid
    },

    wxSessionKey(openid) {
        return "WX_SESSION_KEY_" + openid
    },

    onlineUser() {
        return "ONLINE_USER"
    },

    subscribeTaskFlag(taskId) {
        return `SUBSCRIBE_TASK_FLAG_${taskId}`
    },

    subscribeCD(uid) { //订阅推送cd
        return `SUBSCRIBE_CD_${uid}`
    },

    subscribeLast(uid) { //记录最近的一条推送通知
        return `SUBSCRIBE_LAST_${uid}`
    },

    dhlogLoginCD(uid) {
        return `DHLOG_LOGIN_CD_${uid}`
    },

    lastSessionKey(uid) {
        return `LAST_SESSION_KEY_${uid}`
    },

    replayReqFlag(reqId) {
        return `REPLAY_REQ_FLAG_${reqId}`
    },

    replayRsp(reqId) { //存同一个请求的返回值
        return `REPLAY_RSP_${reqId}`
    },

    buildGuideRank(roomId) {
        return `BUILD_GUIDE_RANK_${roomId}`
    },

    cashEventLock(eventId, date) {
        return `CASH_EVENT_LOCK_${date}_${eventId}`
    },

    eventSet() {
        return `EVENT_SET`
    },

    eventCache(eventId) {
        return `EVENT_CACHE_${eventId}`
    },

    buildPlansRankInRoom(roomId) {
        return `BUILD_PLANS_RANK_${roomId}`
    },

    todayNewsTypes() {
        return `NEWS_ACTION_TYPE`;
    },
    newsRank(day, type) {
        return `NEWS_RANK_${type}_${day}`;
    },
    dailyWelfare(uid) {
        let now = new Date();
        return `DAILY_WELFARE_${uid}_${now.getUTCFullYear()}_${now.getUTCMonth()}_${now.getUTCDate()}`;
    },
    HMS_TOKEN() {
        return `HMS_TOKEN`
    },
    HMS_LOGIN_TOKEN() {
        return `HMS_LOGIN_TOKEN`
    },
    HMS_ROUTER() {
        return `HMS_ROUTER`
    },
    PARTY_REWARD_GET(uid) {
        const today = util.getNumberDay(util.getTodayZeroTime(6))
        return `PARTY_REWARD_GET_${uid}_${today}`
    },
    happyNewYear24Rank(type) {
        return `HAPPY_NEW_YEAR_RANK_24_${type}`;
    },
    happyNewYear24Record(type) {
        return `HAPPY_NEW_YEAR_RECORD_24_${type}`;
    },
    happyNewYear24Total(type) {
        return `HAPPY_NEW_YEAR_TOTAL_24_${type}`;
    },
    happyNewYear24Cur(type) {
        return `HAPPY_NEW_YEAR_CUR_24_${type}`;
    },
    happyNewYear24RankCache(type) {
        return `HAPPY_NEW_YEAR_RANK_CACHE_24_${type}`;
    },
    happyNewYear24RecordCache(type) {
        return `HAPPY_NEW_YEAR_RECORD_CACHE_24_${type}`;
    },
}
