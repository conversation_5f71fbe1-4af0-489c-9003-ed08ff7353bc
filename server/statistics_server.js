//定时统计数据用，放在web服
require('ts-node').register();

const db = require("./db/db");
const model = require("./mod/model");
const schedule = require('node-schedule');
let config = require("./config");
const router = require("./router/router");
const logger = require("./common/log").getLogger("job_server");
const {dhlog, DH_LOG_EVENT} = require("./common/DHLog");

config.port = 8041;

db.init();
model.init();
router.init();
let redis = db.redis

const statisticsMod = require("./mod/statisticsMod")

// 定时清理存档缓存
const recordMod = require("./mod/recordMod");
const util = require("./common/util").default;
const redisKey = require("./db/redisKey");
const recommendMod = require('./mod/recommendMod');
const userMod = require("./mod/userMod");

let syncRedeemCode = async () => {
    let redeemCodeModel = db.createRedeemCodeModel();
    let redeemCodeHistoryModel = db.createRedeemCodeHistoryModel()
    let redeemCodes = await redeemCodeModel.find({})
    await util.promiseMap(redeemCodes, async (doc) => {
        if (doc.unique >= 0) return
        let code = doc.code
        let key = redisKey.reddemCode(code)
        let count = await redis.scard(key)
        if (count <= 0) return
        doc = doc.toObject()
        doc.count = count
        delete doc._id
        await redeemCodeHistoryModel.updateOne({code}, doc, {upsert: true})
    })

    await util.wait(10 * util.Time.Minute)
    return syncRedeemCode()
}
syncRedeemCode()

let autoClearFeedback = async () => {
    let expireTime = 30 * util.Time.Day
    let feedbackOrderCol = db.createFeedbackOrderModel()
    let minTime = Date.now() - expireTime
    await feedbackOrderCol.deleteMany({timestamp: {$lt: minTime}})
}
let feedbackRule = new schedule.RecurrenceRule();
feedbackRule.hour = 0;
feedbackRule.minute = 10;
feedbackRule.second = 0;
const clearFeedback = schedule.scheduleJob(feedbackRule, async () => {
    logger.info("job start clearFeedback......")
    await autoClearFeedback()
    logger.info("job end clearFeedback")
});

let staticRule = new schedule.RecurrenceRule();
staticRule.hour = 0;
staticRule.minute = 10;
staticRule.second = 0;
schedule.scheduleJob(staticRule, async () => {
    logger.info("job start staticRule......")
    await statisticsMod.heartDisNew()
    logger.info("job end staticRule")
});

let staticRule2 = new schedule.RecurrenceRule();
staticRule2.hour = 1;
staticRule2.minute = 10;
staticRule2.second = 0;
schedule.scheduleJob(staticRule2, async () => {
    logger.info("job start static guideDis .....")
    await statisticsMod.guideDis()
    logger.info("job end static guideDis")
});

let staticRule3 = new schedule.RecurrenceRule();
staticRule3.hour = 2;
staticRule3.minute = 10;
staticRule3.second = 0;
schedule.scheduleJob(staticRule3, async () => {
    logger.info("job start static customerDis .....")
    // await statisticsMod.customerDis()
    logger.info("job end static customerDis")
});

let staticRule4 = new schedule.RecurrenceRule();
staticRule4.hour = 3;
staticRule4.minute = 10;
staticRule4.second = 0;
schedule.scheduleJob(staticRule4, async () => {
    logger.info("job start static commonDis .....")
    await statisticsMod.commonDis()
    logger.info("job end static commonDis")
});

schedule.scheduleJob('0 10 4 * * ?', async () => {
    logger.info("job start customerTask .....")
    await statisticsMod.customerTask();
    logger.info("job end customerTask")
});


// schedule.scheduleJob('0 0 0 * * ?', async () => {
//     logger.info("job start statisticsOnFurnitureUsage .....")
//     await statisticsMod.statisticsOnFurnitureUsage();
//     logger.info("job end statisticsOnFurnitureUsage")
// });

// 秒 分 时
schedule.scheduleJob('0 0 0 * * ?', async () => {
    logger.info("job start statCommonLevelAvg .....")
    await statisticsMod.statCommonLevelAvg();
    logger.info("job end statCommonLevelAvg")
});


// 每天执行一次 清理玩家备份数据
// schedule.scheduleJob('0 0 0 * * ?', async () => {
//     logger.info("job start : clearUserBackupData.....")
//     await userMod.clearUserBackupData();
//     logger.info("job end : clearUserBackupData.....")
// });

schedule.scheduleJob('0 0 0 * * ?', async () => {
    logger.info("job start : clear statistics data.....")
    await statisticsMod.clearData();
    logger.info("job end : clear statistics data.....")
});
