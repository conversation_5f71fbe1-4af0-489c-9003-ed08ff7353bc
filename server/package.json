{"name": "server", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "node fix.js"}, "author": "", "license": "ISC", "dependencies": {"@parse/node-apn": "^5.0.0", "base64url": "^3.0.1", "cos-nodejs-sdk-v5": "^2.9.15", "crypto-js": "^4.1.1", "express": "^4.17.1", "firebase-admin": "^10.0.0", "form-data": "^3.0.0", "googleapis": "^87.0.0", "got": "^11.8.2", "jsonwebtoken": "^8.4.0", "jwk-to-pem": "^2.0.4", "moment": "^2.29.0", "mongoose": "^5.10.7", "node-schedule": "^2.0.0", "node-xlsx": "^0.21.0", "on-headers": "^1.0.2", "pnpm": "^8.6.2", "redis": "^3.0.2", "regenerator-runtime": "^0.13.7", "request": "^2.88.2", "tencentcloud-cls-sdk-js": "^1.0.3", "tencentcloud-sdk-nodejs": "^4.0.220", "thinkingdata-node": "^1.3.0", "uuid": "^8.3.1", "verror": "^1.10.0", "winston": "^3.3.3", "winston-daily-rotate-file": "^4.5.0", "xlsx": "^0.16.9"}, "devDependencies": {"@swc/core": "^1.2.58", "@swc/helpers": "^0.2.12", "google-auth-library": "^7.14.0", "ts-node": "^10.0.0", "typescript": "^4.2.4"}}