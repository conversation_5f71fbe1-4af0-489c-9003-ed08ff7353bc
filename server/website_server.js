require('ts-node').register();
let config = require("./config");
const app = require("./common/app");
const { default: util } = require('./common/util');
const logger = require("./common/log").getLocal()

app.__init()

const downloadUrlConfig = {
    cn: 'https://twgame-hotel-global-1257666530.file.myqcloud.com/web/index_global_cn.html',
    en: 'https://www.dhgames.cn/twomiles/index_global_en.html',
    jp: 'https://www.dhgames.cn/twomiles/index_global_jp.html',
    kr: 'https://www.dhgames.cn/twomiles/index_global_kr.html',
    tc: 'https://www.dhgames.cn/twomiles/index_global_tc.html',
}

let skipToWebSite = (req, res)=>{
    let path = util.getReqPath(req)
    let ip = util.getClientIp(req)
    logger.info(`[${ip}] - `, path)
    let url
    "".substring()
    if (path[path.length - 1] == "/") {
        path = path.substring(0, path.length - 1)
    }
    path = path.split("/website")[1]
    if (path == "" || path == "/") {
        if (config.type == "global") {
            url = downloadUrlConfig.en
        }
        else {
            url = downloadUrlConfig.cn
        }
    }
    else {
        url = downloadUrlConfig[path.substring(1)] || downloadUrlConfig.en
    }

    res.writeHead(302, {Location: url});
    res.end();
}

app.get('/website', skipToWebSite)
app.get('/website/cn', skipToWebSite)
app.get('/website/en', skipToWebSite)
app.get('/website/jp', skipToWebSite)
app.get('/website/kr', skipToWebSite)
app.get('/website/tc', skipToWebSite)