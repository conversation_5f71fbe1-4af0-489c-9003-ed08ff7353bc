
#国内
sudo scp -r -i ./tw_hotel.pem common cypto mod router db game.js package* root@81.69.12.72:/root/server

#sudo ssh -i ./tw_hotel.pem root@81.69.12.72
#sudo ssh -i ./tw_hotel.pem root@81.69.12.72 "~/server/reload.sh"
#sudo ssh -i ./tw_hotel.pem root@81.69.12.72 "cat ~/server/log/info.log"

#国内日志
sudo scp -r -i ./tw_hotel.pem common cypto mod router db game.js package* root@*************:/root/server

#web，测试服
sudo scp -r -i ./tw_hotel.pem common cypto mod router db game.js package* web* fix.js root@************:/root/web
sudo scp -r -i ./tw_hotel.pem common cypto mod router db game.js package* fix.js job_server.js root@************:/root/server_test
sudo scp -r -i ./tw_hotel.pem common cypto mod router db game.js package* web* fix.js tsconfig.json client extend payment root@************:/root/web_test

#定时任务
sudo scp -r -i ./tw_hotel.pem common cypto mod router db job_server.js package* root@*************:/root/job


#海外
#sudo ssh -i ./tw_hotel.pem root@**************
sudo scp -r -i ./tw_hotel.pem common cypto mod router db game.js package* root@**************:/root/server

#海外日志
#sudo ssh -i ./tw_hotel.pem root@*************

<<'COMMENT'
#deploy
npm install pm2 -g
pm2 install pm2-logrotate-ext

echo
```
module.exports = {
  apps: [
    {
      script: 'game.js',
      cwd: "~/server/",
      instances: "max",
      exec_mode: "cluster",
      kill_timeout: 10000,
      wait_ready: true,
      listen_timeout: 5000,
      output: '~/.pm2/logs/out.log',
      error: '~/.pm2/logs/error.log',
      merge_logs: true,
      "log_date_format" : "YYYY-MM-DD HH:mm:ss.SSS"
    },
  ]
}
```
> ecosystem.config

#reload
npm install
#config change?
pm2 reload ~/server/ecosystem.config.js
COMMENT


