
type SwihNodeCallback = (it: cc.Node) => any;
type SwihToggleCallback = (it: cc.Toggle) => any;

type Point = {
    x: number;
    y: number;
}

var wx: any
var qq: any
var wxPro: any //微信接口promise化

declare namespace cc {

    interface Vec2 {
        set(any): Vec2
        set2(x: number, y: number): Vec2;
        equals2(x: number, y: number): boolean;
        Join(separator?: string): string;
        // toVec3(): Vec3;
        // newVec3(): Vec3;
        toJson(): Point;
        FlipX(): Vec2;
    }

    interface Vec3 {
        set2(x: number, y: number, z: number): Vec3;
        equals2(x: number, y: number, z?: number): boolean;
        Join(separator?: string): string;
        toVec2(): Vec2;
        newVec2(): Vec2;
    }

    interface _BaseNode {
        Data: any;
        _hitTest(vec: Vec2 | Vec3): boolean;
        Child(name: string | number): Node;
        Child(name: string | number, className: string): any;
        Child<T extends Component>(name: string | number, type: { prototype: T }): T;
        FindChild(name: string | number): Node;
        FindChild(name: string | number, className: string): any;
        FindChild<T extends Component>(name: string | number, type: { prototype: T }): T;
        Component<T extends Component>(type: { prototype: T }): T;
        Component(className: string): any;
        Items<T>(list: T[] | number, item: Node | Prefab, setItemData: (it: cc.Node, data: T, i: number) => void, target?: any): void;
        Items<T>(list: T[] | number, setItemData: (it: cc.Node, data: T, i: number) => void, target?: any): void;
        Items<T>(list: T[] | number): void;
        AddItem(item: Node | Prefab, setItemData: (it: cc.Node, i: number) => void, target?: any): void;
        AddItem(setItemData: (it: cc.Node, i: number) => void, target?: any): void;
        Swih(val: string | number | SwihNodeCallback): Node[];
        SetColor(val: string | Color): Node;
        SetSwallowTouches(val: boolean): void;
        IsSwallowTouches(): boolean;
        setLocaleKey(key: string, ...params: any[]);
        getWorldBox()
    }

    interface Component {
        getActive(): boolean;
        setActive(val: boolean): void;
        getPosition(out?: cc.Vec2): cc.Vec2;
        Child(name: string | number): Node;
        Child(name: string | number, className: string): any;
        Child<T extends Component>(name: string | number, type: { prototype: T }): T;
        FindChild(name: string | number): Node;
        FindChild(name: string | number, className: string): any;
        FindChild<T extends Component>(name: string | number, type: { prototype: T }): T;
        Component<T extends Component>(type: { prototype: T }): T;
        Component(className: string): any;
        SetColor(val: string | Color): Component;
        GetColor(): Color;
    }

    interface Animation {
        playAsync(name?: string): Promise<void>;
        playToFinished(callback: Function, name?: string): AnimationState;
    }

    interface Label {
        _forceUpdateRenderData(): void;// v2.2.0
        SetColor(val: string | Color): Label;
        setLocaleKey(key: string, ...params: any[]);
    }

    interface Sprite {
        setLocaleKey(key: string);
    }

    interface ScrollView {
        Items<T>(list: T[] | number, setItemData?: (it: cc.Node, data: T, i: number) => void, target?: any): void;
        AddItem(setItemData: (it: cc.Node, i: number) => void, target?: any): void;
        Find(predicate: (value: Node, index: number, obj: Node[]) => unknown, thisArg?: any): Node;
        IsEmpty(): boolean;
        List(len: number, setItemData?: (it: cc.Node, i: number) => void, target?: any): void;
    }

    interface ToggleContainer {
        Swih(val: string | number | SwihToggleCallback): Toggle[];
        Tabs(val: string | number): Toggle;
    }

    // 按钮扩展
    export class ButtonEx extends Component {
        interactable: boolean;
        static DefaultClickPath: string;
    }

    // scrollview扩展
    export class ScrollViewEx extends Component {
    }

    // 播放等待的点
    export class LabelWaitDot extends Component {
        play(val?: string): void;
        stop(val?: string): void;
    }

    // 滚动数字
    export class LabelRollNumber extends Component {
        setPrefix(val: string): LabelRollNumber;
        set(val: number): LabelRollNumber;
        to(end: number, duration?: number): void;
        by(val: number, duration?: number): void;
    }

    // 时间文本
    export class LabelTimer extends Component {
        string: string;
        setPrefix(val: string): LabelTimer;
        setFormat(val: string): LabelTimer;
        setEndTime(val: number): LabelTimer;
        setPause(val: boolean): void;
        run(time: number, callback?: Function): void;
    }

    // 多选颜色
    export class MultiColor extends Component {
        setColor(idx: number | boolean): void;
    }

    // 多选精灵
    export class MultiFrame extends Component {
        addFrame(sf: cc.SpriteFrame): void;
        setFrame(idx: number | boolean): void;
        getFrame(idx: number | boolean): SpriteFrame;
        getIndex(): number;
        frameCount(): number;
        getSpriteFrame(): SpriteFrame;
        random(): number;
        clean(): void;
    }

    // 多语言label
    export class LocaleLabel extends Component {
        string: string;
        setKey(key: string, ...params: any[]);
        updateLang();
        updateString();
    }

    // 多语言RichText
    export class LocaleRichText extends Component {
        setKey(key: string, ...params: any[]);
    }

    // 多语言Sprite
    export class LocaleSprite extends Component {
        setKey(key: string);
        addSpriteFrame(val: cc.SpriteFrame);
    }

    function instantiate2(item: Node | Prefab, parent: Node | Component): Node;
}

type ProcessCallback = (completedCount: number, totalCount: number, item?: any) => void;

type EventItem = {
    callback: Function;
    target: any;
}

type AudioAsset = {
    mod: string;// 模块名
    url: string;
    audio: cc.AudioClip;
}

type PnlParam = {
    isClean?: boolean;
    isAct?: boolean;
    isMask?: boolean;
    Index?: number;
    adaptHeight?: number;
    adIndex?: number;
}

type WindParam = {
    isClean?: boolean;
    isAutoReleaseSprite?: boolean;
}

// 加载pnl信息
type LoadPnlInfo = {
    id: number;
    name: string; //传入名
    url: string; //实际pnl路径
    params?: any[]; //参数
}

class BaseViewCtrl extends cc.Component {
    readonly _state: string;
    addListener(type: string, cb: Function, target?: any);
    removeListener(type: string);
    emit(type: string | number, ...params: any);
    addClickEvent(cmpt: cc.Component, handler: string, data?: string);
    // addWdtCtrl(path: string, type: typeof mc.BaseWdtCtrl): mc.BaseWdtCtrl;
    listenEventMaps(): { enter?: boolean }[];
    getModel<T>(key: string): T;
    async onLoadSprites(): Promise<void>;
    async retainSpriteFrameByPrefab(pfb: cc.Prefab): Promise<void>;
    onReleaseSprites(): void;
    releaseCacheSpriteFrameByPrefab(pfb: cc.Prefab): void;
    onResotreSprites(): void;
    resotreSpriteFrameByPrefab(pfb: cc.Prefab): void;
}

declare namespace mc {

    export const GameNameSpace: string;
    export var currWind: BaseWindCtrl;
    export var currWindName: string;
    export var lang: string; //当前语言
    export function init(name: string, root: cc.Node, lang?: string, changeLang?: boolean): void;
    export function lockTouch(val: boolean);
    export function addmodel(type: string);
    export function getWindNode(): cc.Node;
    export function getViewNode(): cc.Node;
    export function getNoticeNode(): cc.Node;
    export function getOpenPnls(): BasePnlCtrl[];

    export enum Event {
        MVC_ERROR_MSG,
        /** (key: string | BasePnlCtrl, ...params: any) */
        OPEN_PNL,
        /** (key: string | BasePnlCtrl) */
        HIDE_PNL,
        /** (val?: string, ignores: string) */
        HIDE_ALL_PNL,
        /** (key: string | BasePnlCtrl) */
        CLOSE_PNL,
        /** (val?: string) */
        CLOSE_ALL_PNL,
        /** (mod: string) */
        CLOSE_MOD_PNL,
        /** (key: string, complete?: Function, progress?: (done: number, total: number) => void) */
        PRELOAD_PNL,
        LOAD_BEGIN_PNL,
        LOAD_END_PNL,
        PNL_ENTER,
        PNL_LEAVE,
        CLEAN_ALL_UNUSED,
        /** (id: number) */
        GIVEUP_LOAD_PNL,
        /** (key: string, ...params: any) */
        GOTO_WIND,
        /** (key: string, complete?: Function, progress?: (done: number, total: number) => void) */
        PRELOAD_WIND,
        WIND_ENTER,
        CLEAN_CACHE_WIND,
        LOAD_BEGIN_WIND,
        LOAD_END_WIND,
        /** (complete?: Function, progress?: (done: number, total: number) => void) */
        LOAD_ALL_NOTICE,
        LOAD_BEGIN_SPRITE,
        LOAD_END_SPRITE,
        /** 热更新事件 */
        HOT_UPDATE_EVENT,
        HOT_UPDATE_END,
    }

    export class BasePnlCtrl extends BaseViewCtrl {
        readonly key: string; //传入名
        readonly mod: string; //所属模块名
        readonly url: string; //路径
        readonly Index: number; //层级
        readonly isClean: boolean; //是否清理
        async onCreate(): Promise<void>;
        onEnter(...params: any);
        onRemove();
        onClean();
        hide();
        close();
        setOpacity(val: number); //设置UI的透明度
        setParam(opts: PnlParam);
    }

    export class BaseWindCtrl extends BaseViewCtrl {
        readonly key: string; //传入名 即模块名
        async onCreate(): Promise<void>;
        onEnter(...params: any);
        onLeave();
        onClean();
        setParam(opts: WindParam);
        onBack();
    }

    export class BaseNoticeCtrl extends BaseViewCtrl {
        async onCreate(): Promise<void>;
        onClean();
        open();
        hide();
    }

    export class BaseWdtCtrl extends BaseViewCtrl {
        onCreate();
        onClean();
    }

    export class BaseLogCtrl extends cc.Component {
        onCreate();
        onClean();
        onLoggerListener(type: string, content: string);
        close();
    }

    export class BaseModel {
        readonly type: string;
        constructor(type: string);
        protected onCreate();
        protected onClean();
        protected emit(type: string | number, ...params: any);
        protected getModel<T>(key: string): T;
    }

    export class modelMgr {
        static add(...params: BaseModel[]): void;
        static get<T>(key: string): T;
        static reset(model: BaseModel): void;
    }
}

var eventCenter: {
    emit(type: number | string, ...params: any): void;
    get(type: number | string, ...params: any): any;
    async req(type: number | string, ...params: any): any;
    on(type: number | string, callback: Function, target?: any): void;
    once(type: number | string, callback: Function, target?: any): void;
    async wait(type: number | string): Promise<any>;
    off(type: number | string, callback?: Function, target?: any): void;
    clean();
}

var storageMgr: {
    register(key: string, callback: Function, target: any): void;
    loadString(key: string): string;
    saveString(key: string, val: string): void;
    loadNumber(key: string): number;
    saveNumber(key: string, val: number): void;
    loadBool(key: string): boolean;
    saveBool(key: string, val: boolean): void;
    loadJson(key: string): any;
    saveJson(key: string, val: any): void;
    loadBigJson(key: string): any;
    saveBigJson(key: string, val: any): void;
    save(): void;
    clear(): void;
    reset(): void;
    getStorageInfo();
    setStorageInfo(info: any, ver: number);
    syncSaveLazy(): void
    public loadObject(key: string): any
    public saveObject(key: string, val: object)
    getOrgItem(key: string)
    setOrgItem(key, data: string)
    getCheckInfoFromRecord(record?)
}

var hotUpdateMgr: {
    start(packageUrl: string);
    abort();
    redownload();
    getVersion(): string;
    versionCompareHandle2(versionA: string, versionB: string): number;
    cleanCache();
    convertBytesToString(bytes: number): string;
    isUpdating(): boolean;
}

var jsbHelper: {
    public cast(event: string, callback?: Function, jsonObj?: any, target?: any): any
    public async call(event: string, jsonObj?: any)
    public on(event: string, callback?: Function, target?: any)
    public off(event: string, callback?: Function, target?: any)
    public async getPackageSign(): string
}

// declare namespace ut {

//     export var Time: {
//         readonly Day: number;
//         readonly Hour: number;
//         readonly Minute: number;
//         readonly Second: number;
//     }

//     export function now(): number;

//     export function millisecondToString(msd: number): string;

//     export function timediff(start: number, date: string): number;

//     /**
//      * 将一个毫秒数格式化 format('hh:mm:ss')
//      * @param msd 
//      * @param format 默认'mm:ss'
//      */
//     export function millisecondFormat(msd: number, format?: string): string;

//     /**
//      * 将一个秒格式化
//      * @param val 
//      * @param format 默认'mm:ss'
//      */
//     export function secondFormat(val: number, format?: string): string;

//     /**
//      * 将一个时间 format('yyyy-MM-dd hh:mm:ss')
//      * @param format
//      * @param msd
//      */
//     export function dateFormat(format: string, msd?: number): string;

//     /**
//      * 首字母变成大写
//      * @param str 
//      */
//     export function initialUpperCase(str: string): string;

//     /**
//      * 将数字转换为String 中文
//      * @param money 
//      * @param num 
//      */
//     export function simplifyMoneyCh(money: number, num?: number): string;

//     /**
//      * 将数字转换为String 英文
//      * @param money 
//      * @param num 
//      */
//     export function simplifyMoneyEn(money: number, num?: number): string;

//     export function simplifyMoney(money: number, num?: number): string;

//     /**
//      * 名字省略
//      * @param name 
//      * @param max 
//      * @param extra 
//      */
//     export function nameFormator(name: string, max: number, extra?: string): string;

//     /**
//      * 将数字以逗号隔开
//      * @param num 
//      */
//     export function formatNumberByComma(num: number): string;

//     /**
//      * 随机一个整数 包括min和max
//      * @param min [最小值]
//      * @param max [最大值]
//      */
//     export function random(min: number, max?: number): number;

//     /**
//      * 是否有概率
//      * @param odds 概率值必须是100内的数字
//      * @param mul 概率值倍数
//      */
//     export function chance(odds: number, mul: number = 1): boolean

//     /**
//      * 随机一个负数到正数的范围
//      */
//     export function randomRange(min: number, max: number): number;

//     /**
//      * 随机一个下标出来
//      * @param len    [数组长度]
//      * @param count  [需要随机的个数](可不填)
//      * @param ignore [需要忽略的下标](可不填)
//      */
//     export function randomIndex(len: number, count?: number, ignore?: any);

//     /**
//      * 根据权重随机
//      * @param arr 权重数组
//      */
//     export function randomByWeight(arr: any);

//     /**
//      * 新的获取角度
//      * 以a为圆点开始顺时针方向旋转到b点的角度
//      * @param a [圆点]
//      * @param b [目标点]
//      */
//     export function getAngle(a: cc.Vec2 | cc.Vec3, b: cc.Vec2 | cc.Vec3): number;
//     export function normAngle(angle: number): number;

//     /**
//      * Math.sin 返回Y坐标
//      * @param angle 
//      */
//     export function sin(angle: number): number;

//     /**
//      * Math.cos 返回X坐标
//      * @param angle 
//      */
//     export function cos(angle: number): number;

//     /**
//      * 根据角度和距离 获取坐标
//      * @param angle 
//      * @param dis 
//      * @param out 
//      */
//     export function angleToPoint(angle: number, dis: number, out?: cc.Vec2): cc.Vec2;

//     /**
//     * 获取某个节点在某个节点里面的坐标
//     * @param node 需要转换的节点
//     * @param targetNode 要转换到的目标节点
//     */
//     export function convertToNodeAR(node: cc.Node, targetNode: cc.Node, out?: cc.Vec2): cc.Vec2;

//     /**
//     * 获取某个节点的某个坐标在某个节点里面的坐标
//     * @param node 需要转换的节点
//     * @param targetNode 要转换到的目标节点
//     * @param nodePoint 第一个节点的某个坐标，默认原点
//     * @param withCamera 是否需要相机转换（两个节点在不同摄像机下), 默认为true
//     */
//     export function convertToNodePosAR(node: cc.Node, targetNode: cc.Node, nodePoint?: cc.Vec2, out?: cc.Vec2, withCamera: boolean = true): cc.Vec2;

//     /**
//     * 数字 字符串补0,根据长度补出前面差的0
//     * @param num 需要补的数字
//     * @param length 要补的长度默认为2
//     */
//     export function pad(num: number, length: number = 2): string;

//     /**
//      * 将一个数字 分解成多个类型的数字
//      * @param num   [数字]
//      * @param types [你想分解的类型列表 可不填]
//      */
//     export function decomposeNumberToTypes(num: number, types: number[] = [100000, 10000, 1000, 100, 10, 1], out?): any;

//     /**
//      * 将一个字符串转换成向量
//      * @param str 一个字符串必须满足以逗号隔开
//      * @param separator 分隔符默认','
//      */
//     export function stringToVec2(str: string, separator?: string): cc.Vec2;

//     /**
//      * 将一个字符串拆分为数组
//      * @param str 
//      * @param separator 默认|
//      */
//     export function stringToNumbers(str: string, separator?: string): number[];

//     /**
//      * 将一个常数变成1 并保留正负
//      * @param val 
//      */
//     export function normalizeNumber(val: number): number;

//     /**
//      * 将一个数字转换为带正负符号的字符串
//      * @param val 
//      */
//     export function numberToString(val: number): string;

//     /**
//      * 填充一个带参数的字符串
//      * @param text 
//      * @param params 
//      */
//     export function stringFormat(text: string, params: any[]): string;

//     /**
//      * 同步等待时间 (单位秒)
//      * @param delay 
//      */
//     export function wait(delay: number, target?: cc.Component): Promise<void>;

//     /**
//      * 同步等待时间 (单位毫秒)，用setTimeout实现
//      * @param delay 
//      */
//     export function waitTimeout(delay: number): Promise<void>;

//     /**
//      * 等待下一帧
//      * @param frames (需要等待的帧数,默认1)
//      */
//     export function waitNextFrame(frames?: number, target?: cc.Component): Promise<void>;

//     /**
//      * 读取 16 进制颜色
//      * color.fromHEX("#FFFF33");
//      * @param hexString 
//      */
//     export function colorFromHEX(hexString: string): cc.Color;

//     /**
//      * 生成一个唯一ID
//      */
//     export function uid(): string;

//     /**
//      * 是否对象
//      * @param o 
//      */
//     export function isObject(o: any): boolean;

//     /**
//      * 判断是否空对象
//      * @param o 
//      */
//     export function isEmptyObject(o: any);

//     /**
//      * 拷贝对象
//      * @param obj 
//      */
//     export function cloneObject(obj: any): any;

//     /**
//      * 深度拷贝对象
//      * @param obj 
//      */
//     export function deepClone(obj: any, inDeep?: boolean): any;

//     /**
//      * 深度比较两个对象是否相等
//      * @param x 
//      * @param y 
//      */
//     export function compareObject(x: any, y: any): boolean;

//     /**
//      * 组装列表
//      * @param arr 
//      * @param datas 
//      * @param item 
//      * @param parent 
//      * @param cb 
//      */
//     export function items(arr: cc.Node[], datas: any[], item: cc.Node | cc.Prefab, parent: cc.Node, cb: Function): void;

//     /**
//      * 循环值
//      * @param val 
//      * @param len [数组长度] 
//      */
//     export function loopValue(val: number, len: number);

//     /**
//      * 设置屏幕常亮
//      * @param val 
//      */
//     export function setKeepScreenOn(val: boolean);

//     /**
//      * 将一个bool值转成1和0
//      * @param val 
//      */
//     export function boolToNumber(val: boolean): number;

//     /**
//      * 对象给对象赋值
//      * @param target 
//      * @param value 
//      * @param fields 
//      */
//     export function setValue(fields: string, data: any, target?: any): any;

//     /**
//      * http请求
//      * @param method 
//      * @param url 
//      * @param data 
//      * @param cb 
//      */
//     export function httpRequest(method: string, url: string, data?: any): Promise<any>;

//     /**
//      * 是否手机平台
//      */
//     export function isMobile(): boolean;

//     /**
//      * 判断是否是小程序
//      */
//     export function isMiniGame(): boolean

//     /**
//      * 是否ios
//      */
//     export function isIos(): boolean;

//     /**
//      * 判断是安卓
//      */
//     export function isAndroid(): boolean

//     /**
//      * 获取随机字符串
//      */
//     export function getRandomString(len: number): string

//     /**
//      * 创建一个数组
//      * @param count 
//      * @param val 
//      */
//     export function newArray(count: number, val?: any): any[];

//     /**
//      * Array.map的异步版本
//      */
//     export function promiseMap<T, R>(arr: T[], callback?: (T) => R): Promise<R[]>;

//     /**
//      * 对方法进行加锁的修饰器
//      * 加锁的方法必须以promise作为返回值，可以接收一个锁名作为参数
//      * @example addLock  addLock("isWork")
//      */
//     export function addLock(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor

//     export function dataProp(ctorProtoOrOptions?, propName?): any

//     /**
//      * 数字取整
//      * < 1000 按10取整
//      * < 100000 按100取整
//      * >= 100000 按1000取整
//      * @param val 
//      */
//     export function toFloor(val: number): number
// }

// 配置表结构
type JsonConfData = {
    datas: any[]
    // dataIdMap: any
    getById(id: string | number): any
    get(key: string, value: any): any[]
}

// 临时资源结构
type TempAssetData = {
    name: string
    asset: cc.Asset
    url: string
    type: typeof cc.Asset
    refs: { key: string, count: number }[] //引用列表
}

// 资源管理
// var assetsMgr: {

//     debug: boolean;

//     // 初始化
//     async init(onProgess?: (percent: number) => void): Promise<void>;

//     /**
//      * 获取配置表数据
//      * @param name 
//      */
//     getJson(name: string): JsonConfData;

//     /**
//      * 获取配置表数据2
//      * @param name 
//      * @param id 
//      */
//     getJsonData(name: string, id: string | number): any;

//     /**
//      * 获取全局图片
//      * @param name 
//      */
//     getImage(name: string): cc.SpriteFrame;

//     /**
//      * 获取全局预制体
//      * @param name 
//      */
//     getPrefab(name: string): cc.Prefab;

//     /**
//      * 获取声音
//      * @param name 
//      */
//     getAudio(name: string): cc.AudioClip;

//     /**
//      * 获取材质
//      * @param name
//      */
//     getMaterial(name: string): cc.Material;

//     /**
//      * 获取字体
//      * @param name 
//      */
//     getFont(name: string): cc.Font;

//     /**
//      * 转换文本
//      * @param key 
//      * @param params 
//      */
//     lang(key: string, ...params: any[]): string;

//     /**
//      * 加载临时资源 (需要手动释放)
//      * 会先从内存中寻找 如果有就会直接获取 没有将加载进来放入内存 缓存
//      * @param name 
//      * @param type 
//      * @param tag 
//      */
//     async loadTempRes(name: string, type: typeof cc.Asset, tag?: string): Promise<any>;
//     async loadTempRseDir(key: string, type: typeof cc.Asset, tag?: string): Promise<any[]>;

//     /**
//      * 加载远程图片 (需要手动释放)
//      * @param url
//      * @param ext 图片后缀 （.png .jpg）
//      * @param tag
//      */
//     async loadRemote(url: string, ext: string, tag?: string): Promise<cc.SpriteFrame>;

//     /**
//      * 释放临时资源
//      * @param name 
//      * @param tag 
//      */
//     releaseTempRes(name: string, tag?: string);
//     releaseTempAsset(name: string);

//     /**
//      * 释放所有标记的临时资源
//      * @param tag 
//      */
//     releaseTempResByTag(tag: string);

//     /**
//      * 根据path，uuid等找资源url, 更详细的参数参考源码urlTransformer
//      * @param params eg: {path: "manifest/project", bundle: "resources"}
//      * @param option eg: {ext: .manifest}
//      */
//     transform(params: { path: string, bundle: string } | string, option: { ext?: string, __isNative__?: boolean } | any = {}): string

//     /**
//      * 加载一次性资源 不缓存
//      * 注意：需要和releaseOnceRes成对使用
//      * @param url 
//      * @param type 
//      */
//     async loadOnceRes(url: string, type: typeof cc.Asset): Promise<any>;

//     releaseOnceRes(url: string, type: typeof cc.Asset);
// };

// 声音
var audioMgr: {

    bgmVolume: number;
    sfxVolume: number;

    // 初始化
    init(): void;
    // 暂停所有声音
    pauseAll(): void;
    // 恢复所有声音
    resumeAll(): void;
    //
    stopAll(): void;
    clean(): void;

    /**
     * 加载声音
     * @param urls 
     */
    async load(urls: string | string[]): Promise<void>;

    /**
     * 预加载
     * @param url 
     */
    async preload(url: string): Promise<void>;

    /**
     * 加载声音 根据模块来
     * @param mod
     */
    async loadByMod(mod?: string): Promise<void>;

    /**
     * 释放单个声音
     * @param val 
     */
    release(val: string): void;

    /**
     * 释放对应mod的声音
     * @param mod 
     */
    releaseByMod(mod?: string): void;

    /**
     * 释放所有声音
     */
    releaseAll(): void;

    /**
     * 播放背景音乐
     * @param url 
     */
    playBGM(url: string, volume?: number): void;

    /**
     * 停止播放背景音乐
     */
    stopBGM(): void;

    /**
     * 播放音效
     * @param url 
     * @param loop [是否循环]
     * @param cb [播放完成后的回调]
     */
    async playSFX(url: string, volume?: number, startTime?: number, loop?: boolean, cb?: Function): Promise<number>;

    /**
     * 停止播放音效
     * @param val 
     */
    stopSFX(val: number | string | cc.AudioClip): void;

    /**
     * 设置临时的背景音乐声音
     * @param val 
     */
    setTempBgmVolume(val: number): void;
}

// 敏感词过滤
var sensitiveWord: {
    /**
     * 设置敏感词库
     * @param val 敏感词库
     */
    setWords(val: string[]): void;

    /**
     * 过滤敏感词
     * @param content 过滤内容
     */
    filter(content: string): string
}

// 日志
var twlog: {
    open: boolean;
    /**
     * 打印信息
     * @param msg 
     * @param subst 
     */
    info(msg: any | string, ...subst: any[]): void;

    /**
     * 打印错误日志
     * @param msg 
     * @param subst 
     */
    error(msg: any | string, ...subst: any[]): void;

    upLog: {
        info(...params: any);
        warn(...params: any);
        error(...params: any);
        setFilterMsg(msg: any);
        addFilterMsg(msg: any);
    }
}

// 数组
interface Array<T> {

    /**
     * 删除数组一个元素并返回这个元素
     * @param key
     * @param value
     */
    remove(key: any, value?: any): T

    /**
     * 删除满足条件的数组元素
     * @param cb 
     */
    delete(cb: (value: T, index: number) => boolean): T[]

    /**
     * 返回一个随机元素
     */
    random(): T

    /**
     * 随机删除一个元素 并返还这个元素
     */
    randomRemove(): T

    /**
     * 是否有这个元素
     */
    has(key: any, value?: any): boolean

    /**
     * 添加一个元素并返回这个数组
     */
    append(val: T): T[]

    /**
     * 添加一个元素并返回这个元素
     */
    add(val: T): T

    /**
     * 返回最后一个元素
     */
    last(): T

    /**
     * 拼接数组 对象
     */
    join2(cb: (value: T, index: number) => string, separator?: string): string

    /**
     * push数组
     */
    pushArr(arr: T[]): number;

    /**
     * 重新设置这个数组
     */
    set(arr: T[]): T[]

    /**
     * 从后面查找index
     * @param cb 
     */
    findLastIndex(cb: (value: T, index: number) => boolean): number

    /**
     * 从后面查找
     * @param cb 
     */
    findLast(cb: (value: T, index: number) => boolean): T

    unique(): T[]
}

// promise
interface PromiseConstructor {
    /**
     * @param iterators 
     * @description 返回第一个成功的
     */
    any<T>(iterators: readonly (T | PromiseLike<T>)[]): Promise<T>;
}
