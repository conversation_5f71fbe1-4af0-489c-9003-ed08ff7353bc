
let router = require('express').Router();
const logger = require('../common/log').getLogger('feedback');
const { FEED_BACK_SENDER_TYPE } = require('../common/constant');
const { asyncWarp, requestLogger, decryptReq} = require('../common/middleware');
const util = require('../common/util').default;
const feedbackMod = require('../mod/feedbackMod');

router.use(decryptReq)
router.use(requestLogger())

router.get("/getMsgList", asyncWarp(async({uid, type}) => {
    let data = await feedbackMod.getMsgList(uid, type)
    data = data.map((doc) => {
        return util.setValue2("content|timestamp|senderType", doc)
    })
    return {status: 0, data}
}))

router.post("/sendMsg", asyncWarp(async({uid, content, version, platform, type, lang}) => {
    let info = {content, version, platform, senderType: FEED_BACK_SENDER_TYPE.PLAYER, lang}
    await feedbackMod.sendMsg(uid, type, info)
    return {status: 0}
}))

router.get("/delMsgList", asyncWarp(async({uid, type}) => {
    await feedbackMod.delOrder(uid, type)
    return {status: 0}
}))

module.exports = router;