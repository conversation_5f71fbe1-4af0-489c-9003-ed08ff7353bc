const {request<PERSON><PERSON><PERSON>, error<PERSON><PERSON><PERSON>, decryptReq} = require('../common/middleware');
const app = require("../common/app");
const currency = require("./currency");
const store = require("./Store");
const news = require("./News");

module.exports = {
    init() {
        const login = require('./login');
        const common = require('./common');
        const record = require('./record');
        const version = require('./version');
        const share = require('./share');
        const rank = require('./rank')
        const feedback = require('./feedback')
        const room = require('./room')
        const subscribe = require('./subscribe')
        const motherMsg = require('./motherMsg')
        const friend = require('./friend')
        const wdWork = require('./wdWork')
        const test = require('./test')
        const recommend = require('./recommend')
        const compute = require('./compute')
        const coupon = require('./coupon')
        const adSkipCard = require('./adSkipCard')
        const verify = require('./verify')
        const report = require('./report')
        const currency = require('./currency')
        const store = require('./Store')
        const news = require('./News')
        const account = require('./account')
        const party = require('./party')
        const garden = require('./garden')
        const activity = require('./activity')

        const app = require('../common/app');
        app.__init()
        app.use('/', common);
        app.use('/login', login);
        app.use('/record', record);
        app.use('/version', version);
        app.use('/share', share);
        app.use('/rank', rank);
        app.use('/feedback', feedback)
        app.use('/room', room)
        app.use('/subscribe', subscribe)
        app.use('/motherMsg', motherMsg)
        app.use('/friend', friend)
        app.use('/wdWork', wdWork)
        app.use('/test', test)
        app.use('/recommend', recommend)
        app.use('/compute', compute)
        app.use('/coupon', coupon)
        app.use('/adSkipCard', adSkipCard)
        app.use('/verify', verify)
        app.use('/report', report)
        app.use('/currency', currency)
        app.use('/store', store)
        app.use('/news', news)
        app.use('/account', account)
        app.use('/party', party)
        app.use('/garden', garden)
        app.use('/activity', activity)
        app.use(errorHandler);
    }
};
