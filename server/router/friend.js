let router = require('express').Router();
const logger = require('../common/log').getLogger('friend');
const {asyncWarp, decryptReq} = require('../common/middleware');
const util = require('../common/util').default;
const friendMod = require('../mod/friendMod');
const cryptoHelper = require("../cypto/cryptoHelper");
const partyMod = require("../mod/partyMod");
const gardenMod = require("../mod/gardenMod");
const partyPlusMod = require("../mod/partyPlusMod");

router.use(decryptReq)

// 获取好友列表
router.get("/getFriendList", asyncWarp(async ({uid, detail}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        // logger.info(`I - [${ip}] [GET/friend/getFriendList]`, uid, detail)
    } else {
        logger.warn(`I - [${ip}] [GET/friend/getFriendList] uid not found`)
        return {status: -1}
    }

    let list = await friendMod.getFriendList(uid)
    //logger.info(`O - [${ip}] [GET/friend/getFriendList]`, uid, list.length)
    let canJoinPartyNum = await partyMod.getCanJoinPartyNum(uid)
    let canJoinGardenPartyNum = await gardenMod.getCanJoinPartyNum(uid)
    let canJoinPartyV2Num = await partyPlusMod.getTodayCanJoinCnt(uid)
    return {status: 0, list, canJoinPartyNum, canJoinGardenPartyNum, canJoinPartyV2Num}
}))

// 获取申请列表
router.get("/getApplyList", asyncWarp(async ({uid, detail}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/friend/getApplyList]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/friend/getApplyList] uid not found`)
        return {status: -1}
    }

    let list = await friendMod.getApplyList(uid)
    //logger.info(`O - [${ip}] [GET/friend/getApplyList]`, uid, list.length)
    return {status: 0, list}
}))

// 获取推荐列表
router.get("/getRecommendList", asyncWarp(async ({uid, heart, serverId}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/friend/getRecommendList]`, uid, heart, serverId)
    } else {
        logger.warn(`I - [${ip}] [GET/friend/getRecommendList] uid not found`)
        return {status: -1}
    }

    let list = await friendMod.getRecommendList(uid, heart, serverId)
    //logger.info(`O - [${ip}] [GET/friend/getRecommendList]`, uid, list.length)
    return {status: 0, list}
}))

// 申请成为好友
router.post("/applyFriend", asyncWarp(async ({uid, toUid}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/friend/applyFriend]`, uid, toUid)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/applyFriend] uid not found`)
        return {status: -1}
    }

    let status = await friendMod.applyFriend(uid, toUid)
    logger.info(`O - [${ip}] [POST/friend/applyFriend]`, uid, toUid, status)
    return {status}
}))

// 一键申请好友
router.post("/applyAllFriend", asyncWarp(async ({uid, list}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/friend/applyAllFriend]`, uid, list)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/applyAllFriend] uid not found`)
        return {status: -1}
    }

    let status = await friendMod.applyAllFriend(uid, list)
    logger.info(`O - [${ip}] [POST/friend/applyAllFriend]`, uid, status)
    return {status}
}))

// 同意成为好友
router.post("/agreeApplyFriend", asyncWarp(async ({uid, toUid}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/friend/agreeApplyFriend]`, uid, toUid)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/agreeApplyFriend] uid not found`)
        return {status: -1}
    }

    let status = await friendMod.agreeApplyFriend(uid, toUid)
    logger.info(`O - [${ip}] [POST/friend/agreeApplyFriend]`, uid, toUid, status)
    return {status}
}))

// 拒绝成为好友
router.post("/rejectApplyFriend", asyncWarp(async ({uid, toUid}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/friend/rejectApplyFriend]`, uid, toUid)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/rejectApplyFriend] uid not found`)
        return {status: -1}
    }

    let status = await friendMod.rejectApplyFriend(uid, toUid)
    logger.info(`O - [${ip}] [POST/friend/rejectApplyFriend]`, uid, toUid, status)
    return {status}
}))

// 删除好友
router.post("/deleteFriend", asyncWarp(async ({uid, toUid}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [POST/friend/deleteFriend]`, uid, toUid)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/deleteFriend] uid not found`)
        return {status: -1}
    }

    let status = await friendMod.deleteFriend(uid, toUid)
    //logger.info(`O - [${ip}] [POST/friend/deleteFriend]`, uid, toUid, status)
    return {status}
}))

// 同步好感度配置
router.get("/syncFavorabilityConfig", asyncWarp(async ({uid}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/friend/syncFavorabilityConfig]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/friend/syncFavorabilityConfig] uid not found`)
        return {status: -1}
    }

    let {status, config} = await friendMod.syncFavorabilityConfig(uid)
    //logger.info(`O - [${ip}] [GET/friend/syncFavorabilityConfig]`, uid, config)
    return {status, config}
}))

// 增加好感度
router.post("/addFavorability", asyncWarp(async ({uid, toUid, exp}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [POST/friend/addFavorability]`, uid, toUid, exp)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/addFavorability] uid not found`)
        return {status: -1}
    }

    let status = await friendMod.addFavorability(uid, toUid, exp)
    //logger.info(`O - [${ip}] [POST/friend/addFavorability]`, uid, toUid, status)
    return {status}
}))

// 获取好友互动奖励数量
router.get("/getFriendRewardCount", asyncWarp(async ({uid, type}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [GET/friend/getFriendRewardCount]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/friend/getFriendRewardCount] uid not found`)
        return {status: -1}
    }

    let {status, count} = await friendMod.getFriendRewardCount(uid, type)
    logger.info(`O - [${ip}] [GET/friend/getFriendRewardCount]`, uid, count)
    return {status, count}
}))

// 获取好友互动奖励列表
router.get("/getFriendRewardList", asyncWarp(async ({uid, type}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [GET/friend/getFriendRewardList]`, uid, type)
    } else {
        logger.warn(`I - [${ip}] [GET/friend/getFriendRewardList] uid not found`)
        return {status: -1}
    }

    let {status, list} = await friendMod.getFriendRewardList(uid, type)
    logger.info(`O - [${ip}] [GET/friend/getFriendRewardList]`, uid, list.length)
    return {status, list}
}))

// 增加好友互动奖励
router.post("/addFriendReward", asyncWarp(async ({uid, toUid, reward, type}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/friend/addFriendReward]`, uid, toUid, reward)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/addFriendReward] uid not found`)
        return {status: -1}
    }

    let status = await friendMod.addFriendReward(uid, toUid, reward, type)
    logger.info(`O - [${ip}] [POST/friend/addFriendReward]`, uid, toUid, status)
    return {status}
}))

// 领取好友互动奖励
router.post("/receiveFriendReward", asyncWarp(async ({uid, date, type}, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/friend/receiveFriendReward]`, uid, type, date)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/receiveFriendReward] uid not found`)
        return {status: -1}
    }

    let status = await friendMod.receiveFriendReward(uid, date, type)
    logger.info(`O - [${ip}] [POST/friend/receiveFriendReward]`, uid, status)
    return {status}
}))

// 查找用户
router.post("/searchUser", asyncWarp(async ({uid, toUid}, req) => {
    let ip = util.getClientIp(req)
    let data = await cryptoHelper.pkDecrypt(toUid)
    toUid = (data && data.decStr) || toUid
    if (uid) {
        //logger.info(`I - [${ip}] [POST/friend/searchUser]`, uid, toUid)
    } else {
        logger.warn(`I - [${ip}] [POST/friend/searchUser] uid not found`)
        return {status: -1}
    }

    let {status, user} = await friendMod.searchUser(uid, toUid)
    //logger.info(`O - [${ip}] [POST/friend/searchUser]`, uid, toUid, status, user)
    return {status, user}
}))

router.post("/like", asyncWarp(async ({uid, target}) => {
    const status = await friendMod.likeUser(uid, target)
    logger.info(`I - [POST/friend/like] uid:${uid}, status:${status}`)
    return {status}
}))

router.post("/likeRecord", asyncWarp(async ({uid}) => {
    const arr = await friendMod.getLikeRecord(uid)
    logger.info(`I - [POST/friend/likeRecord] uid:${uid}, arr:${arr}`)
    return {status: 0, arr}
}))

module.exports = router;
