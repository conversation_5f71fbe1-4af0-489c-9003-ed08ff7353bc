let router = require('express').Router();
const {asyncWarp, requestLogger, decryptReq} = require('../common/middleware');
const logger = require("../common/log").getLogger("compute");
let verror = require("verror");
const db = require("../db/db");
const util = require('../common/util').default;
const wxMod = require('../mod/wxMod');
const redisKey = require('../db/redisKey');
const config = require("../config")
const userMod = require("../mod/userMod")
const verfiyRecordMod = require("../mod/verfiyRecordMod").verfiyRecordMod;
const cryptoHelper = require('../cypto/cryptoHelper');
const {payMod} = require("../payment/PayModel")

router.post('/verfityRecord', asyncWarp(async ({uid, record, ip, deviceId, gameVer}, req) => {
    let level = await verfiyRecordMod.check(uid, record, false, ip, deviceId, gameVer)
    return {level}
}))

router.post('/deblockUser', asyncWarp(async ({uid}) => {
    let succ = await verfiyRecordMod.deblock(uid)
    return {status: 0, succ}
}))

router.post('/rsaDecrypt', asyncWarp(async ({encryptStr}) => {
    let data = await cryptoHelper.pkDecrypt(encryptStr, null, false)
    if (data) {
        return data
    }
    return {}
}))

router.post('/pkSign', asyncWarp(async ({data, idx}) => {
    let sign = await cryptoHelper.pkSign(data, idx, false)
    return {sign}
}))

router.post('/getWxToken', asyncWarp(async () => {
    let token = await wxMod.getTokenFromCache()
    return {token}
}))

router.post('/setWxTokenExpire', asyncWarp(async ({token}) => {
    let newToken = await wxMod.refreshToken(token)
    return {token: newToken}
}))

router.post('/chekPayOrder', asyncWarp(async (order) => {
    return payMod.check(order)
}))

module.exports = router;