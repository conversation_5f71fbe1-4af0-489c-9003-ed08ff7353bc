let router = require('express').Router();
const { requestLogger, asyncWarp, decryptReq, verifyToken } = require('../common/middleware');
const logger = require("../common/log").getLogger("record");
let verror = require("verror");
const recordMod = require('../mod/recordMod');
const global = require('../common/global');
const lzw = require('../common/lzw');
const util = require('../common/util').default;
const userMod = require("../mod/userMod");
const config = require("../config")
const got = require('got');
const cryptoHelper = require('../cypto/cryptoHelper');
const verifyMod = require('../mod/verifyMod');
const { RecordState } = require('../client/constant/Enums');

router.use(decryptReq)
router.use(requestLogger({
    path: {
        "/record/upload": false,
        "/record/download": false,
        "/record/downloadByUid": false,
        "/uploadDiff": false,
        "/gacha": false,
    }
}));
router.use("/upload", verifyToken({ gameVer: "2.2.6" }))
router.use("/gacha", verifyToken({ gameVer: "2.2.6" }))
router.use("/changeUserInfo", verifyToken())

router.get('/check', asyncWarp(async ({ uid, starSum, playTime }) => {
    // let level = await verifyMod.getBlackLevel(uid)
    // if (level >= 3) {
    //     return {
    //         state: RecordState.SAME,
    //         starSum: starSum,
    //         playTime: playTime,
    //         updateTime: Date.now(),
    //     }
    // }

    let res = await recordMod.checkRecordByUid(uid, { starSum, playTime });
    return res;
}))

router.post('/upload', asyncWarp(async ({
    uid,
    record,
    verifyCode,
    gameVer,
    deviceId,
    token,
    checkType,
    switchDevice
}, req) => {
    let ip = util.getClientIp(req);
    logger.info(`I - [${ip}] [/record/upload]`, uid, gameVer, deviceId, token, checkType)
    if (!uid) {
        return { status: -2 }
    }

    if (util.cmpVersion(gameVer, "10.5.1") == 0) {
        return { status: -3 };
    }

    let recordState = await recordMod.uploadRecord(uid, record, gameVer, ip, deviceId, checkType, switchDevice);
    return { status: 0, recordState };
}))

router.post('/uploadDiff', asyncWarp(async ({ uid, diff, timestamp }, req) => {
    // let ip = util.getClientIp(req);
    // logger.info(`I - [${ip}] [/record/upload]`, uid)
    // if (!uid) {
    //     return {status: -2}
    // }
    if (util.cmpVersion(gameVer, "10.5.1") == 0) {
        return { status: -3 };
    }
    await recordMod.uploadDiffRecord(uid, diff, timestamp);
    return { status: 0 };
}))

//废弃
router.get('/download', asyncWarp(async ({ uid, verifyCode }, req) => {
    let ip = util.getClientIp(req);
    logger.info(`I - [${ip}] [GET/record/download]`, uid)
    let pass = await global.verifyCode(uid, verifyCode);
    if (!pass) {
        logger.warn("downloadRecord verifyCode fail", uid, verifyCode);
        return { status: -1 };
    }
    let record = await recordMod.getRecord(uid);
    if (!record) {
        throw verror({ name: "record not found" });
    }

    try {
        let globalStr = recordMod.getFromRecord("global", record, false);
        let userStr = recordMod.getFromRecord("user", record, false);
        if (!globalStr || !userStr) {
            logger.info("downloadRecord success", uid, record);
        } else {
            logger.info("downloadRecord success", uid, globalStr, userStr);
        }
    } catch (error) {
        logger.error(error)
    }

    record = lzw.oldCompress(JSON.stringify(record));

    return { status: 0, record };
}))

router.post('/download', asyncWarp(async ({ uid, verifyCode, clientRecord, gameVer, deviceId }, req) => {
    let ip = util.getClientIp(req);
    logger.info(`I - [${ip}] [POST/record/download]`, uid, gameVer, deviceId)
    // let pass = await global.verifyCode(uid, verifyCode);
    // if (!pass) {
    //     logger.warn("downloadRecord verifyCode fail", uid, verifyCode);
    //     return {status: -1};
    // }
    let { version, record, stop } = await recordMod.download(uid, clientRecord, gameVer) || {}
    if (stop) {
        return { status: -1001 }
    }
    if (record) {
        return { status: 0, record, version };
    }
    return { status: 1 }
}))

router.get('/downloadByUid', asyncWarp(async ({ uid, downloadUid, gameVer }, req) => {
    let ip = util.getClientIp(req);
    logger.info(`I - [${ip}] [GET/record/downloadByUid]`, uid, downloadUid, gameVer)
    let [record, baseInfo] = await Promise.all([
        recordMod.downloadByUid(downloadUid, gameVer, uid),
        userMod.getNameAndAvatar(downloadUid),
    ])
    if (record) {
        logger.info("downloadByUid success", uid, downloadUid);
        record = cryptoHelper.ase(record, uid)
        return { status: 0, record, baseInfo: baseInfo || {} };
    } else {
        logger.warn("downloadByUid fail", uid, downloadUid);
        return { status: 1 }
    }
}))

router.post('/gacha', asyncWarp(async ({ uid, type, data, gameVer }, req) => {
    let ip = util.getClientIp(req);
    logger.info(`I - [${ip}] [POST/record/gacha]`, uid, type)
    if (!uid || !type || !data) {
        return { status: -1 }
    }
    await recordMod.gacha(uid, type, data, gameVer)
    return { status: 0 }
}))

router.post('/changeDiyInfo', asyncWarp(async ({ uid, info }) => {
    return await userMod.changeDiyInfo(uid, info)
}))

router.get('/clear', asyncWarp(async ({ uid }) => {
    // await userMod.deleteUser(uid);
    return { status: 0 };
}))

router.get('/debug_copy', asyncWarp(async ({ uid, targetId }) => { //实际调用web_server的接口
    let record = await recordMod.debugCopy(targetId)
    return { status: 0, record }
}))

module.exports = router;
