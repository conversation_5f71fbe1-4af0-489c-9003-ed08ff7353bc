let router = require('express').Router();
const logger = require('../common/log').getLogger('currency');
const { asyncWarp, decryptReq, requestLogger } = require('../common/middleware');
const util = require('../common/util').default;
const currencyModel = require('../mod/currencyModel');
const cryptoHelper = require("../cypto/cryptoHelper");

router.use(decryptReq)
router.use(requestLogger({
    path: { "/currency/getTotalPay": false }
}))

// 同步通货余额
router.get("/asynCurrencyBalance", asyncWarp(async ({ uid }, req) => {
    if (!uid) {
        return { status: -1 }
    }

    let balance = await currencyModel.asynCurrencyBalance(uid)
    return { status: 0, balance }
}))

// 创建通货订单
router.post("/createCurrencyOrder", asyncWarp(async ({ uid, action, order, extra, gameVer }, req) => {
    if (!uid) {
        return { status: -1 }
    }
    const lockResult = await currencyModel.acquireLock(uid, order)
    if (!lockResult.success) {
        return { status: -5544, message: "order is pending, please wait." }
    }
    try {
        return await currencyModel.createCurrencyOrder(uid, action, order, extra, gameVer)
    } catch (error) {
        logger.error("createCurrencyOrder error", error)
    }
    await currencyModel.releaseLock(lockResult.lockKey, lockResult.lockValue)
    return { status: -1 }
}))

// 获取已支付订单
router.get("/getPaidOrder", asyncWarp(async ({ uid }, req) => {
    if (!uid) {
        return { status: -1 }
    }

    let list = await currencyModel.getPaidOrder(uid)
    return { status: 0, list }
}))

// 提交订单状态
router.post("/submitCurrencyOrderStatus", asyncWarp(async ({ uid, order, status }, req) => {
    if (!uid) {
        return { status: -1 }
    }
    await currencyModel.submitCurrencyOrderStatus(uid, order, status)
    return { status: 0 }
}))

// 获取总付费 美刀  只取付费成功的订单
router.post("/getTotalPay", asyncWarp(async ({ uid }) => {
    if (!uid) return { status: -1 };
    let pay = await currencyModel.getUserTotalPay(uid);
    let data = await currencyModel.getUserTotalBal(uid);
    return { status: 0, pay, data };
}))

module.exports = router;
