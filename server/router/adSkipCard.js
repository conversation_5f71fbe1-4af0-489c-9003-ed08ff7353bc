let router = require('express').Router();
const logger = require('../common/log').getLogger('adSkipCard');
const { asyncWarp, decryptReq } = require('../common/middleware');
const util = require('../common/util').default;
const adSkipCardMod = require('../mod/adSkipCardMod');
const cryptoHelper = require("../cypto/cryptoHelper");

router.use(decryptReq)

// 创建广告跳过卡订单
router.post("/createAdSkipCardOrder", asyncWarp(async ({ uid, openid, amount, code }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [GET/adSkipCard/createAdSkipCardOrder]`, uid, openid, amount, code)
    } else {
        logger.warn(`I - [${ip}] [GET/adSkipCard/createAdSkipCardOrder] uid not found`)
        return { status: -1 }
    }

    let { status, message, order } = await adSkipCardMod.createAdSkipCardOrder(uid, openid, amount, code)
    logger.info(`O - [${ip}] [GET/adSkipCard/createAdSkipCardOrder]`, uid, status, message, order)
    return { status, message, order }
}))

module.exports = router;