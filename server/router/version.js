let router = require('express').Router();
const logger = require("../common/log").getLogger("version");
let verror = require("verror");
const { requestLogger, asyncWarp, decryptReq } = require('../common/middleware');
const util = require('../common/util').default;
const versionMod = require('../mod/versionMod');

router.use(decryptReq);
router.use(requestLogger());

router.get('/check', asyncWarp(async (info) => {
    return await versionMod.check(info);
}))

module.exports = router;
