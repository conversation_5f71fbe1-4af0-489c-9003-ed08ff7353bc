
let router = require('express').Router();
const logger = require('../common/log').getLogger('rank');
const { asyncWarp, requestLogger, decryptReq } = require('../common/middleware');
const util = require('../common/util').default;
const rankMod = require('../mod/rankMod');
const userMod = require('../mod/userMod');
const verifyMod = require("../mod/verifyMod")
const { USER_TYPE } = require("../common/constant")
const config = require("../config")

router.use(decryptReq)
// router.use(requestLogger());

const TOP_NUM = 50

router.get("/getWorldRank", asyncWarp(async ({ uid, starSum, nickName, avatarUrl }, req) => {
    let ip = util.getClientIp(req);
    if (uid) {
        logger.info(`I - [${ip}] [GET/rank/getWorldRank]`, uid, starSum, nickName)
    } else {
        logger.warn(`getWorldRank uid not found`, starSum, nickName)
        return { status: -2 }
    }

    let userDoc = await userMod.findOne(uid)
    let key = rankMod.getRankName(userDoc.serverId)
    if (!config.debug) {
        if (userDoc.userType == USER_TYPE.GUEST) return {status: -3}
    }

    let success = await rankMod.updateScore(key, uid, starSum, userDoc.nickName, userDoc.avatarUrl); //上传分数
    if (!success) {
        let level = await verifyMod.getBlackLevel(uid)
        await rankMod.remove(uid)
        if (level == 0) {
            let rankList = await rankMod.getTopInfo(key, TOP_NUM)
            let selfRank = {uid, score: starSum, nickName, avatarUrl}
            for (let i = 0; i < rankList.length; i++) {
                let rank = rankList[i]
                if (starSum >= rank.score) {
                    rankList.splice(i, 1, selfRank)
                    break
                }
            }
            if (rankList.length <= 0) {
                rankList.push(selfRank)
            }
            logger.info(`O - [${ip}] [GET/rank/getWorldRank] bad boy`, uid)
            return { rankList, selfRank }
        }
        else {
            logger.info(`O - [${ip}] [GET/rank/getWorldRank]`, uid, -1)
            return { status: -1 }
        }
    }
    let [rankList, selfRank] = await Promise.all([rankMod.getTopInfo(key, TOP_NUM), rankMod.getInfo(key, uid)]) // 获取top50, 获取自己
    logger.info(`O - [${ip}] [GET/rank/getWorldRank]`, uid, rankList.length, selfRank)
    return { rankList, selfRank }
}))

module.exports = router;