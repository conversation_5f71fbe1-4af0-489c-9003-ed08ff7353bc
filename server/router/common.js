let router = require('express').Router();
const { asyncWarp, requestLogger, decryptReq } = require('../common/middleware');
const logger = require("../common/log").getLogger("common");
let verror = require("verror");
const db = require("../db/db");
const util = require('../common/util').default;
const wxMod = require('../mod/wxMod');
const redisKey = require('../db/redisKey');
const config = require("../config")
const userMod = require("../mod/userMod");
const verifyMod = require('../mod/verifyMod');
const currencyModel = require("../mod/currencyModel")
const { CURRENCY, CURRENCY_ACTION, CURRENCY_ORDER_STATUS } = require("../common/constant");
const got = require("got")
const textModeration = require('../mod/textModerationMod');
const activityMod = require("../mod/activityMod");
const cyptoHelper = require("../cypto/cryptoHelper");
const Random = require("../cypto/random");
const recordMod = require('../mod/recordMod');

const VERIFY_SALT = "twgames2025"

router.get('/', asyncWarp(async (params) => {
    return { nowTime: Date.now() };
}))

router.get('/getNowTime', asyncWarp(async (params) => {
    return { nowTime: Date.now() };
}))

router.get('/ping', asyncWarp(async ({ __sessionId, deviceId, gameVer }) => {
    let { uid } = userMod.parseSessionId(__sessionId)
    if (util.cmpVersion(gameVer, "2.9.6") >= 0) {
        let { diff, platform } = await userMod.isDeviceDiffAndGetInfo(uid, deviceId)
        if (diff) {
            return { status: -2, platform }
        }
    }
    let succ = await userMod.heartbeat(null, __sessionId, "ping")
    if (succ) {
        return { status: 0, nowTime: Date.now() }
    } else {
        return { status: -1 }
    }
}))

router.post('/errorReport', asyncWarp(async ({ uid, platform, version, errors }) => {
    let clientErrorModel = db.createClientErrorModel();
    await util.promiseMap(errors, async (error) => {
        error.uid = uid;
        error.platform = platform;
        error.version = version;
        await clientErrorModel.create(error);
    })
    return { status: 0 };
}))

router.use("/reddemCode", decryptReq)
router.use("/reddemCode", requestLogger())
router.get("/reddemCode", asyncWarp(async ({ uid, code }) => {
    // tap转移
    if (code.startsWith("xtapx_")) {
        let tapId = code.replace("xtapx_", "")
        let info = await cyptoHelper.pkDecrypt(tapId)
        let uid = info && info.decStr
        // 不是一个正确的tapId
        if (!uid) return { status: -40900 }
        const usr = await userMod.findOne(uid)
        // tap 对应的玩家id不存在
        if (!usr || !usr.signupTime) return { status: -40901 }
        // 类型须是qq
        if (usr.userType != "qq") return { status: -40902 }
        const record = await recordMod.getRecord(uid)
        const random = new Random()
        random.setSeed(BigInt(usr.signupTime))
        // uid类似 bd548b1e-5954-4c02-81d2-72c0dd72b137
        let token = ""
        for (const char of uid) {
            const randNum = random.nextInt() % 100;
            const xored = (char.charCodeAt(0) ^ randNum ^ VERIFY_SALT.charCodeAt(0));
            token += xored.toString(16).padStart(2, '0');
        }
        // 生成校验码失败？
        if (token == "") return { status: -40903 }
        const resp = {
            heart: recordMod.getFromRecord("global.heart", record),
            candies: recordMod.getFromRecord("global.candies", record),
            biscuits: recordMod.getFromRecord("global.biscuits", record),
            status: 0,
            tapUid: uid,
            token
        }
        return resp
    }

    let redis = db.redis
    let redeemCodeModel = db.createRedeemCodeModel();
    let userRedeemCodeModel = db.createUserRedeemCodeModel();
    let historyCol = db.createRedeemCodeHistoryModel();
    let [doc, isGM] = await Promise.all([redeemCodeModel.findOne({ code: code }), verifyMod.isGM(uid)])
    if (doc) {
        let validTime = doc.validTime || 0;
        let key = redisKey.reddemCode(code)
        if (validTime) {
            let endTime = doc.date + validTime * util.Time.Hour;
            let nowTime = new Date().getTime();
            if (nowTime < doc.date) {
                return { status: -2 } // 兑换码还没开始
            }
            if (nowTime > endTime) {
                await redeemCodeModel.deleteOne({ code: code })
                redis.del(key)
                return { status: -3 } // 兑换码已过期
            }
        }

        let typeStr = doc.type
        let data = []
        if (typeStr.includes(",")) { //新版
            let typeStrs = typeStr.split("|")
            data = typeStrs.map((str) => {
                let [type, id, count, tag] = str.split(",")
                if (!isNaN(id)) {
                    id = Number(id)
                }
                return { type: Number(type), id, count: Number(count), tag }
            })
        } else {
            let [type, id] = typeStr.split("_")
            if (!isNaN(id)) {
                id = Number(id)
            }
            data = [{ type: Number(type), id, count: Number(doc.value) }]
        }

        if (!config.debug && !isGM) {
            let used = await redis.sismember(key, uid)
            if (used) {
                return { status: -4 }
            }
            await redis.sadd(key, uid)
            redis.pexpire(key, validTime * util.Time.Hour || util.Time.Day * 30)
        }

        let unique = doc.unique;

        if (unique === 0 || unique === 1) {
            await redeemCodeModel.deleteOne({ code: code });
        } else if (unique > 1) {
            await redeemCodeModel.updateOne({ code: code }, { "$inc": { "unique": -1 } })
        }
        data.forEach(({ type, id, count }) => {
            if (type == 16) {
                currencyModel.changeCurrencyBalance(uid, CURRENCY.WINDMILL, Number(count))
            } else if (type == 17) {
                currencyModel.changeCurrencyBalance(uid, CURRENCY.SCISSOR, Number(count))
            } else if (type == 9102) {
                // 特殊商店抽奖券
                activityMod.changeCoupon(uid, Number(count));
            } else if (type == 9103) {
                activityMod.updateActivityData(uid, {
                    $inc: { point: Number(count) }
                })
            }
        })
        await userRedeemCodeModel.updateOne({ uid }, {
            $push: {
                codes: {
                    $each: data.map(({ type, id, count }) => {
                        return {
                            type: type + "_" + id,
                            value: count,
                            timestamp: Date.now()
                        }
                    }),
                }
            }
        }, { upsert: true })

        return { status: 0, data: data }
    } else {
        doc = await historyCol.findOne({ code });
        if (doc) {
            let validTime = doc.validTime || 0;
            if (validTime) {
                if (new Date().getTime() > (doc.date + validTime * util.Time.Hour)) {
                    return { status: -3 } // 兑换码已过期
                }
            }
        }
        return { status: -1 }
    }
}))

router.use("/decryptWxData", requestLogger())
router.get("/decryptWxData", asyncWarp(async ({ code, encryptedData, iv }) => {
    let info = await wxMod.code2Session(code);
    if (!info) {
        return { status: -1 }
    }
    try {
        let data = await wxMod.decryptData(encryptedData, iv, info.session_key)
        return { status: 0, data }
    } catch (error) {
        logger.warn("decryptWxData error", encryptedData, iv, info.session_key)
        return { status: -2 }
    }
}))

router.use("/spAward", decryptReq)
router.use("/spAward", requestLogger())
router.get("/spAward", asyncWarp(async ({ uid, types }) => {
    let data = await userMod.getSpAward(uid, types)
    return { status: 0, data }
}))

router.get("/transmitImge", async (req, res) => {
    try {
        let data = await got(req.query.url).buffer();
        res.send(data)
    } catch (error) {
        logger.error(error);
        res.sendStatus(500)
    }
})

router.use("/isValidContent", decryptReq)
router.use("/isValidContent", requestLogger())
router.get("/isValidContent", asyncWarp(async ({ uid, content, isWx }) => {
    if (!uid || !content) return { status: -1 };
    if (!isWx) {
        let e = await textModeration.isValid(content);
        return { status: e ? 0 : -1 };
    }
    let doc = await userMod.findOne(uid);
    if (!doc) return { status: -1 };
    let t = await wxMod.msgSecCheck(uid, doc.openid, content);
    return { status: t ? -1 : 0 };
}))
router.use("/debug_change_logoutTime", decryptReq)
router.get("/debug_change_logoutTime", asyncWarp(async ({ uid, timestamp }) => {
    await userMod.updateOne(uid, { logoutTime: timestamp }, false)
    return { status: 0 };
}))
router.use("/tapTrans", decryptReq)
router.use("/tapTrans", requestLogger())
router.get("/tapTrans", asyncWarp(async ({ uid, tapUid, verify }) => {
    if (!tapUid || !verify) return { status: -40903 }
    const usr = await userMod.findOne(tapUid)
    // tap 对应的玩家id不存在
    if (!usr || !usr.signupTime) return { status: -40904 }
    // 类型须是qq
    if (usr.userType != "qq") return { status: -40905 }
    const toUsr = await userMod.findOne(uid)
    if (!toUsr) return { status: -40907 }
    if (toUsr.userType) {
        if (toUsr.userType != "app_wx" && toUsr.userType != "app_apple") return { status: -40908 }
    }
    const random = new Random()
    random.setSeed(BigInt(usr.signupTime))
    let result = ""
    for (const char of tapUid) {
        const randNum = random.nextInt() % 100;
        const xored = (char.charCodeAt(0) ^ randNum ^ VERIFY_SALT.charCodeAt(0));
        result += xored.toString(16).padStart(2, '0');
    }
    // 校验码不一致
    if (result != verify) return { status: -40906 }
    const r = await userMod.MoveUsr(tapUid, uid, "true", "false", true)
    return { status: r.status }
}))

module.exports = router;
