let router = require('express').Router();
const {asyncWarp, requestLogger, decryptReq} = require('../common/middleware');
const statisticsMod = require('../mod/statisticsMod');
//const curLogMod = require("../mod/curLogMod");

router.use("/heart", decryptReq)
router.post("/heart", asyncWarp(async ({uid, heart}) => {
    await statisticsMod.updateUserHeart(uid, heart)
    return {status: 0}
}))

router.use("/guide", decryptReq)
router.post("/guide", asyncWarp(async ({uid, guideId, progress}) => {
    await statisticsMod.updateUserGuide(uid, guideId, progress)
    return {status: 0}
}))

router.post("/umeng", asyncWarp(async (params) => {
    await statisticsMod.cashEvent(params)
    return {status: 0}
}))

router.use("/hotUpdateStart", decryptReq)
router.post("/hotUpdateStart", asyncWarp(async (info) => {
    await statisticsMod.hotUpdateStart(info)
    return {status: 0}
}))

router.use("/hotUpdateEnd", decryptReq)
router.post("/hotUpdateEnd", asyncWarp(async (info) => {
    await statisticsMod.hotUpdateEnd(info)
    return {status: 0}
}))

// router.use("/clog", decryptReq)
// router.post("/clog", asyncWarp(async ({key, body}) => {
//     curLogMod.onReport({key, body})
//     return {status: 0}
// }))

module.exports = router;
