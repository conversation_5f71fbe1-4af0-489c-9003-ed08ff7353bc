
let router = require('express').Router();
const { asyncWarp, requestLogger, decryptReq } = require('../common/middleware');
const subscribeMod = require('../mod/subscribeMod');

router.use(decryptReq)
router.use(requestLogger());

router.post("/sub", asyncWarp(async (info) => {
    if (!info.uid) {
        return { status: -1 }
    }

    subscribeMod.subscribe(info)
    return { status: 0 }
}))

module.exports = router;