let router = require('express').Router();
const logger = require('../common/log').getLogger('coupon');
const { asyncWarp, decryptReq } = require('../common/middleware');
const util = require('../common/util').default;
const couponMod = require('../mod/couponMod');
const cryptoHelper = require("../cypto/cryptoHelper");

router.use(decryptReq)

// 获取优惠券列表
router.get("/getCouponList", asyncWarp(async ({ uid, openid, channel, isApp }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [GET/coupon/getCouponList]`, uid, openid, channel, isApp)
    } else {
        logger.warn(`I - [${ip}] [GET/coupon/getCouponList] uid not found`)
        return { status: -1 }
    }

    let list = await couponMod.getCouponList(uid, openid, channel, isApp)
    logger.info(`O - [${ip}] [GET/coupon/getCouponList]`, uid, list.length)
    return { status: 0, list }
}))

// 优惠券曝光度提交
router.post("/submitCouponExposure", asyncWarp(async ({ uid, openid, url }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/coupon/submitCouponExposure]`, uid, openid, url)
    } else {
        logger.warn(`I - [${ip}] [POST/coupon/submitCouponExposure] uid not found`)
        return { status: -1 }
    }

    let status = await couponMod.submitCouponExposure(uid, openid, url)
    logger.info(`O - [${ip}] [POST/coupon/submitCouponExposure]`, uid, status)
    return { status }
}))

// 优惠券点击数提交
router.post("/submitCouponClick", asyncWarp(async ({ uid, openid, url }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/coupon/submitCouponClick]`, uid, openid, url)
    } else {
        logger.warn(`I - [${ip}] [POST/coupon/submitCouponClick] uid not found`)
        return { status: -1 }
    }

    let status = await couponMod.submitCouponClick(uid, openid, url)
    logger.info(`O - [${ip}] [POST/coupon/submitCouponClick]`, uid, status)
    return { status }
}))

module.exports = router;