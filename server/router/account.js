let router = require('express').Router();
const {requestLogger, decryptReq, asyncWarp, checkReplay} = require('../common/middleware');
const loginMod = require("../mod/loginMod");
const logger = require("../common/log").getLogger("account");
let verror = require("verror");
const recordMod = require('../mod/recordMod');
const util = require('../common/util').default;
const accountMod = require('../mod/accountMod');
const {Platform} = require('../common/constant');
const config = require("../config")

router.use(requestLogger());
router.use(decryptReq)

/**
 *  归属权验证
 */
router.post("/reqCheckIdentity", asyncWarp(async ({uid, name, code}) => {
    let _ = await accountMod.sureAccountOwner({uid}, name, code);
    return _ ? _ : {status: 0};
}))
router.post("/reqCheckEmail", asyncWarp(async ({uid, name, code}) => {
    let _ = await accountMod.sureAccountOwner({uid}, name, code);
    return _ ? _ : {status: 0};
}))

/**
 * 注销账号
 * @param uid 用户id
 * @return {status:Number} 只有当注销成功,也就是status为0时.
 */
router.post("/reqCancelAccount", asyncWarp(async ({uid, name, code}) => {
    return await accountMod.reqCancel(uid, name, code);
}))

/**
 * 取消注销 处理后重新走登录
 * @param uid 用户id
 */
router.post("/reqRestoreAccount", asyncWarp(async ({uid}) => {
    return await accountMod.reqRestore(uid);
}))


module.exports = router;
