let router = require('express').Router();
const logger = require("../common/log").getLogger("common");
let verror = require("verror");
const { requestLogger, asyncWarp, decryptReq } = require('../common/middleware');
const util = require('../common/util').default;
const verifyMod = require('../mod/verifyMod');
const userMod = require("../mod/userMod")

router.get('/TXRewardAdCallBack', asyncWarp(async ({uid, transid}) => {
    await verifyMod.addTXRewardAdFlag(uid, transid)
    return { isValid: true }
}))

router.use('/updateTXRewardAdFlag', requestLogger());
router.get('/updateTXRewardAdFlag', asyncWarp(async ({uid, transid}) => {
    await verifyMod.removeTXRewardAdFlag(uid, transid)
    return {status: 0}
}))

router.use('/deblockUser', decryptReq);
router.use('/deblockUser', requestLogger());
router.post('/deblockUser', asyncWarp(async ({uid}) => {
    let succ = await verifyMod.deblockUser(uid)
    if (succ) {
        return {status: 0}
    }
    return {status: -1}
}))

router.use('/authIDCard', decryptReq);
router.use('/authIDCard', requestLogger());
router.post('/authIDCard', asyncWarp(async ({uid, idNum, name}) => {
    let {status, age} = await userMod.authIDCard(uid, idNum, name)
    if (status == 0) {
        return {status, age}
    }
    return {status}
}))

module.exports = router;
