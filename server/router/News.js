let router = require('express').Router();
const logger = require("../common/log").getLogger("store")
const newsMod = require("../mod/NewsMod");
const {requestLogger, asyncWarp, decryptReq} = require("../common/middleware");
const config = require("../config")

router.use(decryptReq)
// router.use(requestLogger())

router.post("/getTodayActionTypes", asyncWarp(async (serverId) => {
    let types = await newsMod.tryGetTodayActionTypes()
    let next = await newsMod.getNextFlushTime() - Date.now();
    let day = await newsMod.getToday();
    return {status: 0, types, next, day};
}))

router.post("/reportAction", asyncWarp(async ({uid, type, number, serverId}) => {
    return await newsMod.onReportAction(uid, type, number);
}))

router.post("/getTodayNews", asyncWarp(async (serverId) => {
    return await newsMod.getTodayNews();
}))

router.post("/debug_clear_redis_data", asyncWarp(async () => {
    if (!config.debug) return {status: -8}
    return await newsMod.debug_clear_redis_data();
}))
router.post("/debug_today_random", asyncWarp(async ({types}) => {
    if (!config.debug) return {status: -8}
    return await newsMod.debug_today_random(types);
}))
router.post("/debug_today_rank", asyncWarp(async ({types}) => {
    if (!config.debug) return {status: -8}
    await newsMod.doClear(types);
    return {status: 0};
}))


module.exports = router;