let router = require('express').Router();
const { requestLogger, decryptReq, asyncWarp } = require('../common/middleware');
const loginMod = require("../mod/loginMod");
const logger = require("../common/log").getLogger("test");
let verror = require("verror");
const recordMod = require('../mod/recordMod');

router.use(decryptReq)
router.use(requestLogger());

router.get('/wx', asyncWarp(async (params) => {
    let userInfo = await loginMod.wxLogin(params);
    return userInfo;
}))

router.get('/guest', asyncWarp(async (params) => {
    let userInfo = await loginMod.guestLogin(params);
    return userInfo;
}))

router.get("/wxRelogin", asyncWarp(async params => {
    return await loginMod.wxRelogin(params);
}))

router.post("/wxAppLogin", asyncWarp(async (params) => {
    return await loginMod.wxAppLogin(params);
}));

router.post("/appleLogin", asyncWarp(async (params) => {
    return await loginMod.appleLogin(params);
}));

router.post("/applogin", asyncWarp(async (params) => {
    return await loginMod.appLogin(params);
}));

router.post("/wxAppRelogin", asyncWarp(async (params) => {
    return await loginMod.wxAppRelogin(params);
}));

router.post("/appleRelogin", asyncWarp(async (params) => {
    return await loginMod.appleRelogin(params);
}));

router.get("/syncLoginInfo", asyncWarp(async (params) => {
    await loginMod.syncLoginInfo(params);
    return {status: 0}
}));

router.get("/checkSyncLoginInfo", asyncWarp(async (params) => {
    await loginMod.checkSync(params);
    return {status: 0}
}));

router.get("/web", asyncWarp(async (params) => {
    return await loginMod.webLogin(params);
}));

router.get("/wxTest", asyncWarp(async (params) => {
    return await loginMod.wxTestLogin(params);
}));

router.post("/checkLogin", asyncWarp(async({uid, checkInfo}) => {
    let {recordState} = await recordMod.checkLogin(uid, checkInfo)
    return {status: 0, recordState}
}))

module.exports = router;
