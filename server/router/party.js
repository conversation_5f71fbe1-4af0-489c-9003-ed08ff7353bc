const {decryptReq, asyncWarp, requestLogger} = require("../common/middleware");
let router = require('express').Router();
const partyMod = require('../mod/partyMod')
const partyPlusMod = require('../mod/partyPlusMod')

router.use(decryptReq)
router.use(requestLogger())
router.post("/getPartyInfo", asyncWarp(async ({uid, partyId}) => {
    let data
    if (partyId === -1) data = await partyMod.getLastParty(uid)
    else data = [await partyMod.getPartyState(uid, partyId)]
    return {status: 1, data}
}))
router.post("/startParty", asyncWarp(async ({uid, partyId}) => {
    return await partyMod.startParty(uid, partyId)
}))
router.post("/joinParty", asyncWarp(async ({uid, targetId}) => {
    return await partyMod.joinParty(uid, targetId)
}))
router.post("/finishMyParty", asyncWarp(async ({uid}) => {
    return await partyMod.finishMyParty(uid)
}))
router.post("/debug_set_party_ok", asyncWarp(async ({uid}) => {
    return await partyMod.debug_set_party_ok(uid)
}))
router.post("/debug_reset_cool_cd", asyncWarp(async ({uid, partyId}) => {
    return await partyMod.debug_reset_cool_cd(uid, partyId)
}))

router.post("/startPartyV2", asyncWarp(async ({uid, partyId, dressUp}) => {
    return await partyPlusMod.start(uid, partyId, dressUp)
}))
router.post("/joinPartyV2", asyncWarp(async ({uid, targetId, dressUp}) => {
    return await partyPlusMod.join(uid, targetId, dressUp)
}))
router.post("/getPartyInfoV2", asyncWarp(async ({uid}) => {
    return await partyPlusMod.getPartyInfo(uid)
}))

module.exports = router
