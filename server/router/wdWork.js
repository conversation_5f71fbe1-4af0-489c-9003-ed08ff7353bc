let router = require('express').Router();
const logger = require('../common/log').getLogger('wdWork');
const { asyncWarp, decryptReq } = require('../common/middleware');
const util = require('../common/util').default;
const wdWorkMod = require('../mod/wdWorkMod');
const cryptoHelper = require("../cypto/cryptoHelper");
const loginMod = require('../mod/loginMod');

router.use(decryptReq)

// 获取好友工作列表
router.get("/getFriendWorkList", asyncWarp(async ({ uid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/wdWork/getFriendWorkList]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/wdWork/getFriendWorkList] uid not found`)
        return { status: -1 }
    }

    let list = await wdWorkMod.getFriendWorkList(uid)
    //logger.info(`O - [${ip}] [GET/wdWork/getFriendWorkList]`, uid, list.length)
    return { status: 0, list }
}))

// 获取邀请工作列表
router.get("/getInviteWorkList", asyncWarp(async ({ uid, detail }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/wdWork/getInviteWorkList]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/wdWork/getInviteWorkList] uid not found`)
        return { status: -1 }
    }

    let list = await wdWorkMod.getInviteWorkList(uid)
    //logger.info(`O - [${ip}] [GET/wdWork/getInviteWorkList]`, uid, list.length)
    return { status: 0, list }
}))

// 邀请好友工作
router.post("/inviteWork", asyncWarp(async ({ uid, toUid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/wdWork/inviteWork]`, uid, toUid)
    } else {
        logger.warn(`I - [${ip}] [POST/wdWork/inviteWork] uid not found`)
        return { status: -1 }
    }

    let status = await wdWorkMod.inviteWork(uid, toUid)
    logger.info(`O - [${ip}] [POST/wdWork/inviteWork]`, uid, toUid, status)
    return { status }
}))

// 一键邀请工作
router.post("/inviteAllWork", asyncWarp(async ({ uid, list }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/wdWork/inviteAllWork]`, uid, list)
    } else {
        logger.warn(`I - [${ip}] [POST/wdWork/inviteAllWork] uid not found`)
        return { status: -1 }
    }

    let status = await wdWorkMod.inviteAllWork(uid, list)
    logger.info(`O - [${ip}] [POST/wdWork/inviteAllWork]`, uid, status)
    return { status }
}))

// 同意工作邀请
router.post("/agreeInviteWork", asyncWarp(async ({ uid, toUid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/wdWork/agreeInviteWork]`, uid, toUid)
    } else {
        logger.warn(`I - [${ip}] [POST/wdWork/agreeInviteWork] uid not found`)
        return { status: -1 }
    }

    let status = await wdWorkMod.agreeInviteWork(uid, toUid)
    logger.info(`O - [${ip}] [POST/wdWork/agreeInviteWork]`, uid, toUid, status)
    return { status }
}))

// 拒绝工作邀请
router.post("/rejectInviteWork", asyncWarp(async ({ uid, toUid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/wdWork/rejectInviteWork]`, uid, toUid)
    } else {
        logger.warn(`I - [${ip}] [POST/wdWork/rejectInviteWork] uid not found`)
        return { status: -1 }
    }

    let status = await wdWorkMod.rejectInviteWork(uid, toUid)
    logger.info(`O - [${ip}] [POST/wdWork/rejectInviteWork]`, uid, toUid, status)
    return { status }
}))

// 领取工作奖励
router.post("/receiveWorkReward", asyncWarp(async ({ uid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/wdWork/receiveWorkReward]`, uid)
    } else {
        logger.warn(`I - [${ip}] [POST/wdWork/receiveWorkReward] uid not found`)
        return { status: -1 }
    }

    let status = await wdWorkMod.receiveWorkReward(uid)
    logger.info(`O - [${ip}] [POST/wdWork/receiveWorkReward]`, uid, status)
    return { status }
}))

// 获取我的乌冬在谁家工作
router.get("/getWudongToWork", asyncWarp(async ({ uid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/wdWork/getWudongToWork]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/wdWork/getWudongToWork] uid not found`)
        return { status: -1 }
    }

    let { status, user } = await wdWorkMod.getWudongToWork(uid)
    //logger.info(`O - [${ip}] [GET/wdWork/getWudongToWork]`, uid, status, user)
    return { status, user: user }
}))

// 获取谁的乌冬在我家工作
router.get("/getWudongForWork", asyncWarp(async ({ uid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/wdWork/getWudongForWork]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/wdWork/getWudongForWork] uid not found`)
        return { status: -1 }
    }

    let { status, user } = await wdWorkMod.getWudongForWork(uid)
    //logger.info(`O - [${ip}] [GET/wdWork/getWudongForWork]`, uid, status, user)
    return { status, user }
}))

// 获取我的乌冬工作记录
router.get("/getWudongWrokRecord", asyncWarp(async ({ uid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/wdWork/getWudongWrokRecord]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/wdWork/getWudongWrokRecord] uid not found`)
        return { status: -1 }
    }

    let list = await wdWorkMod.getWudongWrokRecord(uid)
    //logger.info(`O - [${ip}] [GET/wdWork/getWudongWrokRecord]`, uid, list.length)
    return { status: 0, list }
}))

// 获取我的乌冬来访记录
router.get("/getWudongComeRecord", asyncWarp(async ({ uid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/wdWork/getWudongComeRecord]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/wdWork/getWudongComeRecord] uid not found`)
        return { status: -2, list: [] }
    }

    let list = await wdWorkMod.getWudongComeRecord(uid)
    //logger.info(`O - [${ip}] [GET/wdWork/getWudongComeRecord]`, uid, list.length)
    return { status: 0, list }
}))

// 同步乌冬工作状态
router.get("/synchroWudongWorkState", asyncWarp(async ({ uid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/wdWork/synchroWudongWorkState]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/wdWork/synchroWudongWorkState] uid not found`)
        return { status: -1 }
    }

    let isFix = await loginMod.isFixWudong(uid)
    if (isFix) {
        return { status: 0 }
    }

    let { status, toWork, forWork, subscribe } = await wdWorkMod.synchroWudongWorkState(uid)
    //logger.info(`O - [${ip}] [GET/wdWork/synchroWudongWorkState]`, uid, status, toWork, forWork, subscribe)
    return { status, toWork, forWork, subscribe }
}))

// 同步乌冬详细数据
router.get("/synchroWudongWorkDetail", asyncWarp(async ({ uid }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        //logger.info(`I - [${ip}] [GET/wdWork/synchroWudongWorkDetail]`, uid)
    } else {
        logger.warn(`I - [${ip}] [GET/wdWork/synchroWudongWorkDetail] uid not found`)
        return { status: -1 }
    }

    let { status, wudong } = await wdWorkMod.synchroWudongWorkDetail(uid)
    //logger.info(`O - [${ip}] [GET/wdWork/synchroWudongWorkDetail]`, uid, status, wudong)
    return { status, wudong }
}))

module.exports = router;