
let router = require('express').Router();
const { asyncWarp, requestLogger, decryptReq } = require('../common/middleware');
const loginMod = require('../mod/loginMod');
const shareMod = require('../mod/shareMod');
const userMod = require('../mod/userMod');
const wxMod = require('../mod/wxMod');
const cryptoHelper = require("../cypto/cryptoHelper")
const activityMod = require("../mod/activityMod");

router.use("/send", decryptReq);
router.use("/inviteInfo", decryptReq);
router.use("/onEnter", decryptReq);
router.use("/inviteUser", decryptReq);
router.use(requestLogger());

router.get("/onShare", async(req, res, next) =>{ //用于微信真分享判断
    try {
        let uid = req.query.uid;
        let imgUrl = req.query.imgUrl;
        if (!imgUrl) {
            res.end()
            return
        }
        await wxMod.setShareStatus(uid)
        res.writeHead(302, {Location: imgUrl});
        res.end();
    } catch (error) {
        next(error)
    }
})

router.get("/getStatus", asyncWarp(async({uid}) => {
    if (await wxMod.getShareStatus(uid)) {
        return {status: 0}
    }
    return {status: -1};
}))

router.get("/send", asyncWarp(async(info) => {
    shareMod.send(info)
    return {status: 0}
}))

router.get("/inviteCount", asyncWarp(async({uid, type}) => {
    let inviteUsers = await shareMod.getInviteInfo(uid, type, false)
    return {status: 0, inviteCount: inviteUsers.length}
}))

router.get("/inviteInfo", asyncWarp(async({uid, type}) => {
    let inviteUsers = await shareMod.getInviteInfo(uid, type)
    return {status: 0, inviteUsers}
}))

router.post("/onEnter", asyncWarp(async({uid, enterInfo}) => {
    let userDoc = await userMod.findOne(uid)
    loginMod.handleLaunchInfo(userDoc, enterInfo)
    return {status: 0}
}))

router.post("/inviteUser", asyncWarp(async({uid, inviteUid, type}) => {
    let data = await cryptoHelper.pkDecrypt(inviteUid)
    inviteUid = (data && data.decStr) || inviteUid
    if (!inviteUid || uid == inviteUid) {
        return { status: -1 }
    }
    let userDoc = await userMod.findOne(inviteUid)
    if (!userDoc) {
        return { status: -1 }
    }
    let isNew = await userMod.isNewUser(inviteUid, userDoc.signupTime)
    if (isNew) {
        let succ = await shareMod.receive(inviteUid, uid, {isNew: true})
        await activityMod.handleInviteTask(uid, true, false, 1)
        if (!succ) {
            return {status: -500}
        }
    }
    else {
        let info = await shareMod.getBeInvitedInfo(inviteUid)
        if (info) {
            return {status: -2}
        }
        return {status: -3}
    }
    let inviteUsers = await shareMod.getInviteInfo(uid, type)
    return {status: 0, inviteUsers}
}))

module.exports = router;
