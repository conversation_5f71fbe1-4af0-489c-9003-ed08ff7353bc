const { requestLogger, decryptReq, asyncWarp } = require("../common/middleware");
const activityMod = require("../mod/activityMod");
let router = require('express').Router();
const config = require("../config");
const logger = require("../common/log").getLogger("act");
let db = require("../db/db");

const redis = db.redis
const LOCK_KEY_PREFIX = "activity_lock_"
const LOCK_TIMEOUT = 3  // 秒
const MAX_RETRY = 3     // 最大重试次数

const acquireLock = async function (uid, retry = 0) {
    if (!uid) return false
    const result = await redis.set(LOCK_KEY_PREFIX + uid, '1', 'EX', LOCK_TIMEOUT, 'NX');
    if (result !== 'OK' && retry < MAX_RETRY) {
        await new Promise(resolve => setTimeout(resolve, 100)) // 等待100ms
        return acquireLock(uid, retry + 1)
    }
    return result === 'OK'
}

const releaseLock = async function (uid) {
    if (!uid) return
    await redis.del("activity_lock_" + uid)
}

router.use(decryptReq)
router.use(requestLogger({
    path: {
        "/activity/assets": true,
    },
}))

router.post("/assets", asyncWarp(async ({ uid }) => {
    // -1隐藏入口
    // if (!await activityMod.checkVer(uid)) {
    //     return {status: -1}
    // }
    const ada = await activityMod.getActivityData(uid, {
        coupon: 1,
        couponPoint: 1,
        up: 1,
        skin: 1,
        point: 1,
        reward: 1,
        choose: 1,
        videoGet: 1,
        taskLimitData: 1,
        limit: 1
    })
    const data = {
        coupon: 0,
        couponPoint: 0,
        up: 0,
        skin: [],
        point: 0,
        reward: [],
        choose: 0,
        videoGet: 0,
        taskLimitData: [],
        exchange: []
    }
    if (ada) {
        ada.coupon && (data.coupon = ada.coupon)
        ada.couponPoint && (data.couponPoint = ada.couponPoint)
        ada.videoGet && (data.videoGet = ada.videoGet)
        ada.up && (data.up = ada.up)
        ada.skin && (data.skin = ada.skin.map(s => {
            return { id: s.id, num: s.num }
        }))
        // fix 分离兑换和抽奖数据
        const reward = ada.reward || []
        if (ada.limit) {
            ada.limit.forEach(l => {
                if (l.id === -1) return
                // if (l.time < 1723021560000) return
                const idx = reward.indexOf(l.id)
                if (idx >= 0) {
                    reward.splice(idx, 1)
                }
                data.exchange.push(l.id)
            })
        }
        ada.point && (data.point = ada.point)
        data.reward = reward
        ada.choose && (data.choose = ada.choose)
        ada.taskLimitData && (data.taskLimitData = ada.taskLimitData.map(t => {
            return { group: t.group, count: t.count }
        }))
    }
    return { status: 0, data }
}))

router.post("/draw", asyncWarp(async ({ uid, type }) => {
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        return await activityMod.playerDraw(uid, type);
    } finally {
        await releaseLock(uid)
    }
}))

router.post("/getTasks", asyncWarp(async ({ uid }) => {
    if (!await activityMod.checkVer(uid)) {
        return { status: -2 }
    }
    return await activityMod.getTasks(uid)
}))

router.post("/refreshTasks", asyncWarp(async ({ uid, group }) => {
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        return await activityMod.refreshTask(uid, group)
    } finally {
        await releaseLock(uid)
    }
}))

router.post("/finishTask", asyncWarp(async ({ uid, id }) => {
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        return await activityMod.finishTask(uid, id)
    } finally {
        await releaseLock(uid)
    }
}))

router.post("/choose", asyncWarp(async ({ uid, id, replace }) => {
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        return await activityMod.choose(uid, id, replace)
    } finally {
        await releaseLock(uid)
    }
}))

router.post("/exchange", asyncWarp(async ({ uid, id }) => {
    if (!await activityMod.checkVer(uid)) {
        return { status: -1 }
    }
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        return await activityMod.exchange(uid, id)
    } finally {
        await releaseLock(uid)
    }
}))

router.post("/drawRecord", asyncWarp(async ({ uid }) => {
    const ada = await activityMod.getActivityData(uid, { record: 1 })
    let list = []
    if (ada && ada.record) {
        list = ada.record.map(r => {
            return { id: r.id, ch: r.ch, time: r.time }
        })
    }
    return { status: 0, list }
}))

router.post("/exchangeRecord", asyncWarp(async ({ uid }) => {
    const ada = await activityMod.getActivityData(uid, { limit: 1 })
    let list = []
    if (ada && ada.limit) {
        list = ada.limit.map(r => {
            return { id: r.id, time: r.time }
        })
    }
    return { status: 0, list }
}))

router.post("/combine", asyncWarp(async ({ uid, id }) => {
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        return await activityMod.combine(uid, id)
    } finally {
        await releaseLock(uid)
    }
}))

router.post("/advanceTask", asyncWarp(async ({ uid, cnt }) => {
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        return await activityMod.advanceTask(uid, cnt)
    } finally {
        await releaseLock(uid)
    }
}))

router.post("/v_debug_finish_task", asyncWarp(async ({ uid, id }) => {
    if (!config.debug) return
    return activityMod.finishTask(uid, id, true)
}))

router.post("/v_debug_add_point", asyncWarp(async ({ uid, num }) => {
    if (!config.debug) return
    const _ = await activityMod.updateActivityData(uid, { $inc: { point: num } })
    return { status: 0, point: _.point }
}))

// 定向选择
router.post("/upSelect", asyncWarp(async ({ uid, type }) => {
    try {
        await acquireLock(uid)
        return await activityMod.upSelect(uid, type)
    } finally {
        await releaseLock(uid)
    }
}))

// 珍宝积分兑换
router.post("/exchangeV2", asyncWarp(async ({ uid }) => {
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        const { coupon, couponPoint } = await activityMod.exchangeAllCouponPoint(uid)
        return { status: 0, coupon, couponPoint }
    } finally {
        await releaseLock(uid)
    }
}))

// 视频彩蛋获取奖励
router.post("/videoGet", asyncWarp(async ({ uid }) => {
    try {
        const locked = await acquireLock(uid)
        if (!locked) {
            return { status: -5544, message: "系统繁忙,请稍后再试" }
        }
        return await activityMod.videoGet(uid)
    } finally {
        await releaseLock(uid)
    }
}))

module.exports = router;
