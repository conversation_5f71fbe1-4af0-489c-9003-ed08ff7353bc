let router = require('express').Router();
const { requestLogger, decryptReq, asyncWarp, checkReplay } = require('../common/middleware');
const loginMod = require("../mod/loginMod");
const logger = require("../common/log").getLogger("login");
let verror = require("verror");
const recordMod = require('../mod/recordMod');
const util = require('../common/util').default;
const userMod = require('../mod/userMod');
const { Platform } = require('../common/constant');
const config = require("../config")

router.use(decryptReq)
router.use(requestLogger({
    path: {
        "/login/checkLogin": false,
    }
}));

router.use("/wx", checkReplay())
router.get('/wx', asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.wxLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo;
}))

router.post('/wx', asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.wxLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo;
}))

router.use("/guest", checkReplay())
router.get('/guest', asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.guestLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo;
}))

router.post('/guest', asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.guestLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo;
}))

router.use("/qq", checkReplay())
router.get('/qq', asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.qqLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo;
}))

router.post('/qq', asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.qqLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo;
}))

router.get("/wxRelogin", asyncWarp(async params => {
    return await loginMod.wxRelogin(params);
}))

router.post("/wxRelogin", asyncWarp(async params => {
    return await loginMod.wxRelogin(params);
}))

router.use("/wxAppLogin", checkReplay())
router.post("/wxAppLogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.wxAppLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo
}));

router.use("/appleLogin", checkReplay())
router.post("/appleLogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.appleLogin(params)
    loginMod.updateIp(userInfo, req, params)
    return userInfo;
}));

router.use("/applogin", checkReplay())
router.post("/applogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.appLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo
}));

router.post("/wxAppRelogin", asyncWarp(async (params) => {
    return await loginMod.wxAppRelogin(params);
}));

router.post("/appleRelogin", asyncWarp(async (params) => {
    return await loginMod.appleRelogin(params);
}));

router.get('/qqRelogin', asyncWarp(async (params) => {
    let userInfo = await loginMod.qqRelogin(params);
    return userInfo;
}))

router.post('/qqRelogin', asyncWarp(async (params) => {
    let userInfo = await loginMod.qqRelogin(params);
    return userInfo;
}))

router.get("/syncLoginInfo", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    await loginMod.syncLoginInfo(params);
    loginMod.updateIp(params, req, params)
    return { status: 0 }
}));

router.get("/checkSyncLoginInfo", asyncWarp(async (params) => {
    await loginMod.checkSync(params);
    return { status: 0 }
}));

router.use("/web", checkReplay())
router.get("/web", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.webLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo
}));

router.post("/web", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.webLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo
}));

router.get("/wxTest", asyncWarp(async (params) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.wxTestLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo
}));

router.post("/wxTest", asyncWarp(async (params) => {
    params.ip = util.getClientIp(req)
    let userInfo = await loginMod.wxTestLogin(params);
    loginMod.updateIp(userInfo, req, params)
    return userInfo
}));

router.post("/checkLogin", asyncWarp(async ({ uid, checkInfo, gameVer, deviceId }, req) => {
    let ip = util.getClientIp(req);
    logger.info(`I - [${ip}] [/login/checkLogin]`, uid, checkInfo.heart, checkInfo.playTime, gameVer, deviceId)
    let { recordState, timeLeft } = await recordMod.checkLogin(uid, checkInfo, gameVer)
    logger.info(`O - [${ip}] [/login/checkLogin]`, uid, recordState)
    return { status: 0, recordState, timeLeft }
}))

router.post("/genToken", asyncWarp(async ({ uid, type, gameVer, deviceId, code }) => {
    let res = await loginMod.genToken(uid, type, code)
    return res
}))

router.use("/facebookLogin", checkReplay())
router.post("/facebookLogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    return await loginMod.facebookLogin(params);
}));

router.post("/facebookRelogin", asyncWarp(async (params) => {
    return await loginMod.facebookRelogin(params);
}));

router.use("/twitterLogin", checkReplay())
router.post("/twitterLogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    return await loginMod.twitterLogin(params);
}));

router.post("/twitterRelogin", asyncWarp(async (params) => {
    return await loginMod.twitterRelogin(params);
}));

router.use("/googleLogin", checkReplay())
router.post("/googleLogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    return await loginMod.googleLogin(params);
}));

router.post("/googleReLogin", asyncWarp(async (params) => {
    return await loginMod.googleRelogin(params);
}));

router.use("/fbInstantLogin", checkReplay())
router.get("/fbInstantLogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    return await loginMod.fbInstantLogin(params);
}));

router.post("/fbInstantReLogin", asyncWarp(async (params) => {
    return await loginMod.fbInstantReLogin(params);
}));

router.use("/hmsLogin", checkReplay())
router.post("/hmsLogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    return await loginMod.hmsLogin(params);
}));

router.post("/hmsReLogin", asyncWarp(async (params) => {
    return await loginMod.hmsReLogin(params);
}));

router.post("/reconnect", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    return await loginMod.reconnect(params);
}));

router.post("/isDeviceDiff", asyncWarp(async ({ uid, deviceId }, req) => {
    let diff = await userMod.isDeviceDiff(uid, deviceId)
    return { status: 0, diff }
}));

router.post("/switchDevice", asyncWarp(async ({ uid, deviceId }, req) => {
    await recordMod.switchDevice(uid, deviceId)
    return { status: 0 }
}));

router.get("/getSwitches", asyncWarp(async ({ gameVer, platform }) => {
    let switches = await loginMod.getSwitches(gameVer, platform)
    return { status: 0, switches }
}))

router.get("/getCompensationRewards", asyncWarp(async ({ uid, timestamp, gameVer }) => {
    return await userMod.getCompensationRewards(uid, timestamp, gameVer)
}))

router.use("/xiaomiLogin", checkReplay())
router.post("/xiaomiLogin", asyncWarp(async (params, req) => {
    params.ip = util.getClientIp(req)
    return await loginMod.xiaomiLogin(params);
}));

router.post("/xiaomiRelogin", asyncWarp(async (params) => {
    return await loginMod.xiaomiRelogin(params);
}));

module.exports = router;
