let router = require('express').Router();
const logger = require('../common/log').getLogger('currency');
const { asyncWarp, decryptReq, requestLogger } = require('../common/middleware');
const util = require('../common/util').default;
const currencyModel = require('../mod/currencyModel');

router.use(decryptReq)
router.use(requestLogger())

router.post("/checkOrder", asyncWarp(async ({ uid }) => {
    
}))

module.exports = router;