let router = require('express').Router();
const logger = require("../common/log").getLogger("store")
const storeMod = require("../mod/storeMod");
const {requestLogger, asyncWarp} = require("../common/middleware");
router.use(requestLogger())

// 苹果商店退款
router.post("/apple_refund", asyncWarp(async (data) => {
    if (!data || !Object.keys(data).length) throw new Error(`debug_apple_refund data数据为空:${data}`);
    let bid = data.bid,
        bvrs = data.bvrs,
        environment = data.environment,
        password = data.password,
        notification_type = data.notification_type,
        unified_receipt = data.unified_receipt;
    logger.info("苹果退款:", notification_type);
    if (notification_type !== 'REFUND') return 200;
    // 状态码 0 表示通知有效
    if (unified_receipt.status !== 0) throw new Error(`debug_apple_refund 通知状态码不正确:${unified_receipt.status}`);
    let latest_receipt_info = unified_receipt.latest_receipt_info;
    for (let info of latest_receipt_info) {
        await storeMod.deal_apple_refund_order_info(
            info.cancellation_date_ms
            , info.cancellation_reason
            , info.original_purchase_date_ms
            , info.original_transaction_id
            , info.product_id);
    }
    return 200;
}))

module.exports = router;