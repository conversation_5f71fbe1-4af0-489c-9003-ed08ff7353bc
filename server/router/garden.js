const {decryptReq, asyncWarp, requestLogger} = require("../common/middleware");
let router = require('express').Router();
const gardenMod = require('../mod/gardenMod')

router.use(decryptReq)
router.use(requestLogger())

router.post("/onPlayerAction", asyncWarp(async ({target, action}) => {
    let status = await gardenMod.OnPlayerAction(target, action)
    return {status}
}))

router.post("/getPlayerAction", asyncWarp(async ({uid, lastRequestTime}) => {
    let data = await gardenMod.GetPlayerAction(uid, lastRequestTime)
    return {status: 1, data}
}))

router.post("/startParty", asyncWarp(async ({uid, partyId, step}) => {
    return await gardenMod.startParty(uid, partyId, step)
}))
router.post("/joinParty", asyncWarp(async ({uid, targetId}) => {
    const status = await gardenMod.joinParty(uid, targetId)
    return {status}
}))
router.post("/finishMyParty", asyncWarp(async ({uid}) => {
    return await gardenMod.finishMyParty(uid)
}))

module.exports = router
