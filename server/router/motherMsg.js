
let router = require('express').Router();
const logger = require('../common/log').getLogger('motherMsg');
const util = require('../common/util').default;
const motherMsgMod = require('../mod/motherMsgMod');
const { asyncWarp, requestLogger, decryptReq} = require('../common/middleware');
const { monitorMod } = require("../mod/monitorMod")


router.use(decryptReq)
router.use(requestLogger())

router.post("/submit", asyncWarp(async({uid, content}) => {
    await motherMsgMod.submit(uid, content)
    return {status: 0}
}))

router.post("/showMaker", asyncWarp(async({uid, points}) => {
    // await monitorMod.add(uid, points)
    return {status: 0}
}))

module.exports = router;
