let router = require('express').Router();
const {asyncWarp, requestLogger, decryptReq} = require('../common/middleware');
const roomMod = require('../mod/roomMod');
const {default: util} = require("../common/util");

router.use(decryptReq)
router.use(requestLogger());

router.post("/like", asyncWarp(async ({uid, toUid, roomId}) => {
    if (!uid || !toUid || roomId == null) {
        return {status: -1}
    }
    await roomMod.like(uid, toUid, roomId)
    return {status: 0}
}))

router.get("/likeInfo", asyncWarp(async ({uid, roomId}) => {
    let data = await roomMod.getLikeInfo(uid, roomId)
    return {status: 0, data}
}))

router.post("/savePlan", asyncWarp(async ({uid, content, md5, oldMD5}) => {
    await roomMod.savePlan(uid, content, md5, oldMD5)
    return {status: 0}
}))

router.post("/contributeSaveInfo", asyncWarp(async ({uid, roomId, saveInfo, heartLimit, imageBase64}) => {
    let code = await roomMod.saveBuildGuideInfo(uid, roomId, saveInfo, heartLimit, imageBase64);
    return {status: code}
}))

router.get("/contributeList", asyncWarp(async ({uid, roomId, heart, flushCount}) => {
    let data = await roomMod.reqGuideList(uid, roomId, heart, flushCount) || [];
    code = util.Code.SUCCESS;
    data = await roomMod.getRoomInfo(roomId, data);
    return {status: code, data}
}))
// 获取不同类型的info集合
router.get("/getSaveInfos", asyncWarp(async ({roomId, state, page, size}) => {
    let total = await roomMod.getTotalGuideInfoByStateAndRoomId(roomId, state);
    let saveInfos = [];
    if (total > 0) {
        saveInfos = await roomMod.getGuideInfoByStateAndRoomId(roomId, state, page, size);
    }
    let code = 0;
    return {status: code, saveInfos, total: total}
}))

router.get("/getSaveInfoDetail", asyncWarp(async ({saveId}) => {
    let saveInfo = await roomMod.getSaveInfo(saveId);
    let code = saveInfo ? util.Code.SUCCESS : util.Code.ERROR;
    return {status: code, saveInfo}
}))

router.post("/changeSaveInfo", asyncWarp(async ({saveId, state, imageBase64}) => {
    let code = await roomMod.changeSaveInfo(saveId, state, imageBase64);
    return {status: code}
}))

router.post("/buySaveInfo", asyncWarp(async ({uid, saveId}) => {
    let code = await roomMod.buySaveInfo(uid, saveId);
    return {status: code}
}))

// 2.0 点赞
router.post("/roomFav", asyncWarp(async ({self, planId}) => {
    let code = await roomMod.fav(self, planId, 1);
    return {status: code}
}))
router.post("/roomFavInfo", asyncWarp(async ({planId}) => {
    let data = await roomMod.getFavInfo(planId);
    let code = util.Code.ERROR;
    if (data) {
        code = util.Code.SUCCESS;
    }
    return {status: code, data}
}))

// 精选方案3.0 相关接口
/**
 * 晚报的随机，考虑到可能某个房间玩家可能随不出任何方案，
 *      所以客户端上传玩家解锁的房间列表和要随几套方案，服务器来处理,尽量保证不是同一个房间或者不同方案
 * 一个是房间内的随机
 * @param type 随机类型
 * @param rooms [201,202,301]
 * @param num 随几个方案返回
 * @param heart 玩家人气
 *  该接口只返回了方案的唯一id
 */
router.post("/getBuildPlans", asyncWarp(async ({type, rooms, num, heart}) => {
    return await roomMod.tryGetBuildPlanData(type, rooms, num, heart);
}))

router.post("/getBuildPlanInfo", asyncWarp(async ({ids}) => {
    return await roomMod.getBuildPlanInfo(ids);
}))

// 玩家购买装修方案 / 点赞
router.post("/buyBuildPlan", asyncWarp(async ({uid, planId}) => {
    return await roomMod.doLikeBuildPlan3_0(uid, planId);
}))

// 获取与自己有关的装修方案
router.post("/aboutSelf", asyncWarp(async ({uid}) => {
    return await roomMod.doGetAboutSelf(uid);
}))

// 玩家对方案推荐的回应 可能有多个房间 接受state = 2,否则 state =3.
router.post("/buildPlanChoice", asyncWarp(async ({uid, rooms, state}) => {
    for (let roomId of rooms) {
        await roomMod.changePlanState3_0(uid, roomId, state);
    }
    return {status: 0};
}))
/**
 * @param uid uid
 * @param roomId 房间id
 * @param furnitures 家具数量
 * @param heart 方案蜡烛
 * @param saveInfo 方案信息
 */
router.post("/buildPlanContribute", asyncWarp(async ({uid, roomId, furnitures, heart, saveInfo}) => {
    await roomMod.clientSavePlan(uid, roomId, furnitures, heart, saveInfo);
    return {status: 0};
}))

/**
 * 返回一个code
 *  -1 : 代表库里面没有该玩家的指定房间的方案,可以直接上传
 *  0 : 库里已经有存在的方案,方案的状态是默认状态(未审核)
 *  1 : 库里已经有存在的方案,方案的状态是精选(但是未确认)
 *  2 : 库里已经有存在的方案,方案的状态是精选(已经未确认，会展示给其他玩家)
 *  3 : 库里已经有存在的方案,方案的状态是废弃,可以直接上传覆盖
 */
router.post("/getRoomBuildPlanInfo", asyncWarp(async ({uid, roomId}) => {
    return await roomMod.getRoomBuildPlanInfo(uid, roomId);
}))

/**
 * @param uid 用户id
 * @param roomId 房间id,房号
 * @param furnNum 方案内家具数量
 * @param heart 该方案所需蜡烛
 * @param saveInfo 方案信息
 * @param code 二次确认的tk值
 */
router.post("/submitBuildPlanInfo", asyncWarp(async ({uid, roomId, furnNum, heart, saveInfo, tk}) => {
    return await roomMod.submitBuildPlanInfo(uid, roomId, furnNum, heart, saveInfo, tk);
}))

module.exports = router;