let router = require('express').Router();
const logger = require('../common/log').getLogger('recommend');
const { asyncWarp, requestLogger, decryptReq } = require('../common/middleware');
const util = require('../common/util').default;
const recommendMod = require('../mod/recommendMod');

router.use(decryptReq)

// 获取推荐列表
router.get("/getRecommendList", asyncWarp(async ({ uid, filter }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [GET/recommend/getRecommendList]`, uid, filter)
    } else {
        logger.warn(`getRecommendList uid not found`)
        return { status: -1 }
    }

    let list = await recommendMod.getRecommendList(uid, filter)
    logger.info(`O - [${ip}] [GET/recommend/getRecommendList]`, uid, 0)
    return { status: 0, list: list }
}))

// 添加用户行为记录
router.post("/addBehaviourRecord", asyncWarp(async ({ uid, roomId, type, count }, req) => {
    let ip = util.getClientIp(req)
    if (uid) {
        logger.info(`I - [${ip}] [POST/recommend/addBehaviourRecord]`, uid, roomId, type, count)
    } else {
        logger.warn(`addBehaviourRecord uid not found`)
        return { status: -1 }
    }

    recommendMod.addBehaviourRecord(uid, roomId, type, count)
    return { status: 0 }
}))

module.exports = router;