const { default: util } = require("./util");
const global = require("../common/global")
const logger = require("../common/log").getLogger("idCardVerify")
const crypto = require("crypto")
const got = require("got")

let idCardVerify = {
    GAME_CD: "3002",
    base16_key: "0b0ab8e6f04190958e90dfbaef180f7e", //16进制

    //省级地址码校验
    checkProv : function (val) {
        let pattern = /^[1-9][0-9]/;
        let provs = {11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江 ",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北 ",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏 ",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门"};
        if(pattern.test(val)) {
            if(provs[val]) {
                return 0;
            }
            else {
                // console.log("省级地址码校验错误");
                return -6;
            }
        }
        else {
            // console.log("省地址码格式错误");
            return -5;
        }
    },

   //出生日期码校验
   checkDate : function (val) {
        var pattern = /^(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)$/;
        if(pattern.test(val)) {
            var year = val.substring(0, 4);
            var month = val.substring(4, 6);
            var date = val.substring(6, 8);
            var adultAge = (parseInt(year) + 18).toString();
            var date2 = new Date(year + "-" + month + "-" + date);
            var dateAdult = new Date(adultAge + "-" + month + "-" + date);
            var dateNow = (new Date()).getTime();

            // 为1表示已成年 0表示未成年
            let isAdult = 0;
            if (dateNow >= dateAdult) {
                isAdult = 1;
            }
            else {
                isAdult = 0;
            }
            if(date2 && date2.getMonth() == (parseInt(month) - 1)) {
                return {status: 0, isAdult: isAdult};
            }
            else {
                // console.log("出生日期码校验错误");
                return {status: -4};
            }
        }
        else {
            // console.log("出生日期格式错误");
            return {status: -3};
        }
    }, 

    //校验码校验
    checkCode : function (val) {
        let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        let factor = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ];
        let parity = [ 1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2 ];
        let code = val.substring(17);
        if(p.test(val)) {
            let sum = 0;
            for(let i=0;i<17;i++) {
                sum += val[i]*factor[i];
            }
            if(parity[sum % 11] == code.toUpperCase()) {
                return 0;
            }
            else {
                // console.log("校验码校验错误");
                return -2;
            }
        }
        else {
            // console.log("身份证格式不对");
            return -1;
        }
    },

    //验证身份证号码
    checkID : function (val) {
        let res1 = this.checkCode(val);
        if(res1 == 0) {
            let date = val.substring(6,14);
            let res2 = this.checkDate(date);
            
            if(res2.status == 0) {
                let res3 = this.checkProv(val.substring(0,2));
                if(res3 == 0) {
                    return {status: 0, isAdult: res2.isAdult};
                }
                else {
                    return {status: res3};
                }
            }
            else {
                return {status: res2};
            }
        }
        else {
            return {status: res1};
        }
    },

    //验证中文姓名
    checkTrueName : function (val) {
        var nameReg = /^[\u4E00-\u9FA5.·]{0,}$/;
        if (val.length != 0) {
            if (nameReg.test(val)) {
                return true;
            }
            else {
                // console.log("姓名不符合中文姓名");
            }
        }
        else {
            // console.log("姓名为空")
        }
        return false;
    },

    getAge : function (id) {
        let birthday = Number(id.substring(6, 14))
        let now = Number(util.dateFormat("yyyyMMdd"))
        return Math.floor((now - birthday) / 10000)
    },

    //真认证
    async auth(uid, cardName, cardNum, retry = 5) {
        let plaintext = cardName + "@" + cardNum;
        let player_info = this.encodeAes(plaintext);
    
        let reqData = {
            game_cd: this.GAME_CD,
            id: uid,
            player_info: player_info,
        };
    
        // 检测玩家是否已经实名
        let url = "http://anti-obsession.dev-dh.com:18888/api/authentication_check"
        try {
            let ret = await got.post(url, {
                json: reqData,
                timeout: 10 * util.Time.Second,
            }).json()
            if (ret.err_code == "0" || ret.err_code == "-201000") {
                return true
            }
            else {
                if (retry > 0 && ret.err_code != "-201002") { //这个验证不通过
                    return this.auth(uid, cardName, cardNum, retry - 1)
                }
                else {
                    logger.warn("authIDCard fail", uid, cardName, cardNum, ret)
                    return false
                }
            }
        } catch (error) {
            if (retry > 0) {
                return this.auth(uid, cardName, cardNum, retry - 1)
            }
            else {
                logger.warn("authIDCard fail", uid, cardName, cardNum, error)
                return false
            }
        }
    },

    encodeAes(word) {
        if (!word) {
            return ''
        }
        if (typeof word != 'string') {
            word = JSON.stringify(word)
        }
        if (!this.bytesKey) {
            this.bytesKey = util.hexstring2btye(this.base16_key);
        }

        const iv = Buffer.from(util.getRandomString(12));
    
        const cipher = crypto.createCipheriv('aes-128-gcm', Buffer.from(this.bytesKey), iv);
        const encrypted = cipher.update(word, 'utf8');
        const finalstr = cipher.final();
        const tag = cipher.getAuthTag();
        const res = Buffer.concat([encrypted, finalstr, tag]);
        return Buffer.concat([iv, res]).toString("base64");
    },
}

module.exports = idCardVerify;