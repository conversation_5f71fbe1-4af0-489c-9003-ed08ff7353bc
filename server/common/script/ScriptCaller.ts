import {EventConst} from "./EventConst";
import AsyncObj from "./AsyncObj";

export default class ScriptCaller {
    private _callDict: any = {};

    constructor() {
    }

    public clear(): void {
        this._callDict = {};
    }

    public register(asyncObj: AsyncObj): any {
        if (!asyncObj.getFunc() || !asyncObj.getObj()) return;
        let _type = asyncObj.getType(), _cur = this._callDict[_type];
        !_cur && (_cur = this._callDict[_type] = []);
        for (let n = 0; n < _cur.length; n++) {
            let i = _cur[n];
            if (i.func == asyncObj.getFunc() && i.obj == asyncObj.getObj()) {
                asyncObj.setEventId(EventConst.EVENT_NONE);
            }
        }
        asyncObj.setEventId(_cur.push(asyncObj) - 1);
    }

    public unRegister(asyncObj: AsyncObj): void {
        let _type = asyncObj.getType(), _cur = this._callDict[_type];
        !_cur && (this._callDict.splice(asyncObj.getEventId(), 1));
    }

    // 同步执行，按理来说是根据push的顺序执行
    public async call(type: EventConst): Promise<void> {
        let t = this._callDict[type];
        if (t) {
            for (let n = 0; n < t.length; n++) {
                let f = t[n];
                await f.func(f.obj);
                f.once && this.unRegister(f);
            }
        }
    }


}