import {EventConst} from "./EventConst";

export default class AsyncObj {
    // 事件类型
    private readonly type: EventConst;
    // 事件触发后调用什么方法
    private readonly func: any;
    // 参数传递
    private readonly obj: any;
    // 事件id, 正常的事件都是大于-1的
    private eventId: number = -1;
    // 只运行被执行一次,运行一次后自动取消注册
    private once: boolean = false;

    constructor(type: EventConst, func: any, obj: any, once: boolean = false) {
        this.type = type;
        this.func = func;
        this.obj = obj;
        this.once = once;
    }

    public getType(): EventConst {
        return this.type;
    }

    public getFunc(): any {
        return this.func;
    }

    public getObj(): any {
        return this.obj;
    }

    public getEventId(): number {
        return this.eventId;
    }

    public setEventId(eventId: number) {
        this.eventId = this.eventId || eventId;
    }

    public isOnce(): boolean {
        return this.once;
    }
}

