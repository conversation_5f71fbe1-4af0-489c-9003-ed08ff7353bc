export default class ItemChangeObj {
    private product_num: number; // 道具变化数量
    private product_id: string; // 道具id
    private log_type: number; // 1增加 2 减少
    private extend: any; // 扩展信息
    private create_ts: number; // 19位时间戳
    private channel: number; // 道具产销途径
    private balance: number; // 操作后的道具余额
    public static ITEM_TYPE: {
        WINDMILL: number;
    } = {
        WINDMILL: 16
    };

    constructor(product_num: number, product_id: string
        , channel: number, balance: number, log_typ: number = 1, extend = {}) {
        this.product_num = product_num, this.product_id = product_id, this.channel = channel
            , this.balance = balance, this.log_type = log_typ, this.extend = extend
            , this.create_ts = Date.now() * 1000000;
    }

    public setExtend(extend: {}) {
        this.extend = extend;
    }

    public static getItemChangeObjByType(type: number = ItemChangeObj.ITEM_TYPE.WINDMILL) {

    }

    public static create_serial_no_by_user_info(userInfo: any = {}) {
        if (userInfo && userInfo['session_id']) return parseInt(userInfo['session_id']) + 1;
        return Date.now() * 1000000;
    }

}
