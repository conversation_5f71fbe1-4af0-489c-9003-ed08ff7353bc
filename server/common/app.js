let express = require('express');
let app = express();
let bodyParser = require('body-parser');
const config = require("../config");
const db = require('../db/db');
const logger = require("./log").getLogger("app");

app.__init = function () {
    app.use(express.static('wwwroot'));
    app.use(bodyParser.json({ limit: '10mb', strict: false }));
    app.use(bodyParser.urlencoded({ limit: '10mb', extended: false }));

    //设置跨域访问
    app.all('*', function (req, res, next) {
        res.header("Access-Control-Allow-Origin", "*");
        res.header("Access-Control-Allow-Headers", "Authorization,X-Requested-With,content-type");
        res.header("Access-Control-Allow-Methods", "PUT,POST,GET,DELETE,OPTIONS");
        res.header("X-Powered-By", ' 3.2.1')
        res.header("Content-Type", "application/json;charset=utf-8");
        next();
    });

    const server = require('http').createServer(app)
    server.listen(config.port, () => {
        logger.info(process.pid, 'Express server listening on port ' + server.address().port)
        if (process.send) {
            process.send("ready")
        }
    })

    process.on('SIGINT', () => {
        logger.info(process.pid, "SIGINT signal received, Exit...")
        server.close(function (err) {
            if (err) {
                logger.error("server close error", process.pid, err);
                process.exit(1)
            }
            setTimeout(()=>{
                db.close(()=>{
                    setTimeout(()=>{
                        process.exit(0)
                    }, 100)
                })
            }, 2000) //等一些异步操作
        })
    })

    process.on('message', (msg) => {
        if (msg == 'shutdown') {
            logger.info('Closing all connections...', process.pid)
            setTimeout(() => {
                logger.info('Finished closing connections', process.pid)
                process.exit(0)
            }, 5000)
        }
    })
}

module.exports = app;