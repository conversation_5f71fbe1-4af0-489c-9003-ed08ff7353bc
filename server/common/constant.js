const userType = {
    APP_WX: "app_wx",
    APPLE: "app_apple",
    GUEST: "app_guest",
    WX: null,
    QQ: "qq",
    FB: "app_fb",
    TWITTER: "app_twitter",
    GOOGLE: "app_google",
    HMS: "app_hms",
    FBINSTANT: "fb_instant",
    XIAOMI: "xiaomi",
}

const syncState = {
    SAME: 0,
    DEVICE_DIFF: 1,
    VERSION_DIFF: 2,
    SYNC: 3,
}

const ShareType = {
    UNKNOWN: 0,
    UNLOCK_CUSTOMER: 1,
    RANK: 2,
    DAILY_TASK: 3,
    AD: 4,
    //以上为常规分享，活动分享加个前缀区分，如1001
}

const FeedBackSenderType = {
    PLAYER: 0,
    SERVICE: 1,
}

const InviteType = {
    NORMAL: 0,
}

const SpAwardType = {
    APP_WX_LOGIN: 0, //app小程序登录奖励
}

const SwitchType = {
    APP_WX_LOGIN: 0, //小程序引导app下载弹窗开关
    SHARE_REWARD: 1, // 是否显示分享奖励
    APP_WX_LOGIN_BY_PLAYER: 2, //小程序引导app下载弹窗开关(主动)
    ATT: 3, //苹果审核屏蔽
    AD_SKIP_CARD: 4,
    MT_LINK: 5, // 美团外卖联动
}

const Platform = {
    WX: "wx",
    QQ: "qq",
    GOOGLE: "google",
    APPLE: "apple",
    ANDROID: "android",
    IOS: "ios",
    HMS: "hms", // 华为
    FBINSTANT: "fb_instant",
}

const CURRENCY = {// 通货
    WINDMILL: 'windmill',// 风车
    SCISSOR: 'scissor',// 剪刀
    IAP_THYK: "iap_thyk", //小月卡领取次数
    IAP_CZYK: "iap_czyk", //大月卡领取次数
}

// 通货行为
const CURRENCY_ACTION = {
    RECEIVE_DAILY_WINDMILL: 'receive_daily_windmill',// 领取每日风车
    RECEIVE_DAILY_THLB_GIFT: 'receive_daily_thlb_gift',//限时优惠

    EXCHANGE_SCISSOR: 'exchange_scissor',// 兑换剪刀
    EXCHANGE_BISCUITS_1: 'exchange_biscuits_1',// 兑换饼干礼包1
    EXCHANGE_BISCUITS_2: 'exchange_biscuits_2',// 兑换饼干礼包2
    EXCHANGE_BISCUITS_3: 'exchange_biscuits_3',// 兑换饼干礼包3
    EXCHANGE_CANDIES_1: 'exchange_candies_1',// 兑换糖果礼包1
    EXCHANGE_CANDIES_2: 'exchange_candies_2',// 兑换糖果礼包2
    EXCHANGE_CANDIES_3: 'exchange_candies_3',// 兑换糖果礼包3
    EXCHANGE_THLB_4: 'exchange_thlb_4', // 限时特惠风车礼包
    EXCHANGE_THLB_2001: 'exchange_thlb_2001', // 限时特惠糖果礼包
    WUDONG_AD: 'wudong_ad',// 乌冬广告
    TAITAN_AD: 'taitan_ad',// 泰坦广告
    PAOPAO_AD: 'paopao_ad',// 泡泡广告
    DOUBLE_AD: 'double_ad',// 双倍广告
    FUGUI_AD: 'fugui_ad',// 富贵广告
    YOYO_AD: 'yoyo_ad',// 摇摇乐广告
    GRANDMA_AD: 'grandma_ad',// 老奶奶广告
    OFFLINE_AD: 'offline_ad',// 离线收益
    TIP_AD: 'tip_ad',// 小费罐
    FRIST_AD: 'frist_ad',// 首看豪礼
    PRAISE_AD: 'praise_ad',// 口碑
    DOUBLE_CLAIM_AD: 'double_claim_ad',// 双倍领取奖励
    BISCUITS_AD: 'biscuits_ad',// 饼干不足奖励
    CANDIES_AD: 'candies_ad',// 糖果不足奖励
    HOLIDAY_SIGN_AD: 'holiday_sign_ad', // 节日活动补签奖励
    ELF_EGG_INCUBATE_AD: 'elf_egg_incubate_ad', // 快速孵化精灵蛋广告
    STAFF_LOTTERY_AD: 'staff_lottery_ad',// 员工探索广告
    FILM_SIGNED_AD: 'film_signed_ad', // 影片签约
    FILM_SIGNED: 'film_signed',
    WA_SAI_AD: 'washai_update_item', //哇塞店铺刷新

    // 使用风车跳过广告
    FUGUI_AD_WINDMILL: 'fugui_ad_windmill',// 富贵广告 使用15风车
    DOUBLE_AD_WINDMILL: 'double_ad_windmill',// 双倍广告 使用15风车
    TAITAN_AD_WINDMILL: 'taitan_ad_windmill',// 泰坦广告 使用15风车
    PAOPAO_AD_WINDMILL: 'paopao_ad_windmill',// 泡泡广告 使用15风车
    WUDONG_STAMINA_WINDMILL: 'wudong_stamina_windmill',// 乌冬增加体力 使用10风车

    //领取月卡每日奖励，不在配置表中
    RECEIVE_DAILY_MC: "RECEIVE_DAILY_MC",

    //----------付费

    //礼包
    IAP_XSZL: 'iap_xszl', //新手助力
    IAP_CXYX: 'iap_cxyx', //畅享游戏
    IAP_JXTH: 'iap_jxth', //精选特惠
    IAP_GASZ: 'iap_gasz', //关爱手指
    IAP_LRTZ: "iap_lrtz", //懒人贴纸
    IAP_BDJJ: "iap_bdjj", //霸道讲价
    IAP_SMXY: "iap_smxy", //时髦新衣
    IAP_CJLB: "iap_cjlb", //春节礼包

    IAP_XSZL_V2: 'iap_xszl_v2', //新手助力
    IAP_CXYX_V2: 'iap_cxyx_v2', //畅享游戏
    IAP_JXTH_V2: 'iap_jxth_v2', //精选特惠

    //月卡
    IAP_THYK: "IAP_THYK", //小月卡
    IAP_CZYK: "IAP_CZYK", //大月卡

    // 限时特惠（付费）
    IAP_THLB_1: 'iap_thlb_1',
    IAP_THLB_2: 'iap_thlb_2',
    IAP_THLB_3: 'iap_thlb_3',
    IAP_THLB_1005: 'iap_thlb_1005',
    IAP_THLB_1006: 'iap_thlb_1006',
    // 限时特惠（风车兑换道具）
}

// 通货订单状态
const CURRENCY_ORDER_STATUS = {
    NON_PAYMENT: 0,// 未支付
    PAYMENT_SUCCESS: 1,// 支付成功 1
    PAYMENT_FAIL: 2,// 支付失败 2
    COMPLETED: 3,// 已完成 3
    CANCELED: 4,// 已取消 4
}

const SubscribeType = {
    GALI_EXPLORE: "GALI_EXPLORE",
    WUDONG_WORK: 'WUDONG_WORK',
    FILM_AWARD: "FILM_AWARD", //电影排片结束
    OFFLINE_AWARD: "OFFLINE_AWARD", //离线奖励满了
    LOST_12_HOUR: "LOST_12_HOUR", //流失召回
    BEAUTY_SS: 'BEAUTY_SS',
    LOST_28_HOUR: "LOST_28_HOUR",
    LOST_72_HOUR: "LOST_72_HOUR",
    LOST_168_HOUR: "LOST_168_HOUR",
    BI_INITIATIVE_PUSH: "BI_INITIATIVE_PUSH", //BI 主动推送  无需cd限制
}

const ShopItemType = {
    RECEIVE_DAILY_WINDMILL: 1, //免费风车
    EXCHANGE_SCISSOR: 2, //兑换剪刀
    EXCHANGE_BISCUITS: 3, //兑换饼干
    EXCHANGE_CANDIES: 4, //兑换糖果
    WINDMILL: 5, //风车
    PACKAGE: 6, //礼包
    MONTH_CARD: 7, //月卡
    SHOP_PAY: 11, // 每日随机购买
    SHOP_FREE: 12 // 每日免费领取
}

const FriendRewardType = {
    LUCKY_BAG: null, //在好友家找到的福袋， 为了兼容性这里设置为null
    NEWYEAR_LUCK_BAG: 1, //新年福袋
}

const ADD_IP_SCORE = {
    REGISTER_NOT_TOURISTS: 100, // 非游客注册
    REGISTER_TOURISTS: 50, // 游客注册
    BIND_ACCOUNT: 90, // 游客绑定账号
    LOGIN: 3, // 登录
    UPLOAD_RECORD: 5 // 上传存档
}

// 部分功能开关
const FUNCTION_OPEN = {
    IOS_AUDIT: "IOS_AUDIT",
}


module.exports = {
    USER_TYPE: userType,
    SYNC_STATE: syncState,
    SHARE_TYPE: ShareType,
    FEED_BACK_SENDER_TYPE: FeedBackSenderType,
    INVITE_TYPE: InviteType,
    SpAwardType,
    SwitchType,
    Platform,
    CURRENCY,
    CURRENCY_ACTION,
    CURRENCY_ORDER_STATUS,
    SubscribeType,
    ShopItemType,
    FriendRewardType,
    ADD_IP_SCORE,
    FUNCTION_OPEN
}
