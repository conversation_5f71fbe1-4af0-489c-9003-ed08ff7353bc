/**
 * 卓杭上报模块
 * https://dhbi.yuque.com/books
 */

const SALT = "fbiubi9gewaga=niu1n3091mlnoahgawng"

import config from "../config"
import got from "got"
import cryptoHelper from "../cypto/cryptoHelper"

const logger = require("../common/log").getLogger("DHLog");

class DHLog {

    private game_cd: string = '3006'

    public async report(event_cfg: { code, type, name }, device_info?, user_info?, event_info?: any) {
        if (!config.dhlog) return
        let log = await this.makeLog(event_cfg, device_info, user_info, event_info)
        await this.request(log)
    }

    private async makeLog({code, type, name}, device_info?, user_info?, event_info?: any) {
        let create_ts = Date.now() * 1000000
        let event_value: any = {}
        if (device_info) {
            event_value.device_info = device_info
        }
        if (user_info) {
            event_value.user_info = user_info
        }
        if (event_info) {
            event_value.event_info = event_info
        }
        return {
            event_code: code,
            event_type: type,
            event_name: name,
            game_cd: this.game_cd,
            create_ts: String(create_ts),
            event_value: event_value && JSON.stringify(event_value)
        }
    }

    private async request(log) {
        logger.info("dhlog----------", log.event_name, log)
        let url = config.dhlog.url

        let now = Date.now()
        let date = String(Math.floor(now / 1000))
        let auth = cryptoHelper.md5(SALT + date).toLowerCase()

        let headers = {
            'Content-Type': `application/x-www-form-urlencoded;charset=utf-8`,
            'Date': date,
            'Authorization': auth,
        }

        let form = {'data': JSON.stringify(log)}

        try {
            let data: any = await got.post(url, {
                form,
                headers,
                retry: 100,
            }).json();
            if (data.error_code != 0) {
                logger.error("req err", data)
            }
        } catch (error) {
            logger.error("req fail", error)
        }
    }

    public transLang(lang) {
        switch (lang) {
            case 'cn':
                return '1';
            case 'en':
                return "0";
            case 'jp':
                return "9";
            case 'kr':
                return "8";
            case 'tc':
                return "13";
        }
        return '1'
    }
}

export const dhlog = new DHLog()

export const DH_LOG_EVENT = {
    TEST: {
        code: "4100110001",
        type: "community",
        name: "c_register",
    },
    REGISTER: {
        code: "1100110001",
        type: "base",
        name: "register",
    },
    LOGIN: {
        code: "1100110002",
        type: "base",
        name: "login",
    },
    LOGOUT: {
        code: "1100110003",
        type: "base",
        name: "logout",
    },
    PAY: {
        code: "1100110004",
        type: "base",
        name: "pay",
    },
    LEVEL_UP: {
        code: "1100110005",
        type: "base",
        name: "level_up",
    },
    VIP_UP: {
        code: "1100110006",
        type: "base",
        name: "vip_up",
    },
    CHANGE_NAME: {
        code: "1100110007",
        type: "base",
        name: "change_name",
    },
    ONLINE: {
        code: "1100510002",
        type: "server",
        name: "online",
    },
    ITEM_WINDMILL: {
        code: "1100210001",
        type: "product",
        name: "product",
    },

}
