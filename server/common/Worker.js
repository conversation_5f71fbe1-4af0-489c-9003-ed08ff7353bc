const db = require("../db/db");
const {default: util} = require("../common/util");
const userMod = require("../mod/userMod");
const recordMod = require("../mod/recordMod");
const {EventConst} = require("../common/script/EventConst");
const logger = require("./log").getLogger("Worker");

class MI {
    constructor(limit = 10000, maxHeart = 22000) {
        this.missions = [];
        this.lastId = '';
        this.limit = limit;
        this.maxHeart = maxHeart;
        this.timestampStart = Date.now();
        this.timestampEnd = 0;
    }

    async addMission(mission) {
        let err = '';
        !err && !mission && (err = 'mission is empty');
        !err && mission && !mission.type && (err = 'mission\'s type is empty');
        !err && mission && !mission.key && (err = 'mission\'s key is empty');
        // !err && mission && !mission.lastId && (err = 'mission\'s lastId is empty');
        if (!err && this.missions.length) {
            this.missions.forEach(m => {
                let count = 0;
                this.missions.filter(_m => {
                    return m.type === _m.type && (count++);
                })
                count >= 2 && (err = 'duplicate type with ' + m.type);
            })
        }
        if (err) {
            logger.error(err);
            return;
        }
        this.missions.push(mission);
        mission.parent = this;
    }

    /**
     * 统一调用 mission的某个指定的方法,该方法可以接收一个参数传递.
     * @param func 方法名称
     * @param param 参数
     * @returns {Promise<void>}
     * @private
     */
    async _call_target_func(func, param) {
        await util.promiseMap(this.missions, async (m) => {
            func === 'saveDb' && (m.saveDbUseTime = Date.now());
            m[func] && await m[func](param);
            func === 'saveDb' && (m.saveDbUseTime = Date.now() - m.saveDbUseTime);
        })
    }

    /**
     * 尝试调用mission对象的处理数据方法，该方法传递需要的特定参数
     * @param heart
     * @param uid
     * @param userDoc
     * @param recordDoc
     * @returns {Promise<void>}
     * @private
     */
    async _call_deal_func(heart, uid, userDoc, recordDoc) {
        // deal 方法处理完后需要返回true or false代表数据是否有效,来决定totalCount是否增加
        await util.promiseMap(this.missions, async (m) => {
            !m.dealUseTime && (m.dealUseTime = Date.now());
            let result;
            try {
                result = await m.deal(heart, uid, userDoc, recordDoc);
            } catch (error) {
                logger.error('deal with error: ', error);
            }
            result && m.increaseTotalCount();
            m.dealUseTime && (m.dealUseTime = Date.now() - m.dealUseTime);
        });
    }
}

class Mission {
    constructor(type, key) {
        this.type = type;
        this.key = key;
        this.map = {};
        this.totalCount = 0;
        // deal耗时
        this.dealUseTime = 0;
        // saveDb耗时
        this.saveDbUseTime = 0;
        this._init = false;
        !this._init && this.init();
    }

    init() {
        this.taskProgressCol = db.createTaskProgressModel()
        this._init = true;
    }

    async beforeRun() {
        let doc = await this.taskProgressCol.findOne({type: this.type, key: this.key});
        if (doc && doc.result) {
            let result = JSON.parse(doc.result)
            this.map = result.allMap
            this.lastId = result.lastId;
        }
        if (!this.lastId) {
            await this.taskProgressCol.updateOne({type: this.type, key: this.key}, {
                done: 0,
                total: 1000000000,
                expireTime: util.Time.Day,
            }, {upsert: true})
        }
    }

    async deal(heart, uid, userDoc, recordDoc) {
        logger.error("No deal logic for data!!!");
        console.error("No deal logic for data!!!");
        return false;
    }

    async saveDb(docs) {
        await this.taskProgressCol.updateOne({type: this.type, key: this.key}, {
            $inc: {done: docs.length},
            result: JSON.stringify({allMap: this.map, lastId: this.parent.lastId})
        }, {upsert: true});
    }

    async afterEnd(timestampStart) {
        // console.log(`End : ${this.type},${this.key}, totalCount: ${this.totalCount}, total time:${Math.floor((Date.now() - this.timestampStart) / 1000)}s`);
        logger.info(`End : type-${this.type},key-${this.key}, totalCount: ${this.totalCount}, total time:${Math.floor((Date.now() - timestampStart) / 1000)}s`);
    }

    /**
     * totalCount 自增
     * @param num
     */
    increaseTotalCount(num = 1) {
        this.totalCount += num;
    }

    async printf() {
        logger.info(`Run : type-${this.type},key-${this.key}, count: ${this.totalCount}, deal time : ${Date.now() - this.dealUseTime}ms, save time : ${this.saveDbUseTime}ms`);
        this.dealUseTime = 0;
        this.saveDbUseTime = 0;
    }
}

let worker = {
    mission: Mission,
    MI: MI,
    init: () => {
        this.userCol = db.createUserModel();
        this.spAwardCol = db.createSpecialAwardModel()
        this.userTokenCol = db.createUserTokenModel()
        this.invitedCol = db.createInvitedModel()
        this.taskProgressCol = db.createTaskProgressModel()
        this.userHeartCol = db.createStatUserHeartModel()
    },
    tryGetLastIdAndMapData: async (extend) => {

    },
    demoPrintfRunDataInfos: async (extend) => {
        logger.info(`run over : ${extend.type},${extend.key}`);
        logger.info(`统计结束,共计:${extend.totalCount}条有效数据,耗时:${extend.timestampEnd - extend.timestampStart} ms.`)
    },
    /**
     * 一个通用的统计worker
     * @param MI type of MI
     * @param firstRun 默认即可 不要修改
     * @desc 自写方法 beforeRun,deal,saveDb,deal方法必须重写
     * @returns {Promise<*|undefined>}
     */
    work_default: async (MI, firstRun = true) => {
        !firstRun && await MI._call_target_func('printf');
        // 做初次运行之前的事情
        if (firstRun) {
            try {
                await MI._call_target_func('beforeRun');
            } catch (error) {
                logger.error('beforeRun with error: ', error);
            }
        }
        let where = {};
        if (MI.lastId) {
            where._id = {$gt: MI.lastId}
        }
        let docs = await this.userHeartCol.find(where, {uid: 1, heart: 1}).sort({_id: 1}).limit(MI.limit);
        await util.promiseMap(docs, async ({uid, heart}) => {
            if (!uid) return;
            if (MI.maxHeart && heart > MI.maxHeart) return;
            let [userDoc, recordDoc] = await Promise.all([userMod.findOne(uid), recordMod.getRecord(uid)]);
            if (!userDoc || !recordDoc) return;
            heart = util.retainHightDigit(heart, 2);
            await MI._call_deal_func(heart, uid, userDoc, recordDoc);
        });
        if (docs.length > 0) {
            MI.lastId = docs[docs.length - 1]._id;
            try {
                await MI._call_target_func('saveDb', docs);
            } catch (error) {
                logger.error('saveDb with error: ', error);
            }
            return worker.work_default(MI, false);
        }
        // 递归结束
        MI.timestampEnd = MI.timestampEnd || Date.now();
        // 做运行结束之后的事情
        try {
            await MI._call_target_func('afterEnd', MI.timestampStart);
        } catch (error) {
            logger.error('afterEnd with error: ', error);
        }
    }
}
module.exports = worker;