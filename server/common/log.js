const { createLogger, transports, format } = require('winston');
const { combine, label, printf, colorize } = format;
const moment = require("moment");
require('winston-daily-rotate-file')
let config = require("../config")
let debug = config.debug;

const myFormat = printf(info => {
    if (info.level === undefined) {
        console.warn("log level not found", info.label, info.message)
        info.level = "warn"
    }
    return colorize().colorize(info.level, `${moment().format('YYYY-MM-DD HH:mm:ss.SSS')} [${info.level}] [${info.label}] - ${info.message}`)
});

//消息封装，使得可以接受多个参
const logLikeFormat = {
    transform(info) {
        const args = info[Symbol.for('splat')] || [];
        args.unshift(info.message);
        const strArgs = args.map(arg => {
            if (arg === undefined) return "undefined";
            else if (arg === "undefined") return '"undefined"'
            else if (arg instanceof Error) {
                return arg.message + arg.stack;
            }
            try {
                return JSON.stringify(arg);
            } catch (error) {
                return 'json_error'
            }
        }).join(" ")
        info.message = strArgs;
        return info;
    }
};

module.exports = {
    getLogger(name = "default") {
        let log = config.log
        if (log && log.http) {
            let transportAry = [new transports.Http({
                host: log.host,
                port: log.port,
                level: 'info',
            })]
            return createLogger({
                format: combine(
                    label({ label: name }),
                    logLikeFormat,
                ),
                transports: transportAry,
            });
        }

        let transportAry = [
            new transports.DailyRotateFile({
                filename: './log/info.log.%DATE%',    // DATE必须大写
                level: 'info',
                datePattern: 'YYYY-MM-DD',    // DATE
                maxFiles: "30d",
                createSymlink: true,
                symlinkName: "info.log",
                maxSize: "500m",
            }),
            new transports.DailyRotateFile({
                filename: './log/error.log.%DATE%',    // DATE必须大写
                level: 'error',
                datePattern: 'YYYY-MM-DD',    // DATE
                maxFiles: "30d",
                createSymlink: true,
                symlinkName: "error.log",
                maxSize: "500m",
            })
        ]
        if (debug) {
            transportAry.push(new transports.Console({
                level: 'debug',
            }))
        }

        // if (log && log.http) {
        //     transportAry.push(new transports.Http({
        //         host: log.host,
        //         port: log.port,
        //         level: 'info',
        //     }))
        // }

        return createLogger({
            format: combine(
                label({ label: name }),
                logLikeFormat,
                myFormat,
            ),
            transports: transportAry,
        });
    },

    getLoggerByHttp() {
        let transportAry = [
            new transports.DailyRotateFile({
                filename: './log/info.log.%DATE%',    // DATE必须大写
                level: 'info',
                datePattern: 'YYYY-MM-DD',    // DATE
                maxFiles: "30d",
                createSymlink: true,
                symlinkName: "info.log",
                maxSize: "500m",
            }),
            new transports.DailyRotateFile({
                filename: './log/error.log.%DATE%',    // DATE必须大写
                level: 'error',
                datePattern: 'YYYY-MM-DD',    // DATE
                maxFiles: "30d",
                createSymlink: true,
                symlinkName: "error.log",
                maxSize: "500m",
            })
        ]

        if (debug) {
            transportAry.push(new transports.Console({
                level: 'debug',
            }))
        }

        return createLogger({
            format: combine(
                myFormat,
            ),
            transports: transportAry,
        });
    },

    getLocal(name = "default") {
        let transportAry = [
            new transports.DailyRotateFile({
                filename: './log/info.log.%DATE%',    // DATE必须大写
                level: 'info',
                datePattern: 'YYYY-MM-DD',    // DATE
                maxFiles: "30d",
                createSymlink: true,
                symlinkName: "info.log",
                maxSize: "500m",
            }),
            new transports.DailyRotateFile({
                filename: './log/error.log.%DATE%',    // DATE必须大写
                level: 'error',
                datePattern: 'YYYY-MM-DD',    // DATE
                maxFiles: "30d",
                createSymlink: true,
                symlinkName: "error.log",
                maxSize: "500m",
            })
        ]
        if (debug) {
            transportAry.push(new transports.Console({
                level: 'debug',
            }))
        }

        return createLogger({
            format: combine(
                label({ label: name }),
                logLikeFormat,
                myFormat,
            ),
            transports: transportAry,
        });
    },

};