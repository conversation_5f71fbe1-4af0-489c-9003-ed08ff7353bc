let logger = require("../common/log").getLogger();
let onHeaders = require('on-headers');
const VError = require("verror");
const cryptoHelper = require("../cypto/cryptoHelper");
const util = require("./util").default;
const performanceMod = require("../mod/performanceMod")
const userMod = require("../mod/userMod")
const verifyMod = require("../mod/verifyMod")

/**
 * @param {obj} data
 * @param {number} watermark 水印，占用__watermark字段
 * @description  用私钥对一个对象加签
 */
async function encryptRes(data, reqId, path, __enCryIdx) {
    if (reqId) {
        data.__watermark = reqId; //防止数据结构过于简单
    }
    let rawData = JSON.stringify(data);
    let sign = await cryptoHelper.pkSign(rawData, __enCryIdx, true);
    // logger.debug(path, sign, rawData)
    return {rawData, sign};
}

const crpytoConifg = {
    "/": false,
    "/getNowTime": false,
    "/ping": false,
    "/version/check": false,
    "/share/getStatus": false,
    "/share/onShare": false,
    "/share/send": {
        res: false,
    },
    '/errorReport': false,
    "/verfiy/TXRewardAdCallBack": false,
    "/TXRewardAdCallBack": false,

    '/feedback/getMsgList': {
        res: false,
    },
    '/feedback/sendMsg': {
        res: false,
    },
    '/feedback/delMsgList': {
        res: false,
    },
    "/decryptWxData": false,

    '/compute/verfityRecord': false,
    '/compute/deblockUser': false,
    '/report/umeng': false,
}

let isInnerPath = (path) => {
    return path.startsWith("/compute")
}

let isHeartbeatPath = (path) => {
    return !isInnerPath(path) && path != "/ping" && path != "/login/reconnect"
}

module.exports = {
    asyncWarp(func) {
        return async (req, res, next) => {
            try {
                let now = Date.now()
                let params = req.__data || (req.method == "GET" ? req.query : req.body);
                let {uid, token, __sessionId, __reqId} = params;
                let path = util.getReqPath(req);
                let innerPath = isInnerPath(path)
                let heartbeatPath = isHeartbeatPath(path)
                if (heartbeatPath) {
                    userMod.heartbeat(token, __sessionId, path)
                }
                let data = await func(params, req);
                if ((token || __sessionId) && heartbeatPath) {
                    data.nowTime = Date.now()
                }
                let info = crpytoConifg[path];
                if (req.__log) {
                    res.__rspData = JSON.stringify(data);
                }
                if (!(info === false || (info && info.res === false)) && !innerPath) {
                    data = await encryptRes(data, params && params.__reqId, path, req.__enCryIdx);
                }
                let dataStr = JSON.stringify(data)
                if (req.__cacheRsp && __reqId) {
                    userMod.cacheRsp(__reqId, dataStr)
                }
                res.send(dataStr);

                let requestPacketSize = 0, responsePacketSize = dataStr.length;
                // request data size - post
                if (req.method === 'POST' && req.body && (requestPacketSize = JSON.stringify(req.body).length)) {
                }
                if (req.method === 'GET') {
                    requestPacketSize = req.__data ? JSON.stringify(req.__data).length : 0;
                }
                // response data size
                performanceMod.req(path, Date.now() - now, requestPacketSize, responsePacketSize);
            } catch (error) {
                next(error);
            }
        }
    },

    requestLogger(option) {
        option = option || {};
        const pathConfig = option.path || {};
        return (req, res, next) => {
            let path = util.getReqPath(req);
            if (pathConfig[path] === false || req.method == "OPTIONS") {
                next();
                return;
            }
            let params = req.__data || (req.method == "GET" ? req.query : req.body);
            let uid = params.uid;
            let ip = util.getClientIp(req);
            req.__log = true
            logger.info(`I - [${ip}] [${path}]`, params);

            onHeaders(res, function onHeaders() {
                path !== '/doClsQuery' && logger.info(`O - [${ip}] [${path}], Uid: ${uid}`, "Data: ", res.__rspData);
            })

            next();
        }
    },

    errorHandler(err, req, res, next) {
        let params = req.__data || (req.method == "GET" ? req.query : req.body);
        let path = util.getReqPath(req);
        let ip = util.getClientIp(req);
        logger.error(err, VError.info(err), `[ ${ip} ] [ ${path} ], Uid: ${params.uid}`, params);

        res.json({status: -500});
    },

    /**
     *
     * @param {*} req
     * @param {*} res
     * @param {*} next
     * @description 解密请求数据，请求的数据结构必须为{encryStr: string, encryKey: string}
     * 解密后把数据写到req.__data中
     */
    async decryptReq(req, res, next) {
        try {
            if (req.method != "GET" && req.method != "POST") {
                next();
                return;
            }
            let params = req.method == "GET" ? req.query : req.body;
            let path = util.getReqPath(req);
            let ip = util.getClientIp(req);

            let info = crpytoConifg[path];
            if (!(info === false || (info && info.req === false))) {
                let {encryStr, encryKey} = params;

                let data = await cryptoHelper.pkDecrypt(encryStr, encryKey);
                if (data == false) {
                    logger.warn(`decrypt fail - [${ip}] [${path}]`, params);
                    res.json({status: -400});
                    if (params.uid && path == "/reddemCode") {
                        logger.error("reddemCode black", params)
                        verifyMod.addBlackForce(params.uid)
                    }
                } else {
                    req.__data = JSON.parse(data.decStr);
                    req.__enCryIdx = data.index
                    // logger.debug(`decrypt succ - [${ip}] [${path}]`)
                    next();
                }
            } else {
                next();
            }
        } catch (error) {
            next(error);
        }
    },

    async decodeUid(req, res, next) {
        try {
            let params = req.method == "GET" ? req.query : req.body;
            if (params.uid) {
                let info = await cryptoHelper.pkDecrypt(params.uid);
                let uid = info && info.decStr
                if (uid) {
                    params.uid = uid;
                }
            }
            if (params._uid) {
                let info = await cryptoHelper.pkDecrypt(params._uid);
                let _uid = info && info.decStr
                if (_uid) {
                    params._uid = _uid;
                }
            }
            next()
        } catch (error) {
            next(error);
        }
    },

    verifyToken(option) {
        option = option || {};
        return async (req, res, next) => {
            try {
                if (req.method == "OPTIONS") {
                    next();
                    return;
                }
                let params = req.__data || (req.method == "GET" ? req.query : req.body);
                let path = util.getReqPath(req);
                let ip = util.getClientIp(req);
                if (option.gameVer) { //版本兼容
                    if (util.cmpVersion(option.gameVer, params.gameVer) > 0) {
                        logger.warn(`skip verifyToken [${ip}] [${path}]`, params.uid, params.gameVer)
                        next()
                        return
                    }
                }
                if (params.uid && params.token) {
                    let hasToken = await userMod.hasToken(params.uid, params.token)
                    if (hasToken) {
                        next()
                    } else {
                        logger.error(`verifyToken err [${ip}] [${path}]`, params.uid, params.token)
                        res.json({status: -401});
                    }
                } else {
                    logger.error(`verifyToken err [${ip}] [${path}]`, params.uid, params.token)
                    res.json({status: -401});
                }
            } catch (error) {
                next(error);
            }
        }
    },

    checkReplay(option) {
        option = option || {gameVer: "2.9.6"};
        return async (req, res, next) => {
            try {
                if (req.method == "OPTIONS") {
                    next();
                    return;
                }
                let params = req.__data || (req.method == "GET" ? req.query : req.body);
                let path = util.getReqPath(req);
                let ip = util.getClientIp(req);
                if (params.__reqId && params.__reqId.length < 50) {
                    logger.warn(`skip checkReplay [${ip}] [${path}]`, params.uid, params.gameVer)
                    next()
                    return
                }
                if (params.__reqId) {
                    let {isReplay, rsp} = await userMod.checkReplay(params.__reqId)
                    if (!isReplay) {
                        req.__cacheRsp = true
                        next()
                    } else {
                        if (rsp) {
                            if (req.__log) {
                                res.__rspData = rsp;
                            }
                            logger.warn(`replay rsp cache [${ip}] [${path}]`, params.uid, params.__reqId)
                            res.send(rsp)
                        } else {
                            logger.warn(`checkReplay [${ip}] [${path}]`, params.uid, params.__reqId)
                            res.json({status: -402});
                        }
                    }
                } else {
                    next()
                }
            } catch (error) {
                next(error);
            }
        }
    },
};