/**
 * 业务相关的公共函数模块
 */

const NATIVE_CODE = "jxXGNLoJhKQGJYKL";

let global = {
    async verifyCode(uid, { code, type }) {
        if (!code || !type) {
            return false;
        }
        if (type == "wx") {
            if (code == NATIVE_CODE) return true
            try {
                let [{ openid }, userDoc] = await util.promiseMap([wxMod.code2Session(code), userMod.findOne(uid, { "openid": 1 })]);
                if (userDoc && userDoc.openid == openid) {
                    return true;
                }
            } catch (error) {
                logger.warn(VError.info(error))
            }
        }
        else {
            return code == NATIVE_CODE;
        }
        return false;
    },

    async requestComputeSever(path, json, uid) {
        try {
            let computeServer = config.computeServer
            if (!computeServer) return false
            let { ip, port } = computeServer
            let url = `http://${ip}:${port}/compute/${path}`
            return await got.post(url, {
                json: json,
                timeout: 5 * util.Time.Second, //5s都算不完直接放过
            }).json()
        } catch (error) {
            logger.error('requestToComputeSever', uid, error)
            return false
        }
    },

    async requestCryptoSever(path, json) {
        try {
            let cryptoSever = config.cryptoSever
            if (!cryptoSever) return false
            let { ip, port, proto } = cryptoSever
            proto = proto || "http"
            let url = `${proto}://${ip}:${port}/compute/${path}`
            return await got.post(url, {
                json: json,
                timeout: 10 * util.Time.Second,
            }).json()
        } catch (error) {
            logger.error('requestCryptoSever', error)
            return false
        }
    },

    async requestJobServer(path, json) {
        let jobServer = config.jobServer
        if (!jobServer) return false
        return this.rpc(jobServer, path, json)
    },

    async requestPayServer(path, json) {
        let payServer = config.payServer
        if (!payServer) return false
        return this.rpc(payServer, path, json)
    },

    async rpc(server, path, json, timeout = 5 * util.Time.Second) {
        try {
            let { ip, port, proto } = server
            proto = proto || "http"
            let url = `${proto}://${ip}:${port}/compute/${path}`
            return got.post(url, {
                json,
                timeout,
            }).json()
        } catch (error) {
            logger.error('rpc error', server, path, error)
            return false
        }
    }
}

module.exports = global;

let logger = require("../common/log").getLogger('global');
const VError = require("verror");
const userMod = require("../mod/userMod");
const wxMod = require("../mod/wxMod");
const util = require("./util").default;
const config = require("../config")
const got = require("got")