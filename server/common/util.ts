import Vec2 from "../client/map/Vec2";

const Time = {
    Day: 24 * 60 * 60 * 1000,// 天
    Hour: 60 * 60 * 1000,// 时
    Minute: 60 * 1000,// 分
    Second: 1000,// 秒
};

const Code = {
    ERROR: -101,
    SUCCESS: 0
};

function string2UtcTime(dateStr) {
    var dateGroup = dateStr.split(" ");
    var datePart1 = dateGroup[0].split("-");

    if (dateGroup.length == 1) {
        return Date.UTC(datePart1[0], datePart1[1] - 1, datePart1[2]);
    } else {
        var datePart2 = dateGroup[1].split(":");
        return Date.UTC(datePart1[0], datePart1[1] - 1, datePart1[2], datePart2[0], datePart2[1], datePart2[2]);
    }
}

function getDateStr(date) {
    var str = date.getFullYear() + "-";
    if (date.getMonth() + 1 < 10) {
        str += "0" + (date.getMonth() + 1) + "-";
    } else {
        str += (date.getMonth() + 1) + "-";
    }
    if (date.getDate() < 10) {
        str += "0" + date.getDate();
    } else {
        str += date.getDate();
    }
    return str;
}

function getUTCDateStr(date) {
    let str = date.getUTCFullYear() + "-";
    if (date.getUTCMonth() + 1 < 10) {
        str += "0" + (date.getUTCMonth() + 1) + "-";
    } else {
        str += (date.getUTCMonth() + 1) + "-";
    }
    if (date.getUTCDate() < 10) {
        str += "0" + date.getUTCDate();
    } else {
        str += date.getUTCDate();
    }
    return str;
}

function isNumber(val) {
    return !isNaN(val)
}

function getDayTimestamp(day) {
    day = day || 0;
    let now = new Date().getTime() + day * Time.Day;
    return now - (now % Time.Day);
}

function setValue2(fields, data, target?) {
    target = target || {}
    fields.split('|').forEach(m => {
        if (data[m] === null || data[m] === undefined || data[m] === "") {
            return
        }
        target[m] = data[m]
    })
    return target
}

// 对象给对象赋值
function setValue(fields: string, data: any, target?: any) {
    target = target || {}
    fields.split('|').forEach(m => target[m] = data[m])
    return target
}

//适用于比较1.0.1, v1.0.1这类版本号,支持长度不限
//返回值>0表示A比B大，0表示相等
function cmpVersion(versionA, versionB) {
    if (versionA == versionB) return 0
    if (!versionB) return 1
    if (!versionA) return -1

    versionA = versionA.replace("v", ""); //去掉v前缀
    versionB = versionB.replace("v", "");

    let vA = versionA.split('.');
    let vB = versionB.split('.');
    for (let i = 0; i < vA.length; ++i) {
        let a = parseInt(vA[i]);
        let b = parseInt(vB[i] || 0);
        if (a === b) {
            continue;
        } else {
            return a - b;
        }
    }
    if (vB.length > vA.length) {
        return -1;
    } else {
        return 0;
    }
}

function randomInt(max, min) {
    return Math.floor(Math.random() * (max - min) + min);
}


async function promiseMap(arr, callback) {
    if (arr.length <= 0) return arr;
    let promsies = arr;
    if (callback) {
        promsies = arr.map(callback);
    }
    let results = await Promise.all(promsies)
    return results;
}

function concat(arr1, arr2) { //区别于array.conat，这样比较省内存
    for (let element of arr2) {
        arr1.push(element);
    }
    return arr1;
}

function randomArray(array) {
    for (let i = array.length - 1; i >= 0; --i) {
        let randomIndex = randomInt(0, 1e10 + 7) % (i + 1);
        let a = array[randomIndex];
        let b = array[i];
        array[i] = a;
        array[randomIndex] = b;
    }
    return array
}

// 随机字符串
function getRandomString(len = 8) {
    const $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    const maxPos = $chars.length
    let str = ''
    for (let i = 0; i < len; i++) {
        str += $chars.charAt(Math.floor(Math.random() * maxPos))
    }
    return str
}

//判断是否包含非ascii字符
function isAsciiOnly(str) {
    for (var i = 0; i < str.length; i++) {
        if (str.charCodeAt(i) > 127) {
            return false;
        }
    }
    return true;
}

/**
 *
 * @param {number} delay
 * 单位毫秒
 */
async function wait(delay) {
    return new Promise(resolve => setTimeout(() => {
        resolve(0);
    }, delay));
}

function getClientIp(req) {
    try {
        let ip = req.headers['x-forwarded-for'] ||
            req.ip ||
            (req.connection && req.connection.remoteAddress) ||
            (req.socket && req.socket.remoteAddress) ||
            (req.connection && req.connection.socket && req.connection.socket.remoteAddress) || '';
        if (ip.split(',').length > 0) {
            ip = ip.split(',')[0]
        }
        if (ip.startsWith('::ffff:')) { // ipv4 -> ipv6 的格式
            ip = ip.substring(7);
        }
        //ip = ip.substr(ip.lastIndexOf(':') + 1, ip.length);
        return ip;
    } catch (error) {
        console.error(error)
    }
};

function getReqPath(req) {
    let url = req.originalUrl;
    let [path, _] = url.split("?");
    return path;
}

// 数字 字符串补0,根据长度补出前面差的0
const _pad_tbl = {};

function pad(num, length = 2) {
    const len = length - num.toString().length;
    if (len <= 0) {
        return num + '';
    }
    if (!_pad_tbl[len]) {
        _pad_tbl[len] = (new Array(len + 1)).join('0');
    }
    return _pad_tbl[len] + num;
}

function dateFormat(format, msd) {
    const date = msd ? new Date(msd) : new Date()
    const obj = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'q+': Math.floor((date.getMonth() + 3) / 3),
        'S+': date.getMilliseconds(),
    }
    if (/(y+)/i.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (let k in obj) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? String(obj[k]) : pad(obj[k]),
            )
        }
    }
    return format
}

function base64ToImg(data) {
    let base64Data = data.replace(/^data:image\/\w+;base64,/, "");
    let dataBuffer = Buffer.from(base64Data, 'base64');
    return dataBuffer
}

function stringToNumbers(str: string, separator: string = '|') {
    if (!str) {
        return []
    } else if (typeof str === 'number') {
        str = String(str)
    }
    return str.split(separator).filter(m => m.trim() !== '').map(m => Number(m))
}

function random(min: number, max?: number): number {
    if (max === undefined) {
        max = min;
        min = 0;
    }
    if (min >= max) {
        return min;
    }
    return Math.floor(Math.random() * (Math.max(max - min, 0) + 1)) + min;
}

// 概率
function chance(odds: number, mul: number = 100): boolean {
    return odds > 0 && random(0, 100 * mul - 1) < odds * mul;
}

// 随机一个负数到正数的范围
function randomRange(min: number, max: number): number {
    return Math.floor(Math.random() * min) + Math.floor(Math.random() * max);
}

// 随机一个下标出来
function randomIndex(len: number, count?: number, ignore?: any) {
    if (len === 0) {
        return -1;
    }
    let indexs = [], _count = count;
    ignore = Array.isArray(ignore) ? ignore : [ignore];
    _count = _count || 1;
    _count = _count > len ? len : _count;
    for (let i = 0; i < len; i++) {
        if (ignore.indexOf(i) !== -1) {
            continue;
        }
        indexs.push(i);
    }
    let ret = [];
    while (indexs.length > 0 && ret.length < _count) {
        let idx = random(indexs.length - 1);
        ret.push(indexs.splice(idx, 1)[0]);
    }
    if (ret.length === 0) {
        return -1;
    }
    return count === 1 ? ret[0] : ret;
}

// 根据权重随机
function randomByWeight(arr: any[], weightKey: string = "weight"): number {
    if (arr.length <= 0) {
        return
    }
    let getVal = (val) => {
        if (typeof val == 'number') return val
        return val[weightKey] || 0
    }
    let totalWeight = arr.reduce((sum, x) => sum + getVal(x), 0)
    let offset = Math.random() * totalWeight
    for (let i = 0; i < arr.length; i++) {
        offset -= getVal(arr[i])
        if (offset < 0) {
            return i
        }
    }
}

// 数字取整
function toFloor(val: number) {
    if (val < 10) {
        return Math.floor(val)
    } else if (val < 1000) {
        return Math.floor(Math.floor(val * 0.1) * 10)
    } else if (val < 100000) {
        return Math.floor(Math.floor(val * 0.01) * 100)
    } else {
        return Math.floor(Math.floor(val * 0.001) * 1000)
    }
}

// 生成唯一ID
let _accumulation = 1;
let _last_now = 0;

function uid(): string {
    const now = Date.now();
    if (now !== _last_now) {
        _last_now = now;
        return now + '0001';
    }
    return now + pad(++_accumulation, 4);
}

//获取位数, bit代表几进制
function getDigit(val: number, bit: number = 10) {
    let cnt = 0
    while (val > 0) {
        val = Math.floor(val / bit)
        cnt++
    }
    return cnt || 1
}

//保留最高x位
function retainHightDigit(val: number, retainCount = 2) {
    let digit = getDigit(val)
    let base = Math.pow(10, Math.max(0, digit - retainCount))
    return Math.floor(val / base) * base
}

function hexstring2btye(str) {
    let pos = 0;
    let len = str.length;
    if (len % 2 != 0) {
        return null;
    }
    len /= 2;
    let hexA = new Array();
    for (let i = 0; i < len; i++) {
        let s = str.substr(pos, 2);
        let v = parseInt(s, 16);
        hexA.push(v);
        pos += 2;
    }
    return hexA;
}

// 字符串填充参数
function stringFormat(text: string, params: any[]): string {
    if (!text || !params || params.length === 0) {
        return text;
    }
    params.forEach((p, i) => (text = text.replace(new RegExp('\\{' + i + '\\}', 'g'), p)));
    return text;
}

/**
 * 获取当前时间戳自1970/1/1日过了多少天
 * 这个时间戳需要是毫秒级
 * 注意8点之前和8点之后的时间戳计算出来的day是不相同的
 * @param now 当前时间戳
 */
function getDateOfDay(now = Date.now()) {
    return now - now % Time.Day
}

function joinPath(path1: string, path2: string) {
    if (!path1) return path2
    if (!path2) return path1
    if (path1[path1.length - 1] == "/" || path2[0] == "/") {
        path1 = path1.substring(0, path1.length - 1)
    }
    if (path2[0] == "/") {
        path2 = path2.substring(1)
    }
    return path1 + "/" + path2
}

// 深度拷贝对象
let cloneCache = []

function deepClone(obj, inDeep: boolean = false): any {
    if (!obj) {
        return null;
    }
    if (!inDeep) {
        cloneCache = []
    }
    let objClone = Array.isArray(obj) ? [] : {};
    if (obj && typeof obj === "object") {
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                //判断ojb子元素是否为对象，如果是，递归复制
                let value = obj[key];
                if (value && typeof value === "object") {
                    if (cloneCache.indexOf(value) === -1) {
                        cloneCache.push(value);
                        objClone[key] = deepClone(value, true);
                    }

                } else {
                    //如果不是，简单复制
                    objClone[key] = value;
                }
            }
        }
    }
    if (!inDeep) {
        cloneCache = null;
    }
    return objClone;
}

// 将一个字符串转换成向量
function stringToVec2(str: string, separator: string = ','): cc.Vec2 {
    if (!str) {
        return new Vec2();
    }
    const [x, y] = str.split(separator);
    return new Vec2(parseFloat(x), parseFloat(y));
}

function utcDateFormat(format, msd) {
    const date = msd ? new Date(msd) : new Date()
    const obj = {
        'M+': date.getUTCMonth() + 1,
        'd+': date.getUTCDate(),
        'h+': date.getUTCHours(),
        'm+': date.getUTCMinutes(),
        's+': date.getUTCSeconds(),
        'q+': Math.floor((date.getUTCMonth() + 3) / 3),
        'S+': date.getUTCMilliseconds(),
    }
    if (/(y+)/i.test(format)) {
        format = format.replace(RegExp.$1, (date.getUTCFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (let k in obj) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? String(obj[k]) : pad(obj[k]),
            )
        }
    }
    return format
}

/**
 * 对象存在某个属性就移除
 * @param obj 对象
 * @param attrs 属性列表
 * @param forceCase 忽略大小写
 */
function removeAttrIfExits(obj, attrs = [], forceCase = false) {
    if (!obj || attrs.length === 0) return;
    let keys = Object.keys(obj);
    if (keys.length) {
        attrs.forEach(attr => {
            let r = keys.filter(key => {
                return forceCase ? attr.toLowerCase() === key.toLowerCase() : key === attr;
            })
            r.forEach(attr => {
                delete obj[attr];
            })
        })
    }
    return obj;
}

/**
 * 将一个时间戳计算出自1970年过了多少天
 * 丢弃默认的时分秒毫秒
 * @param timestamp
 */
function getNumberDay(timestamp = 0) {
    if (timestamp == 0) {
        timestamp = Date.now()
        let date = new Date(timestamp);
        date.setHours(0, 0, 0, 0);
        return Math.floor(date.getTime() / this.Time.Day);
    }
    return Math.floor(new Date(timestamp).getTime() / this.Time.Day);
}

/**
 * 获取当天凌晨0点时间戳
 */
function getTodayZeroTime(h = 0, m = 0, s = 0, ms = 0) {
    let date = new Date();
    date.setHours(h, m, s, ms);
    return date.getTime()
}

/**
 * 获取当天凌晨6点的时间戳
 */
function getSixAmTimestamp() {
    // 获取当前时间戳（单位：毫秒）
    let now = new Date().getTime();
    // 计算当天凌晨的时间戳（单位：毫秒）
    let sixAM = new Date();
    sixAM.setHours(6, 0, 0, 0);
    let sixAMTimestamp = sixAM.getTime();
    // 如果当前时间早于6点，减去一天的毫秒数
    if (now < sixAMTimestamp) {
        sixAMTimestamp -= 24 * 60 * 60 * 1000;
    }
    return sixAMTimestamp
}

/**
 * 比较版本号
 * 比如 2.11.0 和 2.12
 * 会自动补齐位数
 * 会转换成 2110 和2120比较
 * 比较是大于min 小于等于 max
 * @param ver
 * @param limit
 */
function str_checker(ver, limit) {
    let min_pass = false;
    let max_pass = false;
    let _ver = ver ? ver.split('.') : [];
    let min = limit.min ? limit.min.split('.') : [];
    let max = limit.max ? limit.max.split('.') : [];
    !min.length && (min_pass = true);
    !max.length && (max_pass = true);
    let max_len = Math.max(_ver.length, Math.max(min.length, max.length));
    let supply = (arr) => {
        while (max_len - arr.length > 0) {
            arr.push(0);
        }
    }
    supply(_ver);
    supply(min);
    supply(max);
    for (let i = 0; i < max_len; i++) {
        !min_pass && min && _ver && Number(_ver[i]) >= Number(min[i]) && (min_pass = true);
        !max_pass && max && _ver && Number(_ver[i]) < Number(max[i]) && (max_pass = true);
    }
    return min_pass && max_pass;
}

function replaceAll(str: string, findValue: string, replaceValue: string): string {
    do {
        str = str.replace(findValue, replaceValue);
    } while (str.indexOf(findValue) != -1);
    return str;
}

/**
 * 提取字符串末尾的数字  没有数字返回0
 * @param str
 */
function getLastCharToNumber(str) {
    str = str.trim();
    if (!str.length) return 0;
    let i = 0, final = '';
    while (i++, true) {
        let _ = str.substring(str.length - i, str.length - i + 1);
        _ = parseInt(_);
        if (Number.isNaN(_)) break;
        final = _ += final;
    }
    let temp = parseInt(final);
    return Number.isNaN(temp) ? 0 : temp;
}

export default {
    Time: Time,

    Code: Code,

    string2UtcTime: string2UtcTime,

    getDateStr: getDateStr,

    getUTCDateStr: getUTCDateStr,

    isNumber: isNumber,

    getDayTimestamp: getDayTimestamp,

    cmpVersion: cmpVersion,

    randomInt: randomInt,

    randomIndex: randomIndex,
    chance,
    promiseMap: promiseMap,
    concat: concat,
    randomArray: randomArray,
    wait: wait,
    getRandomString,
    getClientIp,
    getReqPath,
    dateFormat,
    setValue,
    setValue2,
    base64ToImg,
    stringToNumbers,
    random,
    toFloor,
    uid,
    isAsciiOnly,
    getDigit,
    retainHightDigit,
    hexstring2btye,
    stringFormat,
    randomByWeight,
    getDateOfDay,
    joinPath,
    deepClone,
    stringToVec2,
    utcDateFormat,
    removeAttrIfExits,
    getNumberDay,
    getTodayZeroTime,
    str_checker,
    replaceAll,
    getLastCharToNumber,
    getSixAmTimestamp
};
