require('ts-node').register();
let config = require("./config");

if (config.debug) {
    config.port = 8080;
    config.log.http = false
}

const app = require("./common/app");
const { default: util } = require('./common/util');
const logger = require("./common/log").getLoggerByHttp()

app.__init()

app.post("/", (req, res) => {
    let ip = util.getClientIp(req)
    req.body.message = `[${ip}] ` + req.body.message
    res.end()
    logger.log(req.body)
})