import ModelMgr from "./ModelMgr"

let recordMod = require("../mod/recordMod.js")

export default class DBModel {
    private record: any = null

    public init(record: any) {
        this.record = record
    }

    public register(key: string, ...param) {
        return this.loadObject(key)
    }

    public loadObject(key) {
        return recordMod.getFromRecord(key, this.record)
    }

    public loadString(key) {
        return recordMod.getFromRecord(key, this.record)
    }
}
