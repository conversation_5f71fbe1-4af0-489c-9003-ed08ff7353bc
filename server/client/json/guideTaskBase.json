[{"id": 1001, "name": "guideTaskDesc.1001", "desc": "guideTaskDesc.10001", "sort": 1, "reward": "1,-1,15", "icon": "", "condition": 1001, "prev_id": "", "next_id": 1046, "goto": "s:5,u:BuildInfo,id:8001"}, {"id": 1205, "name": "guideTaskDesc.1205", "desc": "guideTaskDesc.10205", "sort": 2, "reward": "1,-1,20", "icon": "task/phb_xjrw_25", "condition": 1205, "prev_id": 1001, "next_id": 1002, "goto": "guide:1@1"}, {"id": 1046, "name": "guideTaskDesc.1046", "desc": "guideTaskDesc.10046", "sort": 3, "reward": "1,-1,30", "icon": "", "condition": 1046, "prev_id": 1205, "next_id": 1003, "goto": "s:5,u:BuildInfo,id:5001"}, {"id": 1002, "name": "guideTaskDesc.1002", "desc": "guideTaskDesc.10002", "sort": 4, "reward": "1,-1,40", "icon": "main/dalou_shengji_3", "condition": 1002, "prev_id": 1046, "next_id": 1004, "goto": "s:1,u:MainInfo,guide:201@3"}, {"id": 1003, "name": "guideTaskDesc.1003", "desc": "guideTaskDesc.10003", "sort": 5, "reward": "1,-1,50", "icon": "main/xj_icon_zlkr", "condition": 1003, "prev_id": 1002, "next_id": 1048, "goto": "s:1,guide:4@1"}, {"id": 1223, "name": "guideTaskDesc.1223", "desc": "guideTaskDesc.10223", "sort": 6, "reward": "1,-1,30", "icon": "task/phb_xjrw_25", "condition": 1223, "prev_id": 1003, "next_id": 1048, "goto": "guide:1@1"}, {"id": 1004, "name": "guideTaskDesc.1004", "desc": "guideTaskDesc.10004", "sort": 7, "reward": "2,-1,5", "icon": "", "condition": 1004, "prev_id": 1003, "next_id": 1070, "goto": "s:5,u:BuildInfo,id:13001"}, {"id": 1070, "name": "guideTaskDesc.1070", "desc": "guideTaskDesc.10070", "sort": 8, "reward": "1,-1,150", "icon": "", "condition": 1070, "prev_id": 1003, "next_id": 1009, "goto": "s:5,u:BuildInfo,id:15001"}, {"id": 1224, "name": "guideTaskDesc.1224", "desc": "guideTaskDesc.10224", "sort": 9, "reward": "1,-1,50", "icon": "task/phb_xjrw_25", "condition": 1224, "prev_id": 1223, "next_id": 1009, "goto": "guide:1@1"}, {"id": 1048, "name": "guideTaskDesc.1048", "desc": "guideTaskDesc.10048", "sort": 10, "reward": "1,-1,70", "icon": "main/xj_icon_tsxfg", "condition": 1048, "prev_id": 1003, "next_id": 1006, "goto": "s:1,u:MainInfo,guide:101@3"}, {"id": 1006, "name": "guideTaskDesc.1006", "desc": "guideTaskDesc.10006", "sort": 11, "reward": "401,-1,5", "icon": "main/dalou_shengji_5", "condition": 1006, "prev_id": 1003, "next_id": 1207, "goto": "s:1,u:MainInfo,guide:202@5"}, {"id": 1009, "name": "guideTaskDesc.1009", "desc": "guideTaskDesc.10009", "sort": 12, "reward": "1,-1,150", "icon": "", "condition": 1009, "prev_id": 1006, "next_id": 1208, "goto": "s:201,u:FurnInfo,id:TABLE_009"}, {"id": 1207, "name": "guideTaskDesc.1207", "desc": "guideTaskDesc.10207", "sort": 13, "reward": "1,-1,500", "icon": "", "condition": 1207, "prev_id": 1006, "next_id": 1047, "goto": "s:201,u:FurnInfo,id:DECORATE_001"}, {"id": 1208, "name": "guideTaskDesc.1208", "desc": "guideTaskDesc.10208", "sort": 14, "reward": "1,-1,250", "icon": "", "condition": 1208, "prev_id": 1006, "next_id": 1005, "goto": "s:201,u:FurnInfo,id:CABINET_001"}, {"id": 1047, "name": "guideTaskDesc.1047", "desc": "guideTaskDesc.10047", "sort": 15, "reward": "1,-1,200", "icon": "main/xj_icon_zlkr", "condition": 1047, "prev_id": 1003, "next_id": 1037, "goto": "s:1,guide:4@1"}, {"id": 1005, "name": "guideTaskDesc.1005", "desc": "guideTaskDesc.10005", "sort": 16, "reward": "1,-1,150", "icon": "", "condition": 1005, "prev_id": 1003, "next_id": 1032, "goto": "s:5,u:BuildInfo,id:9001"}, {"id": 1037, "name": "guideTaskDesc.1037", "desc": "guideTaskDesc.10037", "sort": 17, "reward": "1,-1,250", "icon": "", "condition": 1037, "prev_id": 1003, "next_id": 1007, "goto": "s:5,u:BuildInfo,id:12001"}, {"id": 1032, "name": "guideTaskDesc.1032", "desc": "guideTaskDesc.10032", "sort": 18, "reward": "1,-1,400", "icon": "", "condition": 1032, "prev_id": 1003, "next_id": 1008, "goto": "s:5,u:BuildInfo,id:11001"}, {"id": 1021, "name": "guideTaskDesc.1021", "desc": "guideTaskDesc.10021", "sort": 19, "reward": "1,-1,650", "icon": "", "condition": 1021, "prev_id": 1003, "next_id": 1038, "goto": "s:5,u:BuildInfo,id:7002"}, {"id": 1039, "name": "guideTaskDesc.1039", "desc": "guideTaskDesc.10039", "sort": 20, "reward": "1,-1,800", "icon": "", "condition": 1039, "prev_id": 1003, "next_id": 1211, "goto": "s:5,u:BuildInfo,id:6002"}, {"id": 1038, "name": "guideTaskDesc.1038", "desc": "guideTaskDesc.10038", "sort": 21, "reward": "1,-1,700", "icon": "", "condition": 1038, "prev_id": 1021, "next_id": 1212, "goto": "s:5,u:BuildInfo,id:8002"}, {"id": 1209, "name": "guideTaskDesc.1209", "desc": "guideTaskDesc.10209", "sort": 22, "reward": "1,-1,50", "icon": "", "condition": 1209, "prev_id": 1006, "next_id": 1210, "goto": "s:201,u:FurnInfo,id:CARPET_001"}, {"id": 1007, "name": "guideTaskDesc.1007", "desc": "guideTaskDesc.10007", "sort": 23, "reward": "1,-1,300", "icon": "main/xj_icon_zlkr", "condition": 1007, "prev_id": 1037, "next_id": 1209, "goto": "s:1,guide:4@1"}, {"id": 1008, "name": "guideTaskDesc.1008", "desc": "guideTaskDesc.10008", "sort": 24, "reward": "1,-1,350", "icon": "main/dalou_shengji_2", "condition": 1008, "prev_id": 1003, "next_id": 1081, "goto": "s:1,u:MainInfo,guide:203@2"}, {"id": 1081, "name": "guideTaskDesc.1081", "desc": "guideTaskDesc.10081", "sort": 25, "reward": "2,-1,10", "icon": "", "condition": 1081, "prev_id": 1006, "next_id": 1021, "goto": "s:201,u:FurnInfo,id:CARPET_005"}, {"id": 1210, "name": "guideTaskDesc.1210", "desc": "guideTaskDesc.10210", "sort": 26, "reward": "1,-1,800", "icon": "", "condition": 1210, "prev_id": 1006, "next_id": 1039, "goto": "s:201,u:FurnInfo,id:DECORATE_015"}, {"id": 1212, "name": "guideTaskDesc.1212", "desc": "guideTaskDesc.10212", "sort": 27, "reward": "1,-1,600", "icon": "", "condition": 1212, "prev_id": 1006, "next_id": 1014, "goto": "s:201,u:FurnInfo,id:DECORATE_006"}, {"id": 1014, "name": "guideTaskDesc.1014", "desc": "guideTaskDesc.10014", "sort": 28, "reward": "1,-1,400", "icon": "main/dalou_shengji_6", "condition": 1014, "prev_id": 1007, "next_id": 1017, "goto": "s:1,u:MainInfo,guide:205@6"}, {"id": 1213, "name": "guideTaskDesc.1213", "desc": "guideTaskDesc.10213", "sort": 29, "reward": "1,-1,900", "icon": "", "condition": 1213, "prev_id": 1006, "next_id": 1013, "goto": "s:201,u:FurnInfo,id:DECORATE_022"}, {"id": 1013, "name": "guideTaskDesc.1013", "desc": "guideTaskDesc.10013", "sort": 30, "reward": "1,-1,350", "icon": "main/xj_icon_zlkr", "condition": 1013, "prev_id": 1007, "next_id": 1018, "goto": "s:1,guide:4@1"}, {"id": 1017, "name": "guideTaskDesc.1017", "desc": "guideTaskDesc.10017", "sort": 31, "reward": "1,-1,250", "icon": "main/xj_icon_wjcy", "condition": 1017, "prev_id": 1014, "next_id": 1015, "goto": "s:3,u:FoodMining,guide:102@0"}, {"id": 1018, "name": "guideTaskDesc.1018", "desc": "guideTaskDesc.10018", "sort": 32, "reward": "1,-1,300", "icon": "", "condition": 1018, "prev_id": 1014, "next_id": 1016, "goto": "s:3,u:FoodInfo,id:2"}, {"id": 1214, "name": "guideTaskDesc.1214", "desc": "guideTaskDesc.10214", "sort": 33, "reward": "1,-1,200", "icon": "", "condition": 1214, "prev_id": 1006, "next_id": 1079, "goto": "s:201,u:FurnInfo,id:CABINET_005"}, {"id": 1079, "name": "guideTaskDesc.1079", "desc": "guideTaskDesc.10079", "sort": 34, "reward": "1,-1,250", "icon": "", "condition": 1079, "prev_id": 1032, "next_id": 1019, "goto": "s:5,u:BuildInfo,id:14001"}, {"id": 1033, "name": "guideTaskDesc.1033", "desc": "guideTaskDesc.10033", "sort": 35, "reward": "1,-1,300", "icon": "", "condition": 1033, "prev_id": 1006, "next_id": 1072, "goto": "s:201,u:FurnInfo,id:TABLE_001"}, {"id": 1072, "name": "guideTaskDesc.1072", "desc": "guideTaskDesc.10072", "sort": 36, "reward": "1,-1,320", "icon": "", "condition": 1072, "prev_id": 1007, "next_id": 1073, "goto": "s:5,u:BuildInfo,id:10001"}, {"id": 1015, "name": "guideTaskDesc.1015", "desc": "guideTaskDesc.10015", "sort": 37, "reward": "402,-1,3", "icon": "", "condition": 1015, "prev_id": 1014, "next_id": 1033, "goto": "s:3,u:BuildInfo,id:6001"}, {"id": 1075, "name": "guideTaskDesc.1075", "desc": "guideTaskDesc.10075", "sort": 38, "reward": "1,-1,400", "icon": "", "condition": 1075, "prev_id": 1014, "next_id": 1216, "goto": "s:3,u:BuildInfo,id:7001"}, {"id": 1016, "name": "guideTaskDesc.1016", "desc": "guideTaskDesc.10016", "sort": 39, "reward": "1,-1,600", "icon": "", "condition": 1016, "prev_id": 1014, "next_id": 1214, "goto": "s:3,u:BuildInfo,id:11001"}, {"id": 1211, "name": "guideTaskDesc.1211", "desc": "guideTaskDesc.10211", "sort": 40, "reward": "1,-1,1000", "icon": "", "condition": 1211, "prev_id": 1004, "next_id": 1213, "goto": "s:5,u:BuildInfo,id:15002"}, {"id": 1073, "name": "guideTaskDesc.1073", "desc": "guideTaskDesc.10073", "sort": 41, "reward": "1,-1,360", "icon": "main/xj_icon_tskll", "condition": 1073, "prev_id": 1048, "next_id": 1036, "goto": "s:1,u:MainInfo,guide:102@1"}, {"id": 1019, "name": "guideTaskDesc.1019", "desc": "guideTaskDesc.10019", "sort": 42, "reward": "402,-1,4", "icon": "other/gui_ys_lwl01", "condition": 1019, "prev_id": 1018, "next_id": 1075, "goto": "s:3,u:FoodMining,guide:301@2"}, {"id": 1011, "name": "guideTaskDesc.1011", "desc": "guideTaskDesc.10011", "sort": 43, "reward": "1,-1,330", "icon": "main/dalou_shengji_4", "condition": 1011, "prev_id": 1003, "next_id": 1222, "goto": "s:1,u:MainInfo,guide:204@4"}, {"id": 1036, "name": "guideTaskDesc.1036", "desc": "guideTaskDesc.10036", "sort": 44, "reward": "1,-1,350", "icon": "", "condition": 1036, "prev_id": 1019, "next_id": 1215, "goto": "s:3,u:FoodInfo,id:3"}, {"id": 1219, "name": "guideTaskDesc.1219", "desc": "guideTaskDesc.10219", "sort": 45, "reward": "1,-1,100", "icon": "", "condition": 1219, "prev_id": 1006, "next_id": 1221, "goto": "s:201,u:FurnInfo,id:PENDANT_004"}, {"id": 1215, "name": "guideTaskDesc.1215", "desc": "guideTaskDesc.10215", "sort": 46, "reward": "1,-1,500", "icon": "main/xj_icon_zlkr", "condition": 1215, "prev_id": 1013, "next_id": 1091, "goto": "s:1,guide:4@1"}, {"id": 1220, "name": "guideTaskDesc.1220", "desc": "guideTaskDesc.10220", "sort": 47, "reward": "1,-1,400", "icon": "", "condition": 1220, "prev_id": 1006, "next_id": 1011, "goto": "s:201,u:FurnInfo,id:PENDANT_098"}, {"id": 1216, "name": "guideTaskDesc.1216", "desc": "guideTaskDesc.10216", "sort": 48, "reward": "1,-1,1100", "icon": "", "condition": 1216, "prev_id": 1037, "next_id": 1217, "goto": "s:5,u:BuildInfo,id:12002"}, {"id": 1222, "name": "guideTaskDesc.1222", "desc": "guideTaskDesc.10222", "sort": 49, "reward": "402,-1,3", "icon": "", "condition": 1222, "prev_id": 1014, "next_id": 1051, "goto": "s:3,u:BuildInfo,id:10001"}, {"id": 1053, "name": "guideTaskDesc.1053", "desc": "guideTaskDesc.10053", "sort": 50, "reward": "1,-1,1800", "icon": "", "condition": 1053, "prev_id": 1006, "next_id": 1074, "goto": "s:201,u:FurnInfo,id:CARPET_007"}, {"id": 1040, "name": "guideTaskDesc.1040", "desc": "guideTaskDesc.10040", "sort": 51, "reward": "1,-1,750", "icon": "", "condition": 1040, "prev_id": 1006, "next_id": 1219, "goto": "s:201,u:FurnInfo,id:TABLE_010"}, {"id": 1091, "name": "guideTaskDesc.1091", "desc": "guideTaskDesc.10091", "sort": 52, "reward": "1,-1,2500", "icon": "", "condition": 1091, "prev_id": 1032, "next_id": 1040, "goto": "s:5,u:BuildInfo,id:11002"}, {"id": 1217, "name": "guideTaskDesc.1217", "desc": "guideTaskDesc.10217", "sort": 53, "reward": "1,-1,2600", "icon": "", "condition": 1217, "prev_id": 1006, "next_id": 1218, "goto": "s:201,u:FurnInfo,id:BED_002"}, {"id": 1218, "name": "guideTaskDesc.1218", "desc": "guideTaskDesc.10218", "sort": 54, "reward": "1,-1,2650", "icon": "", "condition": 1218, "prev_id": 1006, "next_id": 1220, "goto": "s:201,u:FurnInfo,id:CABINET_002"}, {"id": 1221, "name": "guideTaskDesc.1221", "desc": "guideTaskDesc.10221", "sort": 55, "reward": "1,-1,1000", "icon": "main/xj_icon_zlkr", "condition": 1221, "prev_id": 1215, "next_id": 1031, "goto": "s:1,guide:4@1"}, {"id": 1031, "name": "guideTaskDesc.1031", "desc": "guideTaskDesc.10031", "sort": 56, "reward": "1,-1,550", "icon": "", "condition": 1031, "prev_id": 1036, "next_id": 1020, "goto": "s:3,u:FoodInfo,id:4"}, {"id": 1041, "name": "guideTaskDesc.1041", "desc": "guideTaskDesc.10041", "sort": 57, "reward": "1,-1,700", "icon": "", "condition": 1041, "prev_id": 1014, "next_id": 1100, "goto": "s:3,u:BuildInfo,id:8001"}, {"id": 1074, "name": "guideTaskDesc.1074", "desc": "guideTaskDesc.10074", "sort": 58, "reward": "402,-1,5", "icon": "", "condition": 1074, "prev_id": 1013, "next_id": 1069, "goto": "s:5,u:BuildInfo,id:16001"}, {"id": 1080, "name": "guideTaskDesc.1080", "desc": "guideTaskDesc.10080", "sort": 59, "reward": "1,-1,1400", "icon": "", "condition": 1080, "prev_id": 1006, "next_id": 1081, "goto": "s:201,u:FurnInfo,id:PENDANT_006"}, {"id": 1059, "name": "guideTaskDesc.1059", "desc": "guideTaskDesc.10059", "sort": 60, "reward": "1,-1,2800", "icon": "", "condition": 1059, "prev_id": 1006, "next_id": 1097, "goto": "s:201,u:FurnInfo,id:TABLE_002"}, {"id": 1057, "name": "guideTaskDesc.1057", "desc": "guideTaskDesc.10057", "sort": 61, "reward": "1,-1,6000", "icon": "", "condition": 1057, "prev_id": 1014, "next_id": 1082, "goto": "s:3,u:BuildInfo,id:5002"}, {"id": 1022, "name": "guideTaskDesc.1022", "desc": "guideTaskDesc.10022", "sort": 62, "reward": "1,-1,2000", "icon": "main/dalou_shengji_5", "condition": 1022, "prev_id": 1006, "next_id": 1083, "goto": "s:1,u:MainInfo,guide:206@7"}, {"id": 1020, "name": "guideTaskDesc.1020", "desc": "guideTaskDesc.10020", "sort": 63, "reward": "402,-1,10", "icon": "main/xj_icon_zlkr", "condition": 1020, "prev_id": 1221, "next_id": 1059, "goto": "s:1,guide:4@1"}, {"id": 1138, "name": "guideTaskDesc.1138", "desc": "guideTaskDesc.10138", "sort": 64, "reward": "2,-1,5", "icon": "", "condition": 1138, "prev_id": 1022, "next_id": "", "goto": "s:202,u:FurnInfo,id:CARPET_012"}, {"id": 1076, "name": "guideTaskDesc.1076", "desc": "guideTaskDesc.10076", "sort": 65, "reward": "1,-1,450", "icon": "", "condition": 1076, "prev_id": 1075, "next_id": 1023, "goto": "s:3,u:BuildInfo,id:14001"}, {"id": 1023, "name": "guideTaskDesc.1023", "desc": "guideTaskDesc.10023", "sort": 66, "reward": "401,-1,10", "icon": "", "condition": 1023, "prev_id": 1022, "next_id": 1084, "goto": "s:202,u:FurnInfo,id:CABINET_001"}, {"id": 1083, "name": "guideTaskDesc.1083", "desc": "guideTaskDesc.10083", "sort": 67, "reward": "1,-1,600", "icon": "", "condition": 1083, "prev_id": 1022, "next_id": 1024, "goto": "s:202,u:FurnInfo,id:SIT_001"}, {"id": 1084, "name": "guideTaskDesc.1084", "desc": "guideTaskDesc.10084", "sort": 68, "reward": "1,-1,800", "icon": "", "condition": 1084, "prev_id": 1022, "next_id": 1089, "goto": "s:202,u:FurnInfo,id:SIT_005"}, {"id": 1051, "name": "guideTaskDesc.1051", "desc": "guideTaskDesc.10051", "sort": 69, "reward": "1,-1,1000", "icon": "main/xj_icon_tsxfg", "condition": 1051, "prev_id": 1073, "next_id": 1099, "goto": "s:1,u:MainInfo,guide:101@3"}, {"id": 1097, "name": "guideTaskDesc.1097", "desc": "guideTaskDesc.10097", "sort": 70, "reward": "1,-1,1000", "icon": "", "condition": 1097, "prev_id": 1031, "next_id": 1053, "goto": "s:3,u:FoodInfo,id:5"}, {"id": 1100, "name": "guideTaskDesc.1100", "desc": "guideTaskDesc.10100", "sort": 71, "reward": "402,-1,5", "icon": "main/xj_icon_zlkr", "condition": 1100, "prev_id": 1020, "next_id": 1057, "goto": "s:1,guide:4@1"}, {"id": 1099, "name": "guideTaskDesc.1099", "desc": "guideTaskDesc.10099", "sort": 72, "reward": "1,-1,2500", "icon": "main/xj_icon_tskll", "condition": 1099, "prev_id": 1073, "next_id": 1041, "goto": "s:1,u:MainInfo,guide:102@1"}, {"id": 1089, "name": "guideTaskDesc.1089", "desc": "guideTaskDesc.10089", "sort": 73, "reward": "1,-1,2300", "icon": "", "condition": 1089, "prev_id": 1022, "next_id": 1117, "goto": "s:202,u:FurnInfo,id:DECORATE_016"}, {"id": 1117, "name": "guideTaskDesc.1117", "desc": "guideTaskDesc.10117", "sort": 74, "reward": "4,9100,35", "icon": "", "condition": 1117, "prev_id": 1006, "next_id": "", "goto": "s:201,u:FurnInfo,id:CARPET_010"}, {"id": 1225, "name": "guideTaskDesc.1225", "desc": "guideTaskDesc.10225", "sort": 75, "reward": "1,-1,1500", "icon": "main/dalou_shengji_17", "condition": 1225, "prev_id": 1022, "next_id": 1064, "goto": "s:1,u:MainInfo,guide:220@21"}, {"id": 1056, "name": "guideTaskDesc.1056", "desc": "guideTaskDesc.10056", "sort": 76, "reward": "1,-1,1000", "icon": "main/dalou_shengji_15", "condition": 1056, "prev_id": 1022, "next_id": 1065, "goto": "s:1,u:MainInfo,guide:207@17"}, {"id": 1034, "name": "guideTaskDesc.1034", "desc": "guideTaskDesc.10034", "sort": 77, "reward": "1,-1,240", "icon": "", "condition": 1034, "prev_id": 1006, "next_id": 1169, "goto": "s:201,u:FurnInfo,id:XISHU_001"}, {"id": 1035, "name": "guideTaskDesc.1035", "desc": "guideTaskDesc.10035", "sort": 78, "reward": "1,-1,350", "icon": "", "condition": 1035, "prev_id": 1006, "next_id": 1012, "goto": "s:201,u:FurnInfo,id:LAVATORY_001"}, {"id": 1050, "name": "guideTaskDesc.1050", "desc": "guideTaskDesc.10050", "sort": 79, "reward": "1,-1,1500", "icon": "", "condition": 1050, "prev_id": 1006, "next_id": 1051, "goto": "s:201,u:FurnInfo,id:PENDANT_040"}, {"id": 1082, "name": "guideTaskDesc.1082", "desc": "guideTaskDesc.10082", "sort": 80, "reward": "1,-1,2000", "icon": "", "condition": 1082, "prev_id": 1081, "next_id": 1022, "goto": "s:201,u:FurnInfo,id:DECORATE_027"}, {"id": 1024, "name": "guideTaskDesc.1024", "desc": "guideTaskDesc.10024", "sort": 81, "reward": "1,-1,1700", "icon": "main/xj_icon_zlkr", "condition": 1024, "prev_id": 1100, "next_id": 1102, "goto": "s:1,guide:4@1"}, {"id": 1102, "name": "guideTaskDesc.1102", "desc": "guideTaskDesc.10102", "sort": 82, "reward": "1,-1,1500", "icon": "", "condition": 1102, "prev_id": 1097, "next_id": 1056, "goto": "s:3,u:FoodInfo,id:6"}, {"id": 1069, "name": "guideTaskDesc.1069", "desc": "guideTaskDesc.10069", "sort": 83, "reward": "4,9100,25", "icon": "", "condition": 1069, "prev_id": 1016, "next_id": 1076, "goto": "s:3,u:BuildInfo,id:10002"}, {"id": 1077, "name": "guideTaskDesc.1077", "desc": "guideTaskDesc.10077", "sort": 84, "reward": "1,-1,500", "icon": "", "condition": 1077, "prev_id": 1076, "next_id": 1078, "goto": "s:3,u:BuildInfo,id:15001"}, {"id": 1096, "name": "guideTaskDesc.1096", "desc": "guideTaskDesc.10096", "sort": 85, "reward": "1,-1,1800", "icon": "main/dalou_shengji_8", "condition": 1096, "prev_id": 1022, "next_id": 1092, "goto": "s:1,u:MainInfo,guide:209@8"}, {"id": 1060, "name": "guideTaskDesc.1060", "desc": "guideTaskDesc.10060", "sort": 86, "reward": "1,-1,7500", "icon": "", "condition": 1060, "prev_id": 1041, "next_id": 1067, "goto": "s:3,u:BuildInfo,id:9002"}, {"id": 1088, "name": "guideTaskDesc.1088", "desc": "guideTaskDesc.10088", "sort": 87, "reward": "1,-1,2200", "icon": "", "condition": 1088, "prev_id": 1022, "next_id": 1089, "goto": "s:202,u:FurnInfo,id:PENDANT_001"}, {"id": 1113, "name": "guideTaskDesc.1113", "desc": "guideTaskDesc.10113", "sort": 88, "reward": "1,-1,11000", "icon": "", "condition": 1113, "prev_id": 1074, "next_id": "", "goto": "s:5,u:BuildInfo,id:7003"}, {"id": 1118, "name": "guideTaskDesc.1118", "desc": "guideTaskDesc.10118", "sort": 89, "reward": "1,-1,14000", "icon": "", "condition": 1118, "prev_id": 1113, "next_id": "", "goto": "s:5,u:BuildInfo,id:8003"}, {"id": 1010, "name": "guideTaskDesc.1010", "desc": "guideTaskDesc.10010", "sort": 90, "reward": "1,-1,140", "icon": "", "condition": 1010, "prev_id": 1006, "next_id": 1032, "goto": "s:201,u:FurnInfo,id:SIT_011"}, {"id": 1012, "name": "guideTaskDesc.1012", "desc": "guideTaskDesc.10012", "sort": 91, "reward": "1,-1,370", "icon": "", "condition": 1012, "prev_id": 1006, "next_id": 1013, "goto": "s:201,u:FurnInfo,id:RECREATION_008"}, {"id": 1078, "name": "guideTaskDesc.1078", "desc": "guideTaskDesc.10078", "sort": 92, "reward": "1,-1,550", "icon": "", "condition": 1078, "prev_id": 1077, "next_id": 1020, "goto": "s:3,u:BuildInfo,id:16001"}, {"id": 1042, "name": "guideTaskDesc.1042", "desc": "guideTaskDesc.10042", "sort": 93, "reward": "1,-1,850", "icon": "", "condition": 1042, "prev_id": 1014, "next_id": 1050, "goto": "s:3,u:BuildInfo,id:12001"}, {"id": 1098, "name": "guideTaskDesc.1098", "desc": "guideTaskDesc.10098", "sort": 94, "reward": "1,-1,2500", "icon": "main/xj_icon_zlkr", "condition": 1098, "prev_id": 1024, "next_id": 1063, "goto": "s:1,guide:4@1"}, {"id": 1068, "name": "guideTaskDesc.1068", "desc": "guideTaskDesc.10068", "sort": 95, "reward": "1,-1,2500", "icon": "", "condition": 1068, "prev_id": 1022, "next_id": 1069, "goto": "s:202,u:FurnInfo,id:DECORATE_029"}, {"id": 1025, "name": "guideTaskDesc.1025", "desc": "guideTaskDesc.10025", "sort": 96, "reward": "1,-1,4000", "icon": "main/dalou_shengji_9", "condition": 1025, "prev_id": 1022, "next_id": 1026, "goto": "s:1,u:MainInfo,guide:210@9"}, {"id": 1109, "name": "guideTaskDesc.1109", "desc": "guideTaskDesc.10109", "sort": 97, "reward": "1,-1,11600", "icon": "", "condition": 1109, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:14001"}, {"id": 1026, "name": "guideTaskDesc.1026", "desc": "guideTaskDesc.10026", "sort": 98, "reward": "2,-1,5", "icon": "main/xj_icon_hccl", "condition": 1026, "prev_id": 1025, "next_id": 1027, "goto": "s:2,u:<PERSON>mp<PERSON><PERSON>ff"}, {"id": 1028, "name": "guideTaskDesc.1028", "desc": "guideTaskDesc.10028", "sort": 99, "reward": "1,-1,5000", "icon": "soup/3002_liuhuang", "condition": 1028, "prev_id": 1025, "next_id": 1043, "goto": "s:2,u:SoupInfo,id:3002"}, {"id": 1043, "name": "guideTaskDesc.1043", "desc": "guideTaskDesc.10043", "sort": 100, "reward": "1,-1,5500", "icon": "main/xj_icon_tssld", "condition": 1043, "prev_id": 1025, "next_id": 1044, "goto": "s:2,u:<PERSON>mp<PERSON><PERSON>ff"}, {"id": 1103, "name": "guideTaskDesc.1103", "desc": "guideTaskDesc.10103", "sort": 101, "reward": "1,-1,2000", "icon": "", "condition": 1103, "prev_id": 1102, "next_id": 1061, "goto": "s:3,u:FoodInfo,id:7"}, {"id": 1052, "name": "guideTaskDesc.1052", "desc": "guideTaskDesc.10052", "sort": 102, "reward": "1,-1,2500", "icon": "", "condition": 1052, "prev_id": 1003, "next_id": 1097, "goto": "s:5,u:BuildInfo,id:1002"}, {"id": 1093, "name": "guideTaskDesc.1093", "desc": "guideTaskDesc.10093", "sort": 103, "reward": "1,-1,3000", "icon": "", "condition": 1093, "prev_id": 1079, "next_id": 1094, "goto": "s:5,u:BuildInfo,id:2002"}, {"id": 1063, "name": "guideTaskDesc.1063", "desc": "guideTaskDesc.10063", "sort": 104, "reward": "1,-1,3500", "icon": "", "condition": 1063, "prev_id": 1006, "next_id": 1064, "goto": "s:201,u:FurnInfo,id:RECREATION_009"}, {"id": 1108, "name": "guideTaskDesc.1108", "desc": "guideTaskDesc.10108", "sort": 105, "reward": "1,-1,12000", "icon": "", "condition": 1108, "prev_id": 1006, "next_id": "", "goto": "s:201,u:FurnInfo,id:PENDANT_119"}, {"id": 1107, "name": "guideTaskDesc.1107", "desc": "guideTaskDesc.10107", "sort": 106, "reward": "1,-1,11500", "icon": "", "condition": 1107, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:9001"}, {"id": 1095, "name": "guideTaskDesc.1095", "desc": "guideTaskDesc.10095", "sort": 107, "reward": "1,-1,9000", "icon": "", "condition": 1095, "prev_id": 1014, "next_id": 1104, "goto": "s:3,u:BuildInfo,id:12002"}, {"id": 1027, "name": "guideTaskDesc.1027", "desc": "guideTaskDesc.10027", "sort": 108, "reward": "1,-1,11000", "icon": "", "condition": 1027, "prev_id": 1025, "next_id": 1028, "goto": "s:2,u:BuildInfo,id:10001"}, {"id": 1134, "name": "guideTaskDesc.1134", "desc": "guideTaskDesc.10134", "sort": 109, "reward": "4,9100,35", "icon": "", "condition": 1134, "prev_id": 1022, "next_id": "", "goto": "s:202,u:FurnInfo,id:CARPET_011"}, {"id": 1104, "name": "guideTaskDesc.1104", "desc": "guideTaskDesc.10104", "sort": 110, "reward": "1,-1,3000", "icon": "", "condition": 1104, "prev_id": 1103, "next_id": 1062, "goto": "s:3,u:FoodInfo,id:8"}, {"id": 1151, "name": "guideTaskDesc.1151", "desc": "guideTaskDesc.10151", "sort": 111, "reward": "4,6006,3", "icon": "", "condition": 1151, "prev_id": 1006, "next_id": "", "goto": "s:201,u:FurnInfo,id:TABLE_011"}, {"id": 1044, "name": "guideTaskDesc.1044", "desc": "guideTaskDesc.10044", "sort": 112, "reward": "4,9100,25", "icon": "soup/3003_meigui", "condition": 1044, "prev_id": 1025, "next_id": 1045, "goto": "s:2,u:SoupInfo,id:3003"}, {"id": 1101, "name": "guideTaskDesc.1101", "desc": "guideTaskDesc.10101", "sort": 113, "reward": "1,-1,3000", "icon": "main/xj_icon_zlkr", "condition": 1101, "prev_id": 1098, "next_id": 1093, "goto": "s:1,guide:4@1"}, {"id": 1122, "name": "guideTaskDesc.1122", "desc": "guideTaskDesc.10122", "sort": 114, "reward": "2,-1,10", "icon": "main/dalou_shengji_10", "condition": 1122, "prev_id": 1022, "next_id": "", "goto": "s:1,u:MainInfo,guide:211@10"}, {"id": 1160, "name": "guideTaskDesc.1160", "desc": "guideTaskDesc.10160", "sort": 115, "reward": "4,6005,3", "icon": "", "condition": 1160, "prev_id": 1006, "next_id": "", "goto": "s:201,u:FurnInfo,id:LAVATORY_003"}, {"id": 1045, "name": "guideTaskDesc.1045", "desc": "guideTaskDesc.10045", "sort": 116, "reward": "1,-1,6000", "icon": "soup/3004_qipao", "condition": 1045, "prev_id": 1025, "next_id": 1030, "goto": "s:2,u:SoupInfo,id:3004"}, {"id": 1030, "name": "guideTaskDesc.1030", "desc": "guideTaskDesc.10030", "sort": 117, "reward": "2,-1,5", "icon": "main/xj_icon_tssld", "condition": 1030, "prev_id": 1043, "next_id": 1029, "goto": "s:2,u:<PERSON>mp<PERSON><PERSON>ff"}, {"id": 1169, "name": "guideTaskDesc.1169", "desc": "guideTaskDesc.10169", "sort": 118, "reward": "4,6003,3", "icon": "", "condition": 1169, "prev_id": 1006, "next_id": "", "goto": "s:201,u:FurnInfo,id:RECREATION_010"}, {"id": 1064, "name": "guideTaskDesc.1064", "desc": "guideTaskDesc.10064", "sort": 119, "reward": "1,-1,3700", "icon": "", "condition": 1064, "prev_id": 1022, "next_id": 1099, "goto": "s:202,u:FurnInfo,id:TABLE_010"}, {"id": 1065, "name": "guideTaskDesc.1065", "desc": "guideTaskDesc.10065", "sort": 120, "reward": "1,-1,1700", "icon": "", "condition": 1065, "prev_id": 1006, "next_id": 1066, "goto": "s:201,u:FurnInfo,id:GROUND_002"}, {"id": 1132, "name": "guideTaskDesc.1132", "desc": "guideTaskDesc.10132", "sort": 121, "reward": "1,-1,10000", "icon": "main/dalou_shengji_11", "condition": 1132, "prev_id": 1022, "next_id": "", "goto": "s:1,u:MainInfo,guide:212@11"}, {"id": 1126, "name": "guideTaskDesc.1126", "desc": "guideTaskDesc.10126", "sort": 122, "reward": "1,-1,20000", "icon": "", "condition": 1126, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:14002"}, {"id": 1092, "name": "guideTaskDesc.1092", "desc": "guideTaskDesc.10092", "sort": 123, "reward": "1,-1,8000", "icon": "", "condition": 1092, "prev_id": 1006, "next_id": 1101, "goto": "s:201,u:FurnInfo,id:DECORATE_044"}, {"id": 1029, "name": "guideTaskDesc.1029", "desc": "guideTaskDesc.10029", "sort": 124, "reward": "4,9100,35", "icon": "soup/3005_hongcha", "condition": 1029, "prev_id": 1025, "next_id": "", "goto": "s:2,u:SoupInfo,id:3005"}, {"id": 1110, "name": "guideTaskDesc.1110", "desc": "guideTaskDesc.10110", "sort": 125, "reward": "1,-1,10000", "icon": "main/xj_icon_tslxsy", "condition": 1110, "prev_id": 1073, "next_id": "", "goto": "s:1,u:MainInfo,guide:103@2"}, {"id": 1114, "name": "guideTaskDesc.1114", "desc": "guideTaskDesc.10114", "sort": 126, "reward": "1,-1,5000", "icon": "", "condition": 1114, "prev_id": 1104, "next_id": "", "goto": "s:3,u:FoodInfo,id:9"}, {"id": 1142, "name": "guideTaskDesc.1142", "desc": "guideTaskDesc.10142", "sort": 127, "reward": "2,-1,10", "icon": "main/dalou_shengji_5", "condition": 1142, "prev_id": 1132, "next_id": "", "goto": "s:1,u:MainInfo,guide:213@12"}, {"id": 1115, "name": "guideTaskDesc.1115", "desc": "guideTaskDesc.10115", "sort": 128, "reward": "1,-1,13500", "icon": "", "condition": 1115, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:8001"}, {"id": 1116, "name": "guideTaskDesc.1116", "desc": "guideTaskDesc.10116", "sort": 129, "reward": "1,-1,7000", "icon": "soup/3006_bohe", "condition": 1116, "prev_id": 1030, "next_id": "", "goto": "s:2,u:SoupInfo,id:3006"}, {"id": 1119, "name": "guideTaskDesc.1119", "desc": "guideTaskDesc.10119", "sort": 130, "reward": "1,-1,6000", "icon": "", "condition": 1119, "prev_id": 1114, "next_id": "", "goto": "s:3,u:FoodInfo,id:10"}, {"id": 1120, "name": "guideTaskDesc.1120", "desc": "guideTaskDesc.10120", "sort": 131, "reward": "1,-1,17000", "icon": "", "condition": 1120, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:6002"}, {"id": 1111, "name": "guideTaskDesc.1111", "desc": "guideTaskDesc.10111", "sort": 132, "reward": "1,-1,13000", "icon": "", "condition": 1111, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:7001"}, {"id": 1135, "name": "guideTaskDesc.1135", "desc": "guideTaskDesc.10135", "sort": 133, "reward": "1,-1,25000", "icon": "", "condition": 1135, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:11002"}, {"id": 1049, "name": "guideTaskDesc.1049", "desc": "guideTaskDesc.10049", "sort": 134, "reward": "1,-1,300", "icon": "", "condition": 1049, "prev_id": 1006, "next_id": 1072, "goto": "s:201,u:FurnInfo,id:FLOOR_003"}, {"id": 1173, "name": "guideTaskDesc.1173", "desc": "guideTaskDesc.10173", "sort": 135, "reward": "4,6005,4", "icon": "", "condition": 1173, "prev_id": 1014, "next_id": "", "goto": "s:3,u:BuildInfo,id:5003"}, {"id": 1128, "name": "guideTaskDesc.1128", "desc": "guideTaskDesc.10128", "sort": 136, "reward": "1,-1,16000", "icon": "", "condition": 1128, "prev_id": 1118, "next_id": "", "goto": "s:5,u:BuildInfo,id:13003"}, {"id": 1131, "name": "guideTaskDesc.1131", "desc": "guideTaskDesc.10131", "sort": 137, "reward": "1,-1,17000", "icon": "", "condition": 1131, "prev_id": 1128, "next_id": "", "goto": "s:5,u:BuildInfo,id:15003"}, {"id": 1090, "name": "guideTaskDesc.1090", "desc": "guideTaskDesc.10090", "sort": 138, "reward": "1,-1,2400", "icon": "", "condition": 1090, "prev_id": 1022, "next_id": 1103, "goto": "s:202,u:FurnInfo,id:PENDANT_009"}, {"id": 1121, "name": "guideTaskDesc.1121", "desc": "guideTaskDesc.10121", "sort": 139, "reward": "1,-1,8000", "icon": "soup/3007_niba", "condition": 1121, "prev_id": 1030, "next_id": "", "goto": "s:2,u:SoupInfo,id:3007"}, {"id": 1124, "name": "guideTaskDesc.1124", "desc": "guideTaskDesc.10124", "sort": 140, "reward": "403,-1,5", "icon": "main/xj_icon_tssld", "condition": 1124, "prev_id": 1030, "next_id": "", "goto": "s:2,u:<PERSON>mp<PERSON><PERSON>ff"}, {"id": 1094, "name": "guideTaskDesc.1094", "desc": "guideTaskDesc.10094", "sort": 141, "reward": "1,-1,2500", "icon": "", "condition": 1094, "prev_id": 1022, "next_id": 1095, "goto": "s:202,u:FurnInfo,id:PENDANT_065"}, {"id": 1176, "name": "guideTaskDesc.1176", "desc": "guideTaskDesc.10176", "sort": 142, "reward": "1,-1,15000", "icon": "", "condition": 1176, "prev_id": 1014, "next_id": "", "goto": "s:3,u:BuildInfo,id:13003"}, {"id": 1137, "name": "guideTaskDesc.1137", "desc": "guideTaskDesc.10137", "sort": 143, "reward": "1,-1,18000", "icon": "", "condition": 1137, "prev_id": 1131, "next_id": "", "goto": "s:5,u:BuildInfo,id:11003"}, {"id": 1066, "name": "guideTaskDesc.1066", "desc": "guideTaskDesc.10066", "sort": 144, "reward": "1,-1,1600", "icon": "", "condition": 1066, "prev_id": 1022, "next_id": 1057, "goto": "s:202,u:FurnInfo,id:RECREATION_008"}, {"id": 1105, "name": "guideTaskDesc.1105", "desc": "guideTaskDesc.10105", "sort": 145, "reward": "1,-1,9000", "icon": "", "condition": 1105, "prev_id": 1014, "next_id": "", "goto": "s:3,u:BuildInfo,id:4002"}, {"id": 1062, "name": "guideTaskDesc.1062", "desc": "guideTaskDesc.10062", "sort": 146, "reward": "1,-1,9500", "icon": "", "condition": 1062, "prev_id": 1014, "next_id": 1025, "goto": "s:3,u:BuildInfo,id:1002"}, {"id": 1058, "name": "guideTaskDesc.1058", "desc": "guideTaskDesc.10058", "sort": 147, "reward": "2,-1,5", "icon": "main/dalou_shengji_12", "condition": 1058, "prev_id": 1022, "next_id": 1068, "goto": "s:1,u:MainInfo,guide:208@14"}, {"id": 1144, "name": "guideTaskDesc.1144", "desc": "guideTaskDesc.10144", "sort": 148, "reward": "1,-1,18000", "icon": "", "condition": 1144, "prev_id": 1131, "next_id": "", "goto": "s:5,u:BuildInfo,id:1003"}, {"id": 1139, "name": "guideTaskDesc.1139", "desc": "guideTaskDesc.10139", "sort": 149, "reward": "1,-1,18000", "icon": "", "condition": 1139, "prev_id": 1144, "next_id": "", "goto": "s:5,u:BuildInfo,id:16003"}, {"id": 1190, "name": "guideTaskDesc.1190", "desc": "guideTaskDesc.10190", "sort": 150, "reward": "1,-1,22000", "icon": "", "condition": 1190, "prev_id": 1014, "next_id": "", "goto": "s:3,u:BuildInfo,id:8003"}, {"id": 1125, "name": "guideTaskDesc.1125", "desc": "guideTaskDesc.10125", "sort": 151, "reward": "1,-1,7000", "icon": "", "condition": 1125, "prev_id": 1119, "next_id": "", "goto": "s:3,u:FoodInfo,id:11"}, {"id": 1136, "name": "guideTaskDesc.1136", "desc": "guideTaskDesc.10136", "sort": 152, "reward": "4,9100,75", "icon": "main/xj_icon_tsxfg", "condition": 1136, "prev_id": 1051, "next_id": "", "goto": "s:1,u:MainInfo,guide:101@3"}, {"id": 1127, "name": "guideTaskDesc.1127", "desc": "guideTaskDesc.10127", "sort": 153, "reward": "1,-1,9000", "icon": "soup/3008_shengjiang", "condition": 1127, "prev_id": 1124, "next_id": "", "goto": "s:2,u:SoupInfo,id:3008"}, {"id": 1130, "name": "guideTaskDesc.1130", "desc": "guideTaskDesc.10130", "sort": 154, "reward": "1,-1,8000", "icon": "", "condition": 1130, "prev_id": 1125, "next_id": "", "goto": "s:3,u:FoodInfo,id:12"}, {"id": 1112, "name": "guideTaskDesc.1112", "desc": "guideTaskDesc.10112", "sort": 155, "reward": "4,9100,35", "icon": "", "condition": 1112, "prev_id": 1022, "next_id": "", "goto": "s:202,u:FurnInfo,id:PENDANT_098"}, {"id": 1085, "name": "guideTaskDesc.1085", "desc": "guideTaskDesc.10085", "sort": 156, "reward": "1,-1,1400", "icon": "", "condition": 1085, "prev_id": 1022, "next_id": 1086, "goto": "s:202,u:FurnInfo,id:WALL_004"}, {"id": 1067, "name": "guideTaskDesc.1067", "desc": "guideTaskDesc.10067", "sort": 157, "reward": "1,-1,2100", "icon": "", "condition": 1067, "prev_id": 1022, "next_id": 1058, "goto": "s:202,u:FurnInfo,id:DECORATE_006"}, {"id": 1143, "name": "guideTaskDesc.1143", "desc": "guideTaskDesc.10143", "sort": 158, "reward": "1,-1,12000", "icon": "", "condition": 1143, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:OUTPE_004"}, {"id": 1061, "name": "guideTaskDesc.1061", "desc": "guideTaskDesc.10061", "sort": 159, "reward": "1,-1,2100", "icon": "", "condition": 1061, "prev_id": 1022, "next_id": 1091, "goto": "s:202,u:FurnInfo,id:PENDANT_056"}, {"id": 1086, "name": "guideTaskDesc.1086", "desc": "guideTaskDesc.10086", "sort": 160, "reward": "1,-1,1300", "icon": "", "condition": 1086, "prev_id": 1022, "next_id": 1087, "goto": "s:202,u:FurnInfo,id:CARPET_002"}, {"id": 1054, "name": "guideTaskDesc.1054", "desc": "guideTaskDesc.10054", "sort": 161, "reward": "1,-1,500", "icon": "", "condition": 1054, "prev_id": 1022, "next_id": 1055, "goto": "s:202,u:FurnInfo,id:FLOOR_002"}, {"id": 1148, "name": "guideTaskDesc.1148", "desc": "guideTaskDesc.10148", "sort": 162, "reward": "4,9100,75", "icon": "main/xj_icon_tskll", "condition": 1148, "prev_id": 1099, "next_id": "", "goto": "s:1,u:MainInfo,guide:102@1"}, {"id": 1204, "name": "guideTaskDesc.1204", "desc": "guideTaskDesc.10204", "sort": 163, "reward": "2,-1,5", "icon": "", "condition": 1204, "prev_id": 1068, "next_id": "", "goto": "s:202,u:FurnInfo,id:WALLPAPER_009"}, {"id": 1150, "name": "guideTaskDesc.1150", "desc": "guideTaskDesc.10150", "sort": 164, "reward": "1,-1,16000", "icon": "", "condition": 1150, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:OUTSIT_004"}, {"id": 1157, "name": "guideTaskDesc.1157", "desc": "guideTaskDesc.10157", "sort": 165, "reward": "1,-1,18000", "icon": "", "condition": 1157, "prev_id": 1144, "next_id": "", "goto": "s:5,u:BuildInfo,id:3003"}, {"id": 1129, "name": "guideTaskDesc.1129", "desc": "guideTaskDesc.10129", "sort": 166, "reward": "1,-1,21000", "icon": "", "condition": 1129, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:3002"}, {"id": 1123, "name": "guideTaskDesc.1123", "desc": "guideTaskDesc.10123", "sort": 167, "reward": "1,-1,18000", "icon": "", "condition": 1123, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:10002"}, {"id": 1141, "name": "guideTaskDesc.1141", "desc": "guideTaskDesc.10141", "sort": 168, "reward": "1,-1,26000", "icon": "", "condition": 1141, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:12002"}, {"id": 1182, "name": "guideTaskDesc.1182", "desc": "guideTaskDesc.10182", "sort": 169, "reward": "1,-1,22000", "icon": "", "condition": 1182, "prev_id": 1014, "next_id": "", "goto": "s:3,u:BuildInfo,id:10003"}, {"id": 1133, "name": "guideTaskDesc.1133", "desc": "guideTaskDesc.10133", "sort": 170, "reward": "1,-1,10000", "icon": "soup/3009_meigui", "condition": 1133, "prev_id": 1124, "next_id": "", "goto": "s:2,u:SoupInfo,id:3009"}, {"id": 1140, "name": "guideTaskDesc.1140", "desc": "guideTaskDesc.10140", "sort": 171, "reward": "1,-1,10000", "icon": "", "condition": 1140, "prev_id": 1130, "next_id": "", "goto": "s:3,u:FoodInfo,id:13"}, {"id": 1156, "name": "guideTaskDesc.1156", "desc": "guideTaskDesc.10156", "sort": 172, "reward": "1,-1,20000", "icon": "", "condition": 1156, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:DECORATE_009"}, {"id": 1161, "name": "guideTaskDesc.1161", "desc": "guideTaskDesc.10161", "sort": 173, "reward": "1,-1,20000", "icon": "", "condition": 1161, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:DECORATE_017"}, {"id": 1159, "name": "guideTaskDesc.1159", "desc": "guideTaskDesc.10159", "sort": 174, "reward": "1,-1,20000", "icon": "", "condition": 1159, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:DECORATE_010"}, {"id": 1203, "name": "guideTaskDesc.1203", "desc": "guideTaskDesc.10203", "sort": 175, "reward": "1,-1,21500", "icon": "", "condition": 1203, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:DECORATE_049"}, {"id": 1106, "name": "guideTaskDesc.1106", "desc": "guideTaskDesc.10106", "sort": 176, "reward": "1,-1,2600", "icon": "", "condition": 1106, "prev_id": 1022, "next_id": "", "goto": "s:202,u:FurnInfo,id:PENDANT_112"}, {"id": 1154, "name": "guideTaskDesc.1154", "desc": "guideTaskDesc.10154", "sort": 177, "reward": "1,-1,28000", "icon": "", "condition": 1154, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:8002"}, {"id": 1162, "name": "guideTaskDesc.1162", "desc": "guideTaskDesc.10162", "sort": 178, "reward": "1,-1,29000", "icon": "", "condition": 1162, "prev_id": 1025, "next_id": "", "goto": "s:2,u:BuildInfo,id:7002"}, {"id": 1171, "name": "guideTaskDesc.1171", "desc": "guideTaskDesc.10171", "sort": 179, "reward": "2,-1,15", "icon": "main/dalou_shengji_5", "condition": 1171, "prev_id": 1142, "next_id": "", "goto": "s:1,u:MainInfo,guide:214@13"}, {"id": 1153, "name": "guideTaskDesc.1153", "desc": "guideTaskDesc.10153", "sort": 180, "reward": "1,-1,17000", "icon": "", "condition": 1153, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:RECREATION_014"}, {"id": 1145, "name": "guideTaskDesc.1145", "desc": "guideTaskDesc.10145", "sort": 181, "reward": "403,-1,6", "icon": "main/xj_icon_tssld", "condition": 1145, "prev_id": 1124, "next_id": "", "goto": "s:2,u:<PERSON>mp<PERSON><PERSON>ff"}, {"id": 1146, "name": "guideTaskDesc.1146", "desc": "guideTaskDesc.10146", "sort": 182, "reward": "1,-1,11000", "icon": "soup/3010_taohua", "condition": 1146, "prev_id": 1145, "next_id": "", "goto": "s:2,u:SoupInfo,id:3010"}, {"id": 1149, "name": "guideTaskDesc.1149", "desc": "guideTaskDesc.10149", "sort": 183, "reward": "1,-1,11000", "icon": "", "condition": 1149, "prev_id": 1140, "next_id": "", "goto": "s:3,u:FoodInfo,id:14"}, {"id": 1179, "name": "guideTaskDesc.1179", "desc": "guideTaskDesc.10179", "sort": 184, "reward": "1,-1,20000", "icon": "", "condition": 1179, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:OUTSIT_001"}, {"id": 1174, "name": "guideTaskDesc.1174", "desc": "guideTaskDesc.10174", "sort": 185, "reward": "1,-1,16000", "icon": "", "condition": 1174, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:SIT_028"}, {"id": 1177, "name": "guideTaskDesc.1177", "desc": "guideTaskDesc.10177", "sort": 186, "reward": "1,-1,16000", "icon": "", "condition": 1177, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:CABINET_015"}, {"id": 1087, "name": "guideTaskDesc.1087", "desc": "guideTaskDesc.10087", "sort": 187, "reward": "1,-1,2000", "icon": "", "condition": 1087, "prev_id": 1022, "next_id": 1059, "goto": "s:202,u:FurnInfo,id:WALL_008"}, {"id": 1055, "name": "guideTaskDesc.1055", "desc": "guideTaskDesc.10055", "sort": 188, "reward": "1,-1,1500", "icon": "", "condition": 1055, "prev_id": 1022, "next_id": 1056, "goto": "s:202,u:FurnInfo,id:WALL_003"}, {"id": 1155, "name": "guideTaskDesc.1155", "desc": "guideTaskDesc.10155", "sort": 189, "reward": "1,-1,15000", "icon": "main/xj_icon_tslxsy", "condition": 1155, "prev_id": 1110, "next_id": "", "goto": "s:1,u:MainInfo,guide:103@2"}, {"id": 1152, "name": "guideTaskDesc.1152", "desc": "guideTaskDesc.10152", "sort": 190, "reward": "1,-1,12000", "icon": "soup/3011_niunai", "condition": 1152, "prev_id": 1146, "next_id": "", "goto": "s:2,u:SoupInfo,id:3011"}, {"id": 1185, "name": "guideTaskDesc.1185", "desc": "guideTaskDesc.10185", "sort": 191, "reward": "1,-1,21000", "icon": "", "condition": 1185, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:DECORATE_024"}, {"id": 1181, "name": "guideTaskDesc.1181", "desc": "guideTaskDesc.10181", "sort": 192, "reward": "1,-1,20000", "icon": "", "condition": 1181, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:PENDANT_041"}, {"id": 1158, "name": "guideTaskDesc.1158", "desc": "guideTaskDesc.10158", "sort": 193, "reward": "1,-1,12000", "icon": "", "condition": 1158, "prev_id": 1149, "next_id": "", "goto": "s:3,u:FoodInfo,id:15"}, {"id": 1196, "name": "guideTaskDesc.1196", "desc": "guideTaskDesc.10196", "sort": 194, "reward": "1,-1,23000", "icon": "", "condition": 1196, "prev_id": 1014, "next_id": "", "goto": "s:3,u:BuildInfo,id:12003"}, {"id": 1163, "name": "guideTaskDesc.1163", "desc": "guideTaskDesc.10163", "sort": 195, "reward": "1,-1,13000", "icon": "soup/3012_kafei", "condition": 1163, "prev_id": 1152, "next_id": "", "goto": "s:2,u:SoupInfo,id:3012"}, {"id": 1165, "name": "guideTaskDesc.1165", "desc": "guideTaskDesc.10165", "sort": 196, "reward": "1,-1,30000", "icon": "main/xj_icon_tsxfg", "condition": 1165, "prev_id": 1136, "next_id": "", "goto": "s:1,u:MainInfo,guide:101@3"}, {"id": 1200, "name": "guideTaskDesc.1200", "desc": "guideTaskDesc.10200", "sort": 197, "reward": "1,-1,22000", "icon": "", "condition": 1200, "prev_id": 1014, "next_id": "", "goto": "s:3,u:BuildInfo,id:3003"}, {"id": 1166, "name": "guideTaskDesc.1166", "desc": "guideTaskDesc.10166", "sort": 198, "reward": "1,-1,13000", "icon": "", "condition": 1166, "prev_id": 1158, "next_id": "", "goto": "s:3,u:FoodInfo,id:16"}, {"id": 1167, "name": "guideTaskDesc.1167", "desc": "guideTaskDesc.10167", "sort": 199, "reward": "403,-1,7", "icon": "main/xj_icon_tssld", "condition": 1167, "prev_id": 1145, "next_id": "", "goto": "s:2,u:<PERSON>mp<PERSON><PERSON>ff"}, {"id": 1170, "name": "guideTaskDesc.1170", "desc": "guideTaskDesc.10170", "sort": 200, "reward": "1,-1,14000", "icon": "soup/3013_naicha", "condition": 1170, "prev_id": 1167, "next_id": "", "goto": "s:2,u:SoupInfo,id:3013"}, {"id": 1202, "name": "guideTaskDesc.1202", "desc": "guideTaskDesc.10202", "sort": 201, "reward": "1,-1,23000", "icon": "", "condition": 1202, "prev_id": 1014, "next_id": "", "goto": "s:3,u:BuildInfo,id:4003"}, {"id": 1175, "name": "guideTaskDesc.1175", "desc": "guideTaskDesc.10175", "sort": 202, "reward": "4,6003,4", "icon": "", "condition": 1175, "prev_id": 1166, "next_id": "", "goto": "s:3,u:FoodInfo,id:17"}, {"id": 1180, "name": "guideTaskDesc.1180", "desc": "guideTaskDesc.10180", "sort": 203, "reward": "4,7001,3", "icon": "soup/3014_fengmi", "condition": 1180, "prev_id": 1170, "next_id": "", "goto": "s:2,u:SoupInfo,id:3014"}, {"id": 1172, "name": "guideTaskDesc.1172", "desc": "guideTaskDesc.10172", "sort": 204, "reward": "1,-1,15000", "icon": "", "condition": 1172, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:CARPET_004"}, {"id": 1184, "name": "guideTaskDesc.1184", "desc": "guideTaskDesc.10184", "sort": 205, "reward": "4,6003,4", "icon": "", "condition": 1184, "prev_id": 1175, "next_id": "", "goto": "s:3,u:FoodInfo,id:18"}, {"id": 1187, "name": "guideTaskDesc.1187", "desc": "guideTaskDesc.10187", "sort": 206, "reward": "4,7002,3", "icon": "soup/3015_guihua", "condition": 1187, "prev_id": 1180, "next_id": "", "goto": "s:2,u:SoupInfo,id:3015"}, {"id": 1197, "name": "guideTaskDesc.1197", "desc": "guideTaskDesc.10197", "sort": 207, "reward": "1,-1,20000", "icon": "", "condition": 1197, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:PENDANT_011"}, {"id": 1188, "name": "guideTaskDesc.1188", "desc": "guideTaskDesc.10188", "sort": 208, "reward": "4,6005,4", "icon": "", "condition": 1188, "prev_id": 1184, "next_id": "", "goto": "s:3,u:FoodInfo,id:19"}, {"id": 1183, "name": "guideTaskDesc.1183", "desc": "guideTaskDesc.10183", "sort": 209, "reward": "1,-1,20000", "icon": "", "condition": 1183, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:OUTPE_007"}, {"id": 1193, "name": "guideTaskDesc.1193", "desc": "guideTaskDesc.10193", "sort": 210, "reward": "4,6006,4", "icon": "", "condition": 1193, "prev_id": 1188, "next_id": "", "goto": "s:3,u:FoodInfo,id:20"}, {"id": 1198, "name": "guideTaskDesc.1198", "desc": "guideTaskDesc.10198", "sort": 211, "reward": "4,7004,3", "icon": "soup/3017_guozhi", "condition": 1198, "prev_id": 1192, "next_id": "", "goto": "s:2,u:SoupInfo,id:3017"}, {"id": 1201, "name": "guideTaskDesc.1201", "desc": "guideTaskDesc.10201", "sort": 212, "reward": "1,-1,21000", "icon": "", "condition": 1201, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:WALL_038"}, {"id": 1191, "name": "guideTaskDesc.1191", "desc": "guideTaskDesc.10191", "sort": 213, "reward": "403,-1,8", "icon": "main/xj_icon_tssld", "condition": 1191, "prev_id": 1167, "next_id": "", "goto": "s:2,u:<PERSON>mp<PERSON><PERSON>ff"}, {"id": 1192, "name": "guideTaskDesc.1192", "desc": "guideTaskDesc.10192", "sort": 214, "reward": "4,7002,3", "icon": "soup/3016_aicao", "condition": 1192, "prev_id": 1191, "next_id": "", "goto": "s:2,u:SoupInfo,id:3016"}, {"id": 1189, "name": "guideTaskDesc.1189", "desc": "guideTaskDesc.10189", "sort": 215, "reward": "1,-1,21000", "icon": "", "condition": 1189, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:PENDANT_047"}, {"id": 1199, "name": "guideTaskDesc.1199", "desc": "guideTaskDesc.10199", "sort": 216, "reward": "4,6003,4", "icon": "", "condition": 1199, "prev_id": 1193, "next_id": "", "goto": "s:3,u:FoodInfo,id:21"}, {"id": 1147, "name": "guideTaskDesc.1147", "desc": "guideTaskDesc.10147", "sort": 217, "reward": "1,-1,12000", "icon": "", "condition": 1147, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:WALL_024"}, {"id": 1186, "name": "guideTaskDesc.1186", "desc": "guideTaskDesc.10186", "sort": 218, "reward": "1,-1,20000", "icon": "", "condition": 1186, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:PENDANT_048"}, {"id": 1178, "name": "guideTaskDesc.1178", "desc": "guideTaskDesc.10178", "sort": 219, "reward": "1,-1,20000", "icon": "", "condition": 1178, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:PENDANT_045"}, {"id": 1168, "name": "guideTaskDesc.1168", "desc": "guideTaskDesc.10168", "sort": 220, "reward": "1,-1,20000", "icon": "", "condition": 1168, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:WALL_034"}, {"id": 1195, "name": "guideTaskDesc.1195", "desc": "guideTaskDesc.10195", "sort": 221, "reward": "1,-1,21000", "icon": "", "condition": 1195, "prev_id": 1171, "next_id": "", "goto": "s:302,u:FurnInfo,id:WALL_036"}, {"id": 1194, "name": "guideTaskDesc.1194", "desc": "guideTaskDesc.10194", "sort": 222, "reward": "1,-1,20000", "icon": "", "condition": 1194, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:PENDANT_010"}, {"id": 1164, "name": "guideTaskDesc.1164", "desc": "guideTaskDesc.10164", "sort": 223, "reward": "4,6004,3", "icon": "", "condition": 1164, "prev_id": 1142, "next_id": "", "goto": "s:301,u:FurnInfo,id:PENDANT_036"}]