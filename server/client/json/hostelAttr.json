[{"id": 1001, "type": 1, "cost": "2,-1,10", "value": "22,28", "value_1": "60,80", "value_2": "25,45,0.6", "income": "", "cookieflow": ""}, {"id": 1002, "type": 1, "cost": "2,-1,20", "value": "21,27", "value_1": "63,83", "value_2": "26,46,0.59", "income": "", "cookieflow": ""}, {"id": 1003, "type": 1, "cost": "2,-1,40", "value": "20,26", "value_1": "66,86", "value_2": "27,47,0.57", "income": "", "cookieflow": ""}, {"id": 1004, "type": 1, "cost": "2,-1,60", "value": "19,25", "value_1": "69,89", "value_2": "28,48,0.56", "income": "", "cookieflow": ""}, {"id": 1005, "type": 1, "cost": "2,-1,90", "value": "18,24", "value_1": "72,92", "value_2": "29,49,0.54", "income": "", "cookieflow": ""}, {"id": 1006, "type": 1, "cost": "2,-1,120", "value": "17,23", "value_1": "75,95", "value_2": "30,50,0.53", "income": "", "cookieflow": ""}, {"id": 1007, "type": 1, "cost": "2,-1,160", "value": "16,22", "value_1": "78,98", "value_2": "32,52,0.51", "income": "", "cookieflow": ""}, {"id": 1008, "type": 1, "cost": "2,-1,200", "value": "15,21", "value_1": "81,101", "value_2": "34,54,0.5", "income": "", "cookieflow": ""}, {"id": 1009, "type": 1, "cost": "2,-1,240", "value": "14,20", "value_1": "84,104", "value_2": "36,56,0.48", "income": "", "cookieflow": ""}, {"id": 1010, "type": 1, "cost": "2,-1,280", "value": "13,19", "value_1": "87,107", "value_2": "38,58,0.47", "income": "", "cookieflow": ""}, {"id": 1011, "type": 1, "cost": "2,-1,320", "value": "12,18", "value_1": "90,110", "value_2": "40,60,0.45", "income": "", "cookieflow": ""}, {"id": 1012, "type": 1, "cost": "2,-1,360", "value": "11,17", "value_1": "93,113", "value_2": "42,62,0.44", "income": "", "cookieflow": ""}, {"id": 1013, "type": 1, "cost": "2,-1,400", "value": "10,16", "value_1": "96,116", "value_2": "44,64,0.42", "income": "", "cookieflow": ""}, {"id": 1014, "type": 1, "cost": "2,-1,440", "value": "9,15", "value_1": "99,119", "value_2": "46,66,0.41", "income": "", "cookieflow": ""}, {"id": 1015, "type": 1, "cost": "2,-1,490", "value": "8,14", "value_1": "102,122", "value_2": "48,68,0.39", "income": "", "cookieflow": ""}, {"id": 1016, "type": 1, "cost": "2,-1,540", "value": "7,13", "value_1": "105,125", "value_2": "50,70,0.38", "income": "", "cookieflow": ""}, {"id": 1017, "type": 1, "cost": "2,-1,590", "value": "6,12", "value_1": "108,128", "value_2": "52,72,0.36", "income": "", "cookieflow": ""}, {"id": 1018, "type": 1, "cost": "2,-1,640", "value": "6,12", "value_1": "108,128", "value_2": "52,72,0.35", "income": "5,50|3,50", "cookieflow": ""}, {"id": 1019, "type": 1, "cost": "2,-1,690", "value": "5,11", "value_1": "111,131", "value_2": "54,74,0.33", "income": "5,50|3,50", "cookieflow": ""}, {"id": 1020, "type": 1, "cost": "2,-1,750", "value": "5,11", "value_1": "111,131", "value_2": "54,74,0.32", "income": "5,50|3,50|2,60", "cookieflow": 15}, {"id": 1021, "type": 1, "cost": "2,-1,810", "value": "4,10", "value_1": "114,134", "value_2": "56,76,0.3", "income": "5,50|3,50|2,60", "cookieflow": 15}, {"id": 1022, "type": 1, "cost": "2,-1,870", "value": "3,9", "value_1": "117,137", "value_2": "58,78,0.3", "income": "5,50|3,50|2,60", "cookieflow": 15}, {"id": 1023, "type": 1, "cost": "2,-1,940", "value": "3,9", "value_1": "117,137", "value_2": "58,78,0.3", "income": "5,50|3,50|2,60|6,70", "cookieflow": 35}, {"id": 1024, "type": 1, "cost": "2,-1,1020", "value": "2,8", "value_1": "120,140", "value_2": "60,80,0.3", "income": "5,50|3,50|2,60|6,70", "cookieflow": 35}, {"id": 1025, "type": 1, "cost": "", "value": "2,8", "value_1": "120,140", "value_2": "62,82,0.3", "income": "5,50|3,50|2,60|6,70|8,80", "cookieflow": 70}, {"id": 2001, "type": 2, "cost": "2,-1,10", "value": "3,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2002, "type": 2, "cost": "2,-1,20", "value": "3.2,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2003, "type": 2, "cost": "2,-1,40", "value": "3.4,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2004, "type": 2, "cost": "2,-1,60", "value": "3.6,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2005, "type": 2, "cost": "2,-1,90", "value": "3.8,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2006, "type": 2, "cost": "2,-1,120", "value": "4,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2007, "type": 2, "cost": "2,-1,160", "value": "4.3,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2008, "type": 2, "cost": "2,-1,200", "value": "4.6,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2009, "type": 2, "cost": "2,-1,240", "value": "4.9,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2010, "type": 2, "cost": "2,-1,280", "value": "5.2,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2011, "type": 2, "cost": "2,-1,330", "value": "5.6,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2012, "type": 2, "cost": "2,-1,380", "value": "6,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2013, "type": 2, "cost": "2,-1,430", "value": "6.4,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2014, "type": 2, "cost": "2,-1,480", "value": "6.8,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2015, "type": 2, "cost": "2,-1,530", "value": "7.2,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2016, "type": 2, "cost": "2,-1,580", "value": "7.6,0", "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 2017, "type": 2, "cost": "2,-1,640", "value": "7.6,0", "value_1": "", "value_2": "", "income": "201,50", "cookieflow": ""}, {"id": 2018, "type": 2, "cost": "2,-1,700", "value": "7.6,0", "value_1": "", "value_2": "", "income": "201,50|202,50", "cookieflow": ""}, {"id": 2019, "type": 2, "cost": "2,-1,770", "value": "7.6,0", "value_1": "", "value_2": "", "income": "201,50|202,50|301,60", "cookieflow": ""}, {"id": 2020, "type": 2, "cost": "2,-1,840", "value": "8,0", "value_1": "", "value_2": "", "income": "201,50|202,50|301,60", "cookieflow": ""}, {"id": 2021, "type": 2, "cost": "2,-1,910", "value": "8,0", "value_1": "", "value_2": "", "income": "201,50|202,50|301,60|302,60", "cookieflow": ""}, {"id": 2022, "type": 2, "cost": "2,-1,990", "value": "8,0", "value_1": "", "value_2": "", "income": "201,50|202,50|301,60|302,60|401,70", "cookieflow": ""}, {"id": 2023, "type": 2, "cost": "2,-1,1070", "value": "8,0", "value_1": "", "value_2": "", "income": "201,50|202,50|301,60|302,60|401,70|402,70", "cookieflow": ""}, {"id": 2024, "type": 2, "cost": "2,-1,1160", "value": "8.4,0", "value_1": "", "value_2": "", "income": "201,50|202,50|301,60|302,60|401,70|402,70", "cookieflow": ""}, {"id": 2025, "type": 2, "cost": "", "value": "8.8,0", "value_1": "", "value_2": "", "income": "201,50|202,50|301,60|302,60|401,70|402,70", "cookieflow": ""}, {"id": 3001, "type": 3, "cost": "2,-1,5", "value": 50, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3002, "type": 3, "cost": "2,-1,10", "value": 450, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3003, "type": 3, "cost": "2,-1,25", "value": 1200, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3004, "type": 3, "cost": "2,-1,50", "value": 2000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3005, "type": 3, "cost": "2,-1,80", "value": 3000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3006, "type": 3, "cost": "2,-1,110", "value": 4200, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3007, "type": 3, "cost": "2,-1,140", "value": 5800, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3008, "type": 3, "cost": "2,-1,170", "value": 7500, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3009, "type": 3, "cost": "2,-1,200", "value": 9000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3010, "type": 3, "cost": "2,-1,230", "value": 13000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3011, "type": 3, "cost": "2,-1,260", "value": 16000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3012, "type": 3, "cost": "2,-1,290", "value": 20000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3013, "type": 3, "cost": "2,-1,320", "value": 25000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3014, "type": 3, "cost": "2,-1,350", "value": 30000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3015, "type": 3, "cost": "2,-1,390", "value": 36000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3016, "type": 3, "cost": "2,-1,440", "value": 42000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3017, "type": 3, "cost": "2,-1,490", "value": 49000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3018, "type": 3, "cost": "2,-1,550", "value": 56000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3019, "type": 3, "cost": "2,-1,620", "value": 64000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3020, "type": 3, "cost": "2,-1,700", "value": 73000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3021, "type": 3, "cost": "2,-1,790", "value": 83000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3022, "type": 3, "cost": "2,-1,880", "value": 93000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3023, "type": 3, "cost": "2,-1,980", "value": 103000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3024, "type": 3, "cost": "2,-1,1090", "value": 114000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}, {"id": 3025, "type": 3, "cost": "", "value": 126000, "value_1": "", "value_2": "", "income": "", "cookieflow": ""}]