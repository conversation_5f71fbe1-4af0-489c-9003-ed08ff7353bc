[{"id": 1000, "desc": "everydayTaskDesc.1000", "type": 0, "reward": "2,-1,20", "heart_add_factor": "", "condition": 3164}, {"id": 1001, "desc": "everydayTaskDesc.1001", "type": 1, "reward": "401,-1,20", "heart_add_factor": "", "condition": 3000}, {"id": 2001, "desc": "everydayTaskDesc.2001", "type": 2, "reward": "1,-1,600", "heart_add_factor": 4, "condition": 3001}, {"id": 2002, "desc": "everydayTaskDesc.2001", "type": 2, "reward": "1,-1,800", "heart_add_factor": 6, "condition": 3002}, {"id": 2003, "desc": "everydayTaskDesc.2001", "type": 2, "reward": "1,-1,1000", "heart_add_factor": 8, "condition": 3003}, {"id": 3001, "desc": "everydayTaskDesc.3001", "type": 3, "reward": "1,-1,300", "heart_add_factor": 2, "condition": 3004}, {"id": 3002, "desc": "everydayTaskDesc.3002", "type": 3, "reward": "1,-1,600", "heart_add_factor": 4, "condition": 3005}, {"id": 3003, "desc": "everydayTaskDesc.3003", "type": 3, "reward": "1,-1,900", "heart_add_factor": 6, "condition": 3006}, {"id": 4001, "desc": "everydayTaskDesc.4001", "type": 4, "reward": "1,-1,200", "heart_add_factor": 1.3, "condition": 3007}, {"id": 4002, "desc": "everydayTaskDesc.4002", "type": 4, "reward": "1,-1,400", "heart_add_factor": 2.6, "condition": 3008}, {"id": 4003, "desc": "everydayTaskDesc.4003", "type": 4, "reward": "1,-1,800", "heart_add_factor": 5.3, "condition": 3009}, {"id": 5001, "desc": "everydayTaskDesc.5001", "type": 5, "reward": "1,-1,200", "heart_add_factor": 1.6, "condition": 3010}, {"id": 5002, "desc": "everydayTaskDesc.5002", "type": 5, "reward": "1,-1,500", "heart_add_factor": 3, "condition": 3011}, {"id": 5003, "desc": "everydayTaskDesc.5003", "type": 5, "reward": "1,-1,1000", "heart_add_factor": 6.3, "condition": 3012}, {"id": 6001, "desc": "everydayTaskDesc.6001", "type": 6, "reward": "1,-1,200", "heart_add_factor": 1, "condition": 3013}, {"id": 6002, "desc": "everydayTaskDesc.6002", "type": 6, "reward": "1,-1,500", "heart_add_factor": 3.6, "condition": 3014}, {"id": 6003, "desc": "everydayTaskDesc.6003", "type": 6, "reward": "1,-1,1000", "heart_add_factor": 7, "condition": 3015}, {"id": 7001, "desc": "everydayTaskDesc.7001", "type": 7, "reward": "4,9100,3", "heart_add_factor": "", "condition": 3016}, {"id": 7002, "desc": "everydayTaskDesc.7002", "type": 7, "reward": "4,9100,4", "heart_add_factor": "", "condition": 3017}, {"id": 7003, "desc": "everydayTaskDesc.7003", "type": 7, "reward": "4,9100,5", "heart_add_factor": "", "condition": 3018}, {"id": 8001, "desc": "everydayTaskDesc.8001", "type": 8, "reward": "2,-1,5", "heart_add_factor": "", "condition": 3019}, {"id": 8002, "desc": "everydayTaskDesc.8002", "type": 8, "reward": "2,-1,10", "heart_add_factor": "", "condition": 3020}, {"id": 8003, "desc": "everydayTaskDesc.8003", "type": 8, "reward": "2,-1,10", "heart_add_factor": "", "condition": 3021}, {"id": 9001, "desc": "everydayTaskDesc.9001", "type": 9, "reward": "1,-1,200", "heart_add_factor": 2.2, "condition": 3022}, {"id": 9002, "desc": "everydayTaskDesc.9001", "type": 9, "reward": "1,-1,500", "heart_add_factor": 5.3, "condition": 3023}, {"id": 9003, "desc": "everydayTaskDesc.9001", "type": 9, "reward": "1,-1,1000", "heart_add_factor": 10, "condition": 3024}, {"id": 10001, "desc": "everydayTaskDesc.10001", "type": 10, "reward": "1,-1,250", "heart_add_factor": 2.8, "condition": 3101}, {"id": 10002, "desc": "everydayTaskDesc.10002", "type": 10, "reward": "1,-1,300", "heart_add_factor": 3, "condition": 3102}, {"id": 10003, "desc": "everydayTaskDesc.10003", "type": 10, "reward": "1,-1,350", "heart_add_factor": 3.2, "condition": 3103}, {"id": 11001, "desc": "everydayTaskDesc.11001", "type": 11, "reward": "1,-1,250", "heart_add_factor": 2.8, "condition": 3111}, {"id": 11002, "desc": "everydayTaskDesc.11002", "type": 11, "reward": "1,-1,350", "heart_add_factor": 3.2, "condition": 3112}, {"id": 12001, "desc": "everydayTaskDesc.12001", "type": 12, "reward": "2,-1,20", "heart_add_factor": "", "condition": 3121}, {"id": 13001, "desc": "everydayTaskDesc.13001", "type": 13, "reward": "1,-1,500", "heart_add_factor": 4, "condition": 3131}, {"id": 14001, "desc": "everydayTaskDesc.14001", "type": 14, "reward": "1,-1,400", "heart_add_factor": 3.5, "condition": 3141}, {"id": 15001, "desc": "everydayTaskDesc.15001", "type": 15, "reward": "1,-1,800", "heart_add_factor": 6.5, "condition": 3151}, {"id": 15002, "desc": "everydayTaskDesc.15002", "type": 15, "reward": "1,-1,1200", "heart_add_factor": 7.5, "condition": 3152}, {"id": 16001, "desc": "everydayTaskDesc.16001", "type": 16, "reward": "1,-1,450", "heart_add_factor": 3.75, "condition": 3161}, {"id": 16002, "desc": "everydayTaskDesc.16002", "type": 16, "reward": "1,-1,450", "heart_add_factor": 4, "condition": 3162}, {"id": 16003, "desc": "everydayTaskDesc.16003", "type": 16, "reward": "1,-1,550", "heart_add_factor": 4.25, "condition": 3163}, {"id": 17001, "desc": "everydayTaskDesc.17001", "type": 17, "reward": "1,-1,800", "heart_add_factor": 5, "condition": 3171}, {"id": 17002, "desc": "everydayTaskDesc.17002", "type": 17, "reward": "2,-1,5", "heart_add_factor": "", "condition": 3172}, {"id": 17003, "desc": "everydayTaskDesc.17003", "type": 17, "reward": "2,-1,10", "heart_add_factor": "", "condition": 3173}, {"id": 18001, "desc": "everydayTaskDesc.18001", "type": 18, "reward": "1,-1,450", "heart_add_factor": 3.5, "condition": 3181}, {"id": 18002, "desc": "everydayTaskDesc.18002", "type": 18, "reward": "1,-1,650", "heart_add_factor": 5, "condition": 3182}, {"id": 19001, "desc": "everydayTaskDesc.19001", "type": 19, "reward": "1,-1,400", "heart_add_factor": 3.25, "condition": 3191}, {"id": 19002, "desc": "everydayTaskDesc.19002", "type": 19, "reward": "1,-1,1000", "heart_add_factor": 6.5, "condition": 3192}, {"id": 20001, "desc": "everydayTaskDesc.20001", "type": 20, "reward": "1,-1,450", "heart_add_factor": 3.25, "condition": 3201}, {"id": 20002, "desc": "everydayTaskDesc.20002", "type": 20, "reward": "1,-1,1200", "heart_add_factor": 3.25, "condition": 3202}, {"id": 20003, "desc": "everydayTaskDesc.20003", "type": 20, "reward": "1,-1,450", "heart_add_factor": 3.25, "condition": 3201}, {"id": 20004, "desc": "everydayTaskDesc.20004", "type": 20, "reward": "1,-1,450", "heart_add_factor": 3.25, "condition": 3201}, {"id": 20005, "desc": "everydayTaskDesc.20005", "type": 20, "reward": "1,-1,450", "heart_add_factor": 3.25, "condition": 3201}, {"id": 21001, "desc": "everydayTaskDesc.21001", "type": 21, "reward": "1,-1,500", "heart_add_factor": 3.3, "condition": 3211}, {"id": 22001, "desc": "everydayTaskDesc.22001", "type": 22, "reward": "1,-1,450", "heart_add_factor": 2, "condition": 3307}, {"id": 22002, "desc": "everydayTaskDesc.22002", "type": 22, "reward": "1,-1,550", "heart_add_factor": 4, "condition": 3308}, {"id": 22003, "desc": "everydayTaskDesc.22003", "type": 22, "reward": "1,-1,650", "heart_add_factor": 6, "condition": 3309}, {"id": 22004, "desc": "everydayTaskDesc.22004", "type": 22, "reward": "1,-1,750", "heart_add_factor": 8, "condition": 3310}]