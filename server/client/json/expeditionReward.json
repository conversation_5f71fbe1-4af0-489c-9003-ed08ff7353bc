[{"id": 1, "output": "1,-1,5", "extra_output_rate": 0, "extra_output": "", "extra_output_weight": "", "output_rate": 30, "lv_limit": 2}, {"id": 2, "output": "1,-1,10", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 4}, {"id": 3, "output": "1,-1,20", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 5}, {"id": 4, "output": "1,-1,30", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 6}, {"id": 5, "output": "1,-1,40", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 7}, {"id": 6, "output": "1,-1,50", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 8}, {"id": 7, "output": "1,-1,60", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 9}, {"id": 8, "output": "1,-1,70", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 10}, {"id": 9, "output": "1,-1,80", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 10}, {"id": 10, "output": "1,-1,90", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 11}, {"id": 11, "output": "1,-1,100", "extra_output_rate": 0.01, "extra_output": "2,-1,1", "extra_output_weight": 1, "output_rate": 30, "lv_limit": 0}]