[{"id": 1101, "desc": "holidayTaskDesc.34", "reward": "801,1101,1|2,-1,5", "condition": 10111}, {"id": 1102, "desc": "holidayTaskDesc.10", "reward": "801,1101,2|401,-1,5", "condition": 10112}, {"id": 1103, "desc": "holidayTaskDesc.75", "reward": "801,1101,2|4,9100,10", "condition": 10113}, {"id": 1104, "desc": "holidayTaskDesc.23", "reward": "801,1101,2|4,9201,1", "condition": 10114}, {"id": 1201, "desc": "holidayTaskDesc.34", "reward": "801,1101,1|2,-1,10", "condition": 10121}, {"id": 1202, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|401,-1,10", "condition": 10122}, {"id": 1203, "desc": "holidayTaskDesc.23", "reward": "801,1101,2|4,9100,15", "condition": 10123}, {"id": 1204, "desc": "holidayTaskDesc.35", "reward": "801,1101,2|4,9201,2", "condition": 10124}, {"id": 1301, "desc": "holidayTaskDesc.10", "reward": "801,1101,2|2,-1,15", "condition": 10131}, {"id": 1302, "desc": "holidayTaskDesc.31", "reward": "801,1101,3|401,-1,10", "condition": 10132}, {"id": 1303, "desc": "holidayTaskDesc.16", "reward": "801,1101,3|4,9100,25", "condition": 10133}, {"id": 1304, "desc": "holidayTaskDesc.38", "reward": "801,1101,3|4,9201,3", "condition": 10134}, {"id": 1401, "desc": "holidayTaskDesc.34", "reward": "801,1101,1|2,-1,20", "condition": 10141}, {"id": 1402, "desc": "holidayTaskDesc.75", "reward": "801,1101,1|401,-1,5", "condition": 10142}, {"id": 1403, "desc": "holidayTaskDesc.23", "reward": "801,1101,1|4,9100,10", "condition": 10143}, {"id": 1404, "desc": "holidayTaskDesc.70_200003", "reward": "801,1101,3|4,9201,1", "condition": 10144}, {"id": 1501, "desc": "holidayTaskDesc.34", "reward": "801,1101,1|2,-1,25", "condition": 10151}, {"id": 1502, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|401,-1,5", "condition": 10152}, {"id": 1503, "desc": "holidayTaskDesc.38", "reward": "801,1101,1|4,9100,15", "condition": 10153}, {"id": 1504, "desc": "holidayTaskDesc.70_200002", "reward": "801,1101,4|4,9201,1", "condition": 10154}, {"id": 1601, "desc": "holidayTaskDesc.23", "reward": "801,1101,1|2,-1,30", "condition": 10161}, {"id": 1602, "desc": "holidayTaskDesc.16", "reward": "801,1101,1|401,-1,5", "condition": 10162}, {"id": 1603, "desc": "holidayTaskDesc.35", "reward": "801,1101,1|4,9100,10", "condition": 10163}, {"id": 1604, "desc": "holidayTaskDesc.70_200003", "reward": "801,1101,4|4,9201,1", "condition": 10164}, {"id": 1701, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|2,-1,35", "condition": 10171}, {"id": 1702, "desc": "holidayTaskDesc.23", "reward": "801,1101,1|401,-1,10", "condition": 10172}, {"id": 1703, "desc": "holidayTaskDesc.38", "reward": "801,1101,1|4,9100,15", "condition": 10173}, {"id": 1704, "desc": "holidayTaskDesc.70_200002", "reward": "801,1101,4|4,9201,1", "condition": 10174}, {"id": "2101", "desc": "holidayTaskDesc.10", "reward": "801,1201,3|4,9100,25", "condition": 10211}, {"id": "2102", "desc": "holidayTaskDesc.35", "reward": "801,1201,3|401,-1,10", "condition": 10212}, {"id": "2103", "desc": "holidayTaskDesc.84", "reward": "801,1201,3|2,-1,5", "condition": 10213}, {"id": "2201", "desc": "holidayTaskDesc.83", "reward": "801,1201,3|4,9100,25", "condition": 10221}, {"id": "2202", "desc": "holidayTaskDesc.31", "reward": "801,1201,3|401,-1,10", "condition": 10222}, {"id": "2203", "desc": "holidayTaskDesc.86", "reward": "801,1201,3|4,9201,2", "condition": 10223}, {"id": "2301", "desc": "holidayTaskDesc.10", "reward": "801,1201,1|4,9100,35", "condition": 10231}, {"id": "2302", "desc": "holidayTaskDesc.75", "reward": "801,1201,2|401,-1,15", "condition": 10232}, {"id": "2303", "desc": "holidayTaskDesc.23", "reward": "801,1201,2|2,-1,10", "condition": 10233}, {"id": "2401", "desc": "holidayTaskDesc.83", "reward": "801,1201,2|4,9007,1", "condition": 10241}, {"id": "2402", "desc": "holidayTaskDesc.38", "reward": "801,1201,1|4,6003,2", "condition": 10242}, {"id": "2403", "desc": "holidayTaskDesc.70_200003", "reward": "801,1201,2|4,7005,1", "condition": 10243}, {"id": "2501", "desc": "holidayTaskDesc.85", "reward": "801,1201,2|4,9001,1", "condition": 10251}, {"id": "2502", "desc": "holidayTaskDesc.86", "reward": "801,1201,2|4,9201,3", "condition": 10252}, {"id": "2503", "desc": "holidayTaskDesc.33", "reward": "801,1201,2|401,-1,15", "condition": 10253}, {"id": "2601", "desc": "holidayTaskDesc.10", "reward": "801,1201,1|4,9100,50", "condition": 10261}, {"id": "2602", "desc": "holidayTaskDesc.70_200003", "reward": "801,1201,2|4,9006,1", "condition": 10262}, {"id": "2603", "desc": "holidayTaskDesc.70_200004", "reward": "801,1201,3|401,-1,10", "condition": 10263}, {"id": "2701", "desc": "holidayTaskDesc.83", "reward": "801,1201,2|402,-1,25", "condition": 10271}, {"id": "2702", "desc": "holidayTaskDesc.23", "reward": "801,1201,3|2,-1,15", "condition": 10272}, {"id": "2703", "desc": "holidayTaskDesc.16", "reward": "801,1201,1|4,6004,2", "condition": 10273}, {"id": "2801", "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,7005,2", "condition": 10281}, {"id": "2802", "desc": "holidayTaskDesc.38", "reward": "801,1201,1|4,7001,2", "condition": 10282}, {"id": "2803", "desc": "holidayTaskDesc.33", "reward": "801,1201,2|4,9002,1", "condition": 10283}, {"id": "2901", "desc": "holidayTaskDesc.23", "reward": "801,1201,2|2,-1,30", "condition": 10291}, {"id": "2902", "desc": "holidayTaskDesc.70_200003", "reward": "801,1201,2|4,9002,2", "condition": 10292}, {"id": "2903", "desc": "holidayTaskDesc.33", "reward": "801,1201,1|401,-1,20", "condition": 10293}, {"id": 3001, "desc": "holidayTaskDesc.104", "reward": "4,9205,1", "condition": 108001}, {"id": 3002, "desc": "holidayTaskDesc.104", "reward": "4,9205,2", "condition": 108002}, {"id": 3003, "desc": "holidayTaskDesc.104", "reward": "4,8242,1", "condition": 108003}, {"id": 3004, "desc": "holidayTaskDesc.104", "reward": "4,8237,1", "condition": 108004}, {"id": 3005, "desc": "holidayTaskDesc.104", "reward": "4,9206,3", "condition": 108005}, {"id": 3006, "desc": "holidayTaskDesc.104", "reward": "4,8025,1", "condition": 108006}, {"id": 3007, "desc": "holidayTaskDesc.104", "reward": "4,8027,1", "condition": 108007}, {"id": 3008, "desc": "holidayTaskDesc.104", "reward": "4,8240,1", "condition": 108008}, {"id": 3009, "desc": "holidayTaskDesc.104", "reward": "4,8238,1", "condition": 108009}, {"id": 3010, "desc": "holidayTaskDesc.104", "reward": "4,8239,1", "condition": 108010}, {"id": 3011, "desc": "holidayTaskDesc.104", "reward": "4,9207,3", "condition": 108011}, {"id": 3012, "desc": "holidayTaskDesc.104", "reward": "4,8241,1", "condition": 108012}, {"id": 3013, "desc": "holidayTaskDesc.104", "reward": "4,8026,1", "condition": 108013}, {"id": 3014, "desc": "holidayTaskDesc.104", "reward": "4,8024,1", "condition": 108014}, {"id": 4001, "desc": "holidayTaskDesc.104", "reward": "4,8532,1", "condition": 108101}, {"id": 4002, "desc": "holidayTaskDesc.104", "reward": "4,8663,2", "condition": 108102}, {"id": 4003, "desc": "holidayTaskDesc.104", "reward": "4,8535,1", "condition": 108103}, {"id": 4004, "desc": "holidayTaskDesc.104", "reward": "4,8533,1", "condition": 108104}, {"id": 4005, "desc": "holidayTaskDesc.104", "reward": "4,9205,2", "condition": 108105}, {"id": 4006, "desc": "holidayTaskDesc.104", "reward": "4,8534,1", "condition": 108106}, {"id": 4007, "desc": "holidayTaskDesc.104", "reward": "4,9206,2", "condition": 108107}, {"id": 4008, "desc": "holidayTaskDesc.104", "reward": "4,8047,1", "condition": 108108}, {"id": 4009, "desc": "holidayTaskDesc.104", "reward": "4,8287,1", "condition": 108109}, {"id": 4010, "desc": "holidayTaskDesc.104", "reward": "4,8048,1", "condition": 108110}, {"id": 4011, "desc": "holidayTaskDesc.104", "reward": "4,8285,1", "condition": 108111}, {"id": 4012, "desc": "holidayTaskDesc.104", "reward": "4,8284,1", "condition": 108112}, {"id": 4013, "desc": "holidayTaskDesc.104", "reward": "1100,2019,1", "condition": 108113}, {"id": 10411, "desc": "holidayTaskDesc.10", "reward": "801,1101,5|4,9100,15", "condition": 10411}, {"id": 10412, "desc": "holidayTaskDesc.35", "reward": "801,1101,3|401,-1,10", "condition": 10412}, {"id": 10421, "desc": "holidayTaskDesc.83", "reward": "801,1101,4|2,-1,5", "condition": 10421}, {"id": 10422, "desc": "holidayTaskDesc.16", "reward": "801,1101,5|4,9007,1", "condition": 10422}, {"id": 10431, "desc": "holidayTaskDesc.75", "reward": "801,1101,4|2,-1,10", "condition": 10431}, {"id": 10432, "desc": "holidayTaskDesc.31", "reward": "801,1101,3|4,9100,25", "condition": 10432}, {"id": 10441, "desc": "holidayTaskDesc.85", "reward": "801,1101,4|4,9006,1", "condition": 10441}, {"id": 10442, "desc": "holidayTaskDesc.106", "reward": "801,1101,3|2,-1,5", "condition": 10442}, {"id": 10451, "desc": "holidayTaskDesc.83", "reward": "801,1101,4|2,-1,5", "condition": 10451}, {"id": 10452, "desc": "holidayTaskDesc.84", "reward": "801,1101,4|4,9006,1", "condition": 10452}, {"id": 10453, "desc": "holidayTaskDesc.10", "reward": "801,1101,3|4,9100,20", "condition": 10453}, {"id": 10461, "desc": "holidayTaskDesc.23", "reward": "801,1101,5|2,-1,10", "condition": 10461}, {"id": 10462, "desc": "holidayTaskDesc.123", "reward": "801,1101,3|4,9004,1", "condition": 10462}, {"id": 10463, "desc": "holidayTaskDesc.33_2", "reward": "801,1101,2|4,9100,10", "condition": 10463}, {"id": 10471, "desc": "holidayTaskDesc.83", "reward": "801,1101,4|2,-1,10", "condition": 10471}, {"id": 10472, "desc": "holidayTaskDesc.85", "reward": "801,1101,5|4,9007,1", "condition": 10472}, {"id": 10473, "desc": "holidayTaskDesc.10", "reward": "801,1101,3|4,9100,25", "condition": 10473}, {"id": 10511, "desc": "ui.christmas_blessings", "reward": "801,1201,1|2,-1,5", "condition": 10511}, {"id": 10512, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|402,-1,5", "condition": 10512}, {"id": 10513, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10513}, {"id": 10514, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,10", "condition": 10514}, {"id": 10521, "desc": "holidayTaskDesc.31", "reward": "801,1201,2|4,9100,25", "condition": 10521}, {"id": 10522, "desc": "holidayTaskDesc.35", "reward": "801,1201,1|1,-1,3000", "condition": 10522}, {"id": 10523, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10523}, {"id": 10524, "desc": "holidayTaskDesc.106", "reward": "801,1201,2|2,-1,10", "condition": 10524}, {"id": 10531, "desc": "returnTaskDesc.10009", "reward": "801,1201,1|1,-1,5000", "condition": 10531}, {"id": 10532, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9001,2", "condition": 10532}, {"id": 10533, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|401,-1,10", "condition": 10533}, {"id": 10534, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,15", "condition": 10534}, {"id": 10541, "desc": "holidayTaskDesc.23", "reward": "801,1201,2|4,9007,1", "condition": 10541}, {"id": 10542, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9002,1", "condition": 10542}, {"id": 10543, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10543}, {"id": 10544, "desc": "holidayTaskDesc.33_2", "reward": "801,1201,2|2,-1,10", "condition": 10544}, {"id": 10551, "desc": "holidayTaskDesc.35", "reward": "801,1201,1|1,-1,3000", "condition": 10551}, {"id": 10552, "desc": "holidayTaskDesc.38", "reward": "801,1201,1|4,9005,2", "condition": 10552}, {"id": 10553, "desc": "holidayTaskDesc.84", "reward": "801,1201,3|2,-1,15", "condition": 10553}, {"id": 10554, "desc": "holidayTaskDesc.85", "reward": "801,1201,3|2,-1,15", "condition": 10554}, {"id": 10561, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|4,9100,30", "condition": 10561}, {"id": 10562, "desc": "holidayTaskDesc.123", "reward": "801,1201,1|4,9100,25", "condition": 10562}, {"id": 10563, "desc": "holidayTaskDesc.23", "reward": "801,1201,3|4,9003,1", "condition": 10563}, {"id": 10564, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,20", "condition": 10564}, {"id": 10571, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9003,1", "condition": 10571}, {"id": 10572, "desc": "holidayTaskDesc.10", "reward": "801,1201,2|1,-1,5000", "condition": 10572}, {"id": 10573, "desc": "holidayTaskDesc.106", "reward": "801,1201,2|2,-1,15", "condition": 10573}, {"id": 10574, "desc": "holidayTaskDesc.23", "reward": "801,1101,3|4,9006,1", "condition": 10574}, {"id": 10611, "desc": "ui.halloween_blessings", "reward": "801,1101,1|2,-1,5", "condition": 10614}, {"id": 10612, "desc": "holidayTaskDesc.83", "reward": "801,1101,2|402,-1,5", "condition": 10611}, {"id": 10613, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|1,-1,3000", "condition": 10612}, {"id": 10614, "desc": "holidayTaskDesc.16", "reward": "801,1101,2|401,-1,10", "condition": 10613}, {"id": 10621, "desc": "holidayTaskDesc.31", "reward": "801,1101,2|4,9100,25", "condition": 10621}, {"id": 10622, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|1,-1,3000", "condition": 10622}, {"id": 10623, "desc": "holidayTaskDesc.35", "reward": "801,1101,1|1,-1,3000", "condition": 10623}, {"id": 10624, "desc": "holidayTaskDesc.106", "reward": "801,1101,2|2,-1,10", "condition": 10624}, {"id": 10631, "desc": "holidayTaskDesc.83", "reward": "801,1101,2|401,-1,10", "condition": 10631}, {"id": 10632, "desc": "holidayTaskDesc.75", "reward": "801,1101,2|4,9001,2", "condition": 10632}, {"id": 10633, "desc": "holidayTaskDesc.16", "reward": "801,1101,2|401,-1,15", "condition": 10633}, {"id": 10634, "desc": "returnTaskDesc.10009", "reward": "801,1101,1|1,-1,5000", "condition": 10634}, {"id": 10635, "desc": "holidayTaskDesc.165", "reward": "801,1101,1|2,-1,15", "condition": 10635}, {"id": 10641, "desc": "holidayTaskDesc.23", "reward": "801,1101,3|4,9007,1", "condition": 10641}, {"id": 10642, "desc": "holidayTaskDesc.75", "reward": "801,1101,2|4,9002,1", "condition": 10642}, {"id": 10643, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|1,-1,3000", "condition": 10643}, {"id": 10644, "desc": "holidayTaskDesc.33_2", "reward": "801,1101,2|2,-1,10", "condition": 10644}, {"id": 10645, "desc": "holidayTaskDesc.168", "reward": "801,1101,1|2,-1,10", "condition": 10645}, {"id": 10651, "desc": "holidayTaskDesc.84", "reward": "801,1101,3|2,-1,15", "condition": 10651}, {"id": 10652, "desc": "holidayTaskDesc.85", "reward": "801,1101,3|2,-1,15", "condition": 10652}, {"id": 10653, "desc": "holidayTaskDesc.35", "reward": "801,1101,1|1,-1,3000", "condition": 10653}, {"id": 10654, "desc": "holidayTaskDesc.38", "reward": "801,1101,2|4,9005,2", "condition": 10654}, {"id": 10655, "desc": "holidayTaskDesc.165", "reward": "801,1101,2|2,-1,15", "condition": 10655}, {"id": 10661, "desc": "holidayTaskDesc.83", "reward": "801,1101,2|4,9100,30", "condition": 10661}, {"id": 10662, "desc": "holidayTaskDesc.23", "reward": "801,1101,5|4,9003,1", "condition": 10662}, {"id": 10663, "desc": "holidayTaskDesc.123", "reward": "801,1101,1|4,9100,25", "condition": 10663}, {"id": 10664, "desc": "holidayTaskDesc.16", "reward": "801,1101,2|401,-1,20", "condition": 10664}, {"id": 10665, "desc": "holidayTaskDesc.168", "reward": "801,1101,2|2,-1,10", "condition": 10665}, {"id": 10671, "desc": "holidayTaskDesc.23", "reward": "801,1101,8|4,9006,1", "condition": 10671}, {"id": 10672, "desc": "holidayTaskDesc.84", "reward": "801,1101,6|2,-1,15", "condition": 10672}, {"id": 10673, "desc": "holidayTaskDesc.75", "reward": "801,1101,2|4,9003,1", "condition": 10673}, {"id": 10674, "desc": "holidayTaskDesc.10", "reward": "801,1101,2|1,-1,5000", "condition": 10674}, {"id": 10675, "desc": "holidayTaskDesc.106", "reward": "801,1101,2|2,-1,15", "condition": 10675}, {"id": 10711, "desc": "ui.christmas_blessings", "reward": "801,1201,2|2,-1,5", "condition": 10711}, {"id": 10712, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|402,-1,5", "condition": 10712}, {"id": 10713, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10713}, {"id": 10714, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,10", "condition": 10714}, {"id": 10721, "desc": "holidayTaskDesc.31", "reward": "801,1201,2|4,9100,25", "condition": 10721}, {"id": 10722, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10722}, {"id": 10723, "desc": "holidayTaskDesc.35", "reward": "801,1201,1|1,-1,3000", "condition": 10723}, {"id": 10724, "desc": "holidayTaskDesc.106", "reward": "801,1201,2|2,-1,10", "condition": 10724}, {"id": 10731, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|401,-1,10", "condition": 10731}, {"id": 10732, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9003,1", "condition": 10732}, {"id": 10733, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,15", "condition": 10733}, {"id": 10734, "desc": "returnTaskDesc.10009", "reward": "801,1201,1|1,-1,5000", "condition": 10734}, {"id": 10735, "desc": "holidayTaskDesc.165", "reward": "801,1201,1|2,-1,10", "condition": 10735}, {"id": 10741, "desc": "holidayTaskDesc.23", "reward": "801,1201,3|4,9007,1", "condition": 10741}, {"id": 10742, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9001,2", "condition": 10742}, {"id": 10743, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10743}, {"id": 10744, "desc": "holidayTaskDesc.33_2", "reward": "801,1201,2|2,-1,10", "condition": 10744}, {"id": 10745, "desc": "holidayTaskDesc.168", "reward": "801,1201,1|2,-1,10", "condition": 10745}, {"id": 10751, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|4,9100,30", "condition": 10751}, {"id": 10752, "desc": "holidayTaskDesc.84", "reward": "801,1201,3|2,-1,15", "condition": 10752}, {"id": 10753, "desc": "holidayTaskDesc.123", "reward": "801,1201,1|4,9100,25", "condition": 10753}, {"id": 10754, "desc": "holidayTaskDesc.38", "reward": "801,1201,2|4,9005,2", "condition": 10754}, {"id": 10755, "desc": "holidayTaskDesc.165", "reward": "801,1201,2|2,-1,15", "condition": 10755}, {"id": 10761, "desc": "holidayTaskDesc.23", "reward": "801,1201,5|4,9004,1", "condition": 10761}, {"id": 10762, "desc": "holidayTaskDesc.85", "reward": "801,1201,3|2,-1,15", "condition": 10762}, {"id": 10763, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,20", "condition": 10763}, {"id": 10764, "desc": "holidayTaskDesc.35", "reward": "801,1201,1|1,-1,3000", "condition": 10764}, {"id": 10765, "desc": "holidayTaskDesc.168", "reward": "801,1201,2|2,-1,10", "condition": 10765}, {"id": 10771, "desc": "holidayTaskDesc.23", "reward": "801,1201,9|4,9006,1", "condition": 10771}, {"id": 10772, "desc": "holidayTaskDesc.84", "reward": "801,1201,6|2,-1,15", "condition": 10772}, {"id": 10773, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9002,1", "condition": 10773}, {"id": 10774, "desc": "holidayTaskDesc.10", "reward": "801,1201,2|1,-1,5000", "condition": 10774}, {"id": 10775, "desc": "holidayTaskDesc.106", "reward": "801,1201,2|2,-1,15", "condition": 10775}, {"id": 10811, "desc": "ui.halloween_blessings", "reward": "801,1101,1|2,-1,5", "condition": 10614}, {"id": 10812, "desc": "holidayTaskDesc.16", "reward": "801,1101,2|401,-1,10", "condition": 10613}, {"id": 10813, "desc": "holidayTaskDesc.31", "reward": "801,1101,2|4,9100,25", "condition": 10621}, {"id": 10814, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|1,-1,3000", "condition": 10612}, {"id": 10821, "desc": "holidayTaskDesc.83", "reward": "801,1101,2|402,-1,5", "condition": 10611}, {"id": 10822, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|1,-1,3000", "condition": 10622}, {"id": 10823, "desc": "holidayTaskDesc.16", "reward": "801,1101,2|401,-1,15", "condition": 10633}, {"id": 10824, "desc": "holidayTaskDesc.106", "reward": "801,1101,2|2,-1,10", "condition": 10624}, {"id": 10831, "desc": "holidayTaskDesc.83", "reward": "801,1101,2|401,-1,10", "condition": 10631}, {"id": 10832, "desc": "holidayTaskDesc.75", "reward": "801,1101,2|4,9001,2", "condition": 10632}, {"id": 10833, "desc": "holidayTaskDesc.35", "reward": "801,1101,1|1,-1,3000", "condition": 10623}, {"id": 10834, "desc": "holidayTaskDesc.33_2", "reward": "801,1101,2|2,-1,10", "condition": 10644}, {"id": 10835, "desc": "holidayTaskDesc.168", "reward": "801,1101,1|2,-1,10", "condition": 10645}, {"id": 10841, "desc": "returnTaskDesc.10009", "reward": "801,1101,1|1,-1,5000", "condition": 10634}, {"id": 10842, "desc": "holidayTaskDesc.75", "reward": "801,1101,2|4,9002,1", "condition": 10642}, {"id": 10843, "desc": "holidayTaskDesc.10", "reward": "801,1101,1|1,-1,3000", "condition": 10643}, {"id": 10844, "desc": "holidayTaskDesc.23", "reward": "801,1101,3|4,9007,1", "condition": 10641}, {"id": 10845, "desc": "holidayTaskDesc.165", "reward": "801,1101,1|2,-1,15", "condition": 10635}, {"id": 10851, "desc": "holidayTaskDesc.83", "reward": "801,1101,2|4,9100,30", "condition": 10661}, {"id": 10852, "desc": "holidayTaskDesc.23", "reward": "801,1101,5|4,9003,1", "condition": 10662}, {"id": 10853, "desc": "holidayTaskDesc.123", "reward": "801,1101,1|4,9100,25", "condition": 10663}, {"id": 10854, "desc": "holidayTaskDesc.16", "reward": "801,1101,2|401,-1,20", "condition": 10664}, {"id": 10855, "desc": "holidayTaskDesc.168", "reward": "801,1101,2|2,-1,10", "condition": 10665}, {"id": 10861, "desc": "holidayTaskDesc.84", "reward": "801,1101,3|2,-1,15", "condition": 10651}, {"id": 10862, "desc": "holidayTaskDesc.85", "reward": "801,1101,3|2,-1,15", "condition": 10652}, {"id": 10863, "desc": "holidayTaskDesc.35", "reward": "801,1101,1|1,-1,3000", "condition": 10653}, {"id": 10864, "desc": "holidayTaskDesc.38", "reward": "801,1101,2|4,9005,2", "condition": 10654}, {"id": 10865, "desc": "holidayTaskDesc.165", "reward": "801,1101,2|2,-1,15", "condition": 10655}, {"id": 10871, "desc": "holidayTaskDesc.23", "reward": "801,1101,8|4,9006,1", "condition": 10671}, {"id": 10872, "desc": "holidayTaskDesc.84", "reward": "801,1101,6|2,-1,15", "condition": 10672}, {"id": 10873, "desc": "holidayTaskDesc.75", "reward": "801,1101,2|4,9003,1", "condition": 10673}, {"id": 10874, "desc": "holidayTaskDesc.10", "reward": "801,1101,2|1,-1,5000", "condition": 10674}, {"id": 10875, "desc": "holidayTaskDesc.106", "reward": "801,1101,2|2,-1,15", "condition": 10675}, {"id": 10911, "desc": "ui.christmas_blessings", "reward": "801,1201,2|2,-1,5", "condition": 10711}, {"id": 10912, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|402,-1,5", "condition": 10712}, {"id": 10913, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10713}, {"id": 10914, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,10", "condition": 10714}, {"id": 10921, "desc": "holidayTaskDesc.31", "reward": "801,1201,2|4,9100,25", "condition": 10721}, {"id": 10922, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10722}, {"id": 10923, "desc": "holidayTaskDesc.35", "reward": "801,1201,1|1,-1,3000", "condition": 10723}, {"id": 10924, "desc": "holidayTaskDesc.106", "reward": "801,1201,2|2,-1,10", "condition": 10724}, {"id": 10931, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|401,-1,10", "condition": 10731}, {"id": 10932, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9003,1", "condition": 10732}, {"id": 10933, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,15", "condition": 10733}, {"id": 10934, "desc": "returnTaskDesc.10009", "reward": "801,1201,1|1,-1,5000", "condition": 10734}, {"id": 10935, "desc": "holidayTaskDesc.165", "reward": "801,1201,1|2,-1,10", "condition": 10735}, {"id": 10941, "desc": "holidayTaskDesc.23", "reward": "801,1201,3|4,9007,1", "condition": 10741}, {"id": 10942, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9001,2", "condition": 10742}, {"id": 10943, "desc": "holidayTaskDesc.10", "reward": "801,1201,1|1,-1,3000", "condition": 10743}, {"id": 10944, "desc": "holidayTaskDesc.33_2", "reward": "801,1201,2|2,-1,10", "condition": 10744}, {"id": 10945, "desc": "holidayTaskDesc.168", "reward": "801,1201,1|2,-1,10", "condition": 10745}, {"id": 10951, "desc": "holidayTaskDesc.83", "reward": "801,1201,2|4,9100,30", "condition": 10751}, {"id": 10952, "desc": "holidayTaskDesc.84", "reward": "801,1201,3|2,-1,15", "condition": 10752}, {"id": 10953, "desc": "holidayTaskDesc.123", "reward": "801,1201,1|4,9100,25", "condition": 10753}, {"id": 10954, "desc": "holidayTaskDesc.38", "reward": "801,1201,2|4,9005,2", "condition": 10754}, {"id": 10955, "desc": "holidayTaskDesc.165", "reward": "801,1201,2|2,-1,15", "condition": 10755}, {"id": 10961, "desc": "holidayTaskDesc.23", "reward": "801,1201,5|4,9004,1", "condition": 10761}, {"id": 10962, "desc": "holidayTaskDesc.85", "reward": "801,1201,3|2,-1,15", "condition": 10762}, {"id": 10963, "desc": "holidayTaskDesc.16", "reward": "801,1201,2|401,-1,20", "condition": 10763}, {"id": 10964, "desc": "holidayTaskDesc.35", "reward": "801,1201,1|1,-1,3000", "condition": 10764}, {"id": 10965, "desc": "holidayTaskDesc.168", "reward": "801,1201,2|2,-1,10", "condition": 10765}, {"id": 10971, "desc": "holidayTaskDesc.23", "reward": "801,1201,9|4,9006,1", "condition": 10771}, {"id": 10972, "desc": "holidayTaskDesc.84", "reward": "801,1201,6|2,-1,15", "condition": 10772}, {"id": 10973, "desc": "holidayTaskDesc.75", "reward": "801,1201,2|4,9002,1", "condition": 10773}, {"id": 10974, "desc": "holidayTaskDesc.10", "reward": "801,1201,2|1,-1,5000", "condition": 10774}, {"id": 10975, "desc": "holidayTaskDesc.106", "reward": "801,1201,2|2,-1,15", "condition": 10775}, {"id": 11011, "desc": "holidayTaskDesc.123", "reward": "804,-1,5", "condition": 10776}, {"id": 11012, "desc": "holidayTaskDesc.75", "reward": "804,-1,5", "condition": 10777}, {"id": 11013, "desc": "holidayTaskDesc.38", "reward": "804,-1,10|1,-1,666", "condition": 10778}, {"id": 11014, "desc": "holidayTaskDesc.35", "reward": "804,-1,10|4,9100,10", "condition": 10779}, {"id": 11021, "desc": "holidayTaskDesc.165", "reward": "804,-1,15|2,-1,20", "condition": 10780}, {"id": 11022, "desc": "holidayTaskDesc.31", "reward": "804,-1,5", "condition": 10781}, {"id": 11023, "desc": "holidayTaskDesc.10", "reward": "804,-1,5", "condition": 10782}, {"id": 11024, "desc": "holidayTaskDesc.168", "reward": "804,-1,10|1,-1,888", "condition": 10783}, {"id": 11031, "desc": "holidayTaskDesc.38", "reward": "804,-1,10|4,9100,10", "condition": 10784}, {"id": 11032, "desc": "holidayTaskDesc.234", "reward": "804,-1,15|4,9007,5", "condition": 10785}, {"id": 11033, "desc": "holidayTaskDesc.110", "reward": "804,-1,5", "condition": 10786}, {"id": 11034, "desc": "holidayTaskDesc.62", "reward": "804,-1,5", "condition": 10787}, {"id": 11035, "desc": "holidayTaskDesc.249", "reward": "804,-1,10|1,-1,999", "condition": 10788}, {"id": 11041, "desc": "holidayTaskDesc.248", "reward": "804,-1,10|401,-1,10", "condition": 10789}, {"id": 11042, "desc": "holidayTaskDesc.125", "reward": "804,-1,15|4,9201,5", "condition": 10790}, {"id": 11043, "desc": "holidayTaskDesc.251", "reward": "804,-1,5", "condition": 10791}, {"id": 11044, "desc": "holidayTaskDesc.83", "reward": "804,-1,5", "condition": 10792}, {"id": 11045, "desc": "holidayTaskDesc.253", "reward": "804,-1,10|1,-1,1111", "condition": 10793}, {"id": 11051, "desc": "holidayTaskDesc.250", "reward": "804,-1,10|4,9001,5", "condition": 10794}, {"id": 11052, "desc": "holidayTaskDesc.151", "reward": "804,-1,15|4,9206,5", "condition": 10795}, {"id": 11053, "desc": "holidayTaskDesc.40", "reward": "804,-1,5", "condition": 10796}, {"id": 11054, "desc": "holidayTaskDesc.247", "reward": "804,-1,5", "condition": 10797}, {"id": 11055, "desc": "holidayTaskDesc.208", "reward": "804,-1,10|1,-1,2222", "condition": 10798}, {"id": 11061, "desc": "holidayTaskDesc.245", "reward": "804,-1,10|4,9207,2", "condition": 10799}, {"id": 11062, "desc": "holidayTaskDesc.253", "reward": "804,-1,15|4,9100,25", "condition": 10800}, {"id": 11063, "desc": "holidayTaskDesc.85", "reward": "804,-1,5", "condition": 10801}, {"id": 11064, "desc": "holidayTaskDesc.83", "reward": "804,-1,5", "condition": 10802}, {"id": 11065, "desc": "holidayTaskDesc.35", "reward": "804,-1,10|1,-1,3333", "condition": 10803}, {"id": 11071, "desc": "holidayTaskDesc.246", "reward": "804,-1,10|4,9006,2", "condition": 10804}, {"id": 11072, "desc": "holidayTaskDesc.253", "reward": "804,-1,15|2,-1,20", "condition": 10805}, {"id": 11073, "desc": "holidayTaskDesc.252", "reward": "804,-1,5", "condition": 10806}, {"id": 11074, "desc": "holidayTaskDesc.83", "reward": "804,-1,5", "condition": 10807}, {"id": 11075, "desc": "holidayTaskDesc.135", "reward": "804,-1,10|1,-1,6666", "condition": 10808}, {"id": 11111, "desc": "holidayTaskDesc.75", "reward": "804,-1,10|4,9207,2", "condition": 10809}, {"id": 11112, "desc": "holidayTaskDesc.16", "reward": "804,-1,15|4,9100,25", "condition": 10810}]