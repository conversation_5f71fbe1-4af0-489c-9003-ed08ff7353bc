[{"id": 1, "module": "guide_opening", "mainScene": true, "force": true, "checkFunc": {"func": "checkBiscuits", "arg": 0}, "steps": [{"type": 5, "time": 500, "reportEvent": "guide_01_001", "restartPoint": 1}, {"type": 3, "storyIdx": 0, "taEvent": 3, "proceed": true}, {"type": 3, "storyIdx": 1, "reportEvent": "guide_01_002"}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_bu"}, "offsetPos": "-200,0", "moveTime": 0.8}, "delay": 100}, {"type": 0, "mod": "guideLogic", "func": "setGuideWudongPos", "args": 2, "delay": 800}, {"type": 3, "storyIdx": 2}, {"type": 4, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_bu"}, "isAuido": true}, {"type": 1, "eventType": "UPDATE_HUAMAO_STATE", "args": 1, "restartPoint": 1, "reportEvent": "guide_01_003", "taEvent": 4, "delay": 100}, {"type": 3, "storyIdx": 3}, {"type": 0, "mod": "guideLogic", "func": "createGuideRole", "restartPoint": 0, "delay": 100}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "centre"}, "offsetPos": "-100,2000", "moveTime": 0.6}, "delay": 100}, {"type": 0, "mod": "guideLogic", "func": "setGuideWudongPos", "args": 1, "delay": 100}, {"type": 4, "nodeName": "Wind/MainWind/map_n/role_n/BUBBLE_DIALOG", "nodeDesc": "guide.node_bubble"}, {"type": 3, "storyIdx": 4, "proceed": true, "taEvent": 5, "reportEvent": "guide_01_005"}, {"type": 0, "mod": "guideLogic", "func": "roleToDorm", "delay": 100}, {"type": 3, "storyIdx": 5}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/menu/mainInfo_be", "nodeDesc": "guide.node_main_building", "angle": 90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "MainInfo"}}, {"type": 2, "nodeName": "View/MainInfoPnl/root/change/change_sv/view/content/item_1/button/unlock_be", "waitEvent": "CHANGE_MAIN", "checkDataSave": {"type": 1, "key": 5}}, {"type": 5, "time": 2000, "reportEvent": "guide_01_006", "taEvent": 6, "restartPoint": 2}, {"type": 0, "mod": "guideLogic", "func": "roleToDormBunk", "delay": 1500}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_5"}, "nodeDesc": "guide.node_enter_dorm", "waitEvent": "WIND_ENTER_303", "focus": false, "specialStep": ["DORM_BUNK"], "moveCamera": true, "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_cam_5"}, "offsetPos": "0,0", "moveTime": 0.3}}, {"type": 3, "storyIdx": 6}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "nodeDesc": "guide.node_dorm_build", "angle": -90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}}, {"type": 2, "nodeName": "View/BuildListPnl/rooot/list_n/view/content/item_0/list/view/content/item_be", "nodeDesc": "guide.node_dorm_item", "angle": 45, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildInfo"}}, {"type": 2, "nodeName": "View/BuildInfoPnl/root_n/button_n/unlock_be", "waitEvent": "UNLOCK_DORM_BUILD", "checkDataSave": {"type": 2, "key": 7001}}, {"type": 5, "time": 1200, "reportEvent": "guide_01_007", "taEvent": 7, "restartPoint": 4}, {"type": 0, "mod": "guideLogic", "func": "roleDormSleep", "delay": 500}, {"type": 1, "eventType": "GUIDE_DRAK_SCREEN", "args": true, "delay": 3500}, {"type": 0, "mod": "guideLogic", "func": "dormRoleGetUp", "delay": 100}, {"type": 3, "storyIdx": 7, "proceed": true, "specialStep": ["DORM_DROP_MONEY"]}, {"type": 0, "mod": "guideLogic", "func": "roleOutDorm", "restartPoint": 1, "delay": 100}, {"type": 3, "storyIdx": 8, "proceed": true, "specialStep": ["DORM_SECOND_PUCHASE"]}, {"type": 3, "storyIdx": 9, "moveCamera": true, "args": {"target": {"parent": "Wind/DormWind/map_n/role_n/DROP_BISCUIT"}, "offsetPos": "0,300", "moveTime": 0.4, "delay": 0.25}}, {"type": 2, "nodeName": "Wind/DormWind/map_n/role_n/DROP_BISCUIT", "focus": false, "waitEvent": "SHOW_FLUTTER_MONEY"}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "waitEvent": "UPDATE_BISCUITS", "restartPoint": 1}, {"type": 3, "storyIdx": 10, "taEvent": 8, "specialStep": ["DORM_SECOND_PUCHASE"]}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "angle": -90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}}, {"type": 2, "nodeName": "View/BuildListPnl/rooot/list_n/view/content/item_1/list/lock/button/unlock_build_be", "waitEvent": "UNLOCK_DORM_BUILD", "checkDataSave": {"type": 2, "key": 6001}}, {"type": 3, "storyIdx": 11, "preDelay": 800, "taEvent": 9, "restartPoint": 2}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/back_be", "nodeDesc": "guide.node_dorm_back", "angle": 90, "waitEvent": "WIND_ENTER_303"}, {"type": 1, "eventType": "UPDATE_QIANTAI_STATE", "args": true, "delay": 100}, {"type": 1, "eventType": "GUIDE_ZOOM_CAMERA", "args": {"offsetPos": "380,300", "zoomRatio": 0.52, "moveTime": 0.5}, "delay": 500}, {"type": 3, "dormZoomRatio": 0.55, "storyIdx": 12}, {"type": 2, "nodeName": "Wind/MainWind/map_n/guide_solicit", "nodeDesc": "guide.node_solicit_area", "eventType": "GUIDE_SHOW_FINGER", "args": true, "finger": false, "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "waitEvent": "UNLOCK_ROLE_CLOSE", "restartPoint": 3}, {"type": 3, "storyIdx": 13}, {"type": 1, "eventType": "UPDATE_HUAMAO_STATE", "args": 2, "reportEvent": "guide_01_011", "taEvent": 12, "delay": 100}, {"type": 0, "mod": "guideLogic", "func": "openNewspaper", "delay": 100}]}, {"id": 2, "module": "guide_dining", "mainScene": false, "force": true, "trigger": {"func": "checkDiningUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 3}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_3"}, "nodeDesc": "guide.node_dining_enter", "waitEvent": "WIND_ENTER_303", "focus": false}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}, "angle": -90}, {"type": 2, "nodeName": "View/BuildListPnl/rooot/list_n/view/content/item_1/list/lock/button/unlock_build_be", "waitEvent": "UNLOCK_DINING_BUILD", "checkDataSave": {"type": 3, "key": 13001}}, {"type": 3, "storyIdx": 2, "restartPoint": 0}, {"type": 2, "nodeName": "Wind/DiningWind/map_n/role_n/KITCHEN_1/body/npc/TAITAN/root", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "dining/FoodMining"}}, {"type": 3, "storyIdx": 3, "restartPoint": 0, "specialStep": ["DINING_MINING"]}, {"type": 2, "nodeName": "View/FoodMiningPnl/root/gemLayer_n/item_0", "nodeDesc": "guide.node_dining_gem", "waitEvent": "GUIDE_FLIP_FOOD_MINING", "specialStep": ["DINING_MINING"], "showSkip": true}, {"type": 0, "mod": "guideLogic", "func": "roleGoDiningDesk", "delay": 200, "restartPoint": 0, "specialStep": ["DINING_MINING"]}, {"type": 3, "storyIdx": 4, "specialStep": ["DINING_MINING", "DINING_GO_ORDER"]}, {"type": 2, "nodeName": "View/FoodMiningPnl/root/gemLayer_n/item_1", "focus": false, "waitEvent": "GUIDE_FLIP_FOOD_MINING", "specialStep": ["DINING_MINING", "DINING_GO_ORDER"], "showSkip": true}, {"type": 3, "storyIdx": 5, "restartPoint": 0, "specialStep": ["DINING_MINING", "DINING_GO_ORDER"]}, {"type": 2, "nodeName": "View/FoodMiningPnl/root/gemLayer_n/item_1", "focus": false, "waitEvent": "UPDATE_FOODMINING_COUNT", "specialStep": ["DINING_MINING", "DINING_GO_ORDER"], "showSkip": true}, {"type": 3, "storyIdx": 6, "proceed": true, "restartPoint": 0, "specialStep": ["DINING_MINING", "DINING_GO_ORDER"]}, {"type": 0, "mod": "guideLogic", "func": "closeFoodMiningPnl", "delay": 200, "specialStep": ["DINING_SET_ORDER"]}, {"type": 3, "storyIdx": 7, "restartPoint": 0, "specialStep": ["DINING_SET_ORDER"]}, {"type": 2, "nodeName": "Wind/DiningWind/map_n/role_n/HEIBAN_ONE/root", "waitEvent": "PNL_ENTER_210", "specialStep": ["DINING_SET_ORDER"], "moveCamera": true, "args": {"pnlKey": "dining/FoodList", "target": {"parent": "Wind/DiningWind/map_n/role_n/HEIBAN_ONE/root"}, "offsetPos": "0,0", "moveTime": 0.4}, "showSkip": true}, {"type": 2, "nodeName": "View/FoodListPnl/root/list_sv/view/content/item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "dining/FoodInfo"}, "angle": 45, "specialStep": ["DINING_SET_ORDER"], "showSkip": true}, {"type": 2, "nodeName": "View/FoodInfoPnl/root_n/button_n/unlock_be", "waitEvent": "UNLOCK_FOOD", "specialStep": ["DINING_SET_ORDER"], "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 1, "waitEvent": "PNL_LEAVE_211", "args": {"pnlKey": "dining/FoodUnlockAnim"}}, {"type": 3, "storyIdx": 8, "proceed": true, "specialStep": ["DINING_SET_ORDER"]}, {"type": 0, "mod": "guideLogic", "func": "roleOrder", "delay": 100}, {"type": 3, "storyIdx": 9, "proceed": true, "restartPoint": 0}, {"type": 3, "storyIdx": 10, "restartPoint": 0}]}, {"id": 3, "module": "guide_shower", "mainScene": false, "force": true, "trigger": {"func": "checkShowerUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 2}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_2"}, "focus": false, "waitEvent": "WIND_ENTER_303"}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}, "angle": -90}, {"type": 2, "nodeName": "View/BuildListPnl/rooot/list_n/view/content/item_0/list/lock/button/unlock_build_be", "waitEvent": "UNLOCK_SHOWER_BUILD", "checkDataSave": {"type": 4, "key": 6001}}, {"type": 3, "storyIdx": 2, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_SHOW_PAOPAO", "delay": 200}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/ShowerWind/map_n/role_n/STEWPOT_ONE/guide"}, "offsetPos": "-100,100", "moveTime": 0.6}, "delay": 100}, {"type": 3, "storyIdx": 3, "finger": false}, {"type": 2, "nodeName": "Wind/ShowerWind/map_n/role_n/STEWPOT_ONE/guide", "focus": false, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "shower/CompStuff"}, "showSkip": true}, {"type": 2, "nodeName": "View/CompStuffPnl/root/top/proficiency_be_n", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "shower/CompProficiency"}, "showSkip": true}, {"type": 3, "storyIdx": 4}, {"type": 2, "nodeName": "View/CompProficiencyPnl/root/button_n/goComp", "waitEvent": "PNL_LEAVE_211", "args": {"pnlKey": "shower/CompProficiency"}, "showSkip": true}, {"type": 2, "nodeName": "View/CompStuffPnl/bottom/comp_be", "angle": -90, "showSkip": true, "waitEvent": "CREATE_COMP_STUFF", "restartPoint": 0, "specialStep": ["SHOWER_COMP_PNL"]}, {"type": 2, "nodeName": "View/CompStuffPnl/bottom/comp_be", "focus": false, "angle": -90, "showSkip": true, "waitEvent": "CREATE_COMP_STUFF", "restartPoint": 0, "specialStep": ["SHOWER_COMP_PNL"]}, {"type": 2, "nodeName": "View/CompStuffPnl/root/moveParent_n", "focus": false, "finger": false, "delay": 100, "restartPoint": 0, "specialStep": ["SHOWER_COMP_PNL"]}, {"type": 1, "eventType": "CHANGE_GUIDE_FINGER_ANI", "args": {"box": "View/CompStuffPnl/root/moveParent_n", "item": "View/CompStuffPnl/root/stuff_sv/view/content/item_1", "ani": "yidong", "pnlKey": "shower/CompStuffUp"}, "showSkip": true, "waitEvent": "PNL_ENTER_210"}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "waitEvent": "SOUP_STUFF_COMP_END", "restartPoint": 1}, {"type": 3, "storyIdx": 5, "proceed": true, "specialStep": ["SHOWER_COMP_PNL"]}, {"type": 0, "mod": "guideLogic", "func": "closeStuffUpPnl", "delay": 100, "restartPoint": 1}, {"type": 3, "storyIdx": 6, "proceed": true, "specialStep": ["SHOWER_COMP_PNL"]}, {"type": 0, "mod": "guideLogic", "func": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delay": 100, "specialStep": ["SHOWER_COMP_PNL"], "restartPoint": 0}, {"type": 3, "storyIdx": 7}, {"type": 2, "nodeName": "View/CompStuffPnl/bottom/back_be_n", "waitEvent": "PNL_LEAVE_211", "args": {"pnlKey": "shower/CompStuff"}, "angle": 90, "showSkip": true}, {"type": 3, "storyIdx": 8}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_SHOWER/soupBook", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "shower/SoupBook"}, "angle": -90, "showSkip": true}, {"type": 2, "nodeName": "View/SoupBookPnl/root/soups_sv/view/content/item/0/soup_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "shower/SoupInfo"}, "showSkip": true}, {"type": 2, "nodeName": "View/SoupInfoPnl/root_n/button_n/unlock_be", "waitEvent": "UNLOCK_SOUP", "showSkip": true}, {"type": 0, "mod": "guideLogic", "func": "setGuideFinish", "delay": 100}, {"type": 0, "mod": "guideLogic", "func": "roleGoBath", "delay": 100}, {"type": 3, "storyIdx": 9}]}, {"id": 4, "module": "guide_attic", "mainScene": false, "force": true, "trigger": {"func": "checkAttic<PERSON>nlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkAtticHomeUnlock"}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_attic_click"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_attic_click"}, "focus": false, "waitEvent": "WIND_ENTER_303"}, {"type": 3, "storyIdx": 1}, {"type": 4, "nodeName": "Wind/Kefang10000Wind/map/role/in1/Lock1/clean_eff", "focus": false, "showSkip": true, "checkDataSave": {"type": 12, "key": 0}}, {"type": 1, "eventType": "GUIDE_PLAY_ZHUANGHUANG_ANI", "delay": 3500}, {"type": 3, "storyIdx": 2}, {"type": 2, "nodeName": "Wind/Kefang10000Wind/ui/hse_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "home/HomeSpaceChoice"}, "angle": -90, "showSkip": true}, {"type": 4, "nodeName": "View/HomeSpaceChoicePnl/root_n/1/choices/ground_be_n", "showSkip": true}, {"type": 2, "nodeName": "View/HomeSpaceChoicePnl/root_n/1/ground_second_n/bg/add_be", "waitEvent": "CHANGE_HOMEWINDUI", "showSkip": true}, {"type": 2, "nodeName": "Wind/Kefang10000Wind/map/role/HomeAddGroundParent/HomeAddGround/ground/grid/10", "waitEvent": "GUIDE_CLICK_ADD_GROUND", "showSkip": true}, {"type": 2, "nodeName": "Wind/Kefang10000Wind/map/role/HomeAddGroundParent/HomeAddGround/ground/edit/btns/3", "waitEvent": "UPDATE_ADD_GROUND", "showSkip": true}, {"type": 3, "storyIdx": 3, "restartPoint": 2}, {"type": 2, "nodeName": "View/HomeSpaceChoicePnl/root_n/1/back_be", "waitEvent": "HIDE_PNL_202", "args": {"pnlKey": "home/HomeSpaceChoice"}, "showSkip": true}, {"type": 3, "storyIdx": 4}, {"type": 0, "mod": "guideLogic", "func": "grantHomeFurnsRewards", "restartPoint": 1}, {"type": 3, "storyIdx": 5}, {"type": 2, "nodeName": "Wind/Kefang10000Wind/ui/furn", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "home/HomeFurnitureList"}, "angle": -90, "showSkip": true}, {"type": 3, "storyIdx": 6}]}, {"id": 5, "module": "guide_tipJar", "mainScene": true, "force": true, "checkFunc": {"func": "checkTipUnlock"}, "steps": [{"type": 0, "mod": "guideLogic", "func": "createTipRole", "delay": 100, "restartPoint": 0}, {"type": 3, "storyIdx": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/menu/mainInfo_be", "nodeDesc": "guide.node_main_building", "angle": 90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "MainInfo"}, "showSkip": true}, {"type": 2, "nodeName": "View/MainInfoPnl/root/change/change_sv/view/content/item_3/button/unlock_be", "waitEvent": "CHANGE_MAIN", "showSkip": true}, {"type": 5, "time": 2000, "restartPoint": 1, "specialStep": ["TIP_ROLE"]}, {"type": 3, "storyIdx": 1, "specialStep": ["TIP_ROLE"]}, {"type": 0, "mod": "guideLogic", "func": "setGuideFinish", "delay": 100}, {"type": 0, "mod": "guideLogic", "func": "roleGoTip", "delay": 100}]}, {"id": 6, "module": "guide_room", "mainScene": false, "force": true, "trigger": {"func": "check201RoomUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 201}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 3, "storyIdx": 0}, {"type": 0, "mod": "guideLogic", "func": "roleStandInRoom", "delay": 100}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_201"}, "nodeDesc": "guide.room_enter", "focus": false, "waitEvent": "WIND_ENTER_303"}, {"type": 3, "storyIdx": 1, "specialStep": ["ROOM_SET_DOOR"]}, {"type": 0, "mod": "guideLogic", "func": "roleDropMoney", "delay": 500, "restartPoint": 1}, {"type": 0, "mod": "guideLogic", "func": "showOrderTask", "delay": 100}, {"type": 2, "nodeName": "View/OrderTaskInfoPnl/root/buttons_n/accept_be", "focus": false}, {"type": 3, "storyIdx": 2, "restartPoint": 0}, {"type": 2, "nodeName": "Wind/Kefang1Wind/ui/furn", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/FurnitureList"}, "angle": -90, "showSkip": true, "checkDataSave": {"type": 5, "key": "DECORATE_021", "unlockType": 2401}}, {"type": 2, "nodeName": "View/FurnitureListPnl/rooot/tabs_tc_tce/1", "waitEvent": "GUIDE_FURN_SWITCH", "showSkip": true}, {"type": 2, "nodeName": "View/FurnitureListPnl/rooot/pages_n/1/list/view/content/item_DECORATE_021", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/FurnInfo"}, "angle": 45, "showSkip": true}, {"type": 2, "nodeName": "View/FurnInfoPnl/root_n/button_n/unlock_be", "waitEvent": "UNLOCK_FURN", "showSkip": true}, {"type": 3, "storyIdx": 3, "restartPoint": 3}, {"type": 2, "nodeName": "View/FurnInfoPnl/root_n/button_n/use_be", "waitEvent": "HIDE_ALL_PNL_203", "focus": false, "showSkip": true}, {"type": 2, "nodeName": "Wind/Kefang1Wind/map/addMenu/3", "nodeDesc": "guide.room_furn_pos", "waitEvent": "GUIDE_FURN_USE", "showSkip": true}, {"type": 3, "storyIdx": 4, "restartPoint": 6}, {"type": 2, "nodeName": "Wind/Kefang1Wind/ui/furn", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/FurnitureList"}, "focus": false, "angle": -90, "showSkip": true}, {"type": 2, "nodeName": "View/FurnitureListPnl/rooot/pages_n/1/list/view/content/item_DECORATE_021", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/FurnInfo"}, "focus": false, "showSkip": true}, {"type": 2, "nodeName": "View/FurnInfoPnl/root_n/button_n/use_be", "waitEvent": "HIDE_ALL_PNL_203", "focus": false, "showSkip": true}, {"type": 5, "time": 500}, {"type": 2, "nodeName": "Wind/Kefang1Wind/map/addMenu/3", "waitEvent": "GUIDE_FURN_USE", "focus": false, "showSkip": true}, {"type": 1, "eventType": "GUIDE_PLAY_MOVE_FURN_ANI", "delay": 100}, {"type": 3, "storyIdx": 5, "proceed": true, "continueTime": 1.5}, {"type": 1, "eventType": "GUIDE_HIDE_MOVE_FURN_ANI", "delay": 100}, {"type": 3, "storyIdx": 6}]}, {"id": 8, "module": "guide_everyDay_task", "mainScene": true, "force": true, "checkFunc": {"func": "checkTaskUnlock"}, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/menu/task_be_n", "angle": 180, "fingerY": 40, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/Task"}, "showSkip": true}, {"type": 3, "storyIdx": 1}]}, {"id": 35, "module": "guide_unlock_dining", "mainScene": true, "force": true, "checkFunc": {"func": "checkUnlockDining"}, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/menu/mainInfo_be", "nodeDesc": "guide.node_main_building", "angle": 90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "MainInfo"}}, {"type": 3, "storyIdx": 1}, {"type": 1, "eventType": "SET_UI_FOCUS_TARGET_ID", "args": {"focusTargetId": 6, "redraw": true}, "isForceStep": false, "delay": 100}]}, {"id": 9, "module": "guide_rank", "mainScene": true, "force": true, "checkFunc": {"func": "checkRankUnlock"}, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 1}, {"type": 2, "nodeName": "View/UIPnl/left_top_n/menu/rank_be", "angle": -90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "rank/Rank"}, "showSkip": true}, {"type": 1, "eventType": "SHOW_ELFEGG_FLIGHT", "delay": 100}, {"type": 3, "storyIdx": 1}, {"type": 4, "nodeName": "View/RankPnl/root/loading_n/other/rank_top_n/1/ready/startStar_be", "showSkip": true, "restartPoint": 2}, {"type": 3, "storyIdx": 2}]}, {"id": 11, "module": "guide_gali", "mainScene": true, "force": true, "checkFunc": {"func": "checkGaliUnlock"}, "steps": [{"type": 0, "mod": "guideLogic", "func": "setRoleQiantaiServe", "delay": 100, "restartPoint": 0}, {"type": 3, "storyIdx": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/role_n/BUBBLE_TASK_14"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 2, "nodeName": "Wind/MainWind/map_n/role_n/BUBBLE_TASK_14", "angle": 90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/ServeTaskInfo"}}, {"type": 2, "nodeName": "View/ServeTaskInfoPnl/root/buttons_n/none/accept_be", "waitEvent": "PNL_LEAVE_211", "args": {"pnlKey": "common/ServeTaskInfo"}, "focus": false}, {"type": 3, "storyIdx": 1, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/menu/gail_be_n", "angle": 90, "waitEvent": "WIND_ENTER_303"}, {"type": 3, "storyIdx": 2}, {"type": 2, "nodeName": "View/GaLiWheelPnl/bottom/turntable/arrow_be", "angle": -90, "waitEvent": "UPDATE_PROP_COUNT", "showSkip": true, "restartPoint": 0}, {"type": 3, "storyIdx": 3}]}, {"id": 13, "module": "guide_gali_explore", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 2}, {"type": 2, "nodeName": "View/GaLiExplorePropPnl/root/start_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "gali/MiniGame"}, "focus": false, "showSkip": true}, {"type": 3, "storyIdx": 1}]}, {"id": 14, "module": "guide_delegation_task", "mainScene": true, "force": true, "checkFunc": {"func": "checkDelegationTaskUnlock"}, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/menu/task_be_n", "angle": 180, "fingerY": 40, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/Task"}, "showSkip": true}, {"type": 4, "nodeName": "View/TaskPnl/root/tab/tabs_tc_tce/2", "showSkip": true, "focus": false}, {"type": 2, "nodeName": "View/TaskPnl/root/pages_n/2/view/content/item_2_10000", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/ServeTaskInfo"}, "showSkip": true, "focus": false}, {"type": 2, "nodeName": "View/ServeTaskInfoPnl/root/buttons_n/claim_be", "waitEvent": "PNL_LEAVE_211", "args": {"pnlKey": "common/ServeTaskInfo"}, "showSkip": true, "focus": false}, {"type": 3, "storyIdx": 1, "restartPoint": 0}]}, {"id": 17, "module": "guide_gali_minigame", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 2}, {"type": 2, "nodeName": "View/MiniGamePnl/rooot_n/info_be_n", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "gali/GaLiMiniGameInfo"}, "showSkip": true}, {"type": 3, "storyIdx": 1}]}, {"id": 18, "module": "guide_magazine", "mainScene": true, "force": true, "trigger": {"func": "checkMagazineUnlock", "event": "CHANGE_MAIN"}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_journal"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_journal"}, "args": {"pnlKey": "magazine/BuyNewspaperTip"}, "focus": false, "waitEvent": "PNL_ENTER_210", "showSkip": true}, {"type": 2, "nodeName": "View/BuyNewspaperTipPnl/root/buy_be", "args": {"pnlKey": "magazine/Newspaper"}, "focus": false, "waitEvent": "PNL_ENTER_210", "showSkip": true}, {"type": 0, "mod": "guideLogic", "func": "setGuideFinish", "delay": 100}, {"type": 3, "storyIdx": 1}]}, {"id": 19, "module": "guide_cinema", "mainScene": false, "force": true, "trigger": {"func": "checkCinemaUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 6}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_6"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_6"}, "focus": false, "waitEvent": "WIND_ENTER_303"}, {"type": 0, "mod": "guideLogic", "func": "setRoleInCinemaChair", "delay": 100, "restartPoint": 0}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}, "angle": 180, "fingerY": 40}, {"type": 2, "nodeName": "View/BuildListPnl/rooot/list_n/view/content/item_0/list/lock/button/unlock_build_be", "waitEvent": "UNLOCK_CINEMA_BUILD", "focus": false, "checkDataSave": {"type": 6, "key": 3001}}, {"type": 3, "storyIdx": 2, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_CINEMA/signed", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "cinema/Signed"}, "angle": 180, "fingerY": 40}, {"type": 3, "storyIdx": 3}, {"type": 4, "nodeName": "View/SignedPnl/root/buttons_n/0/turn_be", "focus": false}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "waitEvent": "GUIDE_CLOSE_SIGNED_REWARD", "restartPoint": 2}, {"type": 0, "mod": "guideLogic", "func": "closeSignedGetRewardPnl", "delay": 100}, {"type": 3, "storyIdx": 4}, {"type": 2, "nodeName": "Wind/CinemaWind/map_n/role_n/ROWPIECE_1/guide_root", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "Row<PERSON><PERSON><PERSON>"}, "showSkip": true}, {"type": 3, "storyIdx": 5}, {"type": 2, "nodeName": "View/RowPiecePnl/root/content/list_n/item_2/buttons/select_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "cinema/SelectFilm"}, "focus": false, "showSkip": true}, {"type": 2, "nodeName": "View/SelectFilmPnl/rooot/list_sv/view/content/item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "cinema/FilmInfo"}, "focus": false, "showSkip": true}, {"type": 3, "storyIdx": 6}, {"type": 2, "nodeName": "View/FilmInfoPnl/root_n/button_n/select/select_be", "waitEvent": "SELECT_FILM", "focus": false, "showSkip": true}, {"type": 3, "storyIdx": 7, "specialStep": ["CINEMA_ROWPIECE"], "restartPoint": 0}, {"type": 4, "nodeName": "View/RowPiecePnl/root/buttons_n/play_be", "focus": false, "showSkip": true, "fingerY": 40}, {"type": 0, "mod": "guideLogic", "func": "setGuideFinish", "delay": 5000}, {"type": 0, "mod": "guideLogic", "func": "startViewing", "delay": 100, "specialStep": ["CINEMA_END"], "restartPoint": 0}, {"type": 3, "storyIdx": 8}, {"type": 0, "mod": "guideLogic", "func": "closeCinemaRowpiecePnl", "delay": 100}]}, {"id": 20, "module": "guide_yoyo", "mainScene": true, "force": true, "checkFunc": {"func": "checkYoyoUnlock"}, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 0, "mod": "guideLogic", "func": "grantYoyoReward", "delay": 1000, "restartPoint": 1}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/menu/shop_cn_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "shop/ShopInland"}, "angle": -90}, {"type": 3, "storyIdx": 2}, {"type": 1, "eventType": "HIDE_PNL_202", "args": "yoyo/YoyoJackpot", "delay": 100}, {"type": 4, "nodeName": "View/ShopInlandPnl/content_n/0/root/buttons_n/0/adq_be"}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "waitEvent": "YOYO_LOTTERY_COMPLETE", "restartPoint": 1}, {"type": 3, "storyIdx": 3}]}, {"id": 22, "module": "guide_room401", "mainScene": false, "force": true, "trigger": {"func": "check401RoomUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 401}, "steps": [{"type": 5, "time": 1500, "restartPoint": 1}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_401"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 0, "mod": "guideLogic", "func": "roleStandAt401Room", "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 0, "mod": "guideLogic", "func": "roleGoto401Room", "delay": 100}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_401"}, "focus": false, "waitEvent": "WIND_ENTER_303"}, {"type": 3, "storyIdx": 1, "restartPoint": 0, "specialStep": ["MAIRUI_STAND_IN"]}, {"type": 2, "nodeName": "Wind/Kefang5Wind/ui/furn", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/FurnitureList"}, "angle": -90, "showSkip": true}, {"type": 2, "nodeName": "View/FurnitureListPnl/rooot/pages_n/0/list/view/content/item_20/list/lock/button/unlock_furn_be", "waitEvent": "UNLOCK_FURN", "focus": false, "showSkip": true, "checkDataSave": {"type": 7, "key": "STAIRS_001", "unlockType": 3638}}, {"type": 0, "mod": "guideLogic", "func": "role1Goto401Erlou", "delay": 100, "restartPoint": 1}, {"type": 3, "storyIdx": 2, "specialStep": ["MAIRUI_STAND_ER_FLOOR"]}, {"type": 2, "nodeName": "Wind/Kefang5Wind/ui/furn", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/FurnitureList"}, "angle": -90, "focus": false, "showSkip": true}, {"type": 2, "nodeName": "View/FurnitureListPnl/rooot/pages_n/0/list/view/content/item_15/list/lock/button/unlock_furn_be", "waitEvent": "UNLOCK_FURN", "focus": false, "showSkip": true, "checkDataSave": {"type": 7, "key": "OUTPE_021", "unlockType": 2734}}, {"type": 0, "mod": "guideLogic", "func": "tangyongStandIn401Room", "delay": 100}, {"type": 3, "storyIdx": 3, "proceed": true, "restartPoint": 0, "specialStep": ["MAIRUI_STAND_ER_FLOOR", "TANGYONG_STAND_IN_ROOM"]}, {"type": 0, "mod": "guideLogic", "func": "role2Goto401Erlou", "delay": 100}, {"type": 3, "storyIdx": 4}, {"type": 0, "mod": "guideLogic", "func": "setGuideFinish", "delay": 100}, {"type": 0, "mod": "guideLogic", "func": "roleGoto401Shower", "delay": 100}]}, {"id": 23, "module": "guide_weather_rain", "mainScene": true, "force": true, "checkFunc": {"func": "checkWeather<PERSON>nlock"}, "steps": [{"type": 0, "mod": "guideLogic", "func": "createWeatherByType", "args": 3, "restartPoint": 2, "delay": 100}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "centre"}, "offsetPos": "-100,2000", "moveTime": 0.6}, "delay": 500}, {"type": 3, "storyIdx": 0}]}, {"id": 24, "module": "guide_weather_wind", "mainScene": true, "force": true, "steps": [{"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "centre"}, "offsetPos": "-100,2000", "moveTime": 0.6}, "delay": 500, "restartPoint": 2}, {"type": 3, "storyIdx": 0}]}, {"id": 25, "module": "guide_weather_fog", "mainScene": true, "force": true, "steps": [{"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "centre"}, "offsetPos": "-100,2000", "moveTime": 0.6}, "delay": 500, "restartPoint": 2}, {"type": 3, "storyIdx": 0}]}, {"id": 26, "module": "guide_role_car", "mainScene": true, "force": true, "checkFunc": {"func": "checkRoleTaxiUnlock"}, "steps": [{"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "centre"}, "offsetPos": "-100,2000", "moveTime": 0.6}, "delay": 100, "restartPoint": 0}, {"type": 0, "mod": "guideLogic", "func": "createHaHaToTaxi"}, {"type": 2, "nodeName": "Wind/MainWind/map_n/role_n/BUBBLE_NEED_TAXI", "waitEvent": "GUIDE_NEED_CREATE_TAXI"}, {"type": 3, "storyIdx": 0, "restartPoint": 2}, {"type": 1, "eventType": "GUIDE_WAIT_CREATE_TAXI", "delay": 100}, {"type": 0, "mod": "guideLogic", "func": "setGuideFinish", "delay": 100}]}, {"id": 27, "module": "guide_rank_egg", "mainScene": true, "force": true, "checkFunc": {"func": "checkRankEggUnlock"}, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/left_top_n/menu/bag_be", "angle": -90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/Bag"}, "showSkip": true}, {"type": 2, "nodeName": "View/BagPnl/root/list_sv/view/content/item_1/layout/item_be_10001/elf/open_elf_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BagIncubateElf"}, "checkDataSave": {"type": 9, "key": 10001}, "showSkip": true}, {"type": 5, "time": 2500, "restartPoint": 4}, {"type": 2, "nodeName": "View/BagIncubateElfPnl/root/buttons_nbe_n/ok", "waitEvent": "PNL_LEAVE_211", "args": {"pnlKey": "common/BagIncubateElf"}, "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER"}, {"type": 2, "nodeName": "Wind/MainWind/map_n/top_wg_w/ADD_MENU/1", "waitEvent": "GUIDE_ELF_EGG_USE", "showSkip": true}]}, {"id": 28, "module": "guide_staff", "mainScene": true, "force": true, "checkFunc": {"func": "checkStaffUnlock"}, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/staff_be_n", "angle": -90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "staff/Staff"}}, {"type": 2, "nodeName": "View/StaffPnl/root/pages_n/1/item1/item/item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "staff/StaffLockInfo"}, "checkDataSave": {"type": 8, "key": 130001}}, {"type": 2, "nodeName": "View/StaffLockInfoPnl/root/buttons_n/unlock_be", "waitEvent": "UNLOCK_STAFF"}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 2}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/StaffPnl/root/tab/tabs_tc_tce/2", "waitEvent": "GUIDE_STAFF_SWITCH_TAB", "showSkip": true, "specialStep": ["STAFF_LIST"]}, {"type": 2, "nodeName": "View/StaffPnl/root/pages_n/2/view/content/item", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "staff/StaffAssign"}, "showSkip": true}, {"type": 2, "nodeName": "View/StaffAssignPnl/root_n/tabs_n_nbe/0", "waitEvent": "GUIDE_STAFF_SWITCH_TAB", "showSkip": true}, {"type": 2, "nodeName": "View/StaffAssignPnl/root_n/select_n/root/items/view/content/select_be", "waitEvent": "GUIDE_COMMON_CLICK_EVENT", "showSkip": true}, {"type": 2, "nodeName": "View/StaffAssignPnl/root_n/select_n/root/buttons/assign_be", "waitEvent": "HIDE_ALL_PNL_203", "showSkip": true}, {"type": 3, "storyIdx": 2}, {"type": 0, "mod": "guideLogic", "func": "restoreStaffCamera", "delay": 100}]}, {"id": 29, "module": "guide_staff_training", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0, "nodeName": "View/StaffPnl/root/pages_n/0/map", "finger": false, "showSkip": true, "restartPoint": 0, "specialStep": ["STAFF_TRAINING"]}, {"type": 3, "storyIdx": 1, "nodeName": "View/StaffPnl/root/pages_n/0/select/bg", "finger": false, "showSkip": true}, {"type": 3, "storyIdx": 2}, {"type": 2, "nodeName": "View/StaffPnl/root/pages_n/0/buttons/search_be", "waitEvent": "GUIDE_COMMON_CLICK_EVENT", "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 1}, {"type": 3, "storyIdx": 3, "specialStep": ["STAFF_LIST"]}, {"type": 2, "nodeName": "View/StaffPnl/root/tab/tabs_tc_tce/1", "waitEvent": "GUIDE_STAFF_SWITCH_TAB", "showSkip": true}, {"type": 2, "nodeName": "View/StaffPnl/root/pages_n/1/item1/item/item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "staff/StaffInfo"}, "showSkip": true}, {"type": 2, "nodeName": "View/StaffInfoPnl/root/training_be_b", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "staff/StaffTraining"}, "showSkip": true}, {"type": 3, "storyIdx": 4}, {"type": 2, "nodeName": "View/StaffTrainingPnl/root/buttons_n/ok_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "staff/StaffTrainDeduce"}, "checkDataSave": {"type": 11, "key": 11001}, "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 2}, {"type": 3, "storyIdx": 5}]}, {"id": 30, "module": "guide_staff_wudong_skill", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 1}, {"type": 0, "mod": "guideLogic", "func": "showStaffWudongSkillView", "delay": 100}, {"type": 3, "storyIdx": 1, "continueTime": 1.5}]}, {"id": 31, "module": "guide_staff_taitan_skill1", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 1}, {"type": 0, "mod": "guideLogic", "func": "showStaffTaitanView", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "dining/FoodMining"}}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/FoodMiningPnl/bottom/skill_be_n", "waitEvent": "UDPATE_TAITAN_ANI", "showSkip": true}, {"type": 3, "storyIdx": 2}]}, {"id": 32, "module": "guide_staff_taitan_skill2", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0}]}, {"id": 33, "module": "guide_staff_paopao_skill", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 1}, {"type": 0, "mod": "guideLogic", "func": "showStaffPaopaoView", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "shower/CompStuff"}}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/CompStuffPnl/bottom/skill_be_n", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "shower/CompLvSelect"}, "showSkip": true}, {"type": 3, "storyIdx": 2}, {"type": 1, "eventType": "GUIDE_STAFF_PAOPAO_FINGER", "delay": 100}]}, {"id": 34, "module": "guide_gali_auction", "mainScene": false, "force": true, "checkFunc": {"func": "checkGaliAuctionUnlock"}, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 0, "mod": "guideLogic", "func": "grantGaliAuctionGift", "restartPoint": 1}, {"type": 2, "nodeName": "View/GaLiTopPnl/rooot/shop_be_n", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "gali/GaLiAuction"}, "showSkip": true}, {"type": 3, "storyIdx": 1, "preDelay": 1000}, {"type": 2, "nodeName": "View/GaLiAuctionPnl/root/props_n/serveItem_be/btns/1", "waitEvent": "UPDATE_GALI_AUCTION_BALANCE", "showSkip": true}, {"type": 3, "storyIdx": 2, "preDelay": 650, "restartPoint": 1}]}, {"id": 36, "module": "guide_beauty", "mainScene": false, "force": true, "trigger": {"func": "checkBeautyUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 8}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_8"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_8"}, "focus": false, "waitEvent": "WIND_ENTER_303", "showSkip": true}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}, "angle": 180, "fingerY": 40, "showSkip": true}, {"type": 2, "nodeName": "View/BuildListPnl/rooot/list_n/view/content/item_1/list/lock/button/unlock_build_be", "waitEvent": "UNLOCK_BEAUTY_BUILD", "focus": false, "checkDataSave": {"type": 10, "key": 6001}, "showSkip": true}, {"type": 3, "storyIdx": 2, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_BEAUTY/beauty_design", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "beauty/BeautyDesign"}, "angle": 180, "fingerY": 40}, {"type": 3, "storyIdx": 3, "nodeName": "View/BeautyDesignPnl/root/guide/1", "finger": false, "showSkip": true, "continueTime": 0.5}, {"type": 3, "storyIdx": 4}, {"type": 2, "nodeName": "View/BeautyDesignPnl/root/materials_n/1/list/view/content/item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "beauty/BMaterialsInfo"}, "showSkip": true}, {"type": 2, "nodeName": "View/BMaterialsInfoPnl/root_n/button_n/select_be", "waitEvent": "SELECT_BEAUTY_MATERIALS", "showSkip": true}, {"type": 2, "nodeName": "View/BeautyDesignPnl/root/materials_n/2/list/view/content/item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "beauty/BMaterialsInfo"}, "showSkip": true}, {"type": 2, "nodeName": "View/BMaterialsInfoPnl/root_n/button_n/select_be", "waitEvent": "SELECT_BEAUTY_MATERIALS", "showSkip": true}, {"type": 3, "storyIdx": 5}, {"type": 2, "nodeName": "View/BeautyDesignPnl/root/design_be_n", "focus": false, "showSkip": true, "waitEvent": "GUIDE_BEAUTY_DESIGN"}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 1}, {"type": 0, "mod": "guideLogic", "func": "roleStandAtBeauty", "delay": 100, "restartPoint": 3}, {"type": 3, "storyIdx": 6, "waitEvent": "GUIDE_BEAUTY_STYLINGEND"}, {"type": 3, "storyIdx": 7}]}, {"id": 37, "module": "guide_beauty_bed", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_BEAUTY/beauty_design", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "beauty/BeautyDesign"}, "angle": 180, "fingerY": 40}, {"type": 3, "storyIdx": 1}]}, {"id": 38, "module": "guide_beauty_facial_mask", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}, "angle": 180, "fingerY": 40}, {"type": 1, "eventType": "SET_UI_FOCUS_TARGET_ID", "args": {"focusTargetId": 13001, "redraw": true}, "isForceStep": false, "delay": 100}]}, {"id": 39, "module": "hide_sider_ui", "mainScene": true, "force": true, "steps": [{"type": 2, "nodeName": "View/UIPnl/left_top_n/menu/1", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "setting/Setting"}}, {"type": 2, "nodeName": "View/SettingPnl/root/buttons/other_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "setting/OtherSet"}}, {"type": 2, "nodeName": "View/OtherSetPnl/root/buttons_n/hide_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "setting/HideSiderBarUI"}}]}, {"id": 40, "module": "guide_beauty_spokers", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "changeNode": {"parent": "Wind/BeautyWind/map_n/role_n", "child": "guide_root"}, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "beauty/SpokespersonFrontPage"}, "showSkip": true}, {"type": 3, "storyIdx": 1}, {"type": 0, "mod": "guideLogic", "func": "goBeautySpokersNextStep"}, {"type": 3, "storyIdx": 2, "nodeName": "View/SpokespersonFrontPagePnl/root/bg/eq_shouye_box1", "finger": false, "showSkip": true}, {"type": 0, "mod": "guideLogic", "func": "goBeautySpokersNextStep2"}, {"type": 3, "storyIdx": 3, "nodeName": "View/SpokespersonFrontPagePnl/root/person1_n/status1_n/unlock/unlockperson1_be", "finger": false, "showSkip": true}, {"type": 3, "storyIdx": 4}, {"type": 2, "nodeName": "View/SpokespersonFrontPagePnl/root/bg_word/help_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "beauty/SpokespersonDiyHelp"}}, {"type": 0, "mod": "guideLogic", "func": "setGuideFinish", "delay": 100}]}, {"id": 41, "module": "regress_task", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 2, "nodeName": "View/UIPnl/right_buttom_n/guide_task_n_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "boluo/GuideTask"}}, {"type": 1, "delay": 1000}]}, {"id": 42, "module": "home_furn_mall", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 1, "eventType": "HIDE_PNL_202", "args": "home/HomeTaskUnlockTip", "delay": 100}, {"type": 2, "nodeName": "Wind/Kefang10000Wind/ui/furn", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "home/HomeFurnitureList"}, "angle": -90, "showSkip": true}, {"type": 2, "changeNode": {"parent": "View/HomeFurnitureListPnl/rooot/pages_n/0/list/view", "child": "goto_jjt_be"}, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "shop/ShopInland"}, "showSkip": true}, {"type": 3, "storyIdx": 1}]}, {"id": 43, "module": "home_add_new_space", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 1, "eventType": "HIDE_PNL_202", "args": "home/HomeTaskUnlockTip", "delay": 100}, {"type": 2, "nodeName": "Wind/Kefang10000Wind/ui/hse_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "home/HomeSpaceChoice"}, "angle": -90, "showSkip": true}, {"type": 4, "nodeName": "View/HomeSpaceChoicePnl/root_n/1/spaces_sv/view/content/item_1", "nodeDesc": "guide.node_add_new_space", "showSkip": true, "delay": 2000}, {"type": 1, "delay": 100}]}, {"id": 44, "module": "home_party", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 1, "eventType": "HIDE_PNL_202", "args": "home/HomeTaskUnlockTip", "delay": 100}, {"type": 2, "nodeName": "Wind/Kefang10000Wind/ui/other_be_n", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "home/Wardrobe"}, "angle": -90, "showSkip": true}, {"type": 2, "nodeName": "View/WardrobePnl/rooot/di/content_n/3/jbpd_be_n", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "home2/HomePartyNew"}, "showSkip": true}, {"type": 3, "storyIdx": 1}]}, {"id": 45, "module": "guide_newyear_2023", "mainScene": true, "force": true, "checkFunc": {"func": "checkNewyear2023Unlock"}, "steps": [{"type": 3, "storyIdx": 0}, {"type": 2, "nodeName": "View/UIPnl/top/left/trench_nbe_n/0", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "activities/NewYear"}}, {"type": 3, "storyIdx": 1}]}, {"id": 46, "module": "guide_newyear_2023_accu", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0}]}, {"id": 47, "module": "guide_newyear_2023_mama", "mainScene": true, "force": true, "steps": [{"type": 1, "delay": 5000}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/top_layer_n/MAMA_HELICOPTER/guide/root"}, "offsetPos": "-200,-800", "moveTime": 0.3}, "delay": 100}, {"type": 4, "nodeName": "Wind/MainWind/map_n/top_layer_n/MAMA_HELICOPTER/guide/root", "showSkip": true}, {"type": 1, "eventType": "PLAY_MAMA_DROP", "delay": 6200}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "centre"}, "offsetPos": "0,2000", "moveTime": 0.3}, "delay": 500}, {"type": 3, "storyIdx": 0}, {"type": 1, "eventType": "GUIDE_SPECIAL_DRAK_SCREEN", "args": {"id": 47, "progress": 6}}, {"type": 0, "mod": "guideLogic", "func": "showMamaEventGifPnl", "delay": 8000}, {"type": 1, "eventType": "GUIDE_SPECIAL_DRAK_SCREEN", "args": {"id": 47, "progress": 8}}, {"type": 3, "storyIdx": 1}, {"type": 1, "eventType": "PLAY_MAMA_FIREWORK", "args": true}, {"type": 0, "mod": "guideLogic", "func": "showMamaEventGifPnl2", "delay": 3500}, {"type": 1, "eventType": "GUIDE_SPECIAL_DRAK_SCREEN", "args": {"id": 47, "progress": 12}}, {"type": 1, "eventType": "PLAY_MAMA_FIREWORK", "args": false}, {"type": 0, "mod": "guideLogic", "func": "showMamaGift", "delay": 100}]}, {"id": 48, "module": "guide_valentine_2023_begin", "mainScene": true, "force": true, "checkFunc": {"func": "checkValentine2023Unlock"}, "steps": [{"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "centre"}, "offsetPos": "-100,2000", "moveTime": 0.6}, "delay": 100, "restartPoint": 0}, {"type": 3, "storyIdx": 0}, {"type": 1, "eventType": "GUIDE_SPECIAL_DRAK_SCREEN", "args": {"id": 48, "progress": 2}}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/main/menu/task_be_n", "angle": 180, "fingerY": 40, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/Task"}, "showSkip": true}, {"type": 3, "storyIdx": 2, "nodeName": "View/TaskPnl/root/pages_n/1/top/task_top_extra_award_be", "finger": false, "showSkip": true}]}, {"id": 49, "module": "guide_valentine_2023_end", "mainScene": true, "force": true, "steps": [{"type": 1, "eventType": "GUIDE_SPECIAL_DRAK_SCREEN", "args": {"id": 49, "progress": 0}}, {"type": 0, "mod": "guideLogic", "func": "show2023ValentineGifPnl"}, {"type": 3, "storyIdx": 0}, {"type": 0, "mod": "guideLogic", "func": "grant2023ValentineRewards", "delay": 100}]}, {"id": 50, "module": "guide_garden_unlock", "mainScene": false, "force": true, "trigger": {"func": "checkGardenUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 11}, "steps": [{"type": 5, "time": 1000, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_11"}, "offsetPos": "0,0", "moveTime": 0.8}, "delay": 100}, {"type": 3, "storyIdx": 0}]}, {"id": 51, "module": "guide_garden", "mainScene": false, "force": true, "steps": [{"type": 5, "time": 2000, "restartPoint": 1}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_11"}, "offsetPos": "0,0", "moveTime": 0.8}, "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_11"}, "focus": false, "showSkip": true, "waitEvent": "WIND_ENTER_303"}, {"type": 0, "mod": "guideLogic", "func": "chuishuStandInGarden", "delay": 500}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_GARDEN/garden_plant", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "garden/GardenPlantListPnl"}, "focus": false, "showSkip": true}, {"type": 2, "nodeName": "View/GardenPlantListPnl/root/pages_n/1/list/view/content/line_item_0/items/plantshop_item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "garden/GardenShopInfoPnl"}, "focus": false, "showSkip": true}, {"type": 2, "nodeName": "View/GardenShopInfoPnl/root_n/button_n/free_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "garden/GardenBuyPlantPnl"}, "focus": false, "showSkip": true}, {"type": 2, "nodeName": "View/GardenBuyPlantPnl/root/ok_be", "waitEvent": "MAP_ADD_PLANT", "restartPoint": 0, "focus": false, "specialStep": ["GARDEN_BUY_PLANT"], "showSkip": true}, {"type": 2, "nodeName": "Wind/GardenWind/map_n/addMenu/3", "waitEvent": "GUIDE_FURN_USE", "focus": false, "showSkip": true, "restartPoint": 1}, {"type": 3, "storyIdx": 2}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/GardenWind/map_n/role_n/PLANT_100501/body"}, "offsetPos": "0,0", "moveTime": 0.3}, "delay": 0.3}, {"type": 2, "nodeName": "Wind/GardenWind/map_n/role_n/PLANT_100501/body", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "garden/PlantActiveInfoPnl"}, "showSkip": true}, {"type": 2, "nodeName": "View/PlantActiveInfoPnl/root_n/unmaturity_n/status/0/water_be", "waitEvent": "UPDATE_HEART", "focus": false, "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 1}, {"type": 3, "storyIdx": 3, "finger": false}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_GARDEN/garden_mail", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "garden/GardenTaskPnl"}, "angle": 90, "focus": false, "showSkip": true}, {"type": 0, "mod": "guideLogic", "func": "chuishuHangOut", "delay": 100}]}, {"id": 52, "module": "guide_garden_diy", "mainScene": false, "force": true, "steps": [{"type": 0, "mod": "guideLogic", "func": "grantGardenFruits", "delay": 100}, {"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_GARDEN/garden_gardening", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "garden/GardenDiyBagPnl"}, "angle": -90, "focus": false, "showSkip": true, "checkDataSave": {"type": 13, "key": ""}}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/GardenDiyBagPnl/root/list_sv/view/content/item/layout/item_be_41001", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "garden/GardenDiyBagInfoPnl"}, "focus": false, "showSkip": true}, {"type": 2, "nodeName": "View/GardenDiyBagInfoPnl/root_n/button_n/ok_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "garden/GardenDiyPnl"}, "focus": false, "showSkip": true}, {"type": 3, "storyIdx": 2, "nodeName": "View/GardenDiyPnl/root/plant_tabs_sv/view/plant_tabs_tc_tce/0", "focus": false, "showSkip": true, "restartPoint": 0, "specialStep": ["GARDEN_DIY"]}, {"type": 3, "storyIdx": 3, "nodeName": "View/GardenDiyPnl/root/list_n/view/content/item_be_1", "focus": false, "showSkip": true}, {"type": 3, "storyIdx": 4, "nodeName": "View/GardenDiyPnl/root/bg/question_be", "focus": false, "showSkip": true}, {"type": 3, "storyIdx": 5}]}, {"id": 53, "module": "guide_tenant", "mainScene": false, "force": true, "checkFunc": {"func": "checkTenantUnlock"}, "steps": [{"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_201"}, "offsetPos": "200,0", "moveTime": 0.8}, "delay": 100, "restartPoint": 0}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_201"}, "focus": false, "waitEvent": "WIND_ENTER_303"}, {"type": 2, "nodeName": "Wind/Kefang1Wind/ui/tenant", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kef<PERSON>/Tenant"}, "angle": -90}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/TenantPnl/root/buttons_n/publish_be", "waitEvent": "GUIDE_CLICK_TENANT_PUBLISH"}, {"type": 5, "time": 1800, "restartPoint": 1}, {"type": 3, "storyIdx": 2, "specialStep": ["TENANT_TASKS"]}, {"type": 2, "nodeName": "View/TenantPnl/root/pages_n/1/tasks_nbe_n/0", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/TenantWishInfo"}}, {"type": 3, "storyIdx": 3}, {"type": 2, "nodeName": "View/TenantWishInfoPnl/root/buttons_n/select_be", "waitEvent": "HIDE_ALL_PNL_203", "showSkip": true}, {"type": 5, "time": 1800, "restartPoint": 1}, {"type": 3, "storyIdx": 4}, {"type": 1, "eventType": "GUIDE_SPECIAL_DRAK_SCREEN", "args": {"id": 53, "progress": 13}}, {"type": 3, "storyIdx": 5}, {"type": 2, "nodeName": "Wind/Kefang1Wind/ui/tenant", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/TenantRegister"}, "focus": false, "angle": -90, "showSkip": true}, {"type": 2, "nodeName": "View/TenantRegisterPnl/root/get_be_n", "waitEvent": "GUIDE_CLICK_TENANT_END", "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "waitEvent": "PNL_LEAVE_211", "args": {"pnlKey": "common/GeneralReward"}, "restartPoint": 3}, {"type": 0, "mod": "guideLogic", "func": "showTenantMessageView", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "kefang/TenantMessage"}}, {"type": 3, "storyIdx": 6}]}, {"id": 54, "module": "guide_mother_2023_start", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 0, "mod": "guideLogic", "func": "showMotherDayBtn", "delay": 200}, {"type": 2, "nodeName": "View/UIPnl/left_top_n/bottom_menu/mother_be_n", "angle": -90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "activities/WudongMomDiy"}, "showSkip": true}]}, {"id": 55, "module": "guide_mother_2023_end", "mainScene": true, "force": true, "checkFunc": {"func": "check2023MotherDayEnd"}, "steps": [{"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "centre"}, "offsetPos": "-100,2000", "moveTime": 0.6}, "delay": 100, "restartPoint": 2}, {"type": 3, "storyIdx": 0}]}, {"id": 56, "module": "guide_battle_pass_1", "mainScene": true, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 2, "nodeName": "View/UIPnl/left_top_n/bottom_menu/battle_pass_be_n", "angle": -90, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "activities/BattlePass"}, "showSkip": true}, {"type": 3, "storyIdx": 1}, {"type": 0, "mod": "guideLogic", "func": "grant20BPExp", "restartPoint": 1}, {"type": 2, "nodeName": "View/BattlePassPnl/root/list_sv/view/content/item_1/free_item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "activities/BattlePassReward"}, "specialStep": ["BATTLE_PASS"], "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 1}, {"type": 2, "nodeName": "View/BattlePassPnl/root/buttons_n/cost_reward_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "activities/BattlePassOne"}, "specialStep": ["BATTLE_PASS"], "showSkip": true}, {"type": 3, "storyIdx": 2}]}, {"id": 57, "module": "guide_add_friend", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 4, "nodeName": "View/FriendPnl/root/tab/tabs_tc_tce/1", "showSkip": true}, {"type": 3, "storyIdx": 1}, {"type": 4, "nodeName": "View/FriendPnl/root/content/1/mix_scroll_sv/view/content/apply/list/item/agree_be", "showSkip": true}, {"type": 4, "nodeName": "View/FriendPnl/root/tab/tabs_tc_tce/0", "showSkip": true, "restartPoint": 2}, {"type": 3, "nodeName": "View/FriendPnl/root/content/0/friend_list/content/friend_scroll_sv/view/content/item_698a6f50-a746-4a0e-9c4b-c6900d4ab32a/home_be", "finger": false, "storyIdx": 2, "showSkip": true}]}, {"id": 58, "module": "guide_divination", "mainScene": false, "force": true, "trigger": {"func": "checkDivinationUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 12}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_12"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_12"}, "focus": false, "waitEvent": "WIND_ENTER_303", "showSkip": true}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}, "angle": 180, "fingerY": 40, "showSkip": true}, {"type": 2, "nodeName": "View/BuildListPnl/rooot/list_n/view/content/item_1/list/lock/button/unlock_build_be", "waitEvent": "UNLOCK_DIVINATION_BUILD", "focus": false, "checkDataSave": {"type": 14, "key": 2001}, "showSkip": true}, {"type": 3, "storyIdx": 2, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_DIVINATION/shop", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "divination/DivinationShop"}, "angle": 180, "fingerY": 40}, {"type": 3, "storyIdx": 3}, {"type": 2, "nodeName": "View/DivinationShopPnl/root/items_sv/view/content/item/list/item_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "divination/DivinationShopInfo"}, "showSkip": true}, {"type": 2, "nodeName": "View/DivinationShopInfoPnl/root_n/button_n/buy_be", "waitEvent": "UPDATE_DIVINATION_SHOP_ITEM", "showSkip": true}, {"type": 3, "storyIdx": 4, "restartPoint": 0}, {"type": 0, "mod": "guideLogic", "func": "grantFreeTarots", "restartPoint": 1}, {"type": 3, "storyIdx": 5}, {"type": 0, "mod": "guideLogic", "func": "triggerDivinationMiletone", "restartPoint": 1}, {"type": 3, "storyIdx": 6, "nodeName": "Wind/DivinationWind/map_n/role_n/WISHTREE_1/root", "focus": false}, {"type": 2, "nodeName": "Wind/DivinationWind/map_n/role_n/TABLE_1/root", "waitEvent": "PNL_ENTER_210", "moveCamera": true, "args": {"pnlKey": "divination/Divinating", "target": {"parent": "Wind/DivinationWind/map_n/role_n/TABLE_1/root"}, "offsetPos": "0,0", "moveTime": 0.4}, "showSkip": true}, {"type": 3, "storyIdx": 7}]}, {"id": 59, "module": "guide_divination_sofa", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_DIVINATION/shop", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "divination/DivinationShop"}, "angle": 180, "fingerY": 40}]}, {"id": 60, "module": "guide_divination_medi", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}, "angle": 180, "fingerY": 40}, {"type": 1, "eventType": "SET_UI_FOCUS_TARGET_ID", "args": {"focusTargetId": 8001, "redraw": true}, "isForceStep": false, "delay": 100}]}, {"id": 61, "module": "guide_unlock_mail", "mainScene": true, "force": true, "trigger": {"func": "checkMailUnlock", "event": "CHANGE_MAIN"}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/role_n", "child": "LONG_LEGGED_MAILBOX"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 2, "nodeName": "Wind/MainWind/map_n/role_n/LONG_LEGGED_MAILBOX/guide", "focus": false, "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "mail/Mail"}}]}, {"id": 64, "module": "guide_furnish_diy", "mainScene": false, "force": true, "steps": [{"type": 2, "changeNode": {"parent": "View", "child": "opr_te_t"}, "nodeDesc": "guide.node_furnish_diy", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/FurnishDiyHelp"}, "showSkip": true}, {"type": 0, "mod": "guideLogic", "func": "setGuideFinish", "delay": 100}]}, {"id": 65, "module": "guide_clothing", "mainScene": false, "force": true, "trigger": {"func": "checkClothingUnlock", "event": "CHANGE_MAIN"}, "checkFunc": {"func": "checkMapUnlock", "arg": 13}, "steps": [{"type": 5, "time": 1500, "restartPoint": 0}, {"type": 1, "eventType": "GUIDE_MOVE_CAMERA", "args": {"target": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_13"}, "offsetPos": "0,0", "moveTime": 0.2}, "delay": 100}, {"type": 3, "storyIdx": 0}, {"type": 2, "changeNode": {"parent": "Wind/MainWind/map_n/bg", "child": "guide_13"}, "focus": false, "waitEvent": "WIND_ENTER_303", "showSkip": true}, {"type": 3, "storyIdx": 1}, {"type": 2, "nodeName": "View/UIPnl/scene_button_n/build_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/BuildList"}, "angle": 180, "fingerY": 40, "showSkip": true}, {"type": 2, "nodeName": "View/BuildListPnl/rooot/list_n/view/content/item_0/list/lock/button/unlock_build_be", "waitEvent": "UNLOCK_CLOTHING_BUILD", "focus": false, "checkDataSave": {"type": 15, "key": 1001}}, {"type": 3, "storyIdx": 2, "restartPoint": 0}, {"type": 2, "nodeName": "Wind/ClothingWind/map_n/role_n/BENCH_1/npc/0/MIANYANG/root", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "clothing/ClothingGame"}, "showSkip": true}, {"type": 3, "storyIdx": 3}, {"type": 2, "nodeName": "View/ClothingGamePnl/root/gemeLayer_n/item_0", "waitEvent": "GUIDE_CLICK_STEP", "specialStep": ["CLOTHING_GAMING"], "showSkip": true}, {"type": 2, "nodeName": "View/ClothingGamePnl/root/gemeLayer_n/item_1", "waitEvent": "GUIDE_CLICK_STEP", "specialStep": ["CLOTHING_GAMING"], "showSkip": true}, {"type": 3, "storyIdx": 4, "specialStep": ["CLOTHING_GAMING"], "restartPoint": 0}, {"type": 2, "nodeName": "View/ClothingGamePnl/root/gemeLayer_n/item_2", "waitEvent": "GUIDE_CLICK_STEP", "specialStep": ["CLOTHING_GAMING"], "showSkip": true}, {"type": 2, "nodeName": "View/ClothingGamePnl/root/gemeLayer_n/item_4", "waitEvent": "GUIDE_CLICK_STEP", "specialStep": ["CLOTHING_GAMING"], "showSkip": true}, {"type": 2, "nodeName": "View/ClothingGamePnl/bottom/crop_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "clothing/ClothingWork"}, "specialStep": ["CLOTHING_GAMING"], "restartPoint": 0, "showSkip": true}, {"type": 3, "storyIdx": 5, "restartPoint": 0, "specialStep": ["CLOTHING_WORK"]}, {"type": 2, "nodeName": "View/ClothingWorkPnl/root/crop/qipao_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "common/GeneralReward"}, "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 1}, {"type": 2, "nodeName": "View/ClothingWorkPnl/root/unlock_be", "specialStep": ["CLOTHING_WORK"], "waitEvent": "GUIDE_CLICK_STEP", "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER"}, {"type": 3, "storyIdx": 6, "specialStep": ["CLOTHING_WORK"], "restartPoint": 0}, {"type": 2, "nodeName": "View/ClothingWorkPnl/root/cut_be_n", "specialStep": ["CLOTHING_WORK"], "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "clothing/ClothesMadeTip"}, "showSkip": true}, {"type": 2, "nodeName": "View/ClothesMadeTipPnl/root/unlock_be", "waitEvent": "GUIDE_CLICK_STEP", "showSkip": true}, {"type": 1, "eventType": "HIDE_GUIDE_FINGER", "restartPoint": 1}, {"type": 3, "storyIdx": 7, "specialStep": ["CLOTHING_WORK"]}, {"type": 0, "mod": "guideLogic", "func": "grantFreeCloth", "restartPoint": 1}, {"type": 3, "storyIdx": 8}]}, {"id": 66, "module": "guide_clothing_game", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 2, "nodeName": "View/ClothingGamePnl/root/bg/spe_game_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "clothing/ClothingSpeStart"}, "showSkip": true}, {"type": 3, "storyIdx": 1}]}, {"id": 67, "module": "guide_rabbit_shop", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0}]}, {"id": 68, "module": "guide_clothing_diy", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_CLOTHING/diy", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "clothing/ClothingDIY"}, "angle": 180, "fingerY": 40, "showSkip": true}]}, {"id": 69, "module": "guide_bee", "mainScene": false, "force": true, "steps": [{"type": 3, "storyIdx": 0, "restartPoint": 0, "hidePnl": ["common/BuildList"]}, {"type": 0, "mod": "drink", "func": "gen<PERSON><PERSON>", "delay": 300}, {"type": 2, "nodeName": "Wind/DormWind/map_n/role_n/BEE/body/touch", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "dorm/DrinkMaker"}, "showSkip": false}, {"type": 4, "changeNode": {"parent": "View/DrinkMakerPnl/root/conds_n/items_sv/view/content", "child": "item_be_0", "delay": 100}}, {"type": 4, "changeNode": {"parent": "View/DrinkMakerPnl/root/conds_n/items_sv/view/content", "child": "item_be_1"}}, {"type": 2, "nodeName": "View/DrinkMakerPnl/root/buttons_n/ok_be", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "dorm/DrinkGame"}, "showSkip": true}, {"type": 4, "changeNode": {"parent": "View/DrinkGamePnl/root/game_n/2", "child": "stop_move_be"}, "restartPoint": 1}, {"type": 3, "storyIdx": 1, "hidePnl": ["common/BuildList"]}, {"type": 2, "nodeName": "View/UIPnl/buttom_n/UI_DORM/shop/val", "waitEvent": "PNL_ENTER_210", "args": {"pnlKey": "dorm/DrinkShop"}, "showSkip": false, "delay": 1000, "hidePnl": ["dorm/DrinkGame", "dorm/DrinkMaker", "dorm/DrinkReward", "common/BuildList"]}]}]