[{"id": 1, "name": "returnTaskDesc.1", "desc": "returnTaskDesc.10001", "sort": 1, "reward": "1,-1,5000", "icon": "main/xj_icon_zlkr", "condition": 5001, "prev_id": "", "next_id": 2, "goto": "s:1,guide:4@1"}, {"id": 2, "name": "returnTaskDesc.2", "desc": "returnTaskDesc.10002", "sort": 2, "reward": "2,-1,20", "icon": "task/phb_xjrw_25", "condition": 5002, "prev_id": "", "next_id": 3, "goto": "guide:1@1"}, {"id": 3, "name": "returnTaskDesc.3", "desc": "returnTaskDesc.10003", "sort": 3, "reward": "401,-1,5", "icon": "task/dl_sx_xfsx", "condition": 5003, "prev_id": "", "next_id": 4, "goto": "s:1,guide:3"}, {"id": 4, "name": "returnTaskDesc.4", "desc": "returnTaskDesc.10004", "sort": 4, "reward": "401,-1,5", "icon": "task/phb_xjrw_7", "condition": 5004, "prev_id": "", "next_id": 5, "goto": ""}, {"id": 5, "name": "returnTaskDesc.5", "desc": "returnTaskDesc.10005", "sort": 5, "reward": "402,-1,5", "icon": "main/xj_icon_wjcy", "condition": 5005, "prev_id": "", "next_id": 6, "goto": "s:3,u:FoodMining,guide:102@0"}, {"id": 6, "name": "returnTaskDesc.6", "desc": "returnTaskDesc.10006", "sort": 6, "reward": "2,-1,10", "icon": "task/phb_xjrw_3", "condition": 5006, "prev_id": "", "next_id": 7, "goto": "s:1,guide:4@1"}, {"id": 7, "name": "returnTaskDesc.7", "desc": "returnTaskDesc.10007", "sort": 7, "reward": "401,-1,5", "icon": "task/zjm_bt_cj01", "condition": 5007, "prev_id": "", "next_id": 8, "goto": "s:10,u:shop/ShopInland"}, {"id": 8, "name": "returnTaskDesc.8", "desc": "returnTaskDesc.10008", "sort": 8, "reward": "1,-1,5000", "icon": "rank/hqxj_icon_xx_2", "condition": 5008, "prev_id": "", "next_id": 9, "goto": "s:1,u:rank/Rank,tab:1"}, {"id": 9, "name": "returnTaskDesc.9", "desc": "returnTaskDesc.10009", "sort": 9, "reward": "2,-1,5", "icon": "prop/icon_bz", "condition": 5009, "prev_id": "", "next_id": 10, "goto": ""}, {"id": 10, "name": "returnTaskDesc.10", "desc": "returnTaskDesc.10010", "sort": 10, "reward": "1,-1,3000", "icon": "task/phb_xjrw_49", "condition": 5010, "prev_id": "", "next_id": 11, "goto": "s:1,u:staff/Staff,tab:0"}, {"id": 11, "name": "returnTaskDesc.11", "desc": "returnTaskDesc.10011", "sort": 11, "reward": "2,-1,5", "icon": "achieve_icon/icon_fxsjs", "condition": 5011, "prev_id": "", "next_id": 12, "goto": "s:1,u:staff/Staff,tab:1"}, {"id": 12, "name": "returnTaskDesc.12", "desc": "returnTaskDesc.10012", "sort": 12, "reward": "2,-1,5", "icon": "task/phb_xjrw_52", "condition": 5012, "prev_id": "", "next_id": 13, "goto": "s:1,guide:9@2"}, {"id": 13, "name": "returnTaskDesc.13", "desc": "returnTaskDesc.10013", "sort": 13, "reward": "1,-1,3000", "icon": "task/phb_xjrw_23", "condition": 5013, "prev_id": "", "next_id": 14, "goto": "s:7"}, {"id": 14, "name": "returnTaskDesc.14", "desc": "returnTaskDesc.10014", "sort": 14, "reward": "1,-1,3000", "icon": "task/phb_xjrw_26", "condition": 5014, "prev_id": "", "next_id": 15, "goto": "s:7"}, {"id": 15, "name": "returnTaskDesc.15", "desc": "returnTaskDesc.10015", "sort": 15, "reward": "4,9100,25", "icon": "task/phb_xjrw_48", "condition": 5015, "prev_id": "", "next_id": 16, "goto": "s:7,u:gali/GaLiAuction"}, {"id": 16, "name": "returnTaskDesc.16", "desc": "returnTaskDesc.10016", "sort": 16, "reward": "4,9100,25", "icon": "main/xj_icon_hccl", "condition": 5016, "prev_id": "", "next_id": 17, "goto": "s:2,u:shower/CompStuff"}, {"id": 17, "name": "returnTaskDesc.17", "desc": "returnTaskDesc.10017", "sort": 17, "reward": "2,-1,10", "icon": "achieve_icon/icon_hcxns", "condition": 5017, "prev_id": "", "next_id": 18, "goto": "s:2,u:shower/CompStuff"}, {"id": 18, "name": "returnTaskDesc.18", "desc": "returnTaskDesc.10018", "sort": 18, "reward": "2,-1,10", "icon": "achieve_icon/icon_djtd", "condition": 5018, "prev_id": "", "next_id": 19, "goto": "s:1,u:home/Wardrobe,tab:0"}, {"id": 19, "name": "returnTaskDesc.19", "desc": "returnTaskDesc.10019", "sort": 19, "reward": "2,-1,10", "icon": "task/phb_xjrw_5", "condition": 5019, "prev_id": "", "next_id": 20, "goto": "s:6,u:<PERSON><PERSON><PERSON><PERSON>"}, {"id": 20, "name": "returnTaskDesc.20", "desc": "returnTaskDesc.10020", "sort": 20, "reward": "4,9100,25", "icon": "other/rcgm_xhh_icon02", "condition": 5020, "prev_id": "", "next_id": 21, "goto": "s:6,guide:410@0"}, {"id": 21, "name": "returnTaskDesc.21", "desc": "returnTaskDesc.10021", "sort": 21, "reward": "4,9100,25", "icon": "achieve_icon/icon_ypqyzj", "condition": 5021, "prev_id": "", "next_id": 22, "goto": "s:6,u:cinema/Signed"}, {"id": 22, "name": "returnTaskDesc.22", "desc": "returnTaskDesc.10022", "sort": 22, "reward": "4,9100,25", "icon": "staff_assign_map/ygxt_ui_cdicon_dl", "condition": 5022, "prev_id": "", "next_id": 23, "goto": "s:1,u:home/Wardrobe,tab:1"}, {"id": 23, "name": "returnTaskDesc.23", "desc": "returnTaskDesc.10023", "sort": 23, "reward": "2,-1,25", "icon": "task/phb_xjrw_4", "condition": 5023, "prev_id": "", "next_id": 24, "goto": "s:1,u:friend/Friend"}, {"id": 24, "name": "returnTaskDesc.24", "desc": "returnTaskDesc.10024", "sort": 24, "reward": "4,9100,25", "icon": "other/rcgm_kh_icon02", "condition": 5024, "prev_id": "", "next_id": 25, "goto": "s:8,guide:413@0"}, {"id": 25, "name": "returnTaskDesc.25", "desc": "returnTaskDesc.10025", "sort": 25, "reward": "4,9100,25", "icon": "task/phb_xjrw_50", "condition": 5025, "prev_id": "", "next_id": "", "goto": "s:8,u:beauty/BeautyDesign"}]