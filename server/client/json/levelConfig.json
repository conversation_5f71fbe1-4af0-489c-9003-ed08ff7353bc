[{"level_id": 1, "drop": "801,1401,1900|801,1402,0|801,1403,0|801,1404,100|801,1405,0|801,1406,0", "goal": "801,1301,10", "time": 20, "min_interval": 0.7, "max_interval": 0.75}, {"level_id": 2, "drop": "801,1401,1800|801,1402,500|801,1403,0|801,1404,100|801,1405,0|801,1406,0", "goal": "801,1301,15", "time": 20, "min_interval": 0.7, "max_interval": 0.75}, {"level_id": 3, "drop": "801,1401,2200|801,1402,900|801,1403,0|801,1404,125|801,1405,0|801,1406,0", "goal": "801,1301,20", "time": 25, "min_interval": 0.7, "max_interval": 0.75}, {"level_id": 4, "drop": "801,1401,2000|801,1402,1500|801,1403,0|801,1404,1000|801,1405,0|801,1406,0", "goal": "801,1401,12", "time": 25, "min_interval": 0.7, "max_interval": 0.75}, {"level_id": 5, "drop": "801,1401,2000|801,1402,1300|801,1403,0|801,1404,250|801,1405,50|801,1406,100", "goal": "801,1301,25", "time": 25, "min_interval": 0.7, "max_interval": 0.75}, {"level_id": 6, "drop": "801,1401,1500|801,1402,1000|801,1403,0|801,1404,0|801,1405,50|801,1406,0", "goal": "801,1401,15", "time": 20, "min_interval": 0.2, "max_interval": 0.2}, {"level_id": 7, "drop": "801,1401,2100|801,1402,1350|801,1403,600|801,1404,500|801,1405,80|801,1406,120", "goal": "801,1301,35", "time": 30, "min_interval": 0.7, "max_interval": 0.7}, {"level_id": 8, "drop": "801,1401,2500|801,1402,1500|801,1403,700|801,1404,600|801,1405,100|801,1406,130", "goal": "801,1301,45", "time": 30, "min_interval": 0.7, "max_interval": 0.7}, {"level_id": 9, "drop": "801,1401,3700|801,1402,2500|801,1403,300|801,1404,900|801,1405,100|801,1406,130", "goal": "801,1301,50", "time": 35, "min_interval": 0.7, "max_interval": 0.7}, {"level_id": 10, "drop": "801,1401,2300|801,1402,1600|801,1403,800|801,1404,800|801,1405,120|801,1406,150", "goal": "801,1401,20", "time": 35, "min_interval": 0.7, "max_interval": 0.7}, {"level_id": 11, "drop": "801,1401,2500|801,1402,1700|801,1403,850|801,1404,800|801,1405,130|801,1406,160", "goal": "801,1301,50", "time": 35, "min_interval": 0.65, "max_interval": 0.7}, {"level_id": 12, "drop": "801,1401,2600|801,1402,1200|801,1403,0|801,1404,0|801,1405,150|801,1406,0", "goal": "801,1301,35", "time": 20, "min_interval": 0.2, "max_interval": 0.2}, {"level_id": 13, "drop": "801,1401,2700|801,1402,1900|801,1403,1000|801,1404,1000|801,1405,150|801,1406,180", "goal": "801,1402,10", "time": 35, "min_interval": 0.65, "max_interval": 0.7}, {"level_id": 14, "drop": "801,1401,2800|801,1402,2000|801,1403,800|801,1404,1600|801,1405,160|801,1406,190", "goal": "801,1401,25", "time": 40, "min_interval": 0.65, "max_interval": 0.7}, {"level_id": 15, "drop": "801,1401,2900|801,1402,2200|801,1403,950|801,1404,1200|801,1405,170|801,1406,200", "goal": "801,1301,55", "time": 40, "min_interval": 0.65, "max_interval": 0.7}, {"level_id": 16, "drop": "801,1401,3400|801,1402,2500|801,1403,1000|801,1404,1300|801,1405,180|801,1406,210", "goal": "801,1401,30", "time": 40, "min_interval": 0.65, "max_interval": 0.65}, {"level_id": 17, "drop": "801,1401,4000|801,1402,1800|801,1403,600|801,1404,2800|801,1405,100|801,1406,150", "goal": "801,1301,40", "time": 40, "min_interval": 0.65, "max_interval": 0.65}, {"level_id": 18, "drop": "801,1401,3000|801,1402,2200|801,1403,800|801,1404,1800|801,1405,200|801,1406,230", "goal": "801,1402,12", "time": 40, "min_interval": 0.65, "max_interval": 0.65}, {"level_id": 19, "drop": "801,1401,3300|801,1402,3000|801,1403,1100|801,1404,1400|801,1405,210|801,1406,240", "goal": "801,1301,60", "time": 40, "min_interval": 0.65, "max_interval": 0.65}, {"level_id": 20, "drop": "801,1401,3400|801,1402,3500|801,1403,1300|801,1404,1700|801,1405,220|801,1406,250", "goal": "801,1301,60", "time": 40, "min_interval": 0.65, "max_interval": 0.65}, {"level_id": 21, "drop": "801,1401,3000|801,1402,2500|801,1403,1400|801,1404,2100|801,1405,150|801,1406,200", "goal": "801,1402,15", "time": 40, "min_interval": 0.6, "max_interval": 0.65}, {"level_id": 22, "drop": "801,1401,3600|801,1402,3000|801,1403,1200|801,1404,1600|801,1405,240|801,1406,270", "goal": "801,1301,65", "time": 40, "min_interval": 0.6, "max_interval": 0.65}, {"level_id": 23, "drop": "801,1401,3700|801,1402,3500|801,1403,1300|801,1404,1400|801,1405,250|801,1406,280", "goal": "801,1301,70", "time": 40, "min_interval": 0.6, "max_interval": 0.65}, {"level_id": 24, "drop": "801,1401,3600|801,1402,2800|801,1403,1000|801,1404,1500|801,1405,260|801,1406,290", "goal": "801,1401,35", "time": 40, "min_interval": 0.6, "max_interval": 0.65}, {"level_id": 25, "drop": "801,1401,0|801,1402,2000|801,1403,1000|801,1404,0|801,1405,50|801,1406,0", "goal": "801,1301,70", "time": 20, "min_interval": 0.2, "max_interval": 0.2}, {"level_id": 26, "drop": "801,1401,3800|801,1402,3700|801,1403,1300|801,1404,1500|801,1405,280|801,1406,310", "goal": "801,1301,75", "time": 40, "min_interval": 0.55, "max_interval": 0.6}, {"level_id": 27, "drop": "801,1401,3400|801,1402,3600|801,1403,1100|801,1404,1400|801,1405,290|801,1406,320", "goal": "801,1401,35", "time": 40, "min_interval": 0.55, "max_interval": 0.6}, {"level_id": 28, "drop": "801,1401,4500|801,1402,3000|801,1403,2000|801,1404,4000|801,1405,300|801,1406,330", "goal": "801,1301,65", "time": 40, "min_interval": 0.55, "max_interval": 0.6}, {"level_id": 29, "drop": "801,1401,4000|801,1402,2000|801,1403,1200|801,1404,1500|801,1405,300|801,1406,340", "goal": "801,1301,70", "time": 40, "min_interval": 0.55, "max_interval": 0.6}, {"level_id": 30, "drop": "801,1401,4000|801,1402,2500|801,1403,1200|801,1404,3200|801,1405,320|801,1406,350", "goal": "801,1301,75", "time": 45, "min_interval": 0.55, "max_interval": 0.6}]