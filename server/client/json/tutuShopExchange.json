[{"id": 42, "reward": "4,9401,1", "sort": 1, "limit": 3, "cost": 200, "isShow": 1}, {"id": 43, "reward": "4,9402,1", "sort": 2, "limit": 3, "cost": 125, "isShow": 1}, {"id": 9403, "reward": "4,9403,1", "sort": 2, "limit": 3, "cost": 200, "isShow": 1}, {"id": 9404, "reward": "4,9404,1", "sort": 2, "limit": 2, "cost": 200, "isShow": 0}, {"id": 44, "reward": "10,36,1", "sort": 3, "limit": 1, "cost": 60, "isShow": 1}, {"id": 45, "reward": "10,38,1", "sort": 4, "limit": 1, "cost": 55, "isShow": 1}, {"id": 3, "reward": "4,30101,1", "sort": 5, "limit": -1, "cost": 35, "isShow": 1}, {"id": 4, "reward": "4,30102,1", "sort": 6, "limit": -1, "cost": 45, "isShow": 1}, {"id": 5, "reward": "4,30103,1", "sort": 7, "limit": -1, "cost": 20, "isShow": 1}, {"id": 6, "reward": "4,30104,1", "sort": 8, "limit": -1, "cost": 20, "isShow": 1}, {"id": 7, "reward": "4,30105,1", "sort": 9, "limit": -1, "cost": 11, "isShow": 1}, {"id": 8, "reward": "4,30106,1", "sort": 10, "limit": -1, "cost": 22, "isShow": 1}, {"id": 9, "reward": "4,30107,1", "sort": 11, "limit": -1, "cost": 11, "isShow": 1}, {"id": 10, "reward": "4,30108,1", "sort": 12, "limit": -1, "cost": 11, "isShow": 1}, {"id": 11, "reward": "4,30109,1", "sort": 13, "limit": -1, "cost": 11, "isShow": 1}, {"id": 12, "reward": "307,FLOOR_034,2107", "sort": 14, "limit": 1, "cost": 15, "isShow": 1}, {"id": 13, "reward": "4,30201,1", "sort": 15, "limit": -1, "cost": 11, "isShow": 1}, {"id": 14, "reward": "4,30202,1", "sort": 16, "limit": -1, "cost": 13, "isShow": 1}, {"id": 15, "reward": "4,30203,1", "sort": 17, "limit": -1, "cost": 13, "isShow": 1}, {"id": 16, "reward": "4,30204,1", "sort": 18, "limit": -1, "cost": 25, "isShow": 1}, {"id": 17, "reward": "4,30205,1", "sort": 19, "limit": -1, "cost": 25, "isShow": 1}, {"id": 18, "reward": "4,30206,1", "sort": 20, "limit": -1, "cost": 15, "isShow": 1}, {"id": 30401, "reward": "4,30401,1", "sort": 21, "limit": -1, "cost": 25, "isShow": 1}, {"id": 30402, "reward": "4,30402,1", "sort": 22, "limit": -1, "cost": 40, "isShow": 1}, {"id": 30403, "reward": "4,30403,1", "sort": 23, "limit": -1, "cost": 50, "isShow": 1}, {"id": 30404, "reward": "4,30404,1", "sort": 24, "limit": -1, "cost": 30, "isShow": 1}, {"id": 30405, "reward": "4,30405,1", "sort": 25, "limit": -1, "cost": 40, "isShow": 1}, {"id": 30406, "reward": "4,30406,1", "sort": 26, "limit": -1, "cost": 11, "isShow": 1}, {"id": 30407, "reward": "4,30407,1", "sort": 27, "limit": -1, "cost": 50, "isShow": 1}, {"id": 30408, "reward": "4,30408,1", "sort": 28, "limit": -1, "cost": 13, "isShow": 1}, {"id": 30409, "reward": "4,30409,1", "sort": 29, "limit": -1, "cost": 20, "isShow": 1}, {"id": 30410, "reward": "4,30410,1", "sort": 30, "limit": -1, "cost": 25, "isShow": 1}, {"id": 30411, "reward": "4,30411,1", "sort": 31, "limit": -1, "cost": 15, "isShow": 1}, {"id": 30412, "reward": "4,30412,1", "sort": 32, "limit": -1, "cost": 11, "isShow": 1}, {"id": 30413, "reward": "4,30413,1", "sort": 33, "limit": -1, "cost": 25, "isShow": 1}, {"id": 30414, "reward": "4,30414,1", "sort": 34, "limit": -1, "cost": 40, "isShow": 1}, {"id": 30415, "reward": "4,30415,1", "sort": 35, "limit": -1, "cost": 38, "isShow": 1}, {"id": 46, "reward": "307,FLOOR_HOME_102,20100", "sort": 36, "limit": 1, "cost": 17, "isShow": 1}, {"id": 47, "reward": "307,WALL_HOME_102,20200", "sort": 37, "limit": 1, "cost": 18, "isShow": 1}, {"id": 48, "reward": "307,HANDDRAIL_HOME_102,20300", "sort": 38, "limit": 1, "cost": 25, "isShow": 1}, {"id": 49, "reward": "307,WALL_094,2606", "sort": 39, "limit": 1, "cost": 11, "isShow": 1}, {"id": 50, "reward": "307,FLOOR_042,2107", "sort": 40, "limit": 1, "cost": 11, "isShow": 1}, {"id": 19, "reward": "4,30301,1", "sort": 41, "limit": -1, "cost": 15, "isShow": 1}, {"id": 20, "reward": "4,30302,1", "sort": 42, "limit": -1, "cost": 16, "isShow": 1}, {"id": 21, "reward": "4,30303,1", "sort": 43, "limit": -1, "cost": 25, "isShow": 1}, {"id": 22, "reward": "4,30304,1", "sort": 44, "limit": -1, "cost": 13, "isShow": 1}, {"id": 23, "reward": "4,30305,1", "sort": 45, "limit": -1, "cost": 35, "isShow": 1}, {"id": 24, "reward": "4,30306,1", "sort": 46, "limit": -1, "cost": 11, "isShow": 1}, {"id": 25, "reward": "4,30307,1", "sort": 47, "limit": -1, "cost": 13, "isShow": 1}, {"id": 26, "reward": "4,30308,1", "sort": 48, "limit": -1, "cost": 45, "isShow": 1}, {"id": 27, "reward": "4,30309,1", "sort": 49, "limit": -1, "cost": 11, "isShow": 1}, {"id": 28, "reward": "4,30310,1", "sort": 50, "limit": -1, "cost": 15, "isShow": 1}, {"id": 29, "reward": "4,30311,1", "sort": 51, "limit": -1, "cost": 11, "isShow": 1}, {"id": 30, "reward": "4,30312,1", "sort": 52, "limit": -1, "cost": 35, "isShow": 1}, {"id": 31, "reward": "4,30313,1", "sort": 53, "limit": -1, "cost": 43, "isShow": 1}, {"id": 32, "reward": "4,30314,1", "sort": 54, "limit": -1, "cost": 16, "isShow": 1}, {"id": 33, "reward": "4,30315,1", "sort": 55, "limit": -1, "cost": 13, "isShow": 1}, {"id": 34, "reward": "4,30316,1", "sort": 56, "limit": -1, "cost": 27, "isShow": 1}, {"id": 35, "reward": "4,30317,1", "sort": 57, "limit": -1, "cost": 11, "isShow": 1}, {"id": 36, "reward": "4,30318,1", "sort": 58, "limit": -1, "cost": 30, "isShow": 1}, {"id": 37, "reward": "307,FLOOR_HOME_101,20100", "sort": 59, "limit": 1, "cost": 27, "isShow": 1}, {"id": 38, "reward": "307,WALL_HOME_101,20200", "sort": 60, "limit": 1, "cost": 15, "isShow": 1}, {"id": 39, "reward": "307,HANDDRAIL_HOME_101,20300", "sort": 61, "limit": 1, "cost": 25, "isShow": 1}, {"id": 40, "reward": "307,WALL_092,2606", "sort": 62, "limit": 1, "cost": 11, "isShow": 1}, {"id": 41, "reward": "307,FLOOR_041,2107", "sort": 63, "limit": 1, "cost": 11, "isShow": 1}, {"id": 8801, "reward": "4,8801,1", "sort": 64, "limit": -1, "cost": 15, "isShow": 1}, {"id": 8802, "reward": "4,8802,1", "sort": 65, "limit": -1, "cost": 20, "isShow": 1}, {"id": 8803, "reward": "4,8803,1", "sort": 66, "limit": -1, "cost": 27, "isShow": 1}, {"id": 8804, "reward": "4,8804,1", "sort": 67, "limit": -1, "cost": 25, "isShow": 1}, {"id": 8805, "reward": "4,8805,1", "sort": 68, "limit": -1, "cost": 25, "isShow": 1}, {"id": 8806, "reward": "4,8806,1", "sort": 69, "limit": -1, "cost": 20, "isShow": 1}, {"id": 8807, "reward": "4,8807,1", "sort": 70, "limit": -1, "cost": 45, "isShow": 1}, {"id": 8808, "reward": "4,8808,1", "sort": 71, "limit": -1, "cost": 42, "isShow": 1}, {"id": 8809, "reward": "4,8809,1", "sort": 72, "limit": -1, "cost": 23, "isShow": 1}, {"id": 8810, "reward": "4,8810,1", "sort": 73, "limit": -1, "cost": 25, "isShow": 1}, {"id": 8811, "reward": "4,8811,1", "sort": 74, "limit": -1, "cost": 12, "isShow": 1}, {"id": 8812, "reward": "4,8812,1", "sort": 75, "limit": -1, "cost": 11, "isShow": 1}, {"id": 8813, "reward": "4,8813,1", "sort": 76, "limit": -1, "cost": 18, "isShow": 1}, {"id": 8814, "reward": "4,8814,1", "sort": 77, "limit": -1, "cost": 21, "isShow": 1}, {"id": 8815, "reward": "4,8815,1", "sort": 78, "limit": -1, "cost": 12, "isShow": 1}, {"id": 8816, "reward": "4,8816,1", "sort": 79, "limit": -1, "cost": 28, "isShow": 1}, {"id": 61, "reward": "4,8071,1", "sort": 80, "limit": -1, "cost": 50, "isShow": 1}, {"id": 62, "reward": "4,8072,1", "sort": 81, "limit": -1, "cost": 50, "isShow": 1}, {"id": 63, "reward": "4,8073,1", "sort": 82, "limit": -1, "cost": 30, "isShow": 1}, {"id": 64, "reward": "4,8357,1", "sort": 83, "limit": -1, "cost": 36, "isShow": 1}, {"id": 65, "reward": "4,8362,1", "sort": 84, "limit": -1, "cost": 50, "isShow": 1}, {"id": 66, "reward": "4,8358,1", "sort": 85, "limit": -1, "cost": 30, "isShow": 1}, {"id": 67, "reward": "4,8359,1", "sort": 86, "limit": -1, "cost": 24, "isShow": 1}, {"id": 68, "reward": "4,8361,1", "sort": 87, "limit": -1, "cost": 22, "isShow": 1}, {"id": 69, "reward": "4,8817,1", "sort": 88, "limit": -1, "cost": 14, "isShow": 1}, {"id": 70, "reward": "4,8818,1", "sort": 89, "limit": -1, "cost": 22, "isShow": 1}, {"id": 71, "reward": "4,8819,1", "sort": 90, "limit": -1, "cost": 14, "isShow": 1}, {"id": 72, "reward": "4,8360,1", "sort": 91, "limit": -1, "cost": 14, "isShow": 1}, {"id": 73, "reward": "4,8820,1", "sort": 92, "limit": -1, "cost": 10, "isShow": 1}, {"id": 74, "reward": "4,8821,1", "sort": 93, "limit": -1, "cost": 8, "isShow": 1}, {"id": 75, "reward": "4,8822,1", "sort": 94, "limit": -1, "cost": 8, "isShow": 1}, {"id": 76, "reward": "4,8823,1", "sort": 95, "limit": -1, "cost": 12, "isShow": 1}, {"id": 77, "reward": "4,8824,1", "sort": 96, "limit": -1, "cost": 10, "isShow": 1}, {"id": 8083, "reward": "4,8083,1", "sort": 97, "limit": -1, "cost": 50, "isShow": 1}, {"id": 8377, "reward": "4,8377,1", "sort": 98, "limit": -1, "cost": 40, "isShow": 1}, {"id": 8378, "reward": "4,8378,1", "sort": 99, "limit": -1, "cost": 44, "isShow": 1}, {"id": 8379, "reward": "4,8379,1", "sort": 100, "limit": -1, "cost": 30, "isShow": 1}, {"id": 8380, "reward": "4,8380,1", "sort": 101, "limit": -1, "cost": 30, "isShow": 1}, {"id": 8381, "reward": "4,8381,1", "sort": 102, "limit": -1, "cost": 26, "isShow": 1}, {"id": 8382, "reward": "4,8382,1", "sort": 103, "limit": -1, "cost": 16, "isShow": 1}, {"id": 8383, "reward": "4,8383,1", "sort": 104, "limit": -1, "cost": 16, "isShow": 1}, {"id": 8384, "reward": "4,8384,1", "sort": 105, "limit": -1, "cost": 16, "isShow": 1}, {"id": 8844, "reward": "4,8844,1", "sort": 106, "limit": -1, "cost": 14, "isShow": 1}, {"id": 8845, "reward": "4,8845,1", "sort": 107, "limit": -1, "cost": 12, "isShow": 1}, {"id": 8846, "reward": "4,8846,1", "sort": 108, "limit": -1, "cost": 12, "isShow": 1}, {"id": 8847, "reward": "4,8847,1", "sort": 109, "limit": -1, "cost": 10, "isShow": 1}, {"id": 8848, "reward": "4,8848,1", "sort": 110, "limit": -1, "cost": 8, "isShow": 1}, {"id": 8849, "reward": "4,8849,1", "sort": 111, "limit": -1, "cost": 14, "isShow": 1}, {"id": 8055, "reward": "4,8055,1", "sort": 112, "limit": -1, "cost": 54, "isShow": 1}, {"id": 8056, "reward": "4,8056,1", "sort": 113, "limit": -1, "cost": 60, "isShow": 1}, {"id": 8057, "reward": "4,8057,1", "sort": 114, "limit": -1, "cost": 32, "isShow": 1}, {"id": 8058, "reward": "4,8058,1", "sort": 115, "limit": -1, "cost": 60, "isShow": 1}, {"id": 8059, "reward": "4,8059,1", "sort": 116, "limit": -1, "cost": 16, "isShow": 1}, {"id": 8060, "reward": "4,8060,1", "sort": 117, "limit": -1, "cost": 28, "isShow": 1}, {"id": 8061, "reward": "4,8061,1", "sort": 118, "limit": -1, "cost": 56, "isShow": 1}, {"id": 8062, "reward": "4,8062,1", "sort": 119, "limit": -1, "cost": 18, "isShow": 1}, {"id": 8063, "reward": "4,8063,1", "sort": 120, "limit": -1, "cost": 18, "isShow": 1}, {"id": 8064, "reward": "4,8064,1", "sort": 121, "limit": -1, "cost": 18, "isShow": 1}, {"id": 8327, "reward": "4,8327,1", "sort": 122, "limit": -1, "cost": 12, "isShow": 1}, {"id": 8328, "reward": "4,8328,1", "sort": 123, "limit": -1, "cost": 12, "isShow": 1}, {"id": 10041, "reward": "10,41,1", "sort": 124, "limit": 1, "cost": 60, "isShow": 1}, {"id": 8413, "reward": "4,8413,1", "sort": 125, "limit": -1, "cost": 50, "isShow": 0}, {"id": 8414, "reward": "4,8414,1", "sort": 126, "limit": -1, "cost": 44, "isShow": 0}, {"id": 8415, "reward": "4,8415,1", "sort": 127, "limit": -1, "cost": 16, "isShow": 0}, {"id": 8416, "reward": "4,8416,1", "sort": 128, "limit": -1, "cost": 32, "isShow": 0}, {"id": 8417, "reward": "4,8417,1", "sort": 129, "limit": -1, "cost": 10, "isShow": 0}, {"id": 8418, "reward": "4,8418,1", "sort": 130, "limit": -1, "cost": 36, "isShow": 0}, {"id": 8419, "reward": "4,8419,1", "sort": 131, "limit": -1, "cost": 32, "isShow": 0}, {"id": 8420, "reward": "4,8420,1", "sort": 132, "limit": -1, "cost": 36, "isShow": 0}, {"id": 8421, "reward": "4,8421,1", "sort": 133, "limit": -1, "cost": 20, "isShow": 0}, {"id": 8422, "reward": "4,8422,1", "sort": 134, "limit": -1, "cost": 20, "isShow": 0}, {"id": 8423, "reward": "4,8423,1", "sort": 135, "limit": -1, "cost": 32, "isShow": 0}, {"id": 8424, "reward": "4,8424,1", "sort": 136, "limit": -1, "cost": 30, "isShow": 0}, {"id": 8425, "reward": "4,8425,1", "sort": 137, "limit": -1, "cost": 18, "isShow": 0}, {"id": 8426, "reward": "4,8426,1", "sort": 138, "limit": -1, "cost": 16, "isShow": 0}, {"id": 8409, "reward": "4,8409,1", "sort": 139, "limit": -1, "cost": 52, "isShow": 0}, {"id": 8410, "reward": "4,8410,1", "sort": 140, "limit": -1, "cost": 48, "isShow": 0}, {"id": 8411, "reward": "4,8411,1", "sort": 141, "limit": -1, "cost": 28, "isShow": 0}, {"id": 8412, "reward": "4,8412,1", "sort": 142, "limit": -1, "cost": 32, "isShow": 0}, {"id": 8095, "reward": "4,8095,1", "sort": 143, "limit": -1, "cost": 56, "isShow": 0}, {"id": 8863, "reward": "4,8863,1", "sort": 144, "limit": -1, "cost": 14, "isShow": 0}, {"id": 8864, "reward": "4,8864,1", "sort": 145, "limit": -1, "cost": 8, "isShow": 0}, {"id": 8865, "reward": "4,8865,1", "sort": 146, "limit": -1, "cost": 18, "isShow": 0}, {"id": 8866, "reward": "4,8866,1", "sort": 147, "limit": -1, "cost": 16, "isShow": 0}, {"id": 8867, "reward": "4,8867,1", "sort": 148, "limit": -1, "cost": 8, "isShow": 0}, {"id": 8868, "reward": "4,8868,1", "sort": 149, "limit": -1, "cost": 14, "isShow": 0}, {"id": 8869, "reward": "4,8869,1", "sort": 150, "limit": -1, "cost": 16, "isShow": 0}, {"id": 8093, "reward": "4,8093,1", "sort": 151, "limit": -1, "cost": 70, "isShow": 0}]