[{"id": 10000, "weight_1": 0, "weight_2": 0, "weight_3": 0, "weight_4": 0, "desc": "orderTaskDesc.10000_d", "content": "orderTaskDesc.10000", "time": 86400, "complete_talk": "35_0_01", "fav": "", "biscuit": 2000, "offline_add_factor": 0, "heart_add_factor": 0, "reward": "8,35,1", "ex_pr": 1, "ex_reward": "4,9100,5"}, {"id": 100, "weight_1": 100, "weight_2": 100, "weight_3": 80, "weight_4": 60, "desc": "", "content": "orderTaskDesc.100", "time": 7200, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.3, "ex_reward": "4,9100,5"}, {"id": 101, "weight_1": 95, "weight_2": 95, "weight_3": 85, "weight_4": 65, "desc": "", "content": "orderTaskDesc.100", "time": 14400, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.35, "ex_reward": "4,9100,6"}, {"id": 102, "weight_1": 90, "weight_2": 90, "weight_3": 90, "weight_4": 70, "desc": "", "content": "orderTaskDesc.100", "time": 21600, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.4, "ex_reward": "4,9100,7"}, {"id": 103, "weight_1": 85, "weight_2": 85, "weight_3": 95, "weight_4": 75, "desc": "", "content": "orderTaskDesc.100", "time": 28800, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.45, "ex_reward": "4,9100,8"}, {"id": 104, "weight_1": 20, "weight_2": 35, "weight_3": 100, "weight_4": 80, "desc": "", "content": "orderTaskDesc.100", "time": 36000, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.5, "ex_reward": "4,9100,9"}, {"id": 105, "weight_1": 15, "weight_2": 30, "weight_3": 100, "weight_4": 85, "desc": "", "content": "orderTaskDesc.100", "time": 43200, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.55, "ex_reward": "4,9100,10"}, {"id": 106, "weight_1": 10, "weight_2": 25, "weight_3": 100, "weight_4": 90, "desc": "", "content": "orderTaskDesc.100", "time": 50400, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.6, "ex_reward": "4,9100,11"}, {"id": 107, "weight_1": 5, "weight_2": 20, "weight_3": 100, "weight_4": 95, "desc": "", "content": "orderTaskDesc.100", "time": 57600, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.65, "ex_reward": "4,9100,12"}, {"id": 108, "weight_1": 0, "weight_2": 15, "weight_3": 100, "weight_4": 100, "desc": "", "content": "orderTaskDesc.100", "time": 64800, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.7, "ex_reward": "4,9100,13"}, {"id": 109, "weight_1": 0, "weight_2": 10, "weight_3": 100, "weight_4": 100, "desc": "", "content": "orderTaskDesc.100", "time": 72000, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.75, "ex_reward": "4,9100,14"}, {"id": 110, "weight_1": 0, "weight_2": 5, "weight_3": 100, "weight_4": 100, "desc": "", "content": "orderTaskDesc.100", "time": 79200, "complete_talk": "", "fav": "", "biscuit": "", "offline_add_factor": "", "heart_add_factor": "", "reward": "", "ex_pr": 0.8, "ex_reward": "4,9100,15"}]