[{"id": 174, "goods": "4,41016,1", "all_sort": 115, "unlock_heart": 4800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1200,-1,600000"}, {"id": 175, "goods": "4,41013,1", "all_sort": 115, "unlock_heart": 4800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1200,-1,500000"}, {"id": 176, "goods": "4,41014,1", "all_sort": 115, "unlock_heart": 4800, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1200,-1,2000000"}, {"id": 183, "goods": "4,41022,1", "all_sort": 115, "unlock_heart": 5000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1200,-1,3000000"}, {"id": 184, "goods": "4,41023,1", "all_sort": 115, "unlock_heart": 5000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1200,-1,3000000"}, {"id": 231, "goods": "4,41029,1", "all_sort": 115, "unlock_heart": 2000, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,5000000"}, {"id": 185, "goods": "4,41019,1", "all_sort": 115, "unlock_heart": 1000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,800000"}, {"id": 186, "goods": "4,41020,1", "all_sort": 115, "unlock_heart": 1000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,800000"}, {"id": 187, "goods": "4,41021,1", "all_sort": 115, "unlock_heart": 1000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,800000"}, {"id": 192, "goods": "4,41026,1", "all_sort": 115, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,900000"}, {"id": 193, "goods": "4,41027,1", "all_sort": 115, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,900000"}, {"id": 194, "goods": "4,41028,1", "all_sort": 115, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,900000"}, {"id": 267, "goods": "4,41034,1", "all_sort": 115, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,1000000"}, {"id": 348, "goods": "4,41038,1", "all_sort": 115, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,1000000"}, {"id": 349, "goods": "4,41039,1", "all_sort": 115, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,999999"}, {"id": 1, "goods": "4,21102,1", "all_sort": 1, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,60", "cost": "1,-1,55000"}, {"id": 2, "goods": "4,21101,1", "all_sort": 2, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,60", "cost": "1,-1,57500"}, {"id": 3, "goods": "4,21205,1", "all_sort": 3, "unlock_heart": 160, "weight": 10, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.25, "cut_range": "10,60", "cost": "1,-1,57500"}, {"id": 4, "goods": "4,21105,1", "all_sort": 4, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,60", "cost": "1,-1,60000"}, {"id": 5, "goods": "4,21103,1", "all_sort": 5, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,60", "cost": "1,-1,62500"}, {"id": 6, "goods": "4,21111,1", "all_sort": 6, "unlock_heart": 160, "weight": 10, "resident": "", "count": 2, "limit": 4, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,62500"}, {"id": 7, "goods": "4,21108,1", "all_sort": 7, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,60", "cost": "1,-1,65000"}, {"id": 8, "goods": "4,21106,1", "all_sort": 8, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,60", "cost": "1,-1,70000"}, {"id": 9, "goods": "4,21116,1", "all_sort": 9, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,60", "cost": "1,-1,70000"}, {"id": 10, "goods": "4,21107,1", "all_sort": 10, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,60", "cost": "1,-1,72500"}, {"id": 11, "goods": "4,21117,1", "all_sort": 11, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1,-1,72500"}, {"id": 12, "goods": "4,21113,1", "all_sort": 12, "unlock_heart": 160, "weight": 10, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,72500"}, {"id": 13, "goods": "4,21110,1", "all_sort": 13, "unlock_heart": 160, "weight": 10, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,90000"}, {"id": 14, "goods": "4,21118,1", "all_sort": 14, "unlock_heart": 160, "weight": 0, "resident": 1, "count": 1, "limit": 2, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,92500"}, {"id": 15, "goods": "4,21109,1", "all_sort": 15, "unlock_heart": 160, "weight": 10, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,95000"}, {"id": 16, "goods": "4,21112,1", "all_sort": 16, "unlock_heart": 525, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,68250"}, {"id": 17, "goods": "4,21114,1", "all_sort": 17, "unlock_heart": 525, "weight": 10, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,78750"}, {"id": 18, "goods": "4,21115,1", "all_sort": 19, "unlock_heart": 550, "weight": 10, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,50", "cost": "4,9100,75"}, {"id": 19, "goods": "4,21201,1", "all_sort": 22, "unlock_heart": 600, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,87000"}, {"id": 20, "goods": "4,21202,1", "all_sort": 23, "unlock_heart": 600, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,87000"}, {"id": 21, "goods": "4,21203,1", "all_sort": 24, "unlock_heart": 630, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,94500"}, {"id": 22, "goods": "4,21204,1", "all_sort": 25, "unlock_heart": 630, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,97650"}, {"id": 23, "goods": "4,21209,1", "all_sort": 26, "unlock_heart": 660, "weight": 10, "resident": "", "count": 2, "limit": 5, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,95700"}, {"id": 24, "goods": "4,21206,1", "all_sort": 27, "unlock_heart": 660, "weight": 10, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,95700"}, {"id": 25, "goods": "4,21210,1", "all_sort": 29, "unlock_heart": 690, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,103500"}, {"id": 26, "goods": "4,21211,1", "all_sort": 30, "unlock_heart": 690, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,117300"}, {"id": 27, "goods": "4,21212,1", "all_sort": 35, "unlock_heart": 720, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "4,9100,150"}, {"id": 28, "goods": "4,21208,1", "all_sort": 34, "unlock_heart": 720, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,129600"}, {"id": 29, "goods": "4,21213,1", "all_sort": 33, "unlock_heart": 720, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,111600"}, {"id": 30, "goods": "4,21214,1", "all_sort": 38, "unlock_heart": 750, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,120000"}, {"id": 31, "goods": "4,21004,1", "all_sort": 39, "unlock_heart": 750, "weight": 5, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,120000"}, {"id": 32, "goods": "4,21001,1", "all_sort": 45, "unlock_heart": 800, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,136000"}, {"id": 33, "goods": "4,21216,1", "all_sort": 46, "unlock_heart": 800, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,144000"}, {"id": 34, "goods": "4,21010,1", "all_sort": 51, "unlock_heart": 850, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,144500"}, {"id": 35, "goods": "4,21011,1", "all_sort": 52, "unlock_heart": 850, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,148750"}, {"id": 36, "goods": "4,21024,1", "all_sort": 56, "unlock_heart": 900, "weight": 10, "resident": "", "count": 2, "limit": 7, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,153000"}, {"id": 37, "goods": "4,21207,1", "all_sort": 57, "unlock_heart": 900, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,184500"}, {"id": 38, "goods": "4,21018,1", "all_sort": 60, "unlock_heart": 950, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,175750"}, {"id": 39, "goods": "4,21005,1", "all_sort": 59, "unlock_heart": 950, "weight": 5, "resident": "", "count": 2, "limit": 5, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,161500"}, {"id": 40, "goods": "4,21013,1", "all_sort": 63, "unlock_heart": 1000, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,180000"}, {"id": 41, "goods": "4,21019,1", "all_sort": 64, "unlock_heart": 1000, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,185000"}, {"id": 42, "goods": "4,21003,1", "all_sort": 66, "unlock_heart": 1050, "weight": 5, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.2, "cut_range": "10,70", "cost": "2,-1,175"}, {"id": 43, "goods": "4,21020,1", "all_sort": 67, "unlock_heart": 1050, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,200"}, {"id": 44, "goods": "4,21025,1", "all_sort": 68, "unlock_heart": 1100, "weight": 5, "resident": "", "count": 2, "limit": 7, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,170500"}, {"id": 45, "goods": "4,21007,1", "all_sort": 69, "unlock_heart": 1100, "weight": 5, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,242000"}, {"id": 46, "goods": "4,21014,1", "all_sort": 76, "unlock_heart": 1350, "weight": 5, "resident": "", "count": 2, "limit": 7, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,209250"}, {"id": 47, "goods": "4,21015,1", "all_sort": 77, "unlock_heart": 1350, "weight": 5, "resident": "", "count": 2, "limit": 7, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,209250"}, {"id": 48, "goods": "4,21215,1", "all_sort": 78, "unlock_heart": 1350, "weight": 10, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.25, "cut_range": "10,30", "cost": "1,-1,249750"}, {"id": 49, "goods": "4,21041,1", "all_sort": 79, "unlock_heart": 1400, "weight": 3, "resident": "", "count": 2, "limit": 5, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,245000"}, {"id": 50, "goods": "4,21026,1", "all_sort": 80, "unlock_heart": 1400, "weight": 3, "resident": "", "count": 1, "limit": 5, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,252000"}, {"id": 51, "goods": "4,21016,1", "all_sort": 81, "unlock_heart": 1425, "weight": 3, "resident": "", "count": 2, "limit": 7, "cut_rate": 0.15, "cut_range": "10,70", "cost": "4,9100,125"}, {"id": 52, "goods": "4,21017,1", "all_sort": 82, "unlock_heart": 1425, "weight": 3, "resident": "", "count": 2, "limit": 7, "cut_rate": 0.15, "cut_range": "10,70", "cost": "4,9100,125"}, {"id": 53, "goods": "4,21038,1", "all_sort": 83, "unlock_heart": 1450, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,326250"}, {"id": 54, "goods": "4,21039,1", "all_sort": 84, "unlock_heart": 1450, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,250"}, {"id": 55, "goods": "4,21021,1", "all_sort": 85, "unlock_heart": 1475, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,302375"}, {"id": 56, "goods": "4,21027,1", "all_sort": 86, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 1, "limit": 5, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,262500"}, {"id": 57, "goods": "4,21040,1", "all_sort": 87, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 2, "limit": 5, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,307500"}, {"id": 58, "goods": "4,21051,1", "all_sort": 88, "unlock_heart": 1500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,405000"}, {"id": 59, "goods": "4,21002,1", "all_sort": 89, "unlock_heart": 1525, "weight": 3, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.2, "cut_range": "10,50", "cost": "4,9100,200"}, {"id": 60, "goods": "4,21030,1", "all_sort": 90, "unlock_heart": 1575, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,283500"}, {"id": 61, "goods": "4,21042,1", "all_sort": 91, "unlock_heart": 1600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,384000"}, {"id": 62, "goods": "4,21006,1", "all_sort": 92, "unlock_heart": 1600, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,250"}, {"id": 63, "goods": "4,21031,1", "all_sort": 93, "unlock_heart": 1625, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,341250"}, {"id": 64, "goods": "4,21036,1", "all_sort": 94, "unlock_heart": 1650, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,404250"}, {"id": 65, "goods": "4,21050,1", "all_sort": 96, "unlock_heart": 1700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,391000"}, {"id": 66, "goods": "4,21043,1", "all_sort": 97, "unlock_heart": 1700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,500"}, {"id": 67, "goods": "4,21046,1", "all_sort": 98, "unlock_heart": 2000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,590000"}, {"id": 68, "goods": "4,21022,1", "all_sort": 99, "unlock_heart": 2000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,50", "cost": "4,9100,400"}, {"id": 69, "goods": "4,21045,1", "all_sort": 100, "unlock_heart": 2100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "2,-1,800"}, {"id": 70, "goods": "4,21028,1", "all_sort": 101, "unlock_heart": 2100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "2,-1,400"}, {"id": 71, "goods": "4,21047,1", "all_sort": 102, "unlock_heart": 2200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "4,9100,350"}, {"id": 72, "goods": "4,21012,1", "all_sort": 103, "unlock_heart": 2300, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,575000"}, {"id": 73, "goods": "4,21029,1", "all_sort": 104, "unlock_heart": 2300, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,609500"}, {"id": 74, "goods": "4,21048,1", "all_sort": 105, "unlock_heart": 2350, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,587500"}, {"id": 75, "goods": "4,21049,1", "all_sort": 106, "unlock_heart": 2400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,660000"}, {"id": 76, "goods": "4,21037,1", "all_sort": 107, "unlock_heart": 2500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,750000"}, {"id": 77, "goods": "4,21035,1", "all_sort": 108, "unlock_heart": 2500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,350"}, {"id": 78, "goods": "4,21044,1", "all_sort": 109, "unlock_heart": 2500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "2,-1,600"}, {"id": 79, "goods": "4,21023,1", "all_sort": 110, "unlock_heart": 2550, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,675750"}, {"id": 80, "goods": "4,21034,1", "all_sort": 111, "unlock_heart": 2700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,972000"}, {"id": 81, "goods": "4,21052,1", "all_sort": 112, "unlock_heart": 2900, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1131000"}, {"id": 82, "goods": "4,21008,1", "all_sort": 113, "unlock_heart": 3000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "2,-1,300"}, {"id": 83, "goods": "4,21009,1", "all_sort": 114, "unlock_heart": 3000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "2,-1,300"}, {"id": 84, "goods": "4,21032,1", "all_sort": 115, "unlock_heart": 3500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1435000"}, {"id": 85, "goods": "4,21033,1", "all_sort": 116, "unlock_heart": 3500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,1000"}, {"id": 86, "goods": "4,21301,1", "all_sort": 61, "unlock_heart": 975, "weight": 8, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,185250"}, {"id": 87, "goods": "4,21302,1", "all_sort": 41, "unlock_heart": 775, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "2,-1,120"}, {"id": 88, "goods": "4,21303,1", "all_sort": 42, "unlock_heart": 775, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,127875"}, {"id": 89, "goods": "4,21304,1", "all_sort": 18, "unlock_heart": 540, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,72900"}, {"id": 90, "goods": "4,21305,1", "all_sort": 49, "unlock_heart": 825, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,100"}, {"id": 91, "goods": "4,21306,1", "all_sort": 53, "unlock_heart": 875, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,50", "cost": "2,-1,150"}, {"id": 92, "goods": "4,21307,1", "all_sort": 58, "unlock_heart": 925, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,200"}, {"id": 93, "goods": "4,21308,1", "all_sort": 32, "unlock_heart": 710, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,102950"}, {"id": 94, "goods": "4,21309,1", "all_sort": 36, "unlock_heart": 740, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,118400"}, {"id": 95, "goods": "4,21310,1", "all_sort": 62, "unlock_heart": 975, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,180375"}, {"id": 96, "goods": "4,21311,1", "all_sort": 31, "unlock_heart": 705, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,109275"}, {"id": 97, "goods": "4,21312,1", "all_sort": 28, "unlock_heart": 675, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,97875"}, {"id": 98, "goods": "4,21313,1", "all_sort": 21, "unlock_heart": 575, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1,-1,83375"}, {"id": 99, "goods": "4,21314,1", "all_sort": 20, "unlock_heart": 550, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1,-1,77000"}, {"id": 100, "goods": "4,21315,1", "all_sort": 71, "unlock_heart": 1200, "weight": 8, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,35", "cost": "1,-1,198000"}, {"id": 101, "goods": "4,21316,1", "all_sort": 95, "unlock_heart": 1650, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,412500"}, {"id": 102, "goods": "4,21317,1", "all_sort": 54, "unlock_heart": 875, "weight": 10, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,140000"}, {"id": 103, "goods": "4,21318,1", "all_sort": 55, "unlock_heart": 875, "weight": 10, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,153125"}, {"id": 104, "goods": "4,21319,1", "all_sort": 72, "unlock_heart": 1200, "weight": 8, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,35", "cost": "1,-1,252000"}, {"id": 105, "goods": "4,21320,1", "all_sort": 70, "unlock_heart": 1150, "weight": 8, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,35", "cost": "1,-1,235750"}, {"id": 106, "goods": "4,21321,1", "all_sort": 37, "unlock_heart": 740, "weight": 10, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,114700"}, {"id": 107, "goods": "4,21322,1", "all_sort": 47, "unlock_heart": 800, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,140000"}, {"id": 108, "goods": "4,21323,1", "all_sort": 65, "unlock_heart": 1000, "weight": 8, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,200000"}, {"id": 109, "goods": "4,21324,1", "all_sort": 43, "unlock_heart": 780, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,132600"}, {"id": 110, "goods": "4,21325,1", "all_sort": 48, "unlock_heart": 810, "weight": 10, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,137700"}, {"id": 111, "goods": "4,21326,1", "all_sort": 73, "unlock_heart": 1250, "weight": 7, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.2, "cut_range": "10,50", "cost": "2,-1,250"}, {"id": 112, "goods": "4,21327,1", "all_sort": 44, "unlock_heart": 780, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,136500"}, {"id": 113, "goods": "4,21328,1", "all_sort": 50, "unlock_heart": 825, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,144375"}, {"id": 114, "goods": "4,21329,1", "all_sort": 40, "unlock_heart": 760, "weight": 10, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.25, "cut_range": "10,50", "cost": "1,-1,117800"}, {"id": 115, "goods": "4,21330,1", "all_sort": 74, "unlock_heart": 1300, "weight": 7, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,35", "cost": "1,-1,273000"}, {"id": 116, "goods": "4,21331,1", "all_sort": 75, "unlock_heart": 1325, "weight": 7, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,35", "cost": "1,-1,291500"}, {"id": 117, "goods": "4,21401,1", "all_sort": 115, "unlock_heart": 1750, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,446250"}, {"id": 118, "goods": "4,21402,1", "all_sort": 115, "unlock_heart": 3400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1309000"}, {"id": 119, "goods": "4,21403,1", "all_sort": 115, "unlock_heart": 2400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,672000"}, {"id": 120, "goods": "4,21404,1", "all_sort": 115, "unlock_heart": 2600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,806000"}, {"id": 121, "goods": "4,21405,1", "all_sort": 115, "unlock_heart": 3100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,992000"}, {"id": 122, "goods": "4,21406,1", "all_sort": 115, "unlock_heart": 1950, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,526500"}, {"id": 123, "goods": "4,21407,1", "all_sort": 115, "unlock_heart": 2050, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,563750"}, {"id": 124, "goods": "4,21408,1", "all_sort": 115, "unlock_heart": 2200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,605000"}, {"id": 125, "goods": "4,21409,1", "all_sort": 115, "unlock_heart": 1900, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,513000"}, {"id": 126, "goods": "4,21410,1", "all_sort": 115, "unlock_heart": 3200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "2,-1,300"}, {"id": 127, "goods": "4,21411,1", "all_sort": 115, "unlock_heart": 3600, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,400"}, {"id": 128, "goods": "4,21412,1", "all_sort": 115, "unlock_heart": 3300, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,300"}, {"id": 129, "goods": "4,21413,1", "all_sort": 115, "unlock_heart": 3700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1572500"}, {"id": 130, "goods": "4,21414,1", "all_sort": 115, "unlock_heart": 3200, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,275"}, {"id": 131, "goods": "4,21415,1", "all_sort": 115, "unlock_heart": 3300, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1155000"}, {"id": 132, "goods": "4,21416,1", "all_sort": 115, "unlock_heart": 1850, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,490250"}, {"id": 133, "goods": "4,21417,1", "all_sort": 115, "unlock_heart": 2800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,854000"}, {"id": 134, "goods": "4,21418,1", "all_sort": 115, "unlock_heart": 3600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1440000"}, {"id": 135, "goods": "4,21419,1", "all_sort": 115, "unlock_heart": 3800, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,380"}, {"id": 136, "goods": "4,21420,1", "all_sort": 115, "unlock_heart": 3400, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1326000"}, {"id": 137, "goods": "4,21421,1", "all_sort": 115, "unlock_heart": 3100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1007500"}, {"id": 138, "goods": "4,21422,1", "all_sort": 115, "unlock_heart": 2850, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,869250"}, {"id": 139, "goods": "4,21423,1", "all_sort": 115, "unlock_heart": 1800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,468000"}, {"id": 140, "goods": "4,21424,1", "all_sort": 115, "unlock_heart": 1800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,477000"}, {"id": 141, "goods": "4,21425,1", "all_sort": 115, "unlock_heart": 1750, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,437500"}, {"id": 142, "goods": "4,21426,1", "all_sort": 115, "unlock_heart": 4000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,650"}, {"id": 143, "goods": "4,21501,1", "all_sort": 115, "unlock_heart": 3750, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,1556250"}, {"id": 144, "goods": "4,21502,1", "all_sort": 115, "unlock_heart": 3850, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1636250"}, {"id": 145, "goods": "4,21503,1", "all_sort": 115, "unlock_heart": 4100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,400"}, {"id": 146, "goods": "4,21504,1", "all_sort": 115, "unlock_heart": 3900, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1657500"}, {"id": 147, "goods": "4,21505,1", "all_sort": 115, "unlock_heart": 4050, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1741500"}, {"id": 148, "goods": "4,21506,1", "all_sort": 115, "unlock_heart": 3650, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,1387000"}, {"id": 149, "goods": "4,21507,1", "all_sort": 115, "unlock_heart": 4150, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1805250"}, {"id": 150, "goods": "4,21508,1", "all_sort": 115, "unlock_heart": 4500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2115000"}, {"id": 151, "goods": "4,21510,1", "all_sort": 115, "unlock_heart": 4200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,350"}, {"id": 152, "goods": "4,21509,1", "all_sort": 115, "unlock_heart": 4050, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1660500"}, {"id": 153, "goods": "4,21511,1", "all_sort": 115, "unlock_heart": 4300, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,300"}, {"id": 154, "goods": "4,21512,1", "all_sort": 115, "unlock_heart": 4250, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1848750"}, {"id": 155, "goods": "4,21513,1", "all_sort": 115, "unlock_heart": 4200, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1827000"}, {"id": 156, "goods": "4,21514,1", "all_sort": 115, "unlock_heart": 4700, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,500"}, {"id": 157, "goods": "4,21515,1", "all_sort": 115, "unlock_heart": 4300, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1913500"}, {"id": 158, "goods": "4,21516,1", "all_sort": 115, "unlock_heart": 4350, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1935750"}, {"id": 159, "goods": "4,21517,1", "all_sort": 115, "unlock_heart": 4400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1958000"}, {"id": 160, "goods": "4,21518,1", "all_sort": 115, "unlock_heart": 4500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,450"}, {"id": 161, "goods": "4,21519,1", "all_sort": 115, "unlock_heart": 4100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,1701500"}, {"id": 162, "goods": "4,21520,1", "all_sort": 115, "unlock_heart": 4000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,60", "cost": "1,-1,1600000"}, {"id": 163, "goods": "4,21521,1", "all_sort": 115, "unlock_heart": 4400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1760000"}, {"id": 164, "goods": "4,21522,1", "all_sort": 115, "unlock_heart": 4450, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1780000"}, {"id": 165, "goods": "4,21523,1", "all_sort": 115, "unlock_heart": 4550, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1888250"}, {"id": 166, "goods": "4,21524,1", "all_sort": 115, "unlock_heart": 3950, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,1580000"}, {"id": 167, "goods": "4,21525,1", "all_sort": 115, "unlock_heart": 3900, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,60", "cost": "1,-1,1638000"}, {"id": 168, "goods": "4,21526,1", "all_sort": 115, "unlock_heart": 3900, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,60", "cost": "1,-1,1521000"}, {"id": 169, "goods": "4,21527,1", "all_sort": 115, "unlock_heart": 4600, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2024000"}, {"id": 170, "goods": "4,21528,1", "all_sort": 115, "unlock_heart": 3700, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,1443000"}, {"id": 171, "goods": "4,21529,1", "all_sort": 115, "unlock_heart": 4600, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,50", "cost": "4,9100,200"}, {"id": 172, "goods": "4,21530,1", "all_sort": 115, "unlock_heart": 4650, "weight": 3, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,1929750"}, {"id": 173, "goods": "4,21531,1", "all_sort": 115, "unlock_heart": 4800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,700"}, {"id": 177, "goods": "4,8311,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.35, "cut_range": "10,50", "cost": "1,-1,107500"}, {"id": 178, "goods": "4,8315,1", "all_sort": 15, "unlock_heart": 500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,60", "cost": "2,-1,250"}, {"id": 179, "goods": "4,8317,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.35, "cut_range": "10,60", "cost": "1,-1,102500"}, {"id": 180, "goods": "4,8319,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.35, "cut_range": "10,60", "cost": "4,9100,75"}, {"id": 181, "goods": "4,8324,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,60", "cost": "2,-1,200"}, {"id": 182, "goods": "4,8326,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.35, "cut_range": "10,60", "cost": "1,-1,92500"}, {"id": 188, "goods": "4,8677,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,60", "cost": "2,-1,260"}, {"id": 189, "goods": "4,8678,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,60", "cost": "2,-1,300"}, {"id": 190, "goods": "4,8376,1", "all_sort": 15, "unlock_heart": 1000, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,60", "cost": "2,-1,350"}, {"id": 191, "goods": "4,41025,1", "all_sort": 115, "unlock_heart": 500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,300000"}, {"id": 195, "goods": "4,21621,1", "all_sort": 115, "unlock_heart": 4500, "weight": 3, "resident": "", "count": 1, "limit": 6, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2002500"}, {"id": 196, "goods": "4,21622,1", "all_sort": 115, "unlock_heart": 4700, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2091500"}, {"id": 197, "goods": "4,21623,1", "all_sort": 115, "unlock_heart": 4800, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,150"}, {"id": 198, "goods": "4,21625,1", "all_sort": 115, "unlock_heart": 4900, "weight": 3, "resident": "", "count": 1, "limit": 6, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2205000"}, {"id": 199, "goods": "4,21626,1", "all_sort": 115, "unlock_heart": 4900, "weight": 3, "resident": "", "count": 1, "limit": 6, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2229500"}, {"id": 200, "goods": "4,21618,1", "all_sort": 115, "unlock_heart": 5000, "weight": 3, "resident": "", "count": 1, "limit": 6, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2325000"}, {"id": 201, "goods": "4,21615,1", "all_sort": 115, "unlock_heart": 5100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2397000"}, {"id": 202, "goods": "4,21610,1", "all_sort": 115, "unlock_heart": 5200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2444000"}, {"id": 203, "goods": "4,21624,1", "all_sort": 115, "unlock_heart": 5300, "weight": 3, "resident": "", "count": 1, "limit": 6, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2597000"}, {"id": 204, "goods": "4,21627,1", "all_sort": 115, "unlock_heart": 5400, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2592000"}, {"id": 205, "goods": "4,21628,1", "all_sort": 115, "unlock_heart": 5500, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,200"}, {"id": 206, "goods": "4,21609,1", "all_sort": 115, "unlock_heart": 5600, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2716000"}, {"id": 207, "goods": "4,21614,1", "all_sort": 115, "unlock_heart": 5700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2793000"}, {"id": 208, "goods": "4,21619,1", "all_sort": 115, "unlock_heart": 5800, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,2871000"}, {"id": 209, "goods": "4,21603,1", "all_sort": 115, "unlock_heart": 5900, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "4,9100,400"}, {"id": 210, "goods": "4,21607,1", "all_sort": 115, "unlock_heart": 6000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3000000"}, {"id": 211, "goods": "4,21608,1", "all_sort": 115, "unlock_heart": 6100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3080500"}, {"id": 212, "goods": "4,21629,1", "all_sort": 115, "unlock_heart": 6200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3162000"}, {"id": 213, "goods": "4,21611,1", "all_sort": 115, "unlock_heart": 6300, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,300"}, {"id": 214, "goods": "4,21612,1", "all_sort": 115, "unlock_heart": 6400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3296000"}, {"id": 215, "goods": "4,21631,1", "all_sort": 115, "unlock_heart": 6500, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3380000"}, {"id": 216, "goods": "4,21630,1", "all_sort": 115, "unlock_heart": 6600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3465000"}, {"id": 217, "goods": "4,21601,1", "all_sort": 115, "unlock_heart": 6700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3685000"}, {"id": 218, "goods": "4,21620,1", "all_sort": 115, "unlock_heart": 6800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3706000"}, {"id": 219, "goods": "4,21632,1", "all_sort": 115, "unlock_heart": 6900, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,3864000"}, {"id": 220, "goods": "4,21602,1", "all_sort": 115, "unlock_heart": 7000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,50", "cost": "4,9100,500"}, {"id": 221, "goods": "4,21616,1", "all_sort": 115, "unlock_heart": 7100, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4082500"}, {"id": 222, "goods": "4,21617,1", "all_sort": 115, "unlock_heart": 7200, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4176000"}, {"id": 223, "goods": "4,21604,1", "all_sort": 115, "unlock_heart": 7300, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4380000"}, {"id": 224, "goods": "4,21633,1", "all_sort": 115, "unlock_heart": 7400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4588000"}, {"id": 225, "goods": "4,21605,1", "all_sort": 115, "unlock_heart": 7500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,800"}, {"id": 226, "goods": "4,21634,1", "all_sort": 115, "unlock_heart": 7600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,50", "cost": "4,9100,450"}, {"id": 227, "goods": "4,21635,1", "all_sort": 115, "unlock_heart": 7700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4889500"}, {"id": 228, "goods": "4,21636,1", "all_sort": 115, "unlock_heart": 7800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5850000"}, {"id": 229, "goods": "4,21613,1", "all_sort": 115, "unlock_heart": 7900, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,800"}, {"id": 230, "goods": "4,21606,1", "all_sort": 115, "unlock_heart": 8000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "4,9100,1000"}, {"id": 238, "goods": "4,21637,1", "all_sort": 115, "unlock_heart": 8500, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4675000"}, {"id": 239, "goods": "4,21638,1", "all_sort": 115, "unlock_heart": 8600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4945000"}, {"id": 240, "goods": "4,21639,1", "all_sort": 115, "unlock_heart": 8700, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,35", "cost": "1,-1,4872000"}, {"id": 241, "goods": "4,21640,1", "all_sort": 115, "unlock_heart": 8800, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4840000"}, {"id": 242, "goods": "4,21641,1", "all_sort": 115, "unlock_heart": 8900, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5162000"}, {"id": 243, "goods": "4,21642,1", "all_sort": 115, "unlock_heart": 9000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "4,9100,500"}, {"id": 244, "goods": "4,21643,1", "all_sort": 115, "unlock_heart": 9100, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "2,-1,450"}, {"id": 245, "goods": "4,21644,1", "all_sort": 115, "unlock_heart": 9200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4876000"}, {"id": 246, "goods": "4,21645,1", "all_sort": 115, "unlock_heart": 9300, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5673000"}, {"id": 247, "goods": "4,21646,1", "all_sort": 115, "unlock_heart": 9400, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5170000"}, {"id": 248, "goods": "4,21647,1", "all_sort": 115, "unlock_heart": 9500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,4750000"}, {"id": 249, "goods": "4,21648,1", "all_sort": 115, "unlock_heart": 9600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,40", "cost": "4,9100,350"}, {"id": 250, "goods": "4,21649,1", "all_sort": 115, "unlock_heart": 9700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5529000"}, {"id": 251, "goods": "4,21650,1", "all_sort": 115, "unlock_heart": 9800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6076000"}, {"id": 252, "goods": "4,21651,1", "all_sort": 115, "unlock_heart": 9900, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "2,-1,400"}, {"id": 253, "goods": "4,21652,1", "all_sort": 115, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6050000"}, {"id": 254, "goods": "4,21653,1", "all_sort": 115, "unlock_heart": 10100, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6413500"}, {"id": 255, "goods": "4,21654,1", "all_sort": 115, "unlock_heart": 10200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,30", "cost": "1,-1,5865000"}, {"id": 256, "goods": "4,21655,1", "all_sort": 115, "unlock_heart": 10300, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,5665000"}, {"id": 257, "goods": "4,21656,1", "all_sort": 115, "unlock_heart": 10400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,500"}, {"id": 258, "goods": "4,21657,1", "all_sort": 115, "unlock_heart": 10500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7140000"}, {"id": 259, "goods": "4,21658,1", "all_sort": 115, "unlock_heart": 10600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6678000"}, {"id": 260, "goods": "4,21659,1", "all_sort": 115, "unlock_heart": 10700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,8078500"}, {"id": 261, "goods": "4,21660,1", "all_sort": 115, "unlock_heart": 10800, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,800"}, {"id": 262, "goods": "4,21661,1", "all_sort": 115, "unlock_heart": 10900, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "4,9100,450"}, {"id": 263, "goods": "4,21662,1", "all_sort": 115, "unlock_heart": 11000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6985000"}, {"id": 264, "goods": "4,21663,1", "all_sort": 115, "unlock_heart": 11100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,35", "cost": "4,9100,600"}, {"id": 265, "goods": "4,21664,1", "all_sort": 115, "unlock_heart": 11200, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.15, "cut_range": "10,70", "cost": "2,-1,900"}, {"id": 266, "goods": "4,21665,1", "all_sort": 115, "unlock_heart": 11300, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.15, "cut_range": "10,70", "cost": "4,9100,1000"}, {"id": 232, "goods": "4,8388,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,60", "cost": "2,-1,300"}, {"id": 233, "goods": "4,20101,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.35, "cut_range": "10,60", "cost": "4,9100,600"}, {"id": 234, "goods": "4,8400,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.15, "cut_range": "10,50", "cost": "4,9100,300"}, {"id": 235, "goods": "4,8855,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,200000"}, {"id": 236, "goods": "4,8857,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,220"}, {"id": 237, "goods": "4,8858,1", "all_sort": 15, "unlock_heart": 500, "weight": 5, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,100000"}, {"id": 282, "goods": "4,21666,1", "all_sort": 115, "unlock_heart": 11400, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6612000"}, {"id": 283, "goods": "4,21667,1", "all_sort": 115, "unlock_heart": 11500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6325000"}, {"id": 284, "goods": "4,21668,1", "all_sort": 115, "unlock_heart": 11600, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5568000"}, {"id": 285, "goods": "4,21669,1", "all_sort": 115, "unlock_heart": 11700, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,400"}, {"id": 286, "goods": "4,21670,1", "all_sort": 115, "unlock_heart": 11800, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "4,9100,450"}, {"id": 287, "goods": "4,21671,1", "all_sort": 115, "unlock_heart": 11900, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7556500"}, {"id": 288, "goods": "4,21672,1", "all_sort": 115, "unlock_heart": 12000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7680000"}, {"id": 289, "goods": "4,21673,1", "all_sort": 115, "unlock_heart": 12100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7502000"}, {"id": 290, "goods": "4,21674,1", "all_sort": 115, "unlock_heart": 12200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,50", "cost": "2,-1,450"}, {"id": 291, "goods": "4,21675,1", "all_sort": 115, "unlock_heart": 12300, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,650"}, {"id": 292, "goods": "4,21676,1", "all_sort": 115, "unlock_heart": 12400, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7688000"}, {"id": 293, "goods": "4,21677,1", "all_sort": 115, "unlock_heart": 12500, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1,-1,8125000"}, {"id": 294, "goods": "4,21678,1", "all_sort": 115, "unlock_heart": 12600, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,8190000"}, {"id": 295, "goods": "4,21679,1", "all_sort": 115, "unlock_heart": 12700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,750"}, {"id": 296, "goods": "4,21680,1", "all_sort": 115, "unlock_heart": 12800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "4,9100,500"}, {"id": 297, "goods": "4,21681,1", "all_sort": 115, "unlock_heart": 12900, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,6837000"}, {"id": 298, "goods": "4,21682,1", "all_sort": 115, "unlock_heart": 13000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7150000"}, {"id": 299, "goods": "4,21683,1", "all_sort": 115, "unlock_heart": 13100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7074000"}, {"id": 300, "goods": "4,21684,1", "all_sort": 115, "unlock_heart": 13200, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,7524000"}, {"id": 301, "goods": "4,21685,1", "all_sort": 115, "unlock_heart": 13300, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7647500"}, {"id": 302, "goods": "4,21686,1", "all_sort": 115, "unlock_heart": 13400, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.3, "cut_range": "10,40", "cost": "2,-1,350"}, {"id": 303, "goods": "4,21687,1", "all_sort": 115, "unlock_heart": 13500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7290000"}, {"id": 304, "goods": "4,21688,1", "all_sort": 115, "unlock_heart": 13600, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,8432000"}, {"id": 305, "goods": "4,21689,1", "all_sort": 115, "unlock_heart": 13700, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.35, "cut_range": "10,60", "cost": "2,-1,500"}, {"id": 306, "goods": "4,21690,1", "all_sort": 115, "unlock_heart": 13800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,8349000"}, {"id": 307, "goods": "4,21691,1", "all_sort": 115, "unlock_heart": 13900, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,8826500"}, {"id": 308, "goods": "4,21692,1", "all_sort": 115, "unlock_heart": 14000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,30", "cost": "1,-1,7770000"}, {"id": 309, "goods": "4,21693,1", "all_sort": 115, "unlock_heart": 14100, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "1,-1,7755000"}, {"id": 310, "goods": "4,21694,1", "all_sort": 115, "unlock_heart": 14200, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,500"}, {"id": 311, "goods": "4,21695,1", "all_sort": 115, "unlock_heart": 14300, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,9295000"}, {"id": 312, "goods": "4,21696,1", "all_sort": 115, "unlock_heart": 14400, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,9072000"}, {"id": 313, "goods": "4,21697,1", "all_sort": 115, "unlock_heart": 14500, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,8410000"}, {"id": 314, "goods": "4,21698,1", "all_sort": 115, "unlock_heart": 14600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,450"}, {"id": 315, "goods": "4,21699,1", "all_sort": 115, "unlock_heart": 14700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.3, "cut_range": "10,50", "cost": "4,9100,450"}, {"id": 316, "goods": "4,21700,1", "all_sort": 115, "unlock_heart": 14800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,10064000"}, {"id": 317, "goods": "4,21701,1", "all_sort": 115, "unlock_heart": 14900, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,35", "cost": "4,9100,600"}, {"id": 318, "goods": "4,21702,1", "all_sort": 115, "unlock_heart": 15000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.15, "cut_range": "10,70", "cost": "2,-1,700"}, {"id": 319, "goods": "4,21703,1", "all_sort": 115, "unlock_heart": 15100, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "4,9100,700"}, {"id": 320, "goods": "4,21704,1", "all_sort": 15, "unlock_heart": 15200, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "2,-1,450"}, {"id": 321, "goods": "4,21705,1", "all_sort": 15, "unlock_heart": 15300, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,1000"}, {"id": 322, "goods": "4,21706,1", "all_sort": 15, "unlock_heart": 15400, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.15, "cut_range": "10,50", "cost": "1,-1,10626000"}, {"id": 323, "goods": "4,21707,1", "all_sort": 15, "unlock_heart": 15500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,10772500"}, {"id": 324, "goods": "4,21708,1", "all_sort": 15, "unlock_heart": 15600, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,500"}, {"id": 325, "goods": "4,21709,1", "all_sort": 15, "unlock_heart": 15700, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,11304000"}, {"id": 326, "goods": "4,41035,1", "all_sort": 115, "unlock_heart": 500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,3000000"}, {"id": 327, "goods": "4,41036,1", "all_sort": 115, "unlock_heart": 500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,50", "cost": "1200,-1,3000000"}, {"id": 328, "goods": "4,41037,1", "all_sort": 115, "unlock_heart": 500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.3, "cut_range": "10,60", "cost": "4,9100,600"}, {"id": 329, "goods": "4,21710,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6000000"}, {"id": 330, "goods": "4,21711,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6000000"}, {"id": 331, "goods": "4,21712,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,500"}, {"id": 332, "goods": "4,21713,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6800000"}, {"id": 333, "goods": "4,21714,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,700"}, {"id": 334, "goods": "4,21715,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1200,-1,7000000"}, {"id": 335, "goods": "4,21716,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,40", "cost": "4,9100,1000"}, {"id": 336, "goods": "4,21717,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6200000"}, {"id": 337, "goods": "4,21718,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "2,-1,650"}, {"id": 338, "goods": "4,21719,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,8000000"}, {"id": 339, "goods": "4,21720,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6400000"}, {"id": 340, "goods": "4,21721,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,40", "cost": "4,9100,800"}, {"id": 341, "goods": "4,21722,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,650"}, {"id": 342, "goods": "4,21723,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5900000"}, {"id": 343, "goods": "4,21724,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5000000"}, {"id": 344, "goods": "4,21725,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,5200000"}, {"id": 345, "goods": "4,21726,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1200,-1,5000000"}, {"id": 346, "goods": "4,21727,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,50", "cost": "1,-1,4500000"}, {"id": 347, "goods": "4,21728,1", "all_sort": 14, "unlock_heart": 10000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,6500000"}, {"id": 350, "goods": "4,21729,1", "all_sort": 14, "unlock_heart": 15000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,9750000"}, {"id": 351, "goods": "4,21730,1", "all_sort": 14, "unlock_heart": 16000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,11200000"}, {"id": 352, "goods": "4,21731,1", "all_sort": 14, "unlock_heart": 16000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,12000000"}, {"id": 353, "goods": "4,21732,1", "all_sort": 14, "unlock_heart": 16000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,1000"}, {"id": 354, "goods": "4,21733,1", "all_sort": 14, "unlock_heart": 16600, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,10790000"}, {"id": 355, "goods": "4,21734,1", "all_sort": 14, "unlock_heart": 16800, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,600"}, {"id": 356, "goods": "4,21735,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,10710000"}, {"id": 357, "goods": "4,21736,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,11050000"}, {"id": 358, "goods": "4,21737,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,11560000"}, {"id": 359, "goods": "4,21738,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 3, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,650"}, {"id": 360, "goods": "4,21739,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,700"}, {"id": 361, "goods": "4,21740,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,16150000"}, {"id": 362, "goods": "4,21741,1", "all_sort": 14, "unlock_heart": 17500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,14875000"}, {"id": 363, "goods": "4,21742,1", "all_sort": 14, "unlock_heart": 17500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,1000"}, {"id": 364, "goods": "4,21743,1", "all_sort": 14, "unlock_heart": 17500, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,15050000"}, {"id": 365, "goods": "4,21744,1", "all_sort": 14, "unlock_heart": 18000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,1500"}, {"id": 366, "goods": "4,21745,1", "all_sort": 14, "unlock_heart": 12000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7800000"}, {"id": 367, "goods": "4,21746,1", "all_sort": 14, "unlock_heart": 14000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,9100000"}, {"id": 368, "goods": "4,21747,1", "all_sort": 14, "unlock_heart": 15000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,900"}, {"id": 369, "goods": "4,21748,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,12750000"}, {"id": 370, "goods": "4,21749,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,1000"}, {"id": 371, "goods": "4,21750,1", "all_sort": 14, "unlock_heart": 13000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,8450000"}, {"id": 372, "goods": "4,21751,1", "all_sort": 14, "unlock_heart": 12000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,7800000"}, {"id": 373, "goods": "4,21752,1", "all_sort": 14, "unlock_heart": 15000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,800"}, {"id": 374, "goods": "4,21753,1", "all_sort": 14, "unlock_heart": 15000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,11250000"}, {"id": 375, "goods": "4,21754,1", "all_sort": 14, "unlock_heart": 12000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "4,9100,500"}, {"id": 376, "goods": "4,21755,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,11050000"}, {"id": 377, "goods": "4,21756,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,12750000"}, {"id": 378, "goods": "4,21757,1", "all_sort": 14, "unlock_heart": 17000, "weight": 3, "resident": "", "count": 1, "limit": 2, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,14450000"}, {"id": 379, "goods": "4,21758,1", "all_sort": 14, "unlock_heart": 19000, "weight": 3, "resident": "", "count": 1, "limit": 1, "cut_rate": 0.2, "cut_range": "10,30", "cost": "2,-1,1200"}, {"id": 380, "goods": "4,21759,1", "all_sort": 14, "unlock_heart": 19000, "weight": 3, "resident": "", "count": 1, "limit": 4, "cut_rate": 0.2, "cut_range": "10,30", "cost": "1,-1,18050000"}]