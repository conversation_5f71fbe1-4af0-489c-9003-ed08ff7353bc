[{"id": 1001, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_1", "name": "chessmanName.100100", "desc": "chessmanDesc.1001001", "obstacle": ""}, {"id": 1002, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_2", "name": "chessmanName.100200", "desc": "chessmanDesc.1002001", "obstacle": ""}, {"id": 1003, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_3", "name": "chessmanName.100300", "desc": "chessmanDesc.1003001", "obstacle": ""}, {"id": 1004, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_4", "name": "chessmanName.100400", "desc": "chessmanDesc.1004001", "obstacle": ""}, {"id": 1005, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_5", "name": "chessmanName.100500", "desc": "chessmanDesc.1005001", "obstacle": ""}, {"id": 1006, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_6", "name": "chessmanName.100600", "desc": "chessmanDesc.1006001", "obstacle": ""}, {"id": 1007, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_7", "name": "chessmanName.100700", "desc": "chessmanDesc.1007001", "obstacle": ""}, {"id": 1008, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_8", "name": "chessmanName.100800", "desc": "chessmanDesc.1008001", "obstacle": ""}, {"id": 1009, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_9", "name": "chessmanName.100900", "desc": "chessmanDesc.1009001", "obstacle": ""}, {"id": 1010, "cond": "", "reward": "211,-1,1", "weight": 60, "max": 4, "belong": 211, "icon": "cf_qz_1_10", "name": "chessmanName.101000", "desc": "chessmanDesc.1010001", "obstacle": ""}, {"id": 2001, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_1", "name": "chessmanName.200100", "desc": "chessmanDesc.2001001", "obstacle": ""}, {"id": 2002, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_2", "name": "chessmanName.200200", "desc": "chessmanDesc.2002001", "obstacle": ""}, {"id": 2003, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_3", "name": "chessmanName.200300", "desc": "chessmanDesc.2003001", "obstacle": ""}, {"id": 2004, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_4", "name": "chessmanName.200400", "desc": "chessmanDesc.2004001", "obstacle": ""}, {"id": 2005, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_5", "name": "chessmanName.200500", "desc": "chessmanDesc.2005001", "obstacle": ""}, {"id": 2006, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_6", "name": "chessmanName.200600", "desc": "chessmanDesc.2006001", "obstacle": ""}, {"id": 2007, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_7", "name": "chessmanName.200700", "desc": "chessmanDesc.2007001", "obstacle": ""}, {"id": 2008, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_8", "name": "chessmanName.200800", "desc": "chessmanDesc.2008001", "obstacle": ""}, {"id": 2009, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_9", "name": "chessmanName.200900", "desc": "chessmanDesc.2009001", "obstacle": ""}, {"id": 2010, "cond": "", "reward": "210,-1,1", "weight": 60, "max": 4, "belong": 210, "icon": "cf_qz_2_10", "name": "chessmanName.201000", "desc": "chessmanDesc.2010001", "obstacle": ""}, {"id": 3001, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_1", "name": "chessmanName.300100", "desc": "chessmanDesc.3001001", "obstacle": ""}, {"id": 3002, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_2", "name": "chessmanName.300200", "desc": "chessmanDesc.3002001", "obstacle": ""}, {"id": 3003, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_3", "name": "chessmanName.300300", "desc": "chessmanDesc.3003001", "obstacle": ""}, {"id": 3004, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_4", "name": "chessmanName.300400", "desc": "chessmanDesc.3004001", "obstacle": ""}, {"id": 3005, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_5", "name": "chessmanName.300500", "desc": "chessmanDesc.3005001", "obstacle": ""}, {"id": 3006, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_6", "name": "chessmanName.300600", "desc": "chessmanDesc.3006001", "obstacle": ""}, {"id": 3007, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_7", "name": "chessmanName.300700", "desc": "chessmanDesc.3007001", "obstacle": ""}, {"id": 3008, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_8", "name": "chessmanName.300800", "desc": "chessmanDesc.3008001", "obstacle": ""}, {"id": 3009, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_9", "name": "chessmanName.300900", "desc": "chessmanDesc.3009001", "obstacle": ""}, {"id": 3010, "cond": "", "reward": "212,-1,1", "weight": 60, "max": 4, "belong": 212, "icon": "cf_qz_3_10", "name": "chessmanName.301000", "desc": "chessmanDesc.3010001", "obstacle": ""}, {"id": 11001, "cond": "", "reward": "", "weight": 15, "max": 2, "belong": "", "icon": "daoju_liti", "name": "chessmanName.1100100", "desc": "chessmanDesc.11001001", "obstacle": ""}, {"id": 11002, "cond": 3300, "reward": "", "weight": 15, "max": 2, "belong": "", "icon": "dao<PERSON>_shijian", "name": "chessmanName.1100200", "desc": "chessmanDesc.11002001", "obstacle": ""}, {"id": 11003, "cond": 3311, "reward": "", "weight": 10, "max": 2, "belong": "", "icon": "dao<PERSON>_xiaochu", "name": "chessmanName.1100300", "desc": "chessmanDesc.11003001", "obstacle": ""}, {"id": 11004, "cond": "", "reward": "", "weight": 15, "max": 2, "belong": "", "icon": "daoju_lianxian", "name": "chessmanName.1100400", "desc": "chessmanDesc.11004001", "obstacle": ""}, {"id": 11005, "cond": "", "reward": "", "weight": 60, "max": "", "belong": "", "icon": "daoju_zhangai1", "name": "chessmanName.1100500", "desc": "chessmanDesc.11005001", "obstacle": ""}, {"id": 11006, "cond": "", "reward": "", "weight": 60, "max": "", "belong": "", "icon": "daoju_zhangai2", "name": "chessmanName.1100600", "desc": "chessmanDesc.11006001", "obstacle": ""}, {"id": 11007, "cond": 3303, "reward": "", "weight": 15, "max": 2, "belong": "", "icon": "daoju_manghe", "name": "chessmanName.1100700", "desc": "chessmanDesc.11007001", "obstacle": ""}]