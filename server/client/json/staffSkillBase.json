[{"id": 100000010, "uid": 100000010, "type": 1, "target": "", "value": "", "move_speed": "", "pet_move_speed": "", "map_in_force": "", "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_zknn", "desc": "staffSkillDesc.1001", "desc_params": ""}, {"id": 100000020, "uid": 100000020, "type": 1, "target": "", "value": "", "move_speed": "", "pet_move_speed": "", "map_in_force": "", "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_cywj", "desc": "staffSkillDesc.1002", "desc_params": ""}, {"id": 100000030, "uid": 100000030, "type": 1, "target": "", "value": "", "move_speed": "", "pet_move_speed": "", "map_in_force": "", "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_sqlj", "desc": "staffSkillDesc.1003", "desc_params": ""}, {"id": 100000011, "uid": 100000011, "type": 1, "target": 100000, "value": 1, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 1}, {"id": 100000021, "uid": 100000011, "type": 1, "target": 100000, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 2}, {"id": 100000031, "uid": 100000011, "type": 1, "target": 100000, "value": 3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 3}, {"id": 100000041, "uid": 100000011, "type": 1, "target": 100000, "value": 4, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 4}, {"id": 100000051, "uid": 100000011, "type": 1, "target": 100000, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 5}, {"id": 100000061, "uid": 100000011, "type": 1, "target": 100000, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 6}, {"id": 100000071, "uid": 100000011, "type": 1, "target": 100000, "value": 7, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 7}, {"id": 100000081, "uid": 100000011, "type": 1, "target": 100000, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 8}, {"id": 100000091, "uid": 100000011, "type": 1, "target": 100000, "value": 9, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 9}, {"id": 100000101, "uid": 100000011, "type": 1, "target": 100000, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 10}, {"id": 100000111, "uid": 100000011, "type": 1, "target": 100000, "value": 11, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 11}, {"id": 100000121, "uid": 100000011, "type": 1, "target": 100000, "value": 12, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 12}, {"id": 100000131, "uid": 100000011, "type": 1, "target": 100000, "value": 13, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 13}, {"id": 100000141, "uid": 100000011, "type": 1, "target": 100000, "value": 14, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 14}, {"id": 100000151, "uid": 100000011, "type": 1, "target": 100000, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 15}, {"id": 100000161, "uid": 100000011, "type": 1, "target": 100000, "value": 16, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 16}, {"id": 100000171, "uid": 100000011, "type": 1, "target": 100000, "value": 17, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 17}, {"id": 100000181, "uid": 100000011, "type": 1, "target": 100000, "value": 18, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 18}, {"id": 100000191, "uid": 100000011, "type": 1, "target": 100000, "value": 19, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 19}, {"id": 100000201, "uid": 100000011, "type": 1, "target": 100000, "value": 20, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 20}, {"id": 100000211, "uid": 100000011, "type": 1, "target": 100000, "value": 21, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 21}, {"id": 100000221, "uid": 100000011, "type": 1, "target": 100000, "value": 23, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 23}, {"id": 100000231, "uid": 100000011, "type": 1, "target": 100000, "value": 25, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.1", "desc_params": 25}, {"id": 100000012, "uid": 100000012, "type": 2, "target": 100000, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.4", "desc_params": 40}, {"id": 100000022, "uid": 100000012, "type": 2, "target": 100000, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.4", "desc_params": 45}, {"id": 100000032, "uid": 100000012, "type": 2, "target": 100000, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.4", "desc_params": 50}, {"id": 100000042, "uid": 100000012, "type": 2, "target": 100000, "value": 25, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.4", "desc_params": 60}, {"id": 100000013, "uid": 100000013, "type": 18, "target": "", "value": 600, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_zkfw", "desc": "staffSkillDesc.32_1", "desc_params": ""}, {"id": 100000023, "uid": 100000013, "type": 18, "target": "", "value": 900, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_zkfw", "desc": "staffSkillDesc.32", "desc_params": 50}, {"id": 100000033, "uid": 100000013, "type": 18, "target": "", "value": 1200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_zkfw", "desc": "staffSkillDesc.32", "desc_params": 100}, {"id": 100000014, "uid": 100000014, "type": 13, "target": "", "value": 20, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_djcj", "desc": "staffSkillDesc.23", "desc_params": 20}, {"id": 100000024, "uid": 100000014, "type": 13, "target": "", "value": 40, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_djcj", "desc": "staffSkillDesc.23", "desc_params": 40}, {"id": 100000034, "uid": 100000014, "type": 13, "target": "", "value": 60, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_djcj", "desc": "staffSkillDesc.23", "desc_params": 60}, {"id": 100002011, "uid": 100002011, "type": 9, "target": 5, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 5}, {"id": 100002021, "uid": 100002011, "type": 9, "target": 10, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 10}, {"id": 100002031, "uid": 100002011, "type": 9, "target": 15, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 15}, {"id": 100002041, "uid": 100002011, "type": 9, "target": 20, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 20}, {"id": 100002051, "uid": 100002011, "type": 9, "target": 25, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 25}, {"id": 100002061, "uid": 100002011, "type": 9, "target": 30, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 30}, {"id": 100002071, "uid": 100002011, "type": 9, "target": 35, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 35}, {"id": 100002081, "uid": 100002011, "type": 9, "target": 40, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 40}, {"id": 100002091, "uid": 100002011, "type": 9, "target": 45, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 45}, {"id": 100002101, "uid": 100002011, "type": 9, "target": 50, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ttbj", "desc": "staffSkillDesc.19", "desc_params": 50}, {"id": 100002012, "uid": 100002012, "type": 19, "target": "", "value": 1.2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": 7200, "duration": 30, "icon": "ygxt_jnicon_ttbs", "desc": "staffSkillDesc.33", "desc_params": 1.2}, {"id": 100002022, "uid": 100002012, "type": 19, "target": "", "value": 0.75, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": 7200, "duration": 30, "icon": "ygxt_jnicon_ttbs", "desc": "staffSkillDesc.33", "desc_params": 0.75}, {"id": 100002032, "uid": 100002012, "type": 19, "target": "", "value": 0.5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": 7200, "duration": 30, "icon": "ygxt_jnicon_ttbs", "desc": "staffSkillDesc.33", "desc_params": 0.5}, {"id": 100002042, "uid": 100002012, "type": 19, "target": "", "value": 0.3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": 7200, "duration": 30, "icon": "ygxt_jnicon_ttbs", "desc": "staffSkillDesc.33", "desc_params": 0.3}, {"id": 100002013, "uid": 100002013, "type": 1, "target": 100002, "value": 1, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 1}, {"id": 100002023, "uid": 100002013, "type": 1, "target": 100002, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 2}, {"id": 100002033, "uid": 100002013, "type": 1, "target": 100002, "value": 3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 3}, {"id": 100002043, "uid": 100002013, "type": 1, "target": 100002, "value": 4, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 4}, {"id": 100002053, "uid": 100002013, "type": 1, "target": 100002, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 5}, {"id": 100002063, "uid": 100002013, "type": 1, "target": 100002, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 6}, {"id": 100002073, "uid": 100002013, "type": 1, "target": 100002, "value": 7, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 7}, {"id": 100002083, "uid": 100002013, "type": 1, "target": 100002, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 8}, {"id": 100002093, "uid": 100002013, "type": 1, "target": 100002, "value": 9, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 9}, {"id": 100002103, "uid": 100002013, "type": 1, "target": 100002, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 10}, {"id": 100002113, "uid": 100002013, "type": 1, "target": 100002, "value": 11, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 11}, {"id": 100002123, "uid": 100002013, "type": 1, "target": 100002, "value": 12, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 12}, {"id": 100002133, "uid": 100002013, "type": 1, "target": 100002, "value": 13, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 13}, {"id": 100002143, "uid": 100002013, "type": 1, "target": 100002, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 15}, {"id": 100002153, "uid": 100002013, "type": 1, "target": 100002, "value": 17, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 17}, {"id": 100002163, "uid": 100002013, "type": 1, "target": 100002, "value": 20, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.2", "desc_params": 20}, {"id": 100002014, "uid": 100002014, "type": 2, "target": 100002, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.5", "desc_params": 20}, {"id": 100002024, "uid": 100002014, "type": 2, "target": 100002, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.5", "desc_params": 25}, {"id": 100002034, "uid": 100002014, "type": 2, "target": 100002, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.5", "desc_params": 30}, {"id": 100002015, "uid": 100002015, "type": 10, "target": 10, "value": "1,1", "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": 7200, "duration": 30, "icon": "ygxt_jnicon_ttbs", "desc": "staffSkillDesc.20", "desc_params": "10,2,2"}, {"id": 100002025, "uid": 100002015, "type": 10, "target": 20, "value": "2,2", "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": 7200, "duration": 30, "icon": "ygxt_jnicon_ttbs", "desc": "staffSkillDesc.20", "desc_params": "20,3,3"}, {"id": 100002035, "uid": 100002015, "type": 10, "target": 30, "value": "2,3", "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": 7200, "duration": 30, "icon": "ygxt_jnicon_ttbs", "desc": "staffSkillDesc.20", "desc_params": "30,3,4"}, {"id": 100003011, "uid": 100003011, "type": 11, "target": 10, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 10}, {"id": 100003021, "uid": 100003011, "type": 11, "target": 15, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 15}, {"id": 100003031, "uid": 100003011, "type": 11, "target": 20, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 20}, {"id": 100003041, "uid": 100003011, "type": 11, "target": 25, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 25}, {"id": 100003051, "uid": 100003011, "type": 11, "target": 30, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 30}, {"id": 100003061, "uid": 100003011, "type": 11, "target": 35, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 35}, {"id": 100003071, "uid": 100003011, "type": 11, "target": 40, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 40}, {"id": 100003081, "uid": 100003011, "type": 11, "target": 45, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 45}, {"id": 100003091, "uid": 100003011, "type": 11, "target": 50, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 50}, {"id": 100003101, "uid": 100003011, "type": 11, "target": 55, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 55}, {"id": 100003111, "uid": 100003011, "type": 11, "target": 60, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ppbj", "desc": "staffSkillDesc.21", "desc_params": 60}, {"id": 100003012, "uid": 100003012, "type": 1, "target": 100003, "value": 1, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 1}, {"id": 100003022, "uid": 100003012, "type": 1, "target": 100003, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 2}, {"id": 100003032, "uid": 100003012, "type": 1, "target": 100003, "value": 3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 3}, {"id": 100003042, "uid": 100003012, "type": 1, "target": 100003, "value": 4, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 4}, {"id": 100003052, "uid": 100003012, "type": 1, "target": 100003, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 5}, {"id": 100003062, "uid": 100003012, "type": 1, "target": 100003, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 6}, {"id": 100003072, "uid": 100003012, "type": 1, "target": 100003, "value": 7, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 7}, {"id": 100003082, "uid": 100003012, "type": 1, "target": 100003, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 8}, {"id": 100003092, "uid": 100003012, "type": 1, "target": 100003, "value": 9, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 9}, {"id": 100003102, "uid": 100003012, "type": 1, "target": 100003, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 10}, {"id": 100003112, "uid": 100003012, "type": 1, "target": 100003, "value": 11, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 11}, {"id": 100003122, "uid": 100003012, "type": 1, "target": 100003, "value": 12, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 12}, {"id": 100003132, "uid": 100003012, "type": 1, "target": 100003, "value": 13, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 13}, {"id": 100003142, "uid": 100003012, "type": 1, "target": 100003, "value": 14, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 14}, {"id": 100003152, "uid": 100003012, "type": 1, "target": 100003, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.3", "desc_params": 15}, {"id": 100003013, "uid": 100003013, "type": 12, "target": "", "value": 1.5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": 7200, "duration": 15, "icon": "ygxt_jnicon_yjhc", "desc": "staffSkillDesc.22", "desc_params": 15}, {"id": 100003023, "uid": 100003013, "type": 12, "target": "", "value": 1.25, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": 7200, "duration": 20, "icon": "ygxt_jnicon_yjhc", "desc": "staffSkillDesc.22", "desc_params": 20}, {"id": 100003033, "uid": 100003013, "type": 12, "target": "", "value": 1, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": 7200, "duration": 30, "icon": "ygxt_jnicon_yjhc", "desc": "staffSkillDesc.22", "desc_params": 30}, {"id": 100003043, "uid": 100003013, "type": 12, "target": "", "value": 0.85, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": 7200, "duration": 40, "icon": "ygxt_jnicon_yjhc", "desc": "staffSkillDesc.22", "desc_params": 40}, {"id": 100003053, "uid": 100003013, "type": 12, "target": "", "value": 0.7, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": 7200, "duration": 60, "icon": "ygxt_jnicon_yjhc", "desc": "staffSkillDesc.22", "desc_params": 60}, {"id": 100003014, "uid": 100003014, "type": 2, "target": 100003, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.6", "desc_params": 15}, {"id": 100003024, "uid": 100003014, "type": 2, "target": 100003, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.6", "desc_params": 20}, {"id": 100003034, "uid": 100003014, "type": 2, "target": 100003, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.6", "desc_params": 25}, {"id": 130001011, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 100, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 100}, {"id": 130001021, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 97, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 97}, {"id": 130001031, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 94, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 94}, {"id": 130001041, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 91, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 91}, {"id": 130001051, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 88, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 88}, {"id": 130001061, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 85, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 85}, {"id": 130001071, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 82, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 82}, {"id": 130001081, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 79, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 79}, {"id": 130001091, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 76, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 76}, {"id": 130001101, "uid": 130001011, "type": 17, "target": "", "value": 2, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 73, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 73}, {"id": 130002011, "uid": 130002011, "type": 16, "target": 10, "value": 1, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 10}, {"id": 130002021, "uid": 130002011, "type": 16, "target": 10, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 20}, {"id": 130002031, "uid": 130002011, "type": 16, "target": 10, "value": 3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 30}, {"id": 130002041, "uid": 130002011, "type": 16, "target": 10, "value": 4, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 40}, {"id": 130002051, "uid": 130002011, "type": 16, "target": 10, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 60}, {"id": 130002061, "uid": 130002011, "type": 16, "target": 10, "value": 7, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 70}, {"id": 130002071, "uid": 130002011, "type": 16, "target": 10, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 80}, {"id": 130002081, "uid": 130002011, "type": 16, "target": 10, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 100}, {"id": 130002091, "uid": 130002011, "type": 16, "target": 10, "value": 12, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 120}, {"id": 130002101, "uid": 130002011, "type": 16, "target": 10, "value": 14, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 140}, {"id": 130003011, "uid": 130003011, "type": 6, "target": "", "value": 10, "move_speed": 200, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14_1", "desc_params": ""}, {"id": 130003021, "uid": 130003011, "type": 6, "target": "", "value": 9, "move_speed": 214, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 15}, {"id": 130003031, "uid": 130003011, "type": 6, "target": "", "value": 8, "move_speed": 216, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 30}, {"id": 130003041, "uid": 130003011, "type": 6, "target": "", "value": 7, "move_speed": 221, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 50}, {"id": 130003051, "uid": 130003011, "type": 6, "target": "", "value": 6.5, "move_speed": 244, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 70}, {"id": 130004011, "uid": 130004011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 10, "move_speed": 200, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16_1", "desc_params": ""}, {"id": 130004021, "uid": 130004011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 9, "move_speed": 214, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 15}, {"id": 130004031, "uid": 130004011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 216, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 30}, {"id": 130004041, "uid": 130004011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 7, "move_speed": 221, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 50}, {"id": 130004051, "uid": 130004011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6.5, "move_speed": 244, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 70}, {"id": *********, "uid": *********, "type": 4, "target": "", "value": "", "move_speed": 200, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 25, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12_1", "desc_params": ""}, {"id": 130005021, "uid": *********, "type": 4, "target": "", "value": "", "move_speed": 395, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 24, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 15}, {"id": 130005031, "uid": *********, "type": 4, "target": "", "value": "", "move_speed": 464, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 23, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 30}, {"id": 130005041, "uid": *********, "type": 4, "target": "", "value": "", "move_speed": 515, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 21, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 50}, {"id": *********, "uid": *********, "type": 4, "target": "", "value": "", "move_speed": 541, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 19, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 70}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 8, "move_speed": 251, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 40}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 7, "move_speed": 251, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 60}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 6.5, "move_speed": 274, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 80}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 6, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 100}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 5.5, "move_speed": 293, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 120}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_SWEEP", "value": 8, "move_speed": 251, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 40}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_SWEEP", "value": 7, "move_speed": 251, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 60}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_SWEEP", "value": 6.5, "move_speed": 274, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 80}, {"id": 130006042, "uid": *********, "type": 7, "target": "NEED_SWEEP", "value": 6, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 100}, {"id": 130006052, "uid": *********, "type": 7, "target": "NEED_SWEEP", "value": 5.5, "move_speed": 293, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 120}, {"id": 130020011, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 180, "pet_move_speed": 270, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39_1", "desc_params": ""}, {"id": 130020021, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 218, "pet_move_speed": 327, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 10}, {"id": 130020031, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 259, "pet_move_speed": 388, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 20}, {"id": 130020041, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 304, "pet_move_speed": 456, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 30}, {"id": 130020051, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 353, "pet_move_speed": 529, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 40}, {"id": 130020061, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 405, "pet_move_speed": 607, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 50}, {"id": 130020071, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 461, "pet_move_speed": 691, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 60}, {"id": 130020081, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 520, "pet_move_speed": 780, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 70}, {"id": 130020091, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 583, "pet_move_speed": 874, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 80}, {"id": 130020101, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 650, "pet_move_speed": 975, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 90}, {"id": 130020111, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 720, "pet_move_speed": 1080, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 100}, {"id": 130020121, "uid": 130020011, "type": 23, "target": "WATER|FER|SING", "value": 8, "move_speed": 794, "pet_move_speed": 1191, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_jscc", "desc": "staffSkillDesc.39", "desc_params": 110}, {"id": 130020012, "uid": 130020012, "type": 26, "target": "", "value": "1,60", "move_speed": 234, "pet_move_speed": 351, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_gzwjxh", "desc": "staffSkillDesc.42", "desc_params": "1,60"}, {"id": 130020022, "uid": 130020012, "type": 26, "target": "", "value": "1,120", "move_speed": 304, "pet_move_speed": 456, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_gzwjxh", "desc": "staffSkillDesc.42", "desc_params": "1,120"}, {"id": 130020032, "uid": 130020012, "type": 26, "target": "", "value": "1,180", "move_speed": 395, "pet_move_speed": 592, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_gzwjxh", "desc": "staffSkillDesc.42", "desc_params": "1,180"}, {"id": 130008011, "uid": 130008011, "type": 4, "target": "", "value": "", "move_speed": 329, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 18, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 40}, {"id": 130008021, "uid": 130008011, "type": 4, "target": "", "value": "", "move_speed": 384, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 17, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 60}, {"id": 130008031, "uid": 130008011, "type": 4, "target": "", "value": "", "move_speed": 430, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 16, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 80}, {"id": 130008041, "uid": 130008011, "type": 4, "target": "", "value": "", "move_speed": 467, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 15, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 100}, {"id": 130008051, "uid": 130008011, "type": 4, "target": "", "value": "", "move_speed": 500, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 13.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 130}, {"id": 130008012, "uid": 130008012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 9, "move_speed": 318, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 40}, {"id": 130008022, "uid": 130008012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 328, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 60}, {"id": 130008032, "uid": 130008012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 7.5, "move_speed": 365, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 80}, {"id": 130008042, "uid": 130008012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 7, "move_speed": 392, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 100}, {"id": 130008052, "uid": 130008012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6.5, "move_speed": 409, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 120}, {"id": 130009011, "uid": 130009011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 50}, {"id": 130009021, "uid": 130009011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 7, "move_speed": 318, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 80}, {"id": 130009031, "uid": 130009011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6, "move_speed": 348, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 120}, {"id": 130009041, "uid": 130009011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5, "move_speed": 365, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 170}, {"id": 130009012, "uid": 130009012, "type": 14, "target": 3, "value": 50, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 50}, {"id": 130009022, "uid": 130009012, "type": 14, "target": 3, "value": 60, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 60}, {"id": 130009032, "uid": 130009012, "type": 14, "target": 3, "value": 70, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 70}, {"id": 130009042, "uid": 130009012, "type": 14, "target": 3, "value": 80, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 80}, {"id": 130009052, "uid": 130009012, "type": 14, "target": 3, "value": 90, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 90}, {"id": 130009062, "uid": 130009012, "type": 14, "target": 3, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 100}, {"id": 130009072, "uid": 130009012, "type": 14, "target": 3, "value": 120, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 120}, {"id": 130009082, "uid": 130009012, "type": 14, "target": 3, "value": 140, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 140}, {"id": 130009092, "uid": 130009012, "type": 14, "target": 3, "value": 160, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 160}, {"id": 130009102, "uid": 130009012, "type": 14, "target": 3, "value": 180, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 180}, {"id": 130009112, "uid": 130009012, "type": 14, "target": 3, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.26", "desc_params": 200}, {"id": 130010011, "uid": 130010011, "type": 5, "target": "200002|400003", "value": "1.2,0.4", "move_speed": 900, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_qg", "desc": "staffSkillDesc.13_1", "desc_params": ""}, {"id": 130010021, "uid": 130010011, "type": 5, "target": "200002|400003", "value": "1.25,0.75", "move_speed": 1036, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_qg", "desc": "staffSkillDesc.13", "desc_params": 30}, {"id": 130010031, "uid": 130010011, "type": 5, "target": "200002|400003", "value": "1.25,0.9", "move_speed": 1097, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_qg", "desc": "staffSkillDesc.13", "desc_params": 60}, {"id": 130010041, "uid": 130010011, "type": 5, "target": "200002|400003", "value": "1.25,1.15", "move_speed": 1044, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_qg", "desc": "staffSkillDesc.13", "desc_params": 100}, {"id": 130010012, "uid": 130010012, "type": 7, "target": "NEED_SWEEP", "value": 7.5, "move_speed": 253, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 50}, {"id": 130010022, "uid": 130010012, "type": 7, "target": "NEED_SWEEP", "value": 7, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 75}, {"id": 130010032, "uid": 130010012, "type": 7, "target": "NEED_SWEEP", "value": 6.5, "move_speed": 338, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 100}, {"id": 130010042, "uid": 130010012, "type": 7, "target": "NEED_SWEEP", "value": 5, "move_speed": 253, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 125}, {"id": 130010052, "uid": 130010012, "type": 7, "target": "NEED_SWEEP", "value": 5, "move_speed": 313, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 150}, {"id": 130010062, "uid": 130010012, "type": 7, "target": "NEED_SWEEP", "value": 7.5, "move_speed": 851, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_ds", "desc": "staffSkillDesc.17", "desc_params": 175}, {"id": 130011011, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 407, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 16, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 75}, {"id": 130011021, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 450, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 15.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 90}, {"id": 130011031, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 490, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 15, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 105}, {"id": 130011041, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 528, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 14.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 120}, {"id": 130011051, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 561, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 14, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 135}, {"id": 130011061, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 591, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 13.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 150}, {"id": 130011071, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 615, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 13, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 165}, {"id": 130011081, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 635, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 12.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 180}, {"id": 130011091, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 650, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 12, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 195}, {"id": 130011101, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 659, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 11.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 210}, {"id": 130011111, "uid": 130011011, "type": 4, "target": "", "value": "", "move_speed": 663, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 11, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 225}, {"id": 130011012, "uid": 130011012, "type": 6, "target": "", "value": 7, "move_speed": 300, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 75}, {"id": 130011022, "uid": 130011012, "type": 6, "target": "", "value": 5, "move_speed": 313, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 150}, {"id": 130011032, "uid": 130011012, "type": 6, "target": "", "value": 4, "move_speed": 338, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 225}, {"id": 130011042, "uid": 130011012, "type": 6, "target": "", "value": 3.5, "move_speed": 392, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 300}, {"id": 130021011, "uid": 130021011, "type": 25, "target": "", "value": 8, "move_speed": 180, "pet_move_speed": 270, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shsy", "desc": "staffSkillDesc.41_1", "desc_params": ""}, {"id": 130021021, "uid": 130021011, "type": 25, "target": "", "value": 8, "move_speed": 259, "pet_move_speed": 388, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shsy", "desc": "staffSkillDesc.41", "desc_params": 20}, {"id": 130021031, "uid": 130021011, "type": 25, "target": "", "value": 8, "move_speed": 353, "pet_move_speed": 529, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shsy", "desc": "staffSkillDesc.41", "desc_params": 40}, {"id": 130021041, "uid": 130021011, "type": 25, "target": "", "value": 8, "move_speed": 461, "pet_move_speed": 691, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shsy", "desc": "staffSkillDesc.41", "desc_params": 60}, {"id": 130021051, "uid": 130021011, "type": 25, "target": "", "value": 8, "move_speed": 583, "pet_move_speed": 874, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shsy", "desc": "staffSkillDesc.41", "desc_params": 80}, {"id": 130021061, "uid": 130021011, "type": 25, "target": "", "value": 8, "move_speed": 720, "pet_move_speed": 1080, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shsy", "desc": "staffSkillDesc.41", "desc_params": 100}, {"id": 130021012, "uid": 130021012, "type": 27, "target": "", "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_hysy", "desc": "staffSkillDesc.43", "desc_params": 15}, {"id": 130021022, "uid": 130021012, "type": 27, "target": "", "value": 30, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_hysy", "desc": "staffSkillDesc.43", "desc_params": 30}, {"id": 130021032, "uid": 130021012, "type": 27, "target": "", "value": 50, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_hysy", "desc": "staffSkillDesc.43", "desc_params": 50}, {"id": 130021042, "uid": 130021012, "type": 27, "target": "", "value": 75, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_hysy", "desc": "staffSkillDesc.43", "desc_params": 75}, {"id": 130021052, "uid": 130021012, "type": 27, "target": "", "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_hysy", "desc": "staffSkillDesc.43", "desc_params": 100}, {"id": 130021013, "uid": 130021013, "type": 24, "target": "", "value": 8, "move_speed": 180, "pet_move_speed": 270, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shgs", "desc": "staffSkillDesc.40_1", "desc_params": ""}, {"id": 130021023, "uid": 130021013, "type": 24, "target": "", "value": 8, "move_speed": 304, "pet_move_speed": 456, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shgs", "desc": "staffSkillDesc.40", "desc_params": 30}, {"id": 130021033, "uid": 130021013, "type": 24, "target": "", "value": 8, "move_speed": 461, "pet_move_speed": 691, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shgs", "desc": "staffSkillDesc.40", "desc_params": 60}, {"id": 130021043, "uid": 130021013, "type": 24, "target": "", "value": 8, "move_speed": 720, "pet_move_speed": 1080, "map_in_force": 1, "assign_map": 11, "cd": "", "duration": "", "icon": "ygxt_jnicon_shgs", "desc": "staffSkillDesc.40", "desc_params": 100}, {"id": 130012011, "uid": 130012011, "type": 15, "target": "", "value": "", "move_speed": 5, "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_kbzz", "desc": "staffSkillDesc.29", "desc_params": 105}, {"id": 130012021, "uid": 130012011, "type": 15, "target": "", "value": "", "move_speed": 10, "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_kbzz", "desc": "staffSkillDesc.29", "desc_params": 110}, {"id": 130012031, "uid": 130012011, "type": 15, "target": "", "value": "", "move_speed": 15, "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_kbzz", "desc": "staffSkillDesc.29", "desc_params": 115}, {"id": 130012041, "uid": 130012011, "type": 15, "target": "", "value": "", "move_speed": 20, "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_kbzz", "desc": "staffSkillDesc.29", "desc_params": 120}, {"id": 130012051, "uid": 130012011, "type": 15, "target": "", "value": "", "move_speed": 25, "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_kbzz", "desc": "staffSkillDesc.29", "desc_params": 125}, {"id": 130012061, "uid": 130012011, "type": 15, "target": "", "value": "", "move_speed": 30, "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_kbzz", "desc": "staffSkillDesc.29", "desc_params": 130}, {"id": 130012071, "uid": 130012011, "type": 15, "target": "", "value": "", "move_speed": 35, "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_kbzz", "desc": "staffSkillDesc.29", "desc_params": 135}, {"id": 130012081, "uid": 130012011, "type": 15, "target": "", "value": "", "move_speed": 40, "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_kbzz", "desc": "staffSkillDesc.29", "desc_params": 140}, {"id": 130012012, "uid": 130012012, "type": 14, "target": 6, "value": 60, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.28", "desc_params": 60}, {"id": 130012022, "uid": 130012012, "type": 14, "target": 6, "value": 75, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.28", "desc_params": 75}, {"id": 130012032, "uid": 130012012, "type": 14, "target": 6, "value": 90, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.28", "desc_params": 90}, {"id": 130012042, "uid": 130012012, "type": 14, "target": 6, "value": 105, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.28", "desc_params": 105}, {"id": 130012052, "uid": 130012012, "type": 14, "target": 6, "value": 120, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.28", "desc_params": 120}, {"id": 130012062, "uid": 130012012, "type": 14, "target": 6, "value": 135, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.28", "desc_params": 135}, {"id": 130012072, "uid": 130012012, "type": 14, "target": 6, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.28", "desc_params": 150}, {"id": 130018011, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 50}, {"id": 130018021, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 7.5, "move_speed": 306, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 65}, {"id": 130018031, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 7, "move_speed": 318, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 80}, {"id": 130018041, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6.5, "move_speed": 355, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 105}, {"id": 130018051, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6, "move_speed": 348, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 120}, {"id": 130018061, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5.7, "move_speed": 359, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 135}, {"id": 130018071, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5.4, "move_speed": 365, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 150}, {"id": 130018081, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5.1, "move_speed": 365, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 165}, {"id": 130018091, "uid": 130018011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4.8, "move_speed": 361, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 180}, {"id": 130018012, "uid": 130018012, "type": 16, "target": 10, "value": 3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 10, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 45}, {"id": 130018022, "uid": 130018012, "type": 16, "target": 10, "value": 4, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 10, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 60}, {"id": 130018032, "uid": 130018012, "type": 16, "target": 10, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 10, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 75}, {"id": 130018042, "uid": 130018012, "type": 16, "target": 10, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 10, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 90}, {"id": 130018052, "uid": 130018012, "type": 16, "target": 10, "value": 7, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 10, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 105}, {"id": 130018062, "uid": 130018012, "type": 16, "target": 10, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 30, "duration": 10, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 120}, {"id": 130007011, "uid": 130007011, "type": 14, "target": 2, "value": 80, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.27", "desc_params": 80}, {"id": 130007021, "uid": 130007011, "type": 14, "target": 2, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.27", "desc_params": 100}, {"id": 130007031, "uid": 130007011, "type": 14, "target": 2, "value": 120, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.27", "desc_params": 120}, {"id": 130007041, "uid": 130007011, "type": 14, "target": 2, "value": 140, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.27", "desc_params": 140}, {"id": 130007051, "uid": 130007011, "type": 14, "target": 2, "value": 160, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.27", "desc_params": 160}, {"id": 130007061, "uid": 130007011, "type": 14, "target": 2, "value": 180, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.27", "desc_params": 180}, {"id": 130007012, "uid": 130007012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 100}, {"id": 130007022, "uid": 130007012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5, "move_speed": 313, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 150}, {"id": 130007032, "uid": 130007012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4.5, "move_speed": 365, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 200}, {"id": 130007042, "uid": 130007012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4, "move_speed": 392, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 250}, {"id": 130007052, "uid": 130007012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 3.5, "move_speed": 392, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 300}, {"id": 130007013, "uid": 130007013, "type": 3, "target": 2, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.10", "desc_params": 100}, {"id": 130007023, "uid": 130007013, "type": 3, "target": 2, "value": 130, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.10", "desc_params": 130}, {"id": 130007033, "uid": 130007013, "type": 3, "target": 2, "value": 160, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.10", "desc_params": 160}, {"id": 130007043, "uid": 130007013, "type": 3, "target": 2, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 2, "cd": "", "duration": "", "icon": "ygxt_jnicon_wq", "desc": "staffSkillDesc.10", "desc_params": 200}, {"id": 130013011, "uid": 130013011, "type": 17, "target": "", "value": 2, "move_speed": 400, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 70, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 70}, {"id": 130013021, "uid": 130013011, "type": 17, "target": "", "value": 2, "move_speed": 400, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 65, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 65}, {"id": 130013031, "uid": 130013011, "type": 17, "target": "", "value": 2, "move_speed": 400, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 60, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 60}, {"id": 130013041, "uid": 130013011, "type": 17, "target": "", "value": 2, "move_speed": 400, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 50, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 50}, {"id": 130013051, "uid": 130013011, "type": 17, "target": "", "value": 2, "move_speed": 400, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 40, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 40}, {"id": 130013061, "uid": 130013011, "type": 17, "target": "", "value": 2, "move_speed": 400, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": 30, "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.31", "desc_params": 30}, {"id": 130013012, "uid": 130013012, "type": 5, "target": "200002|400003", "value": "1.25,1.1", "move_speed": 1153, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_qg", "desc": "staffSkillDesc.13", "desc_params": 100}, {"id": 130013022, "uid": 130013012, "type": 5, "target": "200002|400003", "value": "1.3,1.4", "move_speed": 1257, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_qg", "desc": "staffSkillDesc.13", "desc_params": 175}, {"id": 130013032, "uid": 130013012, "type": 5, "target": "200002|400003", "value": "1.35,1.7", "move_speed": 1283, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_qg", "desc": "staffSkillDesc.13", "desc_params": 250}, {"id": 130013042, "uid": 130013012, "type": 5, "target": "200002|400003", "value": "1.4,2.1", "move_speed": 1294, "pet_move_speed": "", "map_in_force": 1, "assign_map": "4|1", "cd": "", "duration": "", "icon": "ygxt_jnicon_qg", "desc": "staffSkillDesc.13", "desc_params": 350}, {"id": 130013013, "uid": 130013013, "type": 8, "target": 1, "value": 20, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 20}, {"id": 130013023, "uid": 130013013, "type": 8, "target": 1, "value": 50, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 50}, {"id": 130013033, "uid": 130013013, "type": 8, "target": 1, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 100}, {"id": 130013043, "uid": 130013013, "type": 8, "target": 1, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 150}, {"id": 130013053, "uid": 130013013, "type": 8, "target": 1, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 200}, {"id": 130014011, "uid": 130014011, "type": 4, "target": "", "value": "", "move_speed": 392, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 11, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 150}, {"id": 130014021, "uid": 130014011, "type": 4, "target": "", "value": "", "move_speed": 548, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 225}, {"id": 130014031, "uid": 130014011, "type": 4, "target": "", "value": "", "move_speed": 672, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 9, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 300}, {"id": 130014041, "uid": 130014011, "type": 4, "target": "", "value": "", "move_speed": 749, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 8, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 375}, {"id": 130014012, "uid": 130014012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 100}, {"id": 130014022, "uid": 130014012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5, "move_speed": 313, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 150}, {"id": 130014032, "uid": 130014012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4.5, "move_speed": 428, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 225}, {"id": 130014042, "uid": 130014012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4, "move_speed": 512, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 300}, {"id": 130014013, "uid": 130014013, "type": 3, "target": 6, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.11", "desc_params": 100}, {"id": 130014023, "uid": 130014013, "type": 3, "target": 6, "value": 125, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.11", "desc_params": 125}, {"id": 130014033, "uid": 130014013, "type": 3, "target": 6, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.11", "desc_params": 150}, {"id": 130014043, "uid": 130014013, "type": 3, "target": 6, "value": 175, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.11", "desc_params": 175}, {"id": 130014053, "uid": 130014013, "type": 3, "target": 6, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.11", "desc_params": 200}, {"id": 130014063, "uid": 130014013, "type": 3, "target": 6, "value": 225, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.11", "desc_params": 225}, {"id": 130014073, "uid": 130014013, "type": 3, "target": 6, "value": 250, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 6, "cd": "", "duration": "", "icon": "ygxt_jnicon_fys", "desc": "staffSkillDesc.11", "desc_params": 250}, {"id": 130015011, "uid": 130015011, "type": 16, "target": 10, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 75}, {"id": 130015021, "uid": 130015011, "type": 16, "target": 10, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 90}, {"id": 130015031, "uid": 130015011, "type": 16, "target": 10, "value": 7, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 105}, {"id": 130015041, "uid": 130015011, "type": 16, "target": 10, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 120}, {"id": 130015051, "uid": 130015011, "type": 16, "target": 10, "value": 9, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": 30, "icon": "ygxt_jnicon_ygzdq", "desc": "staffSkillDesc.30", "desc_params": 135}, {"id": 130015012, "uid": 130015012, "type": 14, "target": 5, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 100}, {"id": 130015022, "uid": 130015012, "type": 14, "target": 5, "value": 125, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 125}, {"id": 130015032, "uid": 130015012, "type": 14, "target": 5, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 150}, {"id": 130015042, "uid": 130015012, "type": 14, "target": 5, "value": 175, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 175}, {"id": 130015052, "uid": 130015012, "type": 14, "target": 5, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 200}, {"id": 130015013, "uid": 130015013, "type": 3, "target": 5, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 100}, {"id": 130015023, "uid": 130015013, "type": 3, "target": 5, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 150}, {"id": 130015033, "uid": 130015013, "type": 3, "target": 5, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 200}, {"id": 130015043, "uid": 130015013, "type": 3, "target": 5, "value": 250, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 250}, {"id": 130015053, "uid": 130015013, "type": 3, "target": 5, "value": 300, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 300}, {"id": 130022011, "uid": 130022011, "type": 3, "target": 12, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 100}, {"id": 130022021, "uid": 130022011, "type": 3, "target": 12, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 150}, {"id": 130022031, "uid": 130022011, "type": 3, "target": 12, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 200}, {"id": 130022041, "uid": 130022011, "type": 3, "target": 12, "value": 250, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 250}, {"id": 130022051, "uid": 130022011, "type": 3, "target": 12, "value": 300, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 300}, {"id": 130022061, "uid": 130022011, "type": 3, "target": 12, "value": 350, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 350}, {"id": 130022071, "uid": 130022011, "type": 3, "target": 12, "value": 400, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 400}, {"id": 130022081, "uid": 130022011, "type": 3, "target": 12, "value": 450, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 450}, {"id": 130022091, "uid": 130022011, "type": 3, "target": 12, "value": 500, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 500}, {"id": 130022101, "uid": 130022011, "type": 3, "target": 12, "value": 550, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 550}, {"id": 130022111, "uid": 130022011, "type": 3, "target": 12, "value": 600, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 600}, {"id": 130022121, "uid": 130022011, "type": 3, "target": 12, "value": 650, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_zbw", "desc": "staffSkillDesc.44", "desc_params": 650}, {"id": 130022012, "uid": 130022012, "type": 4, "target": "", "value": "", "move_speed": 207, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 100}, {"id": 130022022, "uid": 130022012, "type": 4, "target": "", "value": "", "move_speed": 467, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 200}, {"id": 130022032, "uid": 130022012, "type": 4, "target": "", "value": "", "move_speed": 830, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 300}, {"id": 130016011, "uid": 130016011, "type": 4, "target": "", "value": "", "move_speed": 407, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 180}, {"id": 130016021, "uid": 130016011, "type": 4, "target": "", "value": "", "move_speed": 544, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 9, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 260}, {"id": 130016031, "uid": 130016011, "type": 4, "target": "", "value": "", "move_speed": 643, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 8, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 340}, {"id": 130016041, "uid": 130016011, "type": 4, "target": "", "value": "", "move_speed": 789, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 7.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 420}, {"id": 130016012, "uid": 130016012, "type": 6, "target": "", "value": 4, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 200}, {"id": 130016022, "uid": 130016012, "type": 6, "target": "", "value": 2.75, "move_speed": 306, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 350}, {"id": 130016032, "uid": 130016012, "type": 6, "target": "", "value": 2.25, "move_speed": 365, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 500}, {"id": 130016042, "uid": 130016012, "type": 6, "target": "", "value": 1.75, "move_speed": 392, "pet_move_speed": "", "map_in_force": 1, "assign_map": "3|2", "cd": "", "duration": "", "icon": "ygxt_jnicon_bhwx", "desc": "staffSkillDesc.14", "desc_params": 700}, {"id": 130016013, "uid": 130016013, "type": 3, "target": 3, "value": 80, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.8", "desc_params": 80}, {"id": 130016023, "uid": 130016013, "type": 3, "target": 3, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.8", "desc_params": 100}, {"id": 130016033, "uid": 130016013, "type": 3, "target": 3, "value": 120, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.8", "desc_params": 120}, {"id": 130016043, "uid": 130016013, "type": 3, "target": 3, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.8", "desc_params": 150}, {"id": 130016053, "uid": 130016013, "type": 3, "target": 3, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.8", "desc_params": 200}, {"id": 130016063, "uid": 130016013, "type": 3, "target": 3, "value": 250, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.8", "desc_params": 250}, {"id": 130016073, "uid": 130016013, "type": 3, "target": 3, "value": 300, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 3, "cd": "", "duration": "", "icon": "ygxt_jnicon_ct", "desc": "staffSkillDesc.8", "desc_params": 300}, {"id": 130017011, "uid": 130017011, "type": 4, "target": "", "value": "", "move_speed": 421, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 9.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 200}, {"id": 130017021, "uid": 130017011, "type": 4, "target": "", "value": "", "move_speed": 531, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 8, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 300}, {"id": 130017031, "uid": 130017011, "type": 4, "target": "", "value": "", "move_speed": 635, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 7, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 400}, {"id": 130017041, "uid": 130017011, "type": 4, "target": "", "value": "", "move_speed": 789, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 6.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 500}, {"id": 130017012, "uid": 130017012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 100}, {"id": 130017022, "uid": 130017012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5, "move_speed": 378, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 175}, {"id": 130017032, "uid": 130017012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4, "move_speed": 392, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 250}, {"id": 130017042, "uid": 130017012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 3, "move_speed": 450, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 400}, {"id": 130017013, "uid": 130017013, "type": 3, "target": 4, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 4, "cd": "", "duration": "", "icon": "ygxt_jnicon_kf", "desc": "staffSkillDesc.9", "desc_params": 100}, {"id": 130017023, "uid": 130017013, "type": 3, "target": 4, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 4, "cd": "", "duration": "", "icon": "ygxt_jnicon_kf", "desc": "staffSkillDesc.9", "desc_params": 150}, {"id": 130017033, "uid": 130017013, "type": 3, "target": 4, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 4, "cd": "", "duration": "", "icon": "ygxt_jnicon_kf", "desc": "staffSkillDesc.9", "desc_params": 200}, {"id": 130017043, "uid": 130017013, "type": 3, "target": 4, "value": 250, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 4, "cd": "", "duration": "", "icon": "ygxt_jnicon_kf", "desc": "staffSkillDesc.9", "desc_params": 250}, {"id": 130017053, "uid": 130017013, "type": 3, "target": 4, "value": 300, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 4, "cd": "", "duration": "", "icon": "ygxt_jnicon_kf", "desc": "staffSkillDesc.9", "desc_params": 300}, {"id": 130017063, "uid": 130017013, "type": 3, "target": 4, "value": 350, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 4, "cd": "", "duration": "", "icon": "ygxt_jnicon_kf", "desc": "staffSkillDesc.9", "desc_params": 350}, {"id": 130017073, "uid": 130017013, "type": 3, "target": 4, "value": 400, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 4, "cd": "", "duration": "", "icon": "ygxt_jnicon_kf", "desc": "staffSkillDesc.9", "desc_params": 400}, {"id": 130019011, "uid": 130019011, "type": 3, "target": 8, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.34", "desc_params": 100}, {"id": 130019021, "uid": 130019011, "type": 3, "target": 8, "value": 120, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.34", "desc_params": 120}, {"id": 130019031, "uid": 130019011, "type": 3, "target": 8, "value": 140, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.34", "desc_params": 140}, {"id": 130019041, "uid": 130019011, "type": 3, "target": 8, "value": 160, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.34", "desc_params": 160}, {"id": 130019051, "uid": 130019011, "type": 3, "target": 8, "value": 180, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.34", "desc_params": 180}, {"id": 130019061, "uid": 130019011, "type": 3, "target": 8, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.34", "desc_params": 200}, {"id": 130019012, "uid": 130019012, "type": 4, "target": "", "value": "", "move_speed": 436, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 190}, {"id": 130019022, "uid": 130019012, "type": 4, "target": "", "value": "", "move_speed": 450, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 9.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 210}, {"id": 130019032, "uid": 130019012, "type": 4, "target": "", "value": "", "move_speed": 457, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 9, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 230}, {"id": 130019042, "uid": 130019012, "type": 4, "target": "", "value": "", "move_speed": 459, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 8.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 250}, {"id": 130019052, "uid": 130019012, "type": 4, "target": "", "value": "", "move_speed": 467, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 7.5, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 300}, {"id": 130019013, "uid": 130019013, "type": 14, "target": 8, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.35", "desc_params": 100}, {"id": 130019023, "uid": 130019013, "type": 14, "target": 8, "value": 120, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.35", "desc_params": 120}, {"id": 130019033, "uid": 130019013, "type": 14, "target": 8, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.35", "desc_params": 150}, {"id": 130019043, "uid": 130019013, "type": 14, "target": 8, "value": 180, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.35", "desc_params": 180}, {"id": 130019053, "uid": 130019013, "type": 14, "target": 8, "value": 220, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 8, "cd": "", "duration": "", "icon": "ygxt_j<PERSON>on_mry", "desc": "staffSkillDesc.35", "desc_params": 220}, {"id": 130023011, "uid": 130023011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 512, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 100}, {"id": 130023021, "uid": 130023011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 648, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 125}, {"id": 130023031, "uid": 130023011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 800, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 150}, {"id": 130023041, "uid": 130023011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 968, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 175}, {"id": 130023051, "uid": 130023011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 1152, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 200}, {"id": 130023061, "uid": 130023011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 1568, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 250}, {"id": 130023071, "uid": 130023011, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 2592, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 350}, {"id": 130023012, "uid": 130023012, "type": 28, "target": "", "value": 1, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_zbsd", "desc": "staffSkillDesc.46", "desc_params": 1}, {"id": 130023022, "uid": 130023012, "type": 28, "target": "", "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_zbsd", "desc": "staffSkillDesc.46", "desc_params": 2}, {"id": 130023032, "uid": 130023012, "type": 28, "target": "", "value": 3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_zbsd", "desc": "staffSkillDesc.46", "desc_params": 3}, {"id": 130023013, "uid": 130023013, "type": 29, "target": "", "value": 0.05, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_sycsj", "desc": "staffSkillDesc.47", "desc_params": 5}, {"id": 130023023, "uid": 130023013, "type": 29, "target": "", "value": 0.1, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_sycsj", "desc": "staffSkillDesc.47", "desc_params": 10}, {"id": 130023033, "uid": 130023013, "type": 29, "target": "", "value": 0.15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_sycsj", "desc": "staffSkillDesc.47", "desc_params": 15}, {"id": 130023043, "uid": 130023013, "type": 29, "target": "", "value": 0.2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_sycsj", "desc": "staffSkillDesc.47", "desc_params": 20}, {"id": 130023053, "uid": 130023013, "type": 29, "target": "", "value": 0.3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 12, "cd": "", "duration": "", "icon": "ygxt_jnicon_sycsj", "desc": "staffSkillDesc.47", "desc_params": 30}, {"id": *********, "uid": *********, "type": 22, "target": "", "value": 20, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.38", "desc_params": 20}, {"id": 131001021, "uid": *********, "type": 22, "target": "", "value": 21, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.38", "desc_params": 21}, {"id": 131001031, "uid": *********, "type": 22, "target": "", "value": 23, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.38", "desc_params": 23}, {"id": *********, "uid": *********, "type": 22, "target": "", "value": 25, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_klzz", "desc": "staffSkillDesc.38", "desc_params": 25}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 8, "move_speed": 288, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 50}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 7.5, "move_speed": 325, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 70}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 7, "move_speed": 354, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 90}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 6.5, "move_speed": 373, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 110}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 6, "move_speed": 381, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 130}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 5.2, "move_speed": 338, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 150}, {"id": *********, "uid": *********, "type": 7, "target": "NEED_TAXI|NEED_DEPOSIT_LUGGAGE", "value": 4.8, "move_speed": 361, "pet_move_speed": "", "map_in_force": 1, "assign_map": 1, "cd": "", "duration": "", "icon": "ygxt_jnicon_jcjc", "desc": "staffSkillDesc.15", "desc_params": 180}, {"id": *********, "uid": *********, "type": 8, "target": 1, "value": 20, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 20}, {"id": *********, "uid": *********, "type": 8, "target": 1, "value": 40, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 40}, {"id": *********, "uid": *********, "type": 8, "target": 1, "value": 70, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 70}, {"id": 131001043, "uid": *********, "type": 8, "target": 1, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 100}, {"id": 131001053, "uid": *********, "type": 8, "target": 1, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 150}, {"id": 131001063, "uid": *********, "type": 8, "target": 1, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xfzz", "desc": "staffSkillDesc.18", "desc_params": 200}, {"id": 131002011, "uid": 131002011, "type": 20, "target": "", "value": 20, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_jsfy", "desc": "staffSkillDesc.36", "desc_params": 20}, {"id": 131002021, "uid": 131002011, "type": 20, "target": "", "value": 21, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_jsfy", "desc": "staffSkillDesc.36", "desc_params": 21}, {"id": 131002031, "uid": 131002011, "type": 20, "target": "", "value": 22, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_jsfy", "desc": "staffSkillDesc.36", "desc_params": 22}, {"id": 131002041, "uid": 131002011, "type": 20, "target": "", "value": 23, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_jsfy", "desc": "staffSkillDesc.36", "desc_params": 23}, {"id": 131002051, "uid": 131002011, "type": 20, "target": "", "value": 24, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_jsfy", "desc": "staffSkillDesc.36", "desc_params": 24}, {"id": 131002061, "uid": 131002011, "type": 20, "target": "", "value": 25, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_jsfy", "desc": "staffSkillDesc.36", "desc_params": 25}, {"id": 131002012, "uid": 131002012, "type": 21, "target": "", "value": 25, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 25}, {"id": 131002022, "uid": 131002012, "type": 21, "target": "", "value": 26, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 26}, {"id": 131002032, "uid": 131002012, "type": 21, "target": "", "value": 27, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 27}, {"id": 131002042, "uid": 131002012, "type": 21, "target": "", "value": 28, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 28}, {"id": 131002052, "uid": 131002012, "type": 21, "target": "", "value": 29, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 29}, {"id": 131002062, "uid": 131002012, "type": 21, "target": "", "value": 30, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 30}, {"id": 131002072, "uid": 131002012, "type": 21, "target": "", "value": 31, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 31}, {"id": 131002082, "uid": 131002012, "type": 21, "target": "", "value": 32, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 32}, {"id": 131002092, "uid": 131002012, "type": 21, "target": "", "value": 33, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 33}, {"id": 131002102, "uid": 131002012, "type": 21, "target": "", "value": 34, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 34}, {"id": 131002112, "uid": 131002012, "type": 21, "target": "", "value": 35, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 35}, {"id": 131002122, "uid": 131002012, "type": 21, "target": "", "value": 37, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 37}, {"id": 131002132, "uid": 131002012, "type": 21, "target": "", "value": 40, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": "", "cd": "", "duration": "", "icon": "ygxt_jnicon_lxsy", "desc": "staffSkillDesc.37", "desc_params": 40}, {"id": 131002013, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 8, "move_speed": 800, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 150}, {"id": 131002023, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 7.5, "move_speed": 820, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 170}, {"id": 131002033, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 7, "move_speed": 824, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 190}, {"id": 131002043, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6.6, "move_speed": 837, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 210}, {"id": 131002053, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6.2, "move_speed": 837, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 230}, {"id": 131002063, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5.8, "move_speed": 824, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 250}, {"id": 131002073, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5.5, "move_speed": 828, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 270}, {"id": 131002083, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5.2, "move_speed": 823, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 290}, {"id": 131002093, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4.9, "move_speed": 807, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 310}, {"id": 131002103, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4.7, "move_speed": 817, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 330}, {"id": 131002113, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4.5, "move_speed": 820, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 350}, {"id": 131002123, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4.1, "move_speed": 841, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 400}, {"id": 131002133, "uid": 131002013, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 3.8, "move_speed": 874, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 450}, {"id": 130024011, "uid": 130024011, "type": 3, "target": 13, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 100}, {"id": 130024021, "uid": 130024011, "type": 3, "target": 13, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 150}, {"id": 130024031, "uid": 130024011, "type": 3, "target": 13, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 200}, {"id": 130024041, "uid": 130024011, "type": 3, "target": 13, "value": 250, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 250}, {"id": 130024051, "uid": 130024011, "type": 3, "target": 13, "value": 300, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 300}, {"id": 130024061, "uid": 130024011, "type": 3, "target": 13, "value": 350, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 350}, {"id": 130024071, "uid": 130024011, "type": 3, "target": 13, "value": 400, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 400}, {"id": 130024081, "uid": 130024011, "type": 3, "target": 13, "value": 450, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 450}, {"id": 130024091, "uid": 130024011, "type": 3, "target": 13, "value": 500, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 500}, {"id": 130024101, "uid": 130024011, "type": 3, "target": 13, "value": 550, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 550}, {"id": 130024111, "uid": 130024011, "type": 3, "target": 13, "value": 600, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 600}, {"id": 130024121, "uid": 130024011, "type": 3, "target": 13, "value": 650, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cfp", "desc": "staffSkillDesc.48", "desc_params": 650}, {"id": 130024012, "uid": 130024012, "type": 4, "target": "", "value": "", "move_speed": 251, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 120}, {"id": 130024022, "uid": 130024012, "type": 4, "target": "", "value": "", "move_speed": 531, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 220}, {"id": 130024032, "uid": 130024012, "type": 4, "target": "", "value": "", "move_speed": 915, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": 10, "duration": "", "icon": "ygxt_jnicon_zdjq", "desc": "staffSkillDesc.12", "desc_params": 320}, {"id": 130025011, "uid": 130025011, "type": 14, "target": 13, "value": 70, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 70}, {"id": 130025021, "uid": 130025011, "type": 14, "target": 13, "value": 80, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 80}, {"id": 130025031, "uid": 130025011, "type": 14, "target": 13, "value": 90, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 90}, {"id": 130025041, "uid": 130025011, "type": 14, "target": 13, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 100}, {"id": 130025051, "uid": 130025011, "type": 14, "target": 13, "value": 110, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 110}, {"id": 130025061, "uid": 130025011, "type": 14, "target": 13, "value": 120, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 120}, {"id": 130025071, "uid": 130025011, "type": 14, "target": 13, "value": 130, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 130}, {"id": 130025081, "uid": 130025011, "type": 14, "target": 13, "value": 140, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 140}, {"id": 130025091, "uid": 130025011, "type": 14, "target": 13, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_gmyf", "desc": "staffSkillDesc.49", "desc_params": 150}, {"id": 130025012, "uid": 130025012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 6, "move_speed": 545, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 175}, {"id": 130025022, "uid": 130025012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 5, "move_speed": 703, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 275}, {"id": 130025032, "uid": 130025012, "type": 7, "target": "NEED_CHAT|NEED_SERVE|NEED_FOOD", "value": 4, "move_speed": 722, "pet_move_speed": "", "map_in_force": 1, "assign_map": "1|2|3|4|5|6|8|12|13", "cd": "", "duration": "", "icon": "ygxt_jnicon_xesw", "desc": "staffSkillDesc.16", "desc_params": 375}, {"id": 130025013, "uid": 130025013, "type": 30, "target": 130025, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cjllk", "desc": "staffSkillDesc.50", "desc_params": 6}, {"id": 130025023, "uid": 130025013, "type": 30, "target": 130025, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cjllk", "desc": "staffSkillDesc.50", "desc_params": 8}, {"id": 130025033, "uid": 130025013, "type": 30, "target": 130025, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_cjllk", "desc": "staffSkillDesc.50", "desc_params": 10}, {"id": 100007011, "uid": 100007011, "type": 1, "target": 100007, "value": 1, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 1}, {"id": 100007021, "uid": 100007011, "type": 1, "target": 100007, "value": 2, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 2}, {"id": 100007031, "uid": 100007011, "type": 1, "target": 100007, "value": 3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 3}, {"id": 100007041, "uid": 100007011, "type": 1, "target": 100007, "value": 4, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 4}, {"id": 100007051, "uid": 100007011, "type": 1, "target": 100007, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 6}, {"id": 100007061, "uid": 100007011, "type": 1, "target": 100007, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 8}, {"id": 100007071, "uid": 100007011, "type": 1, "target": 100007, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 10}, {"id": 100007081, "uid": 100007011, "type": 1, "target": 100007, "value": 12, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 12}, {"id": 100007091, "uid": 100007011, "type": 1, "target": 100007, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_tlsx", "desc": "staffSkillDesc.51", "desc_params": 15}, {"id": 100007012, "uid": 100007012, "type": 2, "target": 100007, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.52", "desc_params": 25}, {"id": 100007022, "uid": 100007012, "type": 2, "target": 100007, "value": 10, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.52", "desc_params": 30}, {"id": 100007032, "uid": 100007012, "type": 2, "target": 100007, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_ggtlzy", "desc": "staffSkillDesc.52", "desc_params": 35}, {"id": 100007013, "uid": 100007013, "type": 31, "target": 100007, "value": 0.08, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_jsclxh", "desc": "staffSkillDesc.53", "desc_params": 8}, {"id": 100007023, "uid": 100007013, "type": 31, "target": 100007, "value": 0.11, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_jsclxh", "desc": "staffSkillDesc.53", "desc_params": 11}, {"id": 100007033, "uid": 100007013, "type": 31, "target": 100007, "value": 0.15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 13, "cd": "", "duration": "", "icon": "ygxt_jnicon_jsclxh", "desc": "staffSkillDesc.53", "desc_params": 15}, {"id": 100008011, "uid": 100008011, "type": 3, "target": 5, "value": 50, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 50}, {"id": 100008021, "uid": 100008011, "type": 3, "target": 5, "value": 75, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 75}, {"id": 100008031, "uid": 100008011, "type": 3, "target": 5, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 100}, {"id": 100008041, "uid": 100008011, "type": 3, "target": 5, "value": 125, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 125}, {"id": 100008051, "uid": 100008011, "type": 3, "target": 5, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 150}, {"id": 100008061, "uid": 100008011, "type": 3, "target": 5, "value": 175, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 175}, {"id": 100008071, "uid": 100008011, "type": 3, "target": 5, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 200}, {"id": 100008081, "uid": 100008011, "type": 3, "target": 5, "value": 225, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 225}, {"id": 100008091, "uid": 100008011, "type": 3, "target": 5, "value": 250, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 250}, {"id": 100008101, "uid": 100008011, "type": 3, "target": 5, "value": 275, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 275}, {"id": 100008111, "uid": 100008011, "type": 3, "target": 5, "value": 300, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 300}, {"id": 100008121, "uid": 100008011, "type": 3, "target": 5, "value": 325, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 325}, {"id": 100008131, "uid": 100008011, "type": 3, "target": 5, "value": 350, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 350}, {"id": 100008141, "uid": 100008011, "type": 3, "target": 5, "value": 375, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 375}, {"id": 100008151, "uid": 100008011, "type": 3, "target": 5, "value": 400, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.7", "desc_params": 400}, {"id": 100008012, "uid": 100008012, "type": 14, "target": 5, "value": 50, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 50}, {"id": 100008022, "uid": 100008012, "type": 14, "target": 5, "value": 75, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 75}, {"id": 100008032, "uid": 100008012, "type": 14, "target": 5, "value": 100, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 100}, {"id": 100008042, "uid": 100008012, "type": 14, "target": 5, "value": 125, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 125}, {"id": 100008052, "uid": 100008012, "type": 14, "target": 5, "value": 150, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 150}, {"id": 100008062, "uid": 100008012, "type": 14, "target": 5, "value": 175, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 175}, {"id": 100008072, "uid": 100008012, "type": 14, "target": 5, "value": 200, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 200}, {"id": 100008082, "uid": 100008012, "type": 14, "target": 5, "value": 225, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 225}, {"id": 100008092, "uid": 100008012, "type": 14, "target": 5, "value": 250, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 250}, {"id": 100008102, "uid": 100008012, "type": 14, "target": 5, "value": 275, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 275}, {"id": 100008112, "uid": 100008012, "type": 14, "target": 5, "value": 300, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 300}, {"id": 100008122, "uid": 100008012, "type": 14, "target": 5, "value": 325, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 325}, {"id": 100008132, "uid": 100008012, "type": 14, "target": 5, "value": 350, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 350}, {"id": 100008142, "uid": 100008012, "type": 14, "target": 5, "value": 375, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 375}, {"id": 100008152, "uid": 100008012, "type": 14, "target": 5, "value": 400, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_xxs", "desc": "staffSkillDesc.24", "desc_params": 400}, {"id": 100008013, "uid": 100008013, "type": 32, "target": 100008, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin1", "desc": "staffSkillDesc.54", "desc_params": 5}, {"id": 100008023, "uid": 100008013, "type": 32, "target": 100008, "value": 8, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin1", "desc": "staffSkillDesc.54", "desc_params": 8}, {"id": 100008033, "uid": 100008013, "type": 32, "target": 100008, "value": 12, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin1", "desc": "staffSkillDesc.54", "desc_params": 12}, {"id": 100008043, "uid": 100008013, "type": 32, "target": 100008, "value": 15, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin1", "desc": "staffSkillDesc.54", "desc_params": 15}, {"id": 100008053, "uid": 100008013, "type": 32, "target": 100008, "value": 18, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin1", "desc": "staffSkillDesc.54", "desc_params": 18}, {"id": 100008014, "uid": 100008014, "type": 33, "target": 100008, "value": 3, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin2", "desc": "staffSkillDesc.55", "desc_params": 3}, {"id": 100008024, "uid": 100008014, "type": 33, "target": 100008, "value": 4, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin2", "desc": "staffSkillDesc.55", "desc_params": 4}, {"id": 100008034, "uid": 100008014, "type": 33, "target": 100008, "value": 5, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin2", "desc": "staffSkillDesc.55", "desc_params": 5}, {"id": 100008044, "uid": 100008014, "type": 33, "target": 100008, "value": 6, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin2", "desc": "staffSkillDesc.55", "desc_params": 6}, {"id": 100008054, "uid": 100008014, "type": 33, "target": 100008, "value": 7, "move_speed": "", "pet_move_speed": "", "map_in_force": 1, "assign_map": 5, "cd": "", "duration": "", "icon": "ygxt_jnicon_ypin2", "desc": "staffSkillDesc.55", "desc_params": 7}]