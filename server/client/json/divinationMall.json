[{"id": 1, "goods": "40,-1,1", "sort": 1, "weight": 70000, "cut_rate": 0, "cut_range": "", "cost": "37,-1,1500", "lv_limit": "", "count": 2}, {"id": 10011, "goods": "38,1,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,200", "lv_limit": 1, "count": 1}, {"id": 10012, "goods": "38,1,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,600", "lv_limit": 2, "count": 1}, {"id": 10013, "goods": "38,1,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,800", "lv_limit": 3, "count": 1}, {"id": 10014, "goods": "38,1,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 4, "count": 1}, {"id": 10021, "goods": "38,2,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,300", "lv_limit": 1, "count": 1}, {"id": 10022, "goods": "38,2,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,600", "lv_limit": 2, "count": 1}, {"id": 10023, "goods": "38,2,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,900", "lv_limit": 3, "count": 1}, {"id": 10024, "goods": "38,2,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 4, "count": 1}, {"id": 10031, "goods": "38,3,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,600", "lv_limit": 1, "count": 1}, {"id": 10032, "goods": "38,3,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 2, "count": 1}, {"id": 10033, "goods": "38,3,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1800", "lv_limit": 3, "count": 1}, {"id": 10034, "goods": "38,3,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2400", "lv_limit": 4, "count": 1}, {"id": 10041, "goods": "38,4,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 1, "count": 1}, {"id": 10042, "goods": "38,4,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2400", "lv_limit": 2, "count": 1}, {"id": 10043, "goods": "38,4,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3600", "lv_limit": 3, "count": 1}, {"id": 10044, "goods": "38,4,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4800", "lv_limit": 4, "count": 1}, {"id": 10051, "goods": "38,5,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1400", "lv_limit": 1, "count": 1}, {"id": 10052, "goods": "38,5,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2800", "lv_limit": 2, "count": 1}, {"id": 10053, "goods": "38,5,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4200", "lv_limit": 3, "count": 1}, {"id": 10054, "goods": "38,5,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,5600", "lv_limit": 4, "count": 1}, {"id": 10061, "goods": "38,6,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,230", "lv_limit": 1, "count": 1}, {"id": 10062, "goods": "38,6,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,690", "lv_limit": 2, "count": 1}, {"id": 10063, "goods": "38,6,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,920", "lv_limit": 3, "count": 1}, {"id": 10064, "goods": "38,6,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1380", "lv_limit": 4, "count": 1}, {"id": 10071, "goods": "38,7,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,520", "lv_limit": 1, "count": 1}, {"id": 10072, "goods": "38,7,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1040", "lv_limit": 2, "count": 1}, {"id": 10073, "goods": "38,7,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1560", "lv_limit": 3, "count": 1}, {"id": 10074, "goods": "38,7,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2080", "lv_limit": 4, "count": 1}, {"id": 10081, "goods": "38,8,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,350", "lv_limit": 1, "count": 1}, {"id": 10082, "goods": "38,8,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1050", "lv_limit": 2, "count": 1}, {"id": 10083, "goods": "38,8,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1400", "lv_limit": 3, "count": 1}, {"id": 10084, "goods": "38,8,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2100", "lv_limit": 4, "count": 1}, {"id": 10091, "goods": "38,9,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1250", "lv_limit": 1, "count": 1}, {"id": 10092, "goods": "38,9,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2500", "lv_limit": 2, "count": 1}, {"id": 10093, "goods": "38,9,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3750", "lv_limit": 3, "count": 1}, {"id": 10094, "goods": "38,9,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,5000", "lv_limit": 4, "count": 1}, {"id": 10101, "goods": "38,10,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,250", "lv_limit": 1, "count": 1}, {"id": 10102, "goods": "38,10,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,750", "lv_limit": 2, "count": 1}, {"id": 10103, "goods": "38,10,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1000", "lv_limit": 3, "count": 1}, {"id": 10104, "goods": "38,10,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 4, "count": 1}, {"id": 10111, "goods": "38,11,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,500", "lv_limit": 1, "count": 1}, {"id": 10112, "goods": "38,11,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1000", "lv_limit": 2, "count": 1}, {"id": 10113, "goods": "38,11,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 3, "count": 1}, {"id": 10114, "goods": "38,11,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2000", "lv_limit": 4, "count": 1}, {"id": 10121, "goods": "38,12,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,500", "lv_limit": 1, "count": 1}, {"id": 10122, "goods": "38,12,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 2, "count": 1}, {"id": 10123, "goods": "38,12,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2000", "lv_limit": 3, "count": 1}, {"id": 10124, "goods": "38,12,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3000", "lv_limit": 4, "count": 1}, {"id": 10131, "goods": "38,13,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,550", "lv_limit": 1, "count": 1}, {"id": 10132, "goods": "38,13,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1100", "lv_limit": 2, "count": 1}, {"id": 10133, "goods": "38,13,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1650", "lv_limit": 3, "count": 1}, {"id": 10134, "goods": "38,13,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2200", "lv_limit": 4, "count": 1}, {"id": 10141, "goods": "38,14,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,650", "lv_limit": 1, "count": 1}, {"id": 10142, "goods": "38,14,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1300", "lv_limit": 2, "count": 1}, {"id": 10143, "goods": "38,14,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1950", "lv_limit": 3, "count": 1}, {"id": 10144, "goods": "38,14,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2600", "lv_limit": 4, "count": 1}, {"id": 10151, "goods": "38,15,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,300", "lv_limit": 1, "count": 1}, {"id": 10152, "goods": "38,15,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,600", "lv_limit": 2, "count": 1}, {"id": 10153, "goods": "38,15,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,900", "lv_limit": 3, "count": 1}, {"id": 10154, "goods": "38,15,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 4, "count": 1}, {"id": 10161, "goods": "38,16,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,750", "lv_limit": 1, "count": 1}, {"id": 10162, "goods": "38,16,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 2, "count": 1}, {"id": 10163, "goods": "38,16,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2250", "lv_limit": 3, "count": 1}, {"id": 10164, "goods": "38,16,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3000", "lv_limit": 4, "count": 1}, {"id": 10171, "goods": "38,17,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,100", "lv_limit": 1, "count": 1}, {"id": 10172, "goods": "38,17,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,300", "lv_limit": 2, "count": 1}, {"id": 10173, "goods": "38,17,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,400", "lv_limit": 3, "count": 1}, {"id": 10174, "goods": "38,17,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,600", "lv_limit": 4, "count": 1}, {"id": 10181, "goods": "38,18,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1350", "lv_limit": 1, "count": 1}, {"id": 10182, "goods": "38,18,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2700", "lv_limit": 2, "count": 1}, {"id": 10183, "goods": "38,18,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4050", "lv_limit": 3, "count": 1}, {"id": 10184, "goods": "38,18,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,5400", "lv_limit": 4, "count": 1}, {"id": 10191, "goods": "38,19,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,150", "lv_limit": 1, "count": 1}, {"id": 10192, "goods": "38,19,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,450", "lv_limit": 2, "count": 1}, {"id": 10193, "goods": "38,19,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,600", "lv_limit": 3, "count": 1}, {"id": 10194, "goods": "38,19,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,900", "lv_limit": 4, "count": 1}, {"id": 10201, "goods": "38,20,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1450", "lv_limit": 1, "count": 1}, {"id": 10202, "goods": "38,20,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2900", "lv_limit": 2, "count": 1}, {"id": 10203, "goods": "38,20,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4350", "lv_limit": 3, "count": 1}, {"id": 10204, "goods": "38,20,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,5800", "lv_limit": 4, "count": 1}, {"id": 10211, "goods": "38,21,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,250", "lv_limit": 1, "count": 1}, {"id": 10212, "goods": "38,21,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,750", "lv_limit": 2, "count": 1}, {"id": 10213, "goods": "38,21,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1000", "lv_limit": 3, "count": 1}, {"id": 10214, "goods": "38,21,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 4, "count": 1}, {"id": 10221, "goods": "38,22,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,750", "lv_limit": 1, "count": 1}, {"id": 10222, "goods": "38,22,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 2, "count": 1}, {"id": 10223, "goods": "38,22,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2250", "lv_limit": 3, "count": 1}, {"id": 10224, "goods": "38,22,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3000", "lv_limit": 4, "count": 1}, {"id": 10231, "goods": "38,23,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,850", "lv_limit": 1, "count": 1}, {"id": 10232, "goods": "38,23,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1700", "lv_limit": 2, "count": 1}, {"id": 10233, "goods": "38,23,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2550", "lv_limit": 3, "count": 1}, {"id": 10234, "goods": "38,23,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3400", "lv_limit": 4, "count": 1}, {"id": 10241, "goods": "38,24,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1650", "lv_limit": 1, "count": 1}, {"id": 10242, "goods": "38,24,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3300", "lv_limit": 2, "count": 1}, {"id": 10243, "goods": "38,24,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4950", "lv_limit": 3, "count": 1}, {"id": 10244, "goods": "38,24,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,6600", "lv_limit": 4, "count": 1}, {"id": 10251, "goods": "38,25,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1650", "lv_limit": 1, "count": 1}, {"id": 10252, "goods": "38,25,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3300", "lv_limit": 2, "count": 1}, {"id": 10253, "goods": "38,25,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4950", "lv_limit": 3, "count": 1}, {"id": 10254, "goods": "38,25,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,6600", "lv_limit": 4, "count": 1}, {"id": 20011, "goods": "39,1,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,200", "lv_limit": 1, "count": 1}, {"id": 20012, "goods": "39,1,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,600", "lv_limit": 2, "count": 1}, {"id": 20013, "goods": "39,1,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,800", "lv_limit": 3, "count": 1}, {"id": 20014, "goods": "39,1,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 4, "count": 1}, {"id": 20021, "goods": "39,2,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,220", "lv_limit": 1, "count": 1}, {"id": 20022, "goods": "39,2,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,660", "lv_limit": 2, "count": 1}, {"id": 20023, "goods": "39,2,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,880", "lv_limit": 3, "count": 1}, {"id": 20024, "goods": "39,2,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1320", "lv_limit": 4, "count": 1}, {"id": 20031, "goods": "39,3,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,240", "lv_limit": 1, "count": 1}, {"id": 20032, "goods": "39,3,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,720", "lv_limit": 2, "count": 1}, {"id": 20033, "goods": "39,3,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,960", "lv_limit": 3, "count": 1}, {"id": 20034, "goods": "39,3,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1440", "lv_limit": 4, "count": 1}, {"id": 20051, "goods": "39,5,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,260", "lv_limit": 1, "count": 1}, {"id": 20052, "goods": "39,5,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,780", "lv_limit": 2, "count": 1}, {"id": 20053, "goods": "39,5,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1040", "lv_limit": 3, "count": 1}, {"id": 20054, "goods": "39,5,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1560", "lv_limit": 4, "count": 1}, {"id": 20061, "goods": "39,6,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,280", "lv_limit": 1, "count": 1}, {"id": 20062, "goods": "39,6,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,840", "lv_limit": 2, "count": 1}, {"id": 20063, "goods": "39,6,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1120", "lv_limit": 3, "count": 1}, {"id": 20064, "goods": "39,6,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1680", "lv_limit": 4, "count": 1}, {"id": 20081, "goods": "39,8,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,180", "lv_limit": 1, "count": 1}, {"id": 20082, "goods": "39,8,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,540", "lv_limit": 2, "count": 1}, {"id": 20083, "goods": "39,8,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,720", "lv_limit": 3, "count": 1}, {"id": 20084, "goods": "39,8,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1080", "lv_limit": 4, "count": 1}, {"id": 20091, "goods": "39,9,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,190", "lv_limit": 1, "count": 1}, {"id": 20092, "goods": "39,9,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,570", "lv_limit": 2, "count": 1}, {"id": 20093, "goods": "39,9,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,760", "lv_limit": 3, "count": 1}, {"id": 20094, "goods": "39,9,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1140", "lv_limit": 4, "count": 1}, {"id": 20111, "goods": "39,11,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,300", "lv_limit": 1, "count": 1}, {"id": 20112, "goods": "39,11,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,900", "lv_limit": 2, "count": 1}, {"id": 20113, "goods": "39,11,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 3, "count": 1}, {"id": 20114, "goods": "39,11,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1800", "lv_limit": 4, "count": 1}, {"id": 20121, "goods": "39,12,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,320", "lv_limit": 1, "count": 1}, {"id": 20122, "goods": "39,12,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,960", "lv_limit": 2, "count": 1}, {"id": 20123, "goods": "39,12,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1280", "lv_limit": 3, "count": 1}, {"id": 20124, "goods": "39,12,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1920", "lv_limit": 4, "count": 1}, {"id": 20141, "goods": "39,14,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,400", "lv_limit": 1, "count": 1}, {"id": 20142, "goods": "39,14,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,800", "lv_limit": 2, "count": 1}, {"id": 20143, "goods": "39,14,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 3, "count": 1}, {"id": 20144, "goods": "39,14,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1600", "lv_limit": 4, "count": 1}, {"id": 20161, "goods": "39,16,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,340", "lv_limit": 1, "count": 1}, {"id": 20162, "goods": "39,16,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,680", "lv_limit": 2, "count": 1}, {"id": 20163, "goods": "39,16,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1020", "lv_limit": 3, "count": 1}, {"id": 20164, "goods": "39,16,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1360", "lv_limit": 4, "count": 1}, {"id": 20191, "goods": "39,19,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,500", "lv_limit": 1, "count": 1}, {"id": 20192, "goods": "39,19,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1000", "lv_limit": 2, "count": 1}, {"id": 20193, "goods": "39,19,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 3, "count": 1}, {"id": 20194, "goods": "39,19,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2000", "lv_limit": 4, "count": 1}, {"id": 20221, "goods": "39,22,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 1, "count": 1}, {"id": 20222, "goods": "39,22,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2400", "lv_limit": 2, "count": 1}, {"id": 20223, "goods": "39,22,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3600", "lv_limit": 3, "count": 1}, {"id": 20224, "goods": "39,22,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4800", "lv_limit": 4, "count": 1}, {"id": 20071, "goods": "39,7,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,400", "lv_limit": 1, "count": 1}, {"id": 20072, "goods": "39,7,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,800", "lv_limit": 2, "count": 1}, {"id": 20073, "goods": "39,7,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 3, "count": 1}, {"id": 20074, "goods": "39,7,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1600", "lv_limit": 4, "count": 1}, {"id": 20101, "goods": "39,10,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,460", "lv_limit": 1, "count": 1}, {"id": 20102, "goods": "39,10,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,920", "lv_limit": 2, "count": 1}, {"id": 20103, "goods": "39,10,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1380", "lv_limit": 3, "count": 1}, {"id": 20104, "goods": "39,10,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1840", "lv_limit": 4, "count": 1}, {"id": 20231, "goods": "39,23,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1300", "lv_limit": 1, "count": 1}, {"id": 20232, "goods": "39,23,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2600", "lv_limit": 2, "count": 1}, {"id": 20233, "goods": "39,23,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3900", "lv_limit": 3, "count": 1}, {"id": 20234, "goods": "39,23,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,5200", "lv_limit": 4, "count": 1}, {"id": 20241, "goods": "39,24,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 1, "count": 1}, {"id": 20242, "goods": "39,24,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3000", "lv_limit": 2, "count": 1}, {"id": 20243, "goods": "39,24,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4500", "lv_limit": 3, "count": 1}, {"id": 20244, "goods": "39,24,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,6000", "lv_limit": 4, "count": 1}, {"id": 20151, "goods": "39,15,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,800", "lv_limit": 1, "count": 1}, {"id": 20152, "goods": "39,15,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1600", "lv_limit": 2, "count": 1}, {"id": 20153, "goods": "39,15,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2400", "lv_limit": 3, "count": 1}, {"id": 20154, "goods": "39,15,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3200", "lv_limit": 4, "count": 1}, {"id": 20201, "goods": "39,20,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1000", "lv_limit": 1, "count": 1}, {"id": 20202, "goods": "39,20,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2000", "lv_limit": 2, "count": 1}, {"id": 20203, "goods": "39,20,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3000", "lv_limit": 3, "count": 1}, {"id": 20204, "goods": "39,20,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4000", "lv_limit": 4, "count": 1}, {"id": 20211, "goods": "39,21,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1100", "lv_limit": 1, "count": 1}, {"id": 20212, "goods": "39,21,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2200", "lv_limit": 2, "count": 1}, {"id": 20213, "goods": "39,21,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3300", "lv_limit": 3, "count": 1}, {"id": 20214, "goods": "39,21,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4400", "lv_limit": 4, "count": 1}, {"id": 20131, "goods": "39,13,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,500", "lv_limit": 1, "count": 1}, {"id": 20132, "goods": "39,13,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1000", "lv_limit": 2, "count": 1}, {"id": 20133, "goods": "39,13,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 3, "count": 1}, {"id": 20134, "goods": "39,13,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2000", "lv_limit": 4, "count": 1}, {"id": 20251, "goods": "39,25,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 1, "count": 1}, {"id": 20252, "goods": "39,25,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2400", "lv_limit": 2, "count": 1}, {"id": 20253, "goods": "39,25,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3600", "lv_limit": 3, "count": 1}, {"id": 20254, "goods": "39,25,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4800", "lv_limit": 4, "count": 1}, {"id": 20261, "goods": "39,26,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 1, "count": 1}, {"id": 20262, "goods": "39,26,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2400", "lv_limit": 2, "count": 1}, {"id": 20263, "goods": "39,26,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3600", "lv_limit": 3, "count": 1}, {"id": 20264, "goods": "39,26,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4800", "lv_limit": 4, "count": 1}, {"id": 20171, "goods": "39,17,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,800", "lv_limit": 1, "count": 1}, {"id": 20172, "goods": "39,17,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1600", "lv_limit": 2, "count": 1}, {"id": 20173, "goods": "39,17,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2400", "lv_limit": 3, "count": 1}, {"id": 20174, "goods": "39,17,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3200", "lv_limit": 4, "count": ""}, {"id": 20181, "goods": "39,18,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,900", "lv_limit": 1, "count": ""}, {"id": 20182, "goods": "39,18,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1800", "lv_limit": 2, "count": ""}, {"id": 20183, "goods": "39,18,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2700", "lv_limit": 3, "count": ""}, {"id": 20184, "goods": "39,18,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3600", "lv_limit": 4, "count": ""}, {"id": 20271, "goods": "39,27,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1100", "lv_limit": 1, "count": ""}, {"id": 20272, "goods": "39,27,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2200", "lv_limit": 2, "count": ""}, {"id": 20273, "goods": "39,27,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3300", "lv_limit": 3, "count": ""}, {"id": 20274, "goods": "39,27,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4400", "lv_limit": 4, "count": ""}, {"id": 20281, "goods": "39,28,1", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1400", "lv_limit": 1, "count": ""}, {"id": 20282, "goods": "39,28,2", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2800", "lv_limit": 2, "count": ""}, {"id": 20283, "goods": "39,28,3", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4200", "lv_limit": 3, "count": ""}, {"id": 20284, "goods": "39,28,4", "sort": 213, "weight": 0, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,5600", "lv_limit": 4, "count": ""}, {"id": 20041, "goods": "39,4,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,160", "lv_limit": 1, "count": ""}, {"id": 20042, "goods": "39,4,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,480", "lv_limit": 2, "count": ""}, {"id": 20043, "goods": "39,4,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,640", "lv_limit": 3, "count": ""}, {"id": 20044, "goods": "39,4,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,960", "lv_limit": 4, "count": ""}, {"id": 20291, "goods": "39,29,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,340", "lv_limit": 1, "count": ""}, {"id": 20292, "goods": "39,29,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1020", "lv_limit": 2, "count": ""}, {"id": 20293, "goods": "39,29,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1360", "lv_limit": 3, "count": ""}, {"id": 20294, "goods": "39,29,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2040", "lv_limit": 4, "count": ""}, {"id": 20301, "goods": "39,30,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,320", "lv_limit": 1, "count": ""}, {"id": 20302, "goods": "39,30,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,960", "lv_limit": 2, "count": ""}, {"id": 20303, "goods": "39,30,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1280", "lv_limit": 3, "count": ""}, {"id": 20304, "goods": "39,30,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1920", "lv_limit": 4, "count": ""}, {"id": 20311, "goods": "39,31,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,300", "lv_limit": 1, "count": ""}, {"id": 20312, "goods": "39,31,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,900", "lv_limit": 2, "count": ""}, {"id": 20313, "goods": "39,31,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1200", "lv_limit": 3, "count": ""}, {"id": 20314, "goods": "39,31,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1800", "lv_limit": 4, "count": ""}, {"id": 20321, "goods": "39,32,1", "sort": 150, "weight": 700, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,640", "lv_limit": 1, "count": ""}, {"id": 20322, "goods": "39,32,2", "sort": 97, "weight": 1050, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1280", "lv_limit": 2, "count": ""}, {"id": 20323, "goods": "39,32,3", "sort": 82, "weight": 1400, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1920", "lv_limit": 3, "count": ""}, {"id": 20324, "goods": "39,32,4", "sort": 23, "weight": 2100, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2560", "lv_limit": 4, "count": ""}, {"id": 20331, "goods": "39,33,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 1, "count": ""}, {"id": 20332, "goods": "39,33,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3000", "lv_limit": 2, "count": ""}, {"id": 20333, "goods": "39,33,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4500", "lv_limit": 3, "count": ""}, {"id": 20334, "goods": "39,33,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,6000", "lv_limit": 4, "count": ""}, {"id": 20341, "goods": "39,34,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1500", "lv_limit": 1, "count": ""}, {"id": 20342, "goods": "39,34,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3000", "lv_limit": 2, "count": ""}, {"id": 20343, "goods": "39,34,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,4500", "lv_limit": 3, "count": ""}, {"id": 20344, "goods": "39,34,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,6000", "lv_limit": 4, "count": ""}, {"id": 20351, "goods": "39,35,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1700", "lv_limit": 1, "count": ""}, {"id": 20352, "goods": "39,35,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3400", "lv_limit": 2, "count": ""}, {"id": 20353, "goods": "39,35,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,5100", "lv_limit": 3, "count": ""}, {"id": 20354, "goods": "39,35,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,6800", "lv_limit": 4, "count": ""}, {"id": 20361, "goods": "39,36,1", "sort": 112, "weight": 1000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,360", "lv_limit": 1, "count": ""}, {"id": 20362, "goods": "39,36,3", "sort": 60, "weight": 1500, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1080", "lv_limit": 2, "count": ""}, {"id": 20363, "goods": "39,36,4", "sort": 38, "weight": 2000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1440", "lv_limit": 3, "count": ""}, {"id": 20364, "goods": "39,36,6", "sort": 1, "weight": 3000, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,2160", "lv_limit": 4, "count": ""}, {"id": 20371, "goods": "39,37,1", "sort": 197, "weight": 300, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,1700", "lv_limit": 1, "count": ""}, {"id": 20372, "goods": "39,37,2", "sort": 181, "weight": 450, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,3400", "lv_limit": 2, "count": ""}, {"id": 20373, "goods": "39,37,3", "sort": 165, "weight": 600, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,5100", "lv_limit": 3, "count": ""}, {"id": 20374, "goods": "39,37,4", "sort": 134, "weight": 900, "cut_rate": 10, "cut_range": "5,35", "cost": "37,-1,6800", "lv_limit": 4, "count": ""}]