import { assetsMgr } from "./AssetsMgr";
import { gameHelper } from "./GameHelper"
import ModelMgr from "./ModelMgr";
import WorldModel from "./world/WorldModel";

enum RoomType {
    HOT_SPRINGS = 0, // 0.温泉
    RESTAURANT, // 1.餐厅
    BIG_ROOM, //大通铺
    KEFANG, //客房
    CINEMA,
}

const DATA_SETTLE = 'offlineSettle'
const DATA_CHANGE_TIME = 'offlineChangeTime'

// 离线计算系统
export default class OfflineSysModel {
    private typeAry: number[] = [];

    private readonly leaveProb: number = 0.3
    private order: number[] = [];
    private visit: {} = {};

    private weight: any = {};
    private prob: any = {};

    private world: WorldModel = null

    private modelMgr: ModelMgr = null

    onCreate(modelMgr: ModelMgr) {
        this.world = modelMgr.get('world')
        this.modelMgr = modelMgr
    }

    //计算离线预期收益
    public getOfflineExpectedIncome(): number {
        let inHotel = this.inHotel();
        let inHotSprings = this.inHotSprings();
        let inRestaurant = this.inRestaurant();
        let inCinema = this.inCinema();
        let expectation = inHotel + inHotSprings + inRestaurant + inCinema;
        return Math.floor(expectation);
    }

      //计算预期收益
      public getOnlineExpectedIncome(): number {
        this.typeAry.length = 0;

        //不要删掉，算概率用的
        this.weight[RoomType.HOT_SPRINGS] = 200;
        this.weight[RoomType.RESTAURANT] = 200;
        this.weight[RoomType.BIG_ROOM] = 300;
        this.weight[RoomType.CINEMA] = 200;

        const kefangs = this.world.getKefangs()
        const kfCount = kefangs.length
        if (kfCount > 0) {
            this.typeAry.push(RoomType.KEFANG);
            this.weight[RoomType.KEFANG] = 300 + 100 * kfCount;
        }
        else {
            this.weight[RoomType.KEFANG] = 0;
        }

        const dorm = this.world.getDorm()
        if (dorm) {
            this.typeAry.push(RoomType.BIG_ROOM);
        }

        const shower = this.world.getShower()
        if (shower) {
            this.typeAry.push(RoomType.HOT_SPRINGS);
        }

        const dining = this.world.getDining()
        if (dining) {
            this.typeAry.push(RoomType.RESTAURANT);
        }

        const cinema = this.world.getCinema()
        if (cinema) {
            this.typeAry.push(RoomType.CINEMA);
        }

        this.calProb()

        if (this.prob[RoomType.KEFANG]) {
            this.prob[RoomType.KEFANG] /= kfCount
        }

        let sum = 0
        if (dorm) {
            sum += dorm.getOnlineIncome() * this.prob[RoomType.BIG_ROOM] * 0.98
        }
        if (kfCount > 0) {
            this.world.getKefangs().forEach(m => {
                sum += m.getOnlineIncome() * this.prob[RoomType.KEFANG] * 0.95
            })
        }
        if (dining) {
            sum += dining.getOnlineIncome() * this.prob[RoomType.RESTAURANT] * 0.98
        }
        if (shower) {
            sum += shower.getOnlineIncome() * this.prob[RoomType.HOT_SPRINGS] * 0.98
        }
        if (cinema) {
            sum += cinema.getOnlineIncome() * this.prob[RoomType.CINEMA] * 0.98
        }

        let tips = this.inGuestTip()
        if (tips) {
            sum += tips * 1.2
        }

        return Math.ceil(sum);
    }

    private calProb() {
        this.order.length = 0;
        let len = this.typeAry.length;
        for (let i = 0; i < len; i++) {
            let rType = this.typeAry[i];
            this.prob[rType] = 0;
            this.order.push(0);
        }

        for (let i = 1; i <= len; i++) {
            this.dfs(0, len, i);
        }

        // for (let i = 0; i < len; i++) {
        //     let rType = this.typeAry[i];
        //     console.log("@@@@", this.prob[rType]);
        // }
    }

    private callProb(len: number, k: number) {
        let total = 0;
        for (let i = 0; i < len; i++) {
            let rType = this.typeAry[i];
            total += this.weight[rType];
        }
        let res = 1;
        for (let i = 0; i < k; i++) {
            let rType = this.order[i];
            let p = this.weight[rType] / total;
            total -= this.weight[rType];
            res *= p;

            if (rType == RoomType.BIG_ROOM) total -= this.weight[RoomType.KEFANG]
            if (rType == RoomType.KEFANG) total -= this.weight[RoomType.BIG_ROOM]
        }

        for (let i = 1; i < k; i++) {
            res *= (1 - this.leaveProb);
        }

        if (total > 0) {
            res *= this.leaveProb;
        }

        for (let i = 0; i < k; i++) {
            let rType = this.order[i];
            if (!this.prob[rType]) {
                this.prob[rType] = 0;
            }
            this.prob[rType] += res;
        }
    }

    private dfs(cur: number, len: number, k: number) {
        if (cur >= k) {
            this.callProb(len, k);
            return;
        }
        for (let i = 0; i < len; i++) {
            let rType = this.typeAry[i];
            if (rType == RoomType.BIG_ROOM) {
                if (this.visit[RoomType.KEFANG]) continue;
            }
            if (rType == RoomType.KEFANG) {
                if (this.visit[RoomType.BIG_ROOM]) continue;
            }
            if (!this.visit[rType]) {
                this.visit[rType] = true;
                this.order[cur] = this.typeAry[i];
                this.dfs(cur + 1, len, k);
                this.visit[rType] = false;
            }
        }
    }

    //计算住店收益
    private inHotel(): number {
        // （房间1号收益+房间2号收益+..+房间n号收益）/房间数量
        const dorm = this.world.getDorm()
        let sum = dorm ? dorm.getOfflineIncome() : 0

        this.world.getKefangs().forEach(m => {
            sum += m.getOfflineIncome()
        })
        return sum
    }

    //计算温泉收益
    private inHotSprings(): number {
        const shower = this.world.getShower()
        return shower ? shower.getOfflineIncome() : 0
    }

    //计算餐厅收益
    private inRestaurant(): number {
        const dining = this.world.getDining()
        return dining ? dining.getOfflineIncome() : 0
    }

    private inCinema() : number {
        const cinema = this.world.getCinema()
        return cinema ? cinema.getOfflineIncome() : 0
    }

    //计算客人小费收益
    private inGuestTip(): number {
        let sumWeight = 0, arr = []
        this.world.getRoleAttrs().forEach(m => {
            const json = assetsMgr.getJsonData('customersBase', m.id)
            if (json && m.isHasTip()) { //暂时只计算绝对值的
                sumWeight += json.visit_weight
                arr.push({ w: json.visit_weight, tip: m.getTip() })
            }
        })
        return arr.reduce((val, cur) => (cur.w / sumWeight * cur.tip) + val, 0)
    }

    //计算住店love
    private inHotelLove(): number {
        // （房间1号收益+房间2号收益+..+房间n号收益）/房间数量
        const dorm = this.world.getDorm()
        let cnt = dorm ? 1 : 0, sum = dorm ? dorm.getOfflineLove() : 0
        this.world.getKefangs().forEach(m => {
            cnt += 1
            sum += m.getOfflineLove()
        })
        return sum / cnt
    }

    //计算温泉love
    private inHotSpringsLove(): number {
        const shower = this.world.getShower()
        return shower ? shower.getOfflineLove() : 0
    }

    //计算餐厅love
    private inRestaurantLove(): number {
        const dining = this.world.getDining()
        return dining ? dining.getOfflineLove() : 0
    }
}