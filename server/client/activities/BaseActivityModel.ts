import {ActivityType} from "../constant/Enums"
// import { aconfig } from "./AConfig"

export default class BaseActivityModel {

    public lastShowTrenchTime: number = 0

    // 获取活动类型
    public getType() {
        return ActivityType.NONE
    }

    // 获取活动名称
    public getName() {
        return ''
    }

    // 当前可领取的奖励
    public getRewards() {
        return null
    }

    // 活动是否有奖励可领取
    public getHasReward() {
        return false
    }

    // 活动是否已完成
    public getFinished() {
        return false
    }

    // 是否显示左上角banner
    public isShowTrench() {
        return !this.getFinished()
    }

    public getConfig() {
        return {id: '', view: null, heartLimit: 0}
    }

    // 红点key
    public getReddotKey() {
        return ''
    }

    // 点击槽位后是否关闭
    public isClickClose() {
        // return !!aconfig.TRENCH_CONF[this.getType()]?.['clickClose']
        return true;
    }

    public getView() {
        return this.getConfig().view ?? ''
    }
}