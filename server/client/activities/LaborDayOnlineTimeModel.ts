import { ActivityType, OnlineTimeActivityId } from "../constant/Enums"
import {gameHelper} from "../GameHelper"
import BaseOnlineTimeModel from "./BaseOnlineTimeModel"
import ModelMgr from "../ModelMgr";
import DBModel from "../DBModel";


export default class LaborDayOnlineTimeModel extends BaseOnlineTimeModel {

    public readonly ID: number = OnlineTimeActivityId.LABOR_DAY

    private modelMgr: ModelMgr = null

    private dbHelper: DBModel;

    // @ts-ignore
    public onCreate(modelMgr: ModelMgr) { this.modelMgr = modelMgr}

    public onClean() { }

    public init() {
        this.dbHelper = this.modelMgr.getDB()
        this._init = true
        // 持久化数据
        const data = this.dbData || this.dbHelper.register('laborDayOnlineTime', 1, this.toDB, this, Object.keys(this.toDB())) || {}
        this.isOpendPnl = data.isOpendPnl || false
        // this.realStarTime = data.realStarTime || 0
        this.onlineTime = data.onlineTime || 0
        // this.finishIds = data.finishIds || []
        //
        if (this.realStarTime === 0) {
            this.realStarTime = gameHelper.now()
        }
        // this.initGifts()
    }

    public getBaseData() {
        if (!this.dbData) {
            this.dbData = this.dbHelper.register('laborDayOnlineTime', 1, this.toDB, this, Object.keys(this.toDB())) || {}
        }
        this.realStarTime = this.dbData?.realStarTime || 0
        this.finishIds = this.dbData?.finishIds || []
        this.initGifts()
    }

    public check() {
        if (this.isInit()) {
            return
        } else {
            this.getBaseData()
            if (this.getFinished()) {
                return
            }
        }

        this.init()
    }

    // 获取活动类型
    public getType() {
        return ActivityType.LABOR_DAY_ONLINE_TIME
    }
    private toDB() {
        return {
            isOpendPnl: this.isOpendPnl,
            realStarTime: this.realStarTime,
            onlineTime: this.onlineTime,
            finishIds: this.finishIds,
        }
    }
}