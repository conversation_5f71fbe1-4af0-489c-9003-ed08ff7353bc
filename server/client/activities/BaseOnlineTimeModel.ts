import { CHECK_CONST } from "../constant/Constant";
import { AdFeedbackGiftInfo } from "../constant/DataType";
import { ActivityType, OnlineTimeActivityId } from "../constant/Enums";
import EventType from "../common/EventType";
import {gameHelper} from "../GameHelper"
import { assetsMgr } from "../AssetsMgr"
import ut from "../../common/util"

import BaseActivityModel from "./BaseActivityModel";



export default class BaseOnlineTimeModel extends BaseActivityModel {

    // 节日id
    public readonly ID: number = OnlineTimeActivityId.NONE

    // 是否打开了对应界面
    public isOpendPnl: boolean = false

    // 该玩家活动真正开启的时间
    public realStarTime: number = 0

    // 当前累计在线时长
    public onlineTime: number = 0

    // 已经完成的列表 代表已经领取的
    public finishIds: number[] = []

    // 礼物列表
    public gifts: AdFeedbackGiftInfo[] = []

    // -------------------------------------------------------------

    public dbData: any = null

    // 配置
    public _json: any = null

    // 是否初始化
    public _init: boolean = false

    // 计时使用
    public tmpTime: number = 0
    private TIME_INTERVAL: number = 1 // 每次记录的间隔（s）


    public getGifts() { return this.gifts }
    public getOnlineTime() { return this.onlineTime }

    public check() { }
    public isInit() { return this._init }
    public isOpen() { return gameHelper.getTempHeart() >= CHECK_CONST.HOLIDAY_CRISTMAS }
    public getRealEndTime() { return this.realStarTime + this.duration }
    public isRealEnd() { return this.realStarTime !== 0 && gameHelper.now() > this.getRealEndTime() }  // 是否真正的结束
    public initJson() {
        if (!this._json) {
            this._json = assetsMgr.getJsonData('onlineTimeBase', this.ID)
        }
    }

    public get json() { return this._json }
    public get startTime() { return gameHelper.isGLobal() ? this._json?.g_start || '' : this._json?.i_start || '' } // 开启时间
    public get endTime() { return gameHelper.isGLobal() ? this._json?.g_end || '' : this._json?.i_end || '' } // 结束时间
    public get heartLimit() { return this._json?.heart || CHECK_CONST.HOLIDAY_CRISTMAS }
    public get duration() { return (this._json?.duration || 10) * ut.Time.Day }
    public get viewPnl() { return this._json?.view || '' }
    public get entranceIcon() { return this._json?.entrance_icon || '' }
    public get bannerIcon() { return this._json?.banner_icon || '' }
    public get titleIcon() { return this._json?.title_icon || '' }

    // 初始化所有奖励
    public initGifts() {
        let gifts = ut.stringToNumbers((assetsMgr.getJsonData('onlineTimeBase', this.ID)?.rewards || ''), ',')
        this.gifts = gifts.map(m => {
            let data = assetsMgr.getJsonData('onlineTimeAward', m)
            return {
                id: data.id,
                targetCount: data.val,
                rewards: gameHelper.stringToConditions(data.reward),
                isFinish: this.finishIds.has(data.id),
            }
        })
    }



    // 当前可领取的奖励
    public getRewards() {
        let rewards = this.gifts.find(m => !m.isFinish && m.targetCount <= this.onlineTime)
        return rewards?.rewards
    }

    // 活动是否有奖励可领取
    public getHasReward() {
        return !this.getFinished() && this.gifts.some(m => !m.isFinish && m.targetCount <= this.onlineTime)
    }

    // 活动是否已完成
    public getFinished() {
        if (!this.isOpen()) { // 基础条件判断
            return true
        } else {
            this.initJson()
            if (this.isRealEnd() || (this.realStarTime === 0 && !gameHelper.checkActivityAutoDate(this.startTime, this.endTime))) { // 时间判断
                return true
            } else if (this.realStarTime !== 0 && this.gifts.length === this.finishIds.length) { // 表示奖励已领取完
                return true
            }
        }
        return false
    }

    // 活动多语言 & 活动界面 & 开启条件限制
    public getConfig() {
        return {
            id: this.entranceIcon,
            banner: this.bannerIcon,
            title: this.titleIcon,
            view: this.viewPnl,
            heartLimit: this.heartLimit,
        }
    }

    public getReddotKey() { return 'online_time' }
}