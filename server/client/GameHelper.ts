import ut from "../common/util"
import {assetsMgr} from "./AssetsMgr"
import ConditionObj from "./common/ConditionObj"
import {SCENE_LEAVE_ODDS} from "./constant/Constant"
import {MapSceneType, ConditionType, FoodMiningType, MessageType, WishType, ChannelType} from "./constant/Enums"
import GlobalModel from "./Global";

class GameHelper {

    private _global: GlobalModel = null

    public get global() {
        return this._global || (this._global = mc.modelMgr.get('global'))
    }

    // 将字符串拆成通用条件信息
    public stringToConditions(str: string): ConditionObj[] {
        try {
            return str.split('|').filter(m => !!m).map(m => new ConditionObj().fromString(m))
        } catch (e) {
            return [];
        }
    }

    public conditionIsNone(str: string) {
        if (!str) {
            return true
        }
        const arr = this.stringToConditions(str)
        return arr.length === 0 || arr[0].type === ConditionType.NONE
    }

    // 计算场景所有爱心 
    public getMapAllLove(fav: number, builds: any[], odds: number) {
        let use_fav = 0, n = 0
        builds.forEach(m => {
            if (!!m.use_fav) {
                use_fav += m.use_fav
                n += 1
            }
        })
        if (n === 0) {
            return fav
        }
        const m = odds * 0.01
        // 与某个设施的互动概率:(1-m)(1-(1-m)n)/mn
        const ff = (1 - m) * (1 - Math.pow(1 - m, n)) / (m * n)
        // 收入之和+互动概率x互动收入之和
        return fav + ff * use_fav
    }

    // 小费
    public stringToTip(str: string) {
        if (!str) {
            return null
        }
        const [t, v] = ut.stringToNumbers(str, ',')
        if (t === undefined || v === undefined) {
            return null
        }
        return {type: t, val: v}
    }

    // 根据实际等级获取对应阶级和等级
    public getRoleStageAndMinLv(id: number, lv: number) {
        const s = id * 1000
        let stge = 1, preMaxLv = 0
        do {
            const json = assetsMgr.getJsonData('customersStage', s + stge)
            if (!json || lv <= json.lv_limt) {
                break
            }
            preMaxLv = json.lv_limt
            stge += 1
        } while (true)
        return {stage: stge, minLv: lv - preMaxLv}
    }

    // 获取客人离开场景概率
    public getLeaveOdds(sceneType: MapSceneType) {
        return SCENE_LEAVE_ODDS[sceneType] || 0
    }

    public conditionsToString(conds: ConditionObj | ConditionObj[]) {
        let list = []
        if (!Array.isArray(conds)) conds = [conds]
        for (let {type, id, count, tag} of conds) {
            let params = [type, id, count]
            if (tag) {
                params.push(tag)
            }
            list.push(params.join(","))
        }
        return list.join("|")
    }

    public now() {
        return Date.now();
    }

    public getTempHeart() {
        return this.global.getTempHeart()
    }

    public isGLobal() {
        return true
    }

}

export const gameHelper = new GameHelper()