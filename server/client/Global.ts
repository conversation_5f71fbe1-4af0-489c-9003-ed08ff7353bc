import ModelMgr from "./ModelMgr"

// 全局模块
export default class GlobalModel {

    private biscuits: number = 0 //饼干
    private candies: number = 0  //糖果
    private heart: number = 0  //心心
    private accTotalBiscuits: number = 0 //累计获得饼干
    private accTotalCandies: number = 0 //累计获得饼干
    private accTotalAdCount: number = 0 //累计观看广告次数
    private accTotalExpelFuguiCount: number = 0 //累计打富贵次数
    private accTotalSolicitTransportCount: any = {} //累计招揽指定交通工具
    private accTotalInviteGrandmaCount: number = 0 //累计邀请老奶奶次数
    private accTotalUseSoupbaseCount: number = 0 //累计使用汤料次数
    private accTotalGaliOutingCount: number = 0 //咖喱累计出游多少次
    private accTotalShowerRepairCount: number = 0 //累计修理温泉设施次数
    private accTotalDiningReplenishCount: number = 0 //累计餐厅补货次数
    private accTotalSeeoffMammonCount: number = 0 //累计欢送财神次数
    private accTotalGetElfinCount: number = 0 //累计和饼干小精灵玩耍次数
    private accTotalSolicitGechangCount: number = 0 //累计招揽歌唱精灵次数
    private accTotalGetJianbaoStaminaCount: number = 0 //累计获得煎包多少体力
    private accTotalBuyWasaiPropCount: number = 0 //累计从哇塞那购买道具次数
    private accTotalSolicitRoleNum: number = 0 //累计招揽客人
    private accTotalCompFinishCount: number = 0 //累计合成完成次数
    private accTotalFoodMiningCount: number = 0 //累厨艺点挖掘次数
    private tempHeart: number = 0 //临时的 用于频繁获取（只读）
    private accTotalPraise: number = 0
    private accYoyoPropCount: number = 0 //累计摇摇卷

    private modelMgr: ModelMgr = null

    public onCreate(modelMgr: ModelMgr) {
        this.modelMgr = modelMgr
    }

    public init() {
        let dbHelper = this.modelMgr.getDB()
        const data = dbHelper.register('global') || {}
        this.biscuits = Math.floor(data.biscuits || 0)
        this.tempHeart = this.heart = Math.floor(data.heart || 0)
        this.candies = Math.floor(data.candies || 0)
        this.accTotalBiscuits = data.accTotalBiscuits || 0
        this.accTotalCandies = data.accTotalCandies || 0
        this.accTotalAdCount = data.accTotalAdCount || 0
        this.accYoyoPropCount = data.accYoyoPropCount || 0
        this.accTotalPraise = data.accTotalPraise || 0
        // this.accTotalExpelFuguiCount = data.accTotalExpelFuguiCount || 0
        // this.accTotalSolicitTransportCount = data.accTotalSolicitTransportCount || {}
        // this.accTotalInviteGrandmaCount = data.accTotalInviteGrandmaCount || 0
        // this.accTotalUseSoupbaseCount = data.accTotalUseSoupbaseCount || 0
        // this.accTotalGaliOutingCount = data.accTotalGaliOutingCount || 0
        // this.accTotalShowerRepairCount = data.accTotalShowerRepairCount || 0
        // this.accTotalDiningReplenishCount = data.accTotalDiningReplenishCount || 0
        this.accTotalSeeoffMammonCount = data.accTotalSeeoffMammonCount || 0
        // this.accTotalGetElfinCount = data.accTotalGetElfinCount || 0
        // this.accTotalSolicitGechangCount = data.accTotalSolicitGechangCount || 0
        // this.accTotalGetJianbaoStaminaCount = data.accTotalGetJianbaoStaminaCount || 0
        // this.accTotalBuyWasaiPropCount = data.accTotalBuyWasaiPropCount || 0
        // this.accTotalSolicitRoleNum = data.accTotalSolicitRoleNum || 0
        this.accTotalCompFinishCount = data.accTotalCompFinishCount || 0
        // this.accTotalFoodMiningCount = data.accTotalFoodMiningCount || 0
    }

    public getBiscuits() { return this.biscuits }
    public getCandies() { return this.candies }
    public getTempHeart() { return this.tempHeart }
    public getHeart() { return this.heart }
    public getAccTotalBiscuits() { return this.accTotalBiscuits }
    public getAccTotalCandies() { return this.accTotalCandies }
    public getAccTotalAdCount() { return this.accTotalAdCount }
    public getAccTotalExpelFuguiCount() { return this.accTotalExpelFuguiCount }
    public getAccTotalInviteGrandmaCount() { return this.accTotalInviteGrandmaCount }
    public getAccTotalUseSoupbaseCount() { return this.accTotalUseSoupbaseCount }
    public getAccTotalGaliOutingCount() { return this.accTotalGaliOutingCount }
    public getAccTotalShowerRepairCount() { return this.accTotalShowerRepairCount }
    public getAccTotalDiningReplenishCount() { return this.accTotalDiningReplenishCount }
    public getAccTotalSeeoffMammonCount() { return this.accTotalSeeoffMammonCount }
    public getAccTotalGetElfinCount() { return this.accTotalGetElfinCount }
    public getAccTotalSolicitGechangCount() { return this.accTotalSolicitGechangCount }
    public getAccTotalGetJianbaoStaminaCount() { return this.accTotalGetJianbaoStaminaCount }
    public getAccTotalBuyWasaiPropCount() { return this.accTotalBuyWasaiPropCount }
    public getAccTotalSolicitRoleNum() { return this.accTotalSolicitRoleNum }
    public getAccTotalCompFinishCount() { return this.accTotalCompFinishCount }
    public getAccTotalFoodMiningCount() { return this.accTotalFoodMiningCount }
    public getAccTotalSolicitTransportCount(type: number) { return this.accTotalSolicitTransportCount[type] || 0 }
    public getAccTotalPraise() { return this.accTotalPraise }
    public getAccYoyoPropCount() { return this.accYoyoPropCount }
}