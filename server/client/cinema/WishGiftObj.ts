import ConditionObj from "../common/ConditionObj"
import { gameHelper } from "../GameHelper"
import { cconfig } from "./CConfig"

// 心愿礼包
export default class WishGiftObj {

    public rewards: ConditionObj[] = [] //奖励列表
    public needCount: number = 0 //需要击中数量
    public isClaim: boolean = false //是否领取了

    public init(id: number, needCount: number, modelMgr) {
        this.rewards = modelMgr.getRewardsById(id)
        this.needCount = needCount
        this.isClaim = false
        return this
    }

    public fromDB(data: any) {
        this.rewards = (data.rewards || []).map(m => new ConditionObj().fromDB(m))
        this.needCount = data.needCount ?? 0
        this.isClaim = !!data.isClaim
        return this
    }

    public toDB() {
        return {
            rewards: this.rewards.map(m => m.toDB()),
            needCount: this.needCount,
            isClaim: this.isClaim,
        }
    }

    public getNeedCount() {
        return Math.floor(this.needCount * cconfig.WISH_PROGRESS_FACTOR)
    }
}