import { CINEMA_LEAVE_ODDS } from "../constant/Constant";
import { CinemaBuildType, FilmPlayState, MapSceneType, NpcType } from "../constant/Enums";
import { MoveTargetType, StateType } from "../constant/StateEnum";
import { gameHelper } from "../GameHelper";
import WorldModel from "../world/WorldModel";
import { assetsMgr } from "../AssetsMgr";
import BaseMapModel from "../map/BaseMapModel";
import ModelMgr from "../ModelMgr";
import CBuildObj from "./CBuildObj";
import { cconfig } from "./CConfig";
import FilmObj from "./FilmObj";
// import RowPieceModel from "./RowPieceModel";
import SignedObj from "./SignedObj";

/**
 * 电影院
 */
export default class CinemaModel extends BaseMapModel {

    private readonly OP_PRAISE_EXTRA_OFFSET = 1

    private builds: CBuildObj[] = []
    private films: FilmObj[] = [] //影片列表
    private signed: SignedObj = null
    // private rowPiece: RowPieceModel = null
    private opPraise: number = 0 //当前产出的口碑
    private accOpPraise: number = 0 //累计获得的口碑

    private playState: FilmPlayState = FilmPlayState.NONE //当前影片播放状态
    private stateTime: number = 0 //状态计时
    private readyEff: number = 0 //当前的准备效率
    private praiseLimit: number = 0 //当前口碑收集上限

    private model: WorldModel = null

    private curRoundCount: number = 0 //当前场次
    // private curPlayFilm: FilmObj = null //当前播放的影片
    private curReadyTimeRatio: number = 1 //当前准备时间比例
    private curHitWishs: number[] = [] //当前影片击中的心愿弹幕
    private curDespairCount: number = 0 //当前显示失望次数

    private showBarrageIntervalTime: number = 0 //客人显示弹幕间隔时间
    private showDespairIntervalTime: number = 0 //客人显示失望间隔时间

    constructor(modelMgr: ModelMgr) {
        super('cinema', modelMgr)
        super.init(MapSceneType.CINEMA)
        this.modelMgr = modelMgr
        this.model = modelMgr.get<WorldModel>('world')
        this.playState = FilmPlayState.NONE
        // 注册持久化数据
        let dbHelper = modelMgr.getDB()
        const data = dbHelper.register(this.uid) || {}
        // 获取所有设施
        if (data.builds) {
            this.builds = data.builds.map(m => new CBuildObj().fromDB(m))
        } else {
            this.builds = assetsMgr.getJson('cinemaUnlock').datas.filter(m => gameHelper.conditionIsNone(m.unlock_cost)).map(m => new CBuildObj().init(m.id, true))
            this.builds.push(new CBuildObj().init(0, true))
        }
        // 影片
        this.films = (data.films || []).map(m => new FilmObj().fromDB(m))
        // 签约信息
        this.signed = data.signed ? new SignedObj().fromDB(data.signed) : new SignedObj().init()
        // 排片
        // const len = this.getBagFilms().length
        // this.rowPiece = data.rowPiece ? new RowPieceModel().fromDB(data.rowPiece, len) : new RowPieceModel().init(len)
        // 产出口碑
        // this.opPraise.value = Math.floor(data.opPraise || 0)
        // this.accOpPraise.value = data.accOpPraise || 0
        this.updateAttr()
    }

    public getSumIncome() { return this.attr.income }
    public getSumFav() { return this.attr.fav }
    // 获取所有的收入 包括使用设施的
    public getOfflineIncome() { return this.builds.reduce((val, cur) => cur.output + val, 0) }
    // 获取所有的收入 包括使用设施的
    public getOfflineLove() { return gameHelper.getMapAllLove(this.attr.fav, this.builds, CINEMA_LEAVE_ODDS) }
    public getSigned() { return this.signed }

    // 当前用于口碑
    public getPraise() { return this.signed?.praise || 0 } //当前拥有的口碑

    public getFilms(){
        return this.films;
    }

    // 刷新属性
    private updateAttr() {
        this.attr.reset()
        this.readyEff = 0
        this.praiseLimit = 0
        this.builds.forEach(m => {
            this.attr.add(m)
            this.readyEff += m.ready_eff
            this.praiseLimit += m.praise_limit
        })
    }
}