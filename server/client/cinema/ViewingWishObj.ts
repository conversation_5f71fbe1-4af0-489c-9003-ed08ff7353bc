import { cconfig } from "./CConfig"

// 观影心愿
export default class ViewingWishObj {

    public types: number[] = [] //类型列表
    public barrageId: number = 0 //弹幕id
    public hitCount: number = 0 //击中数量
    public color: number = 0

    public targetHitCount: number = 0 //一共击中的次数
    public lastHitCount: number = 0 //次数整数 用于外部客人显示弹幕使用

    public init(barrageId: number, types: number[], color: number) {
        this.barrageId = barrageId
        this.types = types
        this.hitCount = this.lastHitCount = this.targetHitCount = 0
        this.color = color
        return this
    }

    public fromDB(data: any) {
        return ut.setValue('barrageId|types|hitCount|color', data, this)
    }

    public toDB() {
        return ut.setValue('barrageId|types|hitCount|color', this)
    }

    public get barrageText() { return 'barrage.' + this.barrageId }

    public getHitCount() {
        return Math.floor(this.hitCount * cconfig.WISH_PROGRESS_FACTOR)
    }

    // 添加击中次数
    public addTargetHitCount(types: number[]) {
        if (this.isHit(types)) {
            this.targetHitCount += 1
        }
    }

    // 是否击中
    public isHit(types: number[]) {
        return this.types.some(m => types.has(m))
    }

    // 检测是否可以显示弹幕
    public checkCanShowBarrage() {
        let hc = this.getHitCount(), add = 0
        if (hc > this.lastHitCount) {
            add = hc - this.lastHitCount
            this.lastHitCount = hc
        }
        return add
    }
}