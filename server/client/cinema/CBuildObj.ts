import BuildObj from "../common/BuildObj"
import { CinemaBuildType } from "../constant/Enums"

// 电影院 建筑设施
export default class CBuildObj extends BuildObj {

    public get ready_eff() { return this.json ? this.json.ready_eff : 0 }
    public get praise_limit() { return this.json ? this.json.praise_limit : 0 }

    public getSceneType() { return 'cinema' }

    public getJsonName() { return 'cinema' }

    // 是否影院座椅
    public isChair() {
        return this.type >= CinemaBuildType.SEAT1 && this.type <= CinemaBuildType.SEAT4
    }
}