import { MessageType, RowPieceState } from "../../../common/constant/Enums"
import EventType from "../../../common/event/EventType"
import { gameHelper } from "../../../common/helper/GameHelper"
import { cconfig } from "./CConfig"
import FilmObj from "./FilmObj"
import SlotObj from "./SlotObj"
import ViewingWishObj from "./ViewingWishObj"
import WishGiftObj from "./WishGiftObj"

// 排片模块
export default class RowPieceModel {

    private slots: SlotObj[] = [] //当前槽位列表
    private isPlayTip: boolean = true //是否弹出播放提示
    private state: RowPieceState = RowPieceState.RESET //当前状态
    private praiseOutputTime: number = 0 //口碑产出时间
    private calendarStartTime: number = 0 //档期开始时间
    private calendarDurationTime: number = 0 //档期持续时间
    private oneTimehitWishCount: number = 0 //每毫秒击中心愿次数
    private wishs: ViewingWishObj[] = [] //观影心愿
    private rewards: WishGiftObj[] = [] //奖励礼包
    private roundCount: number = 0 //当前场次
    private praiseOnlineOutput: number = 0 //口碑在线产出值

    private pollingPlays: number[] = [] //实际播放的列表
    private praiseOutputFactor: number = 0 //口碑产出系数

    private hitWishSumCount: number = 0 //当前排片击中心愿总数

    private praiseCheckInterval: number = 0 //检测间隔
    private isSyncCheckPraiseOutput: boolean = false
    private isSyncCalendarTime: boolean = false

    public init(filmCount: number) {
        this.slots = assetsMgr.getJson('rowPieceSlot').datas.map(m => new SlotObj().init(m))
        this.reset(filmCount)
        return this
    }

    public fromDB(data: any, filmCount: number) {
        const slots: any[] = data.slots || []
        this.slots = assetsMgr.getJson('rowPieceSlot').datas.map(json => {
            const d = slots.remove('id', json.id)
            return d ? new SlotObj().fromDB(d) : new SlotObj().init(json)
        })
        this.isPlayTip = data.isPlayTip ?? true
        this.state = data.state ?? RowPieceState.RESET
        this.praiseOutputTime = data.praiseOutputTime || 0
        this.praiseOnlineOutput = data.praiseOnlineOutput || 0
        this.calendarStartTime = data.calendarStartTime || 0
        this.calendarDurationTime = data.calendarDurationTime || 0
        this.oneTimehitWishCount = data.oneTimehitWishCount || 0
        this.wishs = (data.wishs || []).map(m => new ViewingWishObj().fromDB(m))
        this.rewards = (data.rewards || []).map(m => new WishGiftObj().fromDB(m))
        this.roundCount = data.roundCount || 0
        //
        // 检测一下啊
        if (this.wishs.length === 0) { //如果还没有心愿弹幕 直接重置
            this.reset(filmCount)
        } else if (this.isNotRowPiece()) { //提示排片
            this.addRowPieceMessage()
        } else if (this.state >= RowPieceState.PLAYING) {
            this.filterPollingPlays() //赛选一下
        }
        if (this.state === RowPieceState.PLAYING) {
            // 刷新一下心愿击中次数
            this.updateAllWishHitCount()
            this.wishs.forEach(m => m.checkCanShowBarrage())
            // 计算口碑产出
            const now = gameHelper.now()
            const time = now - this.praiseOutputTime
            if (this.praiseOutputTime > 0) {
                this.praiseOutputTime = now
                const val = time / cconfig.PRAISE_OUTPUT_TIME * this.getOfflinePraiseOutputFactor() * this.praiseOutputFactor
                this.praiseOnlineOutput += val
                // cc.log('口碑离线收益=' + val.toFixed(4) + ', 总值=' + this.praiseOnlineOutput.toFixed(4))
            }
        }
        // 是否有礼物可以领
        if (this.isHasNotClaimGift()) {
            this.addHasGiftMessage()
        } else if (this.state === RowPieceState.END) { //还是这个的话就直接重置
            this.reset(filmCount)
        }
        // cc.log('击中弹幕次数=' + this.hitWishSumCount + '/' + (this.rewards.last()?.needCount || 0) + ', 秒产=' + this.oneTimehitWishCount)
        return this
    }

    public toDB() {
        return {
            slots: this.slots.filter(m => m.isUnlock).map(m => m.toDB()),
            isPlayTip: this.isPlayTip,
            state: this.state,
            praiseOutputTime: this.praiseOutputTime,
            praiseOnlineOutput: this.praiseOnlineOutput,
            calendarStartTime: this.calendarStartTime,
            calendarDurationTime: this.calendarDurationTime,
            oneTimehitWishCount: this.oneTimehitWishCount,
            wishs: this.wishs.map(m => m.toDB()),
            rewards: this.rewards.map(m => m.toDB()),
            roundCount: this.roundCount,
        }
    }

    public getSlots() { return this.slots }
    public getIsPlayTip() { return this.isPlayTip }
    public setIsPlayTip(val: boolean) { this.isPlayTip = val }
    public getState() { return this.state }
    public getWishs() { return this.wishs }
    public getRewards() { return this.rewards }
    public getRoundCount() { return this.roundCount }

    private getOnlinePraiseOutputFactor() {
        return ut.random(cconfig.ONLINE_PRAISE_OUTPUT_FACTOR[0], cconfig.ONLINE_PRAISE_OUTPUT_FACTOR[1])
    }

    private getOfflinePraiseOutputFactor() {
        return ut.random(cconfig.OFFLINE_PRAISE_OUTPUT_FACTOR[0], cconfig.OFFLINE_PRAISE_OUTPUT_FACTOR[1])
    }

    public getPraiseOnlineOutput() { return this.praiseOnlineOutput }

    public changePraiseOnlineOutput(val: number) {
        this.praiseOnlineOutput = Math.max(this.praiseOnlineOutput + val, 0)
    }

    // 获取退到后台后回来 能直接产出的最大口碑数 这里可以看如果在播放中就预留一点口碑
    public getOfflineOutputMaxPraise(isPlayFilm: boolean) {
        const val = Math.floor(isPlayFilm ? Math.min(this.getOnlinePraiseOutputFactor() * this.praiseOutputFactor, this.praiseOnlineOutput) : this.praiseOnlineOutput)
        // 先扣除掉
        this.changePraiseOnlineOutput(-val)
        // 返回
        // cc.log('回到前台 直接产出=' + val.toFixed(4) + ', 剩余=' + this.praiseOnlineOutput.toFixed(4))
        return val
    }

    // 是否还未排片
    public isNotRowPiece() { return this.state <= RowPieceState.NONE }

    // 重置排片
    public reset(filmCount?: number) {
        this.state = RowPieceState.RESET
        this.roundCount = 0
        this.pollingPlays.length = 0
        this.praiseOutputFactor = 0
        this.oneTimehitWishCount = 0
        this.hitWishSumCount = 0
        this.slots.forEach(m => m.reset())
        // 刷新心愿弹幕
        this.updateWishBarrage(filmCount)
        // 通知
        this.addRowPieceMessage()
        eventCenter.emit(EventType.ROW_PIECE_RESET)
        eventCenter.emit(EventType.UPDATE_ROW_PIECE_LAN_TIP)
    }

    // 检测是否刚重置
    public checkIsNone() {
        if (this.state === RowPieceState.RESET) {
            this.state = RowPieceState.NONE
        }
    }

    // 添加排片通知
    private addRowPieceMessage() {
        gameHelper.addMessage({ type: MessageType.WIAT_ROW_PIECE, tag: 'WIAT_ROW_PIECE' }, { key: 'ui.message_117' })
    }

    // 有礼物可以领
    private addHasGiftMessage() {
        gameHelper.addMessage({ type: MessageType.CINEMA_HAS_GIFT, tag: 'CINEMA_HAS_GIFT' }, { key: 'ui.message_118' })
    }

    // 刷新心愿弹幕
    private updateWishBarrage(filmCount: number) {
        this.wishs.length = 0
        this.rewards.length = 0
        filmCount = filmCount ?? (gameHelper.world?.getCinema()?.getBagFilms().length || 0)
        // const filmCount = gameHelper.world.getCinema().getBagFilms().length
        const datas = assetsMgr.getJson('barrageNumGen').datas
        const json = datas.find(m => {
            const [a, b] = ut.stringToNumbers(m.film_need_count, ',')
            return filmCount >= a && filmCount <= b
        }) || datas[0]
        // 随机心愿弹幕
        if (gameHelper.guide.isCanEnterCinema()) {
            this.randomWishBarrage(json)
        } else {
            this.genWishBarrageByGuide()
        }
        // 随机礼包 暂定最多4个
        for (let i = 1; i <= 4; i++) {
            const str = json['reward_' + i] // A,B;A,B|C,D
            if (!str) {
                continue
            }
            const [s, c] = str.split('|')
            // 随机奖励id
            const arr = s.split(';').map(m => {
                const [id, w] = ut.stringToNumbers(m, ',')
                return { id, w }
            })
            const d = arr[ut.randomIndexByWeight(arr, 'w')] || arr.random()
            // 随机数量
            const [c1, c2] = ut.stringToNumbers(c, ',')
            this.rewards.push(new WishGiftObj().init(d.id, ut.random(c1, c2)))
        }
        this.rewards.sort((a, b) => a.needCount - b.needCount) //需要的数量从小到大排个序
    }

    private randomWishBarrage(json: any) {
        const [a, b] = ut.stringToNumbers(json.num, ',')
        const cnt = Math.max(ut.random(a, b), 1) //条目数
        const list: { weight: number, types: string, barrages: number[] }[] = []
        assetsMgr.getJson('barrageBase').datas.forEach(m => {
            for (let i = 0; i < m.num; i++) {
                list.push({ weight: m.weight, types: m.types, barrages: ut.stringToNumbers(m.barrages, ',') })
            }
        })
        const colors = cconfig.BARRAGE_COLORS.map((_, i) => i)
        for (let x = 0; x < cnt && list.length > 0; x++) {
            let i = ut.randomIndexByWeight(list, 'weight')
            let data = list[i]
            if (data) {
                list.splice(i, 1)
            } else {
                data = list.randomRemove()
            }
            data.barrages.delete(id => this.wishs.has('barrageId', id))
            this.wishs.push(new ViewingWishObj().init(data.barrages.random(), ut.stringToNumbers(data.types), colors.randomRemove()))
        }
    }

    // 生成引导的弹幕 固定 1，4，10
    private genWishBarrageByGuide() {
        const colors = cconfig.BARRAGE_COLORS.map((_, i) => i)
        const list = [1, 4, 10]
        list.forEach(id => {
            const data = assetsMgr.getJsonData('barrageBase', id), barrages = ut.stringToNumbers(data.barrages, ',')
            barrages.delete(b => this.wishs.has('barrageId', b))
            this.wishs.push(new ViewingWishObj().init(barrages.random(), ut.stringToNumbers(data.types), colors.randomRemove()))
        })
    }

    // 获取总的心愿击中数量
    public getSumWishHitCount() {
        return this.wishs.reduce((val, cur) => cur.getHitCount() + val, 0)
    }

    // 并刷新心愿击中次数 随机返回击中的心愿用于客人显示
    public updateWishHitCount(film: FilmObj) {
        // 获取当前击中的心愿
        const types = film.types
        const wishs = this.wishs.filter(m => m.isHit(types))
        if (wishs.length > 0) {
            this.updateAllWishHitCount()
            // 刷新UI进度
            eventCenter.emit(EventType.UPDATE_WISH_HIT_COUNT)
            eventCenter.emit(EventType.UPDATE_ROW_PIECE_LAN_TIP) //刷新排片栏提示
            // 是否有礼物可以领取
            if (this.isHasNotClaimGift()) {
                this.addHasGiftMessage()
            }
        }
        // 计算击中差值
        const hitWishs: number[] = []
        wishs.forEach(m => {
            const add = m.checkCanShowBarrage()
            if (add > 0) {
                hitWishs.push(add)
            }
        })
        return {
            hitWishs: hitWishs,
            despairCount: wishs.length > 0 ? 0 : ut.random(cconfig.DESPAIR_COUNT[0], cconfig.DESPAIR_COUNT[1])
        }
    }

    // 刷新心愿击中次数 根据时间
    public updateAllWishHitCount() {
        // 获取当前应该产出的击中数量
        const val = (gameHelper.now() - this.calendarStartTime) * 0.001 * this.oneTimehitWishCount
        // 给所有心愿加上次数
        this.wishs.forEach(m => m.hitCount = Math.min(m.targetHitCount / this.hitWishSumCount * val, m.targetHitCount))
        // cc.log('刷新心愿击中次数 累加值=' + val)
    }

    // 是否放映中
    public isPlaying() {
        return this.state === RowPieceState.PLAYING
    }

    // 开始播放
    public play() {
        this.state = RowPieceState.PLAYING
        // 删除通知
        gameHelper.delMessage(MessageType.WIAT_ROW_PIECE, 'WIAT_ROW_PIECE')
        // 把当前的排片筛选出来 过滤掉没有排片的槽位
        this.filterPollingPlays()
        // 设置下一场的时间
        this.setNextCalendarTime()
        // 场次开始是-1 因为第一场电影会是0
        this.roundCount = -1
        // 排片完成开始放映
        eventCenter.emit(EventType.ROW_PIECE_COMPLETE)
        eventCenter.emit(EventType.UPDATE_ROW_PIECE_LAN_TIP)
    }

    // 筛选可以播放的列表
    private filterPollingPlays() {
        this.pollingPlays.length = 0
        let factor = 0
        for (let i = 0, l = this.slots.length; i < l; i++) {
            const it = this.slots[i]
            if (!!it.filmId) {
                this.pollingPlays.push(i)
                factor += (it.film?.attr?.add_praise_factor || 0)
                const types = it.film.types
                this.wishs.forEach(m => m.addTargetHitCount(types))
            }
        }
        this.hitWishSumCount = this.wishs.reduce((val, cur) => val + cur.targetHitCount, 0)
        this.praiseOutputFactor = factor / this.pollingPlays.length
    }

    // 获取影片id
    public getFilmId(rc?: number) {
        const i = Math.max((rc ?? this.roundCount) % this.pollingPlays.length, 0)
        const index = this.pollingPlays[i]
        return this.slots[index]?.filmId
    }

    // 获取当前播放的影片
    public getCurPlayIndex() {
        const i = Math.max(this.roundCount % this.pollingPlays.length, 0)
        return this.pollingPlays[i] || 0
    }

    // 设置场次
    public setRoundCount(val: number) {
        if (val - this.roundCount === 1) {
            this.roundCount = val //下一个场次始终只会大于1
            return true
        }
        return false
    }

    // 是否还有未领取的礼包
    public isHasNotClaimGift() {
        const sum = this.getSumWishHitCount()
        return this.rewards.some(m => !m.isClaim && sum >= m.getNeedCount())
    }

    // 检测刷新档期
    private async checkUpdateCalendar() {
        if (gameHelper.now() - this.calendarStartTime >= this.calendarDurationTime) {
            this.isSyncCalendarTime = true
            await gameHelper.getServerTime()
            if (gameHelper.now() - this.calendarStartTime >= this.calendarDurationTime) {
                this.calendarStartTime = 0
                this.praiseOutputTime = 0
                this.calendarDurationTime = 0
                this.wishs.forEach(m => m.hitCount = m.targetHitCount) //直接设置所有心愿击中次数
                if (this.isHasNotClaimGift()) {
                    this.state = RowPieceState.END //等待领取
                } else {
                    this.reset() //直接重置
                }
            }
        }
        this.isSyncCalendarTime = false
    }

    // 设置下一个档期的时间
    private async setNextCalendarTime() {
        if (gameHelper.isTimeGoBack()) {
            await gameHelper.getServerTime()
        }
        const now = this.calendarStartTime = this.praiseOutputTime = gameHelper.now()
        const date = new Date(now), hours = date.getHours()
        const calenar = cconfig.VIEWING_CALENDARS.find(m => hours >= m.range[0] && hours < m.range[1]) || cconfig.VIEWING_CALENDARS[0]
        this.calendarDurationTime = calenar.hour * ut.Time.Hour
        // 每秒击中心愿次数
        const duration = Math.floor(this.calendarDurationTime * (ut.random(cconfig.WHC_DURATION_TIME_RANGE[0] * 100, cconfig.WHC_DURATION_TIME_RANGE[1] * 100) * 0.01))
        this.oneTimehitWishCount = Math.min(this.hitWishSumCount, this.rewards.last().needCount) / (duration * 0.001)
        // cc.log('击中弹幕次数=' + this.hitWishSumCount + '/' + (this.rewards.last()?.needCount || 0) + ', 秒产=' + this.oneTimehitWishCount)
    }

    // 获取下个档期剩余时间 (单位秒)
    public getSurplusCalendarTime() {
        return Math.max((this.calendarStartTime + this.calendarDurationTime) - gameHelper.now(), 0) * 0.001
    }

    // 检测口碑产出
    public async checkPraiseOutput() {
        this.praiseCheckInterval = cconfig.PRAISE_OUTPUT_TIME * 0.001
        if (this.praiseOutputTime <= 0) {
            return 0
        } else if (gameHelper.isTimeGoBack()) {
            this.isSyncCheckPraiseOutput = true
            await gameHelper.getServerTime()
        }
        const now = gameHelper.now(), start = this.praiseOutputTime
        const time = now - start, factor = this.getOnlinePraiseOutputFactor()
        this.praiseOutputTime = now
        const praise = time / cconfig.PRAISE_OUTPUT_TIME * factor * this.praiseOutputFactor
        this.praiseOnlineOutput += praise
        this.isSyncCheckPraiseOutput = false
        // cc.log('----------------每分钟口碑产出---------------')
        // cc.log('开始时间=' + ut.dateFormat('hh:mm:ss', start) + ', 结束时间=' + ut.dateFormat('hh:mm:ss', now) + ', 产出值=' + praise.toFixed(4) + ', 总量=' + this.praiseOnlineOutput.toFixed(4))
        // cc.log('系数=' + this.praiseOutputFactor + ', 产量=' + factor + ', 时间差=' + time + 'ms')
        // cc.log('--------------------------------------------')
    }

    public update(dt: number) {
        if (this.calendarStartTime > 0 && !this.isSyncCalendarTime) {
            this.checkUpdateCalendar()
        }
        if (this.praiseOutputTime > 0) {
            this.praiseCheckInterval -= dt
            if (this.praiseCheckInterval <= 0 && !this.isSyncCheckPraiseOutput) {
                this.checkPraiseOutput()
            }
        }
    }
}