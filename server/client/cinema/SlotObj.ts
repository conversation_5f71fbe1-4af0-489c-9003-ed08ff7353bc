import ConditionObj from "../common/ConditionObj"
import { gameHelper } from "../GameHelper"
import ut from "../../common/util"
import FilmObj from "./FilmObj"

// 一个槽位
export default class SlotObj {

    public id: number = 0
    public filmId: number = 0 //放的影片

    public isUnlock: boolean = false //是否解锁 主要用来改变存储
    public unlock_cost: ConditionObj[] = null
    public unlock_limit: ConditionObj[] = null

    public film: FilmObj = null //影片信息

    public init(json: any) {
        this.id = json.id
        this.unlock_cost = json.unlock_cost ? gameHelper.stringToConditions(json.unlock_cost) : null
        this.unlock_limit = json.unlock_limit ? gameHelper.stringToConditions(json.unlock_limit) : null
        this.isUnlock = !this.unlock_cost && !this.unlock_limit
        return this
    }

    public fromDB(data: any) {
        this.unLock()
        this.id = data.id
        this.setFilm(data.filmId)
        return this
    }

    public toDB(init?: boolean) {
        return init ? { id: 'id', filmId: 'filmId', isUnlock: 'isUnlock' } : ut.setValue('id|filmId', this)
    }

    public get index() { return this.id - 1 }

    public reset() {
        this.setFilm(0)
    }

    // 解锁
    public unLock() {
        this.isUnlock = true
        this.unlock_cost = null
        this.unlock_limit = null
    }

    // 设置影片
    public setFilm(id: number) {
        this.filmId = id || 0
        this.film = this.filmId ? new FilmObj().init(this.filmId, 1) : null
    }
}