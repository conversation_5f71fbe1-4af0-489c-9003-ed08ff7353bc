import { ConditionType } from "../constant/Enums"
import ut from "../../common/util"
import ConditionObj from "../common/ConditionObj"
import { cconfig } from "./CConfig"
import { assetsMgr } from "../AssetsMgr"

// 签约信息
export default class SignedObj {

    public praise: number = 0 //当前的口碑
    private outputFilmOdds: number = 0 //当前产出影片的概率
    private costDiscountIndex: number = 0 //当前费用折扣下标
    private lastUpdateCostTime: number = 0 //上次刷新时间

    private cost: ConditionObj = null //当前签约费用

    private confDatas: { id: number, json: any, rewardWeights: number[], groups: string[][], sumRewardWeight: number }[] = [] //配置
    private sumWeight: number = 0 //总权重

    public init() {
        this.outputFilmOdds = cconfig.OUTPUT_FILM_ODDS
        this.setDiscount(0)
        this.praise = Math.floor(this.getDiscount() * 0.01 * cconfig.SIGNED_COST)
        return this.initJson()
    }

    public fromDB(data: any) {
        ut.setValue('praise|outputFilmOdds', data, this)
        this.costDiscountIndex = data.costDiscountIndex || 0
        this.lastUpdateCostTime = data.lastUpdateCostTime || 0
        this.setDiscount(this.costDiscountIndex)
        return this.initJson()
    }

    // 初始化费用
    private initJson() {
        this.sumWeight = 0
        this.confDatas = assetsMgr.getJson('signedTurntable').datas.map(m => {
            this.sumWeight += m.odds
            const rewardWeights = ut.stringToNumbers(m.reward_odds, ',')
            return { id: m.id, json: m, groups: m.items.split('|').filter(m => !!m.trim()).map(m => m.split(',')), rewardWeights: rewardWeights, sumRewardWeight: rewardWeights.reduce((val, cur) => val + cur, 0) }
        })
        return this
    }

    // 获取签约费用
    public getCost() { return this.cost }

    public setDiscount(index: number) {
        if (index < 0) {
            index = 0
        } else if (index >= cconfig.SIGNED_COST_DISCOUNT.length) {
            index = cconfig.SIGNED_COST_DISCOUNT.length - 1
        }
        this.costDiscountIndex = index
        const val = Math.floor(this.getDiscount() * 0.01 * cconfig.SIGNED_COST)
        this.cost = new ConditionObj().init(ConditionType.PRAISE, -1, val)
    }

    // 获取折扣
    public getDiscount() {
        return cconfig.SIGNED_COST_DISCOUNT[this.costDiscountIndex]
    }
}