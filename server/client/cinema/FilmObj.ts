import { FilmAttrJItem, FilmBaseJItem } from "../constant/DataType"
import { ConditionType } from "../constant/Enums"
import { gameHelper } from "../GameHelper"
import ConditionObj from "../common/ConditionObj"
import ut from "../../common/util"
import { assetsMgr } from "../AssetsMgr"

// 一个影片
export default class FilmObj {

    public id: number = 0 //我们用属性id来作为影片id
    public base: FilmBaseJItem = null
    public attr: FilmAttrJItem = null
    private _count: number = 0//数量

    public types: number[] = [] //所属的类型
    public upLimtCost: ConditionObj[] = []
    public upCost: ConditionObj[] = []
    public emojiWeight: number[] = [] //表情权重

    public init(id: number, count: number) {
        this.id = id
        this.count = count
        return this.initJson()
    }

    public fromDB(data: any) {
        return ut.setValue('id|count', data, this).initJson()
    }

    private initJson() {
        this.base = assetsMgr.getJsonData('filmBase', Math.floor(this.id / 1000))
        this.attr = assetsMgr.getJsonData('filmAttr', this.id)
        this.updateJsonInfo()
        return this
    }

    private updateJsonInfo() {
        this.types = ut.stringToNumbers(this.base.type, ',')
        this.upCost = gameHelper.stringToConditions(this.attr.up_cost)
        this.upLimtCost = gameHelper.stringToConditions(this.attr.up_limt_cost)
        this.emojiWeight = ut.stringToNumbers(this.base.emoji_weight, ',')
    }

    public get count() { return this._count }
    public set count(val: number) { this._count = val }
    public get baseId() { return this.base?.id || 0 }
    public get name() { return this.base?.name }
    public get desc() { return this.base?.desc }
    public get icon() { return this.base?.icon }
    public get light() { return this.base?.light }
    public get qua() { return this.base?.qua || 0 }
    public get sort() { return this.attr?.sort }
    public get time() { return this.attr?.time }
    public get up_limt_cost() { return this.attr?.up_limt_cost }
    public get up_cost() { return this.attr?.up_cost }
    public get income() { return this.attr?.income || 0 }
    public get heart() { return this.attr?.heart || 0 }

    public get lv() { return this.attr ? this.attr.id % 1000 : 0 }
    public isMaxlv() { return !this.up_cost }

    // 是否属于某个类型
    public hasType(type: number) {
        return this.types.has(type)
    }

    // 是否可以升级
    public isCanUp() {
        if (this.upLimtCost.length === 0) {
            return false
        }
        const cond = this.upLimtCost[0]
        return this.getUpNeedFilmCount(cond) >= cond.count
    }

    // 获取升级需要的数量
    public getUpNeedFilmCount(cond: ConditionObj) {
        if (!cond || cond.type !== ConditionType.FILM) {
            return 0
        } else if (cond.id === this.id) {
            return this.count - 1 //自己升自己 数量减1
        }
        return gameHelper.world.getCinema()?.getFilmCountById(cond.id)
    }

    // 升级
    public up() {
        const id = this.id + 1
        const attr = assetsMgr.getJsonData('filmAttr', id)
        if (attr) {
            this.id = id
            this.count = 1 //升级过后数量就肯定为1了
            this.attr = attr
            this.updateJsonInfo()
        }
    }
}