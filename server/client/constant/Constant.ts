/////////////// 所有常量（全大写单词间用下划线隔开）///////////////

// 点击间隔
const CLICK_SPACE = 14

// 一格的大小
const TILE_SIZE = 120
// 斜度
const SKEW_ANGLE = 30
// 斜着的外宽高

// 层级最大值
const MAX_ZINDEX = 10000

// 长按时间
const LONG_PRESS_TIME = 0.4

// 排队间距
const QUEUE_SPACE = 80

// 一个电梯最多坐多少个人
const ELEVATOR_ROLE_MAX_COUNT = 4
// 电梯门 关门开门的时间
const ELEVATOR_DOOR_ANIM_TIME = 0.5
// 坐一楼电梯需要消耗的时间
const ELEVATOR_DELAY_TIME = 0.7
// 在电梯里面的大小
const ELEVATOR_ROLE_SCALE = 0.7

// 宣传绘画画笔线条宽度
const DRAW_LINE_WIDTH = 20

// 铃铛播放时间
const BELL_PLAY_TIME = 0.3

// 客人气泡高度
const ROLE_BUBBLE_OFFSETY = 320

// 大通铺离开概率
const DORM_LEAVE_ODDS = 30
// 客房离开概率
const ROOM_LEAVE_ODDS = 5
// 餐厅离开概率
const DINING_LEAVE_ODDS = 30
// 电影院离开概率
const CINEMA_LEAVE_ODDS = 30

// 客人点自己喜欢菜的概率
const ORDER_LOVE_FOOD_ODDS = 5

// 闲话间隔时间
const CHITCHAT_INTERVAL_TIME = [10, 200]

// 客人心愿最高阶段
const ROLE_MAX_STAGE = 5

// 主楼属性配置字段
const MAIN_ATTR_FIELDS = {
    1: [{ icon: 'time', suffix: 's' }, { icon: 'income', suffix: '%' }, { icon: 'icon_kl', suffix: '%' }],
    2: [{ icon: 'time', suffix: 'h' }],
    3: [{ icon: 'income', suffix: '' }],
}

// 设施属性配置表字段
const BUILD_ATTR_FIELDS = ['income', 'use_income', 'output_online', 'output', 'fav', 'use_fav', 'price', 'durability', 'stock', 'heart', 'sleep_eff', 'cooking_eff', 'hotspring_eff', 'time']

// 乌冬配置表字段
const WUDONG_BASE_FIELDS = ['stamina_click', 'stamina_max', 'stamina_eff', 'heart']

// 大楼皮肤配置表字段
const MAIN_SKIN_FIELDS = ['heart']

// 科技属性配置表字段
const TECHNOLOGY_ATTR_FIELDS = [
    { key: 'income', prefix: '+', suffix: '' },
    { key: 'material', prefix: '', suffix: '' },
    { key: 'cut_off', prefix: '-', suffix: '%' },
    { key: 'retime_off', prefix: '-', suffix: 'm' },
    { key: 'mix_eff', prefix: '+', suffix: '%' },
    { key: 'hotspring_eff', prefix: '+', suffix: '%' },
    { key: 'sptime', prefix: '+', suffix: 's' },
]

// 温泉默认水池皮肤
const DEFAULT_WATER_SKIN = 'tangliao_shui'

// 温泉净化道具id
const HOTSPR_EXP_PROP_ID = 2001

// 合成加速时间 单位分
const COMP_SPEEDUP_TIME = 30

// 默认解锁客人ID
const UNLOCK_ROLE_IDS = [1]
// 星妹ID
const XINMEI_ROLE_ID = 35
// 呀呀ID
const YAYA_ROLE_ID = 14

let CHECK_CONST = {
    HOLIDAY_CRISTMAS: 100,
    // 多少蜡烛开启心愿任务
    WISH_TASK_HEART_NEED: 15,
    // 多少蜡烛开启每日任务
    EVERYDAY_TASK_HEART_NEED: 25,
    // 多少蜡烛开启每日成就
    ACHIEVE_TASK_HEART_NEED: 25,
    // 每日任务数量
    EVERYDAY_TASK_COUNT: 5,

    // 多少蜡烛开启好友排行
    FRIEND_RANK_HEART_NEED: 65,

    // 多少蜡烛开启世界排行
    WORLD_RANK_HEART_NEED: 200,

    // 咕咕鸡开启蜡烛数
    GALI_HEART_NEED: 80,

    // 双倍收入开启蜡烛数
    DOUBLE_INCOME_HEART_NEED: 10,

    // 多少蜡烛会出现哇塞
    WASHAI_HEART_NEED: 50,
}

// 条件名字
const CONDITION_NAME = {
    1: 'global.biscuits',
    2: 'global.candies',
    3: 'global.heart',
    101: 'global.gali_dh', //咖喱带回
    102: 'global.role_cs', //客人赠送
    105: 'ui.condition_105', //排行奖励
    106: 'ui.condition_106', //分享奖励
    201: 'global.tech', //技术
    202: 'global.romantic', //浪漫
    203: 'global.comprehend', //领悟
    204: 'global.creativity', //创意
    10000: 'ui.notopen', //暂未开放
}

// 条件说明
const CONDITION_DESC = {
    1: 'ui.condition_1',
    2: 'ui.condition_2',
    3: 'ui.condition_3',
    101: 'ui.condition_101', //咖喱带回
    102: 'ui.condition_102', //客人赠送
    105: 'ui.condition_105', //排行奖励
    201: 'ui.condition_201', //技术
    202: 'ui.condition_202', //浪漫
    203: 'ui.condition_203', //领悟
    204: 'ui.condition_204', //创意
    10000: 'ui.notopen', //暂未开放
}

// 条件图标
const CONDITION_ICON = {
    1: 'biscuits',
    2: 'candies',
    3: 'heart',
    201: 'tech',
}

const CONDITION_OTHER_ICON = {
    12: ['other', 'fav'], //好感
    202: ['mining', 'romantic'], //浪漫
    203: ['mining', 'comprehend'], //领悟
    204: ['mining', 'creativity'], //创意
    401: ['ad', 'wudong_stamina'], //乌冬体力
    402: ['ad', 'taitan_stamina'], //泰坦体力
    501: ['gali', 'ggj_qp_cs'], // 财神
    502: ['gali', 'ggj_qp_fs'], // 福神
    503: ['gali', 'ggj_qp_ss'], // 衰神
    504: ['gali', 'ggj_buff'], // 双倍卡
}

// 条件临时图标
const CONDITION_TEMP_ICON = {
    202: 'romantic', //浪漫
    203: 'comprehend', //领悟
    204: 'creativity', //创意
}

// 一样的图片但是名字不一样
const TEMP_ICON_CHANGE = {
    output_online: 'income',
    output: 'income',
    use_income: 'income',
    price: 'income',
    cut_off: 'income',
    retime_off: 'time',
    sptime: 'time',
    mix_eff: 'icon_xl',
    sleep_eff: 'icon_xl',
    cooking_eff: 'icon_xl',
    hotspring_eff: 'icon_xl',
    stamina_eff: 'icon_xl',
    stamina_max: 'icon_jl',
    stamina_click: 'icon_djcs',
}

// 一样的属性key但是名字不一样
const ATTR_KEY_CHANGE = {
    'ui.main_skin_heart': 'ui.attr_heart',
}

// 条件物品的图标路径
const CONDITION_ITEM_ICON_DIR = {
    4: { dir: 'prop', jsonName: 'itemBase' },
    5: { dir: 'furn', jsonName: 'roomFurnitureAttr' },
    9: { dir: 'food', jsonName: 'foodBase' },
    11: { dir: 'mainskin', jsonName: 'hotelSkinBase' },
    601: { dir: 'gali', jsonName: 'adventureCard' }
}

// 咖喱棋盘图标
const GALI_CHESS_ICON = {
    1: 'biscuits',
    2: 'candies',
    5: 'ggj_qp_wsjy',
    6: 'ggj_qp_fbfx_r|ggj_qp_fbfx_l', // 前进还是后退
    9: 'gali/GALI_TV',
    11: 'gali/GALI_EXPLORE',
    13: 'ggj_qp_yxg',
}

// 咖喱神id
const GALI_GOD_ID = {
    WEALTH: 1,
    MASCOT: 2,
    DESPERATE: 3
}

// 咖喱卡片id
const GALI_CARD_ID = {
    SPECIFY_POINT: 1001,
    RANDOM_CHESS: 1002,
    AVOID_PUNISH: 1003,
    DOUBLE_INCOME: 1004,
    PLEASE_GOD: 1005,
    SEND_GOD: 1006,
    WHEEL_SPEED: 1007,
}

// 咖喱地图路径
const GALI_MAP_DIR = {
    1: 'gali_map/grass/',
    2: 'gali_map/sand/',
    3: 'gali_map/snow/',
    4: 'gali_map/grass/', // 备用
}

// 菜谱挖掘icon名称
const FOOD_MINING_ICON = ['', 'gui_ys_lm', 'gui_ys_lwl', 'gui_ys_cy', 'gui_ys_js', 'gui_dj_sj', 'gui_dj_tl', 'gui_dj_cz', 'gui_dj_hz', 'gui_dj_sz', 'gui_dj_fdj', 'gui_dj_cxks']

// 菜谱挖掘说明icon名称
const MINING_HELPER_ICON = ['', 'sm_rr', 'sm_cc', 'sm_nn', 'sm_cy', 'sm_sj', 'sm_tl', 'sm_cz', 'sm_hslh', 'sm_hslh', 'sm_fdj', 'sm_cxks']

// 客人加速倍数
const ROLE_VELOCITY_MUL = 1.1

// 服务持续时间
const SERVE_DURATION_TIME = 45

// 服务列表
const ROLE_SERVES = {
    // 大厅主楼
    IN_HOTEL: [5001, 5012],
    // 休闲中心
    D_TEA: [5002], //茶歇区
    D_SIT: [5015], //沙发
    D_WASHING: [5010], //洗衣机
    D_SLEEP: [5004, 5006, 5008, 5014], //床
    // 浴池
    WASH: [5005], //冲洗
    BATH: [5011], //温泉池
    MAKEUP: [5009], //梳妆台
    // 餐厅
    ORDER: [5003, 5007], //点餐
    NA_DESSERT: [5013], //甜品区
    NA_TAKEOUT: [5013], //外卖区
    // 客房
    SLEEP: [5004, 5006, 5008, 5014], //床
}

// 哪些服务需要显示出来
const CHANGE_SERVE_NAME = {
    icon_xx: 'icon_xx',
    icon_zz: 'icon_zz',
    icon_yy: 'icon_yy',
}

const SERVE_ADD_LOVE_ODDS = 90 //服务后添加好感的几率

// 通知消息 持续时间 （单位秒）
const NONE_MESSAGE_DURATION = 20

// 设施耐久显示比例
const BUILD_DUR_SHOW_RATIO = 0.1
// 设施库存显示比例
const BUILD_STOCK_SHOW_RATIO = 0.9

// 主动提示闲话间隔
const TIP_CHITCHAT_INTERVAL = 120

// 盲盒皮肤
const BLIND_BOX_SKIN = ['yuanweibinggan', 'jiangtangbinggan', 'mochabinggan', 'qiaokelibinggan', 'caomeibinggan', 'niunaibinggan']


// 特殊属性图标
const ROLE_SPE_ICON = {
    WEALTHY: 'icon_jz', //1.金主：付费时会付更多的钱
    EAT_KING: 'icon_dww', //2.大胃王：有概率连续点菜
    SLEEP_HOBBY: 'icon_smahz', //3.睡觉爱好者：有概率睡很久，付更多的钱
    BATH_HOBBY: 'icon_qj', //4.温泉爱好者：有概率泡很久，付更多的钱
    DORM_HOBBY: 'icon_zfahz', //5.休息室爱好者：在休息室，会额外支付费用
    KEFANG_HOBBY: 'icon_zfahz', //6.房间爱好者：住客房，会额外支付费用
    SHOWER_HOBBY: 'icon_zfahz', //7.温泉爱好者：在温泉时会额外支付费用
    DINING_HOBBY: 'icon_zfahz', //8.餐厅爱好者：在餐厅时会额外支付费用
}

// 每日任务全部完成奖励配置
const EVERYDAY_TASK_FINISH_CONF = [
    { type: 2, count: 20 },
    { type: 3, count: 5 },
]

// pnl层级
const PNL_ZINDEX = {
    ToApp: 1000,
    Shoot: 270,
    Amplify: 260,
    GeneraReward: 250,
    GaLiExploreReward: 250,
    GaLiPhaseReward: 250,
    GiftBox: 242,
    RoleInfo: 241,
    NpcInfo: 241,
    Mmj: 240,
    UnlockRoleTip: 240,
    UnlockNpcTip: 240,
    Announce: 230,
    FeedBack: 230,
    Praise: 230,
    LoveDay: 220,
    Offline: 210,
    Cartoon: 203,
    Story: 202,
    Guide: 201,
    Top: 100,
    GaliShop: 14,
    GaliWheel: 13,
    GaliExplore: 12,
    GaliStage: 11,
    GaliChessMap: 10,
    Bottom: 0,
}

// 广告上报字段
const AD_REPORT_KEYS = {
    0: 'none',
    1: 'isReady',
    2: 'notReady'
}

// 任务条件消耗服务道具通用id
const SERVE_COND_ID = 10000
// 委托任务最多个数
const SERVE_TASK_MAX_COUNT = 5

// 招揽客人对应配置
const SOLICIT_ROLE_CONF = {
    NORMAL: 'role/BODY_SOLICIT_1', // 正常客人
    BLIND_BOX: 'role/BODY_SOLICIT_1', // 盲盒客人
    SAOBA_BOX: 'role/BODY_TRANSPORT_1', // 扫把 （带一个盲盒客人）
    TRANS_FEITAN: 'role/BODY_TRANSPORT_1', // 飞毯（带两个正常客人）
    TRANS_CAR: 'role/BODY_TRANSPORT_1', // 小汽车
    TRANS_BUS: 'role/BODY_TRANSPORT_1', // 大巴车
    JIAN_BAO: 'role/BODY_SOLICIT_2', // 煎包
    GE_CHANG: 'role/BODY_SOLICIT_3', // 歌唱
}

// 延迟关闭pnl时间
const DELAY_CLOSE_PNL_TIME = 0.4

// 新手教程特殊服务任务id
const GUIDE_SERVE_TASK_ID = 10000
// 新手教程预约任务id
const GUIDE_ORDER_TASK_ID = 10000

// 弱引导对应的节点名
const WEAK_GUIDE_NODE_NAME = {
    'dorm': 'guide_5', // 休息室门
    'dining': 'guide_3', // 餐厅门
    'shower': 'guide_2', // 浴室门
    'kefang201': 'guide_201', // 201门
    'kefang202': 'guide_202', // 202门
    'kefang301': 'guide_301', // 301门
    'kefang302': 'guide_302', // 302门
    'tip': 'guide_tip', // 小费罐
}

const MMJ_ACTIVITY_START_TIME = '05-09-00-00-00' // 麻麻节活动开始时间
const MMJ_ACTIVITY_END_TIME = '05-12-00-00-00' // 麻麻节活动结束时间

// 客人在场景的离开概率
const SCENE_LEAVE_ODDS = {
    3: 30, //餐厅离开概率
    4: 5, //客房离开概率
    5: 30, //大通铺离开概率
    6: 30, //电影院离开概率
}

const YOYO_PROP_ID = 9100

export {
    CHECK_CONST,
    CLICK_SPACE,
    TILE_SIZE,
    SKEW_ANGLE,
    LONG_PRESS_TIME,
    QUEUE_SPACE,
    ELEVATOR_ROLE_MAX_COUNT,
    ELEVATOR_DOOR_ANIM_TIME,
    ELEVATOR_DELAY_TIME,
    ELEVATOR_ROLE_SCALE,
    MAIN_ATTR_FIELDS,
    BUILD_ATTR_FIELDS,
    WUDONG_BASE_FIELDS,
    MAIN_SKIN_FIELDS,
    TECHNOLOGY_ATTR_FIELDS,
    DEFAULT_WATER_SKIN,
    HOTSPR_EXP_PROP_ID,
    COMP_SPEEDUP_TIME,
    UNLOCK_ROLE_IDS,
    XINMEI_ROLE_ID,
    YAYA_ROLE_ID,
    DRAW_LINE_WIDTH,
    CONDITION_NAME,
    CONDITION_DESC,
    CONDITION_ICON,
    CONDITION_OTHER_ICON,
    CONDITION_TEMP_ICON,
    TEMP_ICON_CHANGE,
    ATTR_KEY_CHANGE,
    CONDITION_ITEM_ICON_DIR,
    BELL_PLAY_TIME,
    ROLE_BUBBLE_OFFSETY,
    DORM_LEAVE_ODDS,
    ROOM_LEAVE_ODDS,
    DINING_LEAVE_ODDS,
    ORDER_LOVE_FOOD_ODDS,
    CHITCHAT_INTERVAL_TIME,
    ROLE_MAX_STAGE,
    MAX_ZINDEX,
    FOOD_MINING_ICON,
    MINING_HELPER_ICON,
    ROLE_VELOCITY_MUL,
    SERVE_DURATION_TIME,
    ROLE_SERVES,
    CHANGE_SERVE_NAME,
    SERVE_ADD_LOVE_ODDS,
    NONE_MESSAGE_DURATION,
    BUILD_DUR_SHOW_RATIO,
    BUILD_STOCK_SHOW_RATIO,
    TIP_CHITCHAT_INTERVAL,
    BLIND_BOX_SKIN,
    ROLE_SPE_ICON,
    EVERYDAY_TASK_FINISH_CONF,
    PNL_ZINDEX,
    GALI_CHESS_ICON,
    GALI_CARD_ID,
    GALI_GOD_ID,
    AD_REPORT_KEYS,
    SERVE_COND_ID,
    SERVE_TASK_MAX_COUNT,
    SOLICIT_ROLE_CONF,
    DELAY_CLOSE_PNL_TIME,
    GALI_MAP_DIR,
    GUIDE_SERVE_TASK_ID,
    GUIDE_ORDER_TASK_ID,
    WEAK_GUIDE_NODE_NAME,
    MMJ_ACTIVITY_START_TIME,
    MMJ_ACTIVITY_END_TIME,
    CINEMA_LEAVE_ODDS,
    SCENE_LEAVE_ODDS,
    YOYO_PROP_ID,
}
