import ConditionObj from "../common/ConditionObj";

import {
    MapType,
    FurnitureType,
    PropType,
    GuideStepType,
    StoryType,
    FoodMiningType,
    FoodMiningStatus,
    RoleSpeType,
    ShareUseType,
    ExcTaskType, WishType,CurrencyAction
} from "./Enums";

type AdFeedbackGiftInfo = {
    id: number,
    targetCount: number,
    rewards: ConditionObj[],
    isFinish: boolean,
}

type PurchasedShopItemInfo = {
    action: CurrencyAction, //商品唯一标识
    count: number, //购买次数
    lastPurchaseTime: number, //上次购买时间
}
// 地图配置
type MapJsonItem = {
    id: number;
    type: MapType;
    origin: Point;
    w: number;
    h: number;
    walls: Point[];
    walls2: { point: string, size: string }[];
    positions?: Point[];
    spawn?: Point[];
    door?: { id: string, point: string };
    outgr?: { point: string, size: string };
}

// 地图上面掉落区域
type MoneyAreaInfo = {
    id: number;
    points: cc.Vec2[];
    count: number; //个数限制
}

// 主楼属性配置
type HostelAttrJItem = {
    id: number;
    type: number;
    cost: string; //升级消耗
    value: any; //目标值
}

// 主楼维修配置
type HostelChangeJItem = {
    id: number;
    desc: string;
    icon: string;
    change: string; //修缮
    unlock_map: number; //解锁的场景
    cost: string;
    limt: string;
    pre_limt: number; //前置条件
    reward: string;
    sort: number;
}

// 客房配置
type KefangJsonItem = {
    id: number;
    name: string;
    maps: string;
}

// 角色配置
type RoleBaseJItem = {
    id: number;
    body: number;
    skin: string;
    qua: number; //品质
    anim_name: string; //动画替换名字
    visit_weight: number; //来店权重
    speed: number; //移动速度
    sleep_time: number; //睡觉速度
    hotspring_time: number; //泡澡时间
    favorite_food: string; //菜品喜好
    favorite_drink: string; //饮品喜好
    favorite_furniture: string; //家具喜好
    unlock_limt: string; //解锁条件
    unlock_none: string; //解锁问号
    unlock_desc: string; //解锁说明
    unlock_story: string; //解锁剧情
    inhotel_story: string; //第一次入店剧情
    share_award: string; //分享奖励
    share_img_url: string; //分享图片
}

type RoleBaseInfo = {
    id: number;
    conds: ConditionObj[];
    json: RoleBaseJItem;
    blindBoxWeightArr: number[]; // 盲盒权重数组
    blindBoxWeightSum: number; // 盲盒总权重
}

// 客人阶段
type RoleStageJItem = {
    id: number;
    lv_limt: number;
    wish_id: number;
    story: string; //剧情id
    spe_id: string; //特殊效果
}

// 角色属性
type RoleAttrJItem = {
    id: number;
    tip: number; //小费
    lv_love: number; //升级所需好感度
    reward: string; //升级奖励
}

// 道具基础表
type PropBaseJitem = {
    id: number;
    type: PropType;
    desc: string;
    icon: string;
    bag_type: number;
    bag_s_type: number;
    bag_sort: number;
}

// 家具类型配置
type FurnTypeConfigJItem = {
    id: number;
    room_num: number;
    name: string;
    placemen: number; //摆放方式 0.不可重复摆 1.可重复摆 2.直接替换不可操作
    pos: string; //默认摆放位置
    area: number; //所在区域
    storage: number; //收取方式 0.不可收取 1.可收取
}

// 家具属性配置
type FurnitureBaseJItem = {
    id: string;
    type: FurnitureType;
    utype: number; // UI类型
    stype: number; // 大小类型
    icon: string;
    size: string;
    collision: number; //是否参与碰撞
    triggers: string; //触发位置
    trigger_ends: string; //触发结束位置
    use_count: number; //使用人数量
    place: number; //摆放位置 0.任意 1.室内 2.室外
    stand_anim: string; //待机动画
    click_anim: string; //点击动画
    inte_anim: string; //交互动画
    place_anim: string; //摆放动画
    click_sound: string; //点击音效
    place_sound: string; //摆放音效
    swih: number;
    can_clone: number; //是否可以复制
    /*********植物新增一些属性，植物是一种特殊家具，直接继承于家具**********/
    exp: number;// 成长经验 n>n+1
    leaf_output: number;//树叶分钟产量
    heart: number;// 人气
    appear_count: number;//
}

// 家具解锁配置
type FurnitureUnlockJItem = {
    id: string;
    type: number;
    unlock_cost: string; //解锁消耗
    unlock_limit: string; //解锁条件
    fav: number; //离开房间时增加的好感度
    use_fav: number; //使用设施时增加好感度
    income: number;
    use_income: number; //使用设施时获得的收入
    heart: number; //蜡烛
    output_online: string; //在线时，每隔xx秒获得收入(时间（s），收入)
    output: number; //每xx分钟获得收入(时间（min），收入)
    sleep_eff: number; //睡眠效率提升（%）
    sort: number;
    select: string; //规格选择
    limit_day: number; //限时购买
    is_place: number; //是否直接摆放
    home_output: number; // 小家专属每xx分钟获得收入(时间（min），收入)
    home_output_online: string; // 小家专属在线时，每隔xx秒获得收入(时间（s），收入)
    space: number;// 小家空间
    series: number; // 家具所属套系
    //花园相关的
    leaf_output: number;
}

// 房间属性配置
type RoomAttrJItem = {
    id: number;
    cap_lv: number;
    fav: number; //离开房间增加的好感度
    income: number;
}

// 菜品配置
type FoodJItem = {
    id: number;
    icon: string;
    unlock_cost: string; //解锁
    unlock_limt: string; //解锁
}

// 菜品属性配置
type FoodAttrJItem = {
    id: number;
    price: number;
    fav: number;
    heart: number;
    time: number; //制作时间
    lv_cost: string;
    lv_limt: string;
}

// 饮品配置
type DrinkJItem = {
    id: number;
    icon: string;
    time: number;
    unlock: string; //解锁
}

// 设施类型配置
type BuildTypeConfigJItem = {
    id: number;
    name: string; //名字key
}

// 设施解锁配置
type BuildUnlockJItem = {
    id: number;
    type: number;
    icon: string; //图标
    fav: number; //整体增加的好感度
    use_fav: number; //使用设施后增加的好感度
    income: number; //整体收入
    use_income: number; //使用设施后的直接收入
    heart: number; //蜡烛
    output_online: string; //在线时，每隔xx秒获得收入(时间（s），收入)
    output: number; //每xx分钟获得收入(时间（min），收入)
    durability: number; //耐久
    stock: number; //库存
    sleep_eff: number; //睡眠效率提升（%）
    cooking_eff: number; //做菜效率提升（%）
    hotspring_eff: number; //泡温泉效率提升（%）
    ready_eff: number; //播放电影前的准备效率（%）
    praise_limit: number; //口碑上限
    unlock_cost: string; //解锁消耗
    unlock_limit: string; //解锁条件
    idle_anim: string; //闲置动画
    use_anim: string; //交互动画
}

// 汤料效果配置
type SoupbaseItemJTtem = {
    id: number;
    type: string;
    value: string;
    time: number;
    image: string; //客人形象
    skin: string; //池水样式
    eff_skin: string; //特效样式
    name: string;
    desc1: string; //描述
    desc: string; //效果描述
}

// 烧锅道具合成配置
type MixBaseJItem = {
    id: string;
    result: string;
    time: number;
}

// 影片base
type FilmBaseJItem = {
    id: number;
    name: string;
    desc: string;
    icon: string;
    type: string;
    qua: number;
    weight: number; //获取权重
    emoji_weight: string; //表情权重
}

// 影片属性
type FilmAttrJItem = {
    id: number;
    income: number;
    heart: number;
    add_praise_factor: number; //添加口碑系数
    up_limt_cost: string; //升到下一级的条件
    up_cost: string; //升级消耗
    sort: number;
    time: number; //播放时间
}


type UnlockTaskJItem = {
    id: number;
    desc: string;
    weight_1: number;
    weight_2: number;
    weight_3: number;
    weight_4: number;
    task_item_count: number;
    task_item_num: number;
    min_num: number; //最小个数
    biscuit: number;
    offline_add_factor: number;
    heart_add_factor: number;
    ex_pr: number;
    ex_reward: string;
    end_talk: string;
}

// 新手任务
type GuideTaskBaseJItem = {
    id: number;
    name: string;
    desc: string;
    reward: number;
    condition: number; //任务条件ID
    next_id: number; //下一个任务ID
    goto: string; //跳转
}

// 心愿
type WishBaseJItem = {
    id: number;
    desc: string;
    reward: number;
    condition: string; //任务条件ID
}

// 委托
type ServeTaskJItem = {
    id: number;
    desc: string;
    weight: number;
    task_item_count: number;
    task_item_num: number;
    min_num: number; //最小个数
    fav: string;
    biscuit: number;
    offline_add_factor: number;
    heart_add_factor: number;
    ex_pr: number;
    ex_reward: string;
}

// 预约
type OrderTaskJItem = {
    id: number;
    desc: string;
    content: string;
    time: number; //预约时间
    complete_talk: string; //对话
    fav: string;
    biscuit: number;
    offline_add_factor: number;
    heart_add_factor: number;
    ex_pr: number;
    ex_reward: string;
}

// 剧情配置
type StoryBaseJItem = {
    id: number;
    roles: string; //出场人物
    speaker: number; //当前说话的人
    content: string;
    next_id: number; //下一句
}

// 属性
type RoleAttr = {
    mood: number; //心情值
    satiety: number; //饱腹值
    energy: number; //精力值
    clean: number; //清洁度
}

// npc排行晋升(店长任务)
type NpcUpTaskBaseJItem = {
    id: number;
    name: string;
    desc: string;
    task_reward: number;
    condition: number; // 任务条件ID
    next_id: number; // 下一个任务ID
    goto: string; // 跳转
    first_reward: number; // 第一名奖励
    time: number; // 时限
}

type GuideBaseStep = {
    type: GuideStepType;
    delay?: number; //步骤的执行时间，到点后会自动进行下一步
    isWait?: boolean; // 分两种情况（1:点击对话后是否等待，2:聚焦该节点是否需要等待（防widget））
    waitEvent?: string | number; //等待接收某个事件，接收到后进行下一步
    restartPoint?: number; //如果重启游戏，重新进入教程，本身是存储点，数值为跳到的指定步骤
    reportEvent?: string; // 数据上报
    // restartPoint?: number; //如果重启游戏，重新进入教程，需要跳过一些无关的步骤，所以要跳到重启点开始
    // savePoint?: boolean; //存档点，标明步骤为关键步骤（如修改了数据的步骤），会配合重启点跳过一些无关步骤
}

type GuideCallFunc = GuideBaseStep & {
    mod: any,
    func: string,
    args?: any[], //会以...args的方式调用
}

type GuideSendEvent = GuideBaseStep & {
    eventType: string | number,
    args?: any,
}

type GuideSession = GuideBaseStep & {
    nodeName: string; //要点的节点名字，推荐以 guide_ 开头
    nodeDesc: string; // 节点的功能描述key
    focus?: boolean; //缺省默认为true
    eventType?: string; // 是否发送事件
    args?: any; // 事件参数
    moveCamera: boolean; // 是否移动相机
    isAuido: boolean; // 是否播放音效
    checkDataSave: any; // 检测异常数据
}

type GuideStory = GuideSession & {
    storyIdx: number; // 对话下标
    proceed: boolean; // 衔接下一段对话不中断
    //默认的waitEvent为EventType.END_STORY
    //默认nodeName为StoryPnl
}

type GuideWaitNextStep = GuideBaseStep & {
    time: number; // 等待多久进行下一步
}

type GuideMoveCamera = GuideBaseStep & {
    target: string; // 目标节点
    speed: number; // 移动速度
    offsetPos: string; // 目标偏移值（x,y)
}

type GuideStep = GuideCallFunc | GuideSendEvent | GuideSession | GuideStory | GuideWaitNextStep | GuideBaseStep;

type Guide = {
    id: number;  // 教程模块id
    force: boolean; //是否强制教程
    steps: GuideStep[];
}

type Story = {
    text?: string; //文案的key，
    speaker?: string; // 说话人的key
    type?: StoryType; //缺省默认为TEXT
    // startMsg?: string; //故事开始时的消息
    // endMsg?: string; //故事结束时的消息
    // confirmMsg?: string; //点击确认发送的消息
    // cancelmMsg?: string; //点击取消发送的消息
}

type Server = {
    url: string;
}

// 科技属性
type TechnologyConfJItem = {
    id: number;
    page: number;
    next: string;
    pre: string;
    cost: string;
    limt: string;
    pos: number;
    style: number;
    name: string;
    desc: string;
    icon: string;
    income: number; //温泉收入
    material: number; //解锁材料id
    cut_off: number; //降低材料价格 (%)
    retime_off: number; //缩短材料刷新时间 (分钟)
    mix_eff: number; //提升合成时间 (%)
    hotspring_eff: number; //提升泡温泉的时间 (%)
    sptime: number; //延长汤料时间 (秒)
    comp_prop: number; //合成道具
    comp_id: string; //合成id
}

// 科技配置信息
type TechnologyConfInfo = {
    id: number;
    next: number[];
    pre: number[];
    cost: ConditionObj;
    limt: ConditionObj;
    json: TechnologyConfJItem;
    unlock: boolean;
}

// 乌冬基础配置信息
type WuDongBaseJItem = {
    id: number;
    spine_file: string;
    skin: string;
    anim_change: string;
}

// 服务道具信息
type ServeItemInfo = {
    id: number;
    json: any;
    unlock_map: number[];
}

// 分享使用信息
type ShareUseInfo = {
    uid: string;
    type: ShareUseType;
}

// 客人升级奖励信息
type RoleUpRewardInfo = {
    lv: number;
    minLv: number;
    stage: number;
    storyId?: string; //剧情id
    rewards?: ConditionObj[];
}

// 角色特性
type RoleSpAttrJItem = {
    id: number;
    type: RoleSpeType;
    icon: string;
    odds: number;
    mul: number;
    value: number;
    vfx: any;
    desc: string;
}

// 专属任务
type ExclusiveBaseJItem = {
    id: number;
    type: ExcTaskType;
    trigger_type: WishType;
    trigger_cond: any;
    role: number;
    pre_id: number;
    reward: string;
    fav: number;
}

export {
    MapJsonItem,
    MoneyAreaInfo,
    HostelAttrJItem,
    HostelChangeJItem,
    KefangJsonItem,
    RoleBaseJItem,
    RoleBaseInfo,
    RoleStageJItem,
    RoleAttrJItem,
    PropBaseJitem,
    FurnTypeConfigJItem,
    FurnitureBaseJItem,
    FurnitureUnlockJItem,
    RoomAttrJItem,
    FoodJItem,
    FoodAttrJItem,
    DrinkJItem,
    BuildTypeConfigJItem,
    BuildUnlockJItem,
    SoupbaseItemJTtem,
    MixBaseJItem,
    GuideTaskBaseJItem,
    WishBaseJItem,
    ServeTaskJItem,
    OrderTaskJItem,
    StoryBaseJItem,
    RoleAttr,
    GuideStep,
    GuideCallFunc,
    GuideSendEvent,
    GuideStory,
    GuideSession,
    GuideWaitNextStep,
    GuideMoveCamera,
    Guide,
    Story,
    Server,
    TechnologyConfJItem,
    TechnologyConfInfo,
    NpcUpTaskBaseJItem,
    WuDongBaseJItem,
    ServeItemInfo,
    ShareUseInfo,
    RoleUpRewardInfo,
    RoleSpAttrJItem,
    UnlockTaskJItem,
    ExclusiveBaseJItem,
    FilmBaseJItem,
    FilmAttrJItem,
    PurchasedShopItemInfo,
    AdFeedbackGiftInfo
}
