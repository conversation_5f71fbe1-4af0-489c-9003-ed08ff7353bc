/////////////// 所有枚举（全大写单词间用下划线隔开）///////////////

enum MapType {
    RECT, //正常
    SKEW, //斜
    SKEW_L, //斜
    SKEW_R, //斜
}
enum CurrencyAction {
    RECEIVE_DAILY_WINDMILL= 'receive_daily_windmill',// 领取每日风车
    RECEIVE_DAILY_THLB_GIFT= 'receive_daily_thlb_gift',//限时优惠

    EXCHANGE_SCISSOR= 'exchange_scissor',// 兑换剪刀
    EXCHANGE_BISCUITS_1= 'exchange_biscuits_1',// 兑换饼干礼包1
    EXCHANGE_BISCUITS_2= 'exchange_biscuits_2',// 兑换饼干礼包2
    EXCHANGE_BISCUITS_3= 'exchange_biscuits_3',// 兑换饼干礼包3
    EXCHANGE_CANDIES_1= 'exchange_candies_1',// 兑换糖果礼包1
    EXCHANGE_CANDIES_2= 'exchange_candies_2',// 兑换糖果礼包2
    EXCHANGE_CANDIES_3= 'exchange_candies_3',// 兑换糖果礼包3
    EXCHANGE_THLB_4= 'exchange_thlb_4', // 限时特惠风车礼包
    EXCHANGE_THLB_2001= 'exchange_thlb_2001', // 限时特惠糖果礼包
    EXCHANGE_THLB_211 = 'exchange_thlb_211', // 限时特惠培训卷礼包
    WUDONG_AD= 'wudong_ad',// 乌冬广告
    TAITAN_AD= 'taitan_ad',// 泰坦广告
    PAOPAO_AD= 'paopao_ad',// 泡泡广告
    DOUBLE_AD= 'double_ad',// 双倍广告
    FUGUI_AD= 'fugui_ad',// 富贵广告
    YOYO_AD= 'yoyo_ad',// 摇摇乐广告
    GRANDMA_AD= 'grandma_ad',// 老奶奶广告
    OFFLINE_AD= 'offline_ad',// 离线收益
    TIP_AD= 'tip_ad',// 小费罐
    FRIST_AD= 'frist_ad',// 首看豪礼
    PRAISE_AD= 'praise_ad',// 口碑
    DOUBLE_CLAIM_AD= 'double_claim_ad',// 双倍领取奖励
    BISCUITS_AD= 'biscuits_ad',// 饼干不足奖励
    CANDIES_AD= 'candies_ad',// 糖果不足奖励
    HOLIDAY_SIGN_AD= 'holiday_sign_ad', // 节日活动补签奖励
    ELF_EGG_INCUBATE_AD= 'elf_egg_incubate_ad', // 快速孵化精灵蛋广告

    //领取月卡每日奖励，不在配置表中
    RECEIVE_DAILY_MC= "RECEIVE_DAILY_MC",

    //----------付费

    //礼包
    IAP_XSZL= 'iap_xszl', //新手助力
    IAP_CXYX= 'iap_cxyx', //畅享游戏
    IAP_JXTH= 'iap_jxth', //精选特惠
    IAP_GASZ= 'iap_gasz', //关爱手指
    IAP_LRTZ= "iap_lrtz", //懒人贴纸
    IAP_BDJJ= "iap_bdjj", //霸道讲价
    IAP_SMXY= "iap_smxy", //时髦新衣
    IAP_CJLB= "iap_cjlb", //春节礼包

    IAP_XSZL_V2= 'iap_xszl_v2', //新手助力
    IAP_CXYX_V2= 'iap_cxyx_v2', //畅享游戏
    IAP_JXTH_V2= 'iap_jxth_v2', //精选特惠

    //月卡
    IAP_THYK= "IAP_THYK", //小月卡
    IAP_CZYK= "IAP_CZYK", //大月卡

    // 限时特惠（付费）
    IAP_THLB_1= 'iap_thlb_1',
    IAP_THLB_2= 'iap_thlb_2',
    IAP_THLB_3= 'iap_thlb_3',
    IAP_THLB_1005= 'iap_thlb_1005',
    IAP_THLB_1006= 'iap_thlb_1006',
    IAP_THLB_201= 'iap_thlb_201',
    // 限时特惠（风车兑换道具）
}

// 地图场景类型
enum MapSceneType {
    MAIN = 1,// 主楼
    SHOWER, // 浴室
    DINING, // 餐厅
    KEFANG, // 客房 暂时启用 但是不要删除
    DORM, //休闲中心
    CINEMA, //电影院
    GARDEN=11,
    ELEVATOR2 = 102, // 电梯 2楼
    ELEVATOR3, // 电梯 3楼
    ELEVATOR4, // 电梯 4楼
    MAX = 200, //这个以上的都是房号了
    KEFANG201, //201
    KEFANG202, //202
    KEFANG301, //301
    KEFANG302, //302
}

// 来客类型
enum ComeRoleType {
    NONE, //自然来客
    SOLICIT, //招揽来客
    GRANDMA, //老奶奶招来
    GALI, //咖喱带来
}

// 道具类型
enum PropType {
    STEWPOT_COMP = 1, //汤料合成材料
    HOTSPR_EXP, //汤料升级材料
    SOUPBASE, //汤料特殊材料
    SOUPBASE_UP_STAGE, //汤料升阶材料
    SERVE, //服务道具
    EXPLORE_A, //探险道具 a类
    EXPLORE_B, //探险道具 b、c类
    FURNITURE, //特殊家具
    SUMMON_PROP//召唤道具
}


// 通用条件类型
enum ConditionType {
    NONE,
    BISCUITS, //饼干 1
    CANDIES, //糖果 2
    HEART, //心心 3
    PROP, //道具 4
    FURNITURE, //家具 5
    // MAIN_LV, //主楼等级 6
    ROLE_UP_STAGE = 7, //客人升阶 7
    UNLOCK_ROLE, //客人解锁 8
    FOOD, //菜品 9
    WUDONG_SKIN, //乌冬皮肤 10
    MAIN_SKIN, //大楼皮肤 11
    ROLE_FAV, //客人好感 12
    PRAISE, //口碑 13
    FILM, //影片 14
    GALI_TAKE = 101, //咖喱带回 101
    ROLE_GIVE, //客人赠送 102
    BUILD_LV, //设施等级 103
    WISH_ID, //奖励一个愿望 104
    RANK_AWARD, //排行奖励 105
    SHARE_AWARD, //分享奖励 106
    TASK_AWARD, //任务奖励 107
    TECH = 201, //技术 201
    ROMANTIC, //浪漫 202
    COMPREHEND, //领悟 203
    CREATIVITY, //创意 204
    DORM_BUILD = 301, //大通铺设施 301
    DINING_BUILD, //餐厅设施 302
    SHOWER_BUILD, //浴室设施 303
    CINEMA_BUILD, //影院设施 304
    SOLICIT_STAMINA = 401, //揽客体力 401
    TAITAN_STAMINA, //泰坦体力 402
    GALI_GOD_WEALTH = 501, // 财神
    GALI_GOD_MASCOT, // 福神
    GALI_GOD_DESPERATE, // 衰神
    GALI_BUFF_DOUBLE, // 咖喱双倍buff
    GALI_PROP_CARD = 601, // 咖喱道具卡
    NOT_OPEN = 10000, //暂未开放 10000
    MAX,
}

// 任务条件类型
enum TaskConditionType {
    BISCUITS = 1, //赠送饼干
    TOTAL_HEART, //累计蜡烛 2
    UNLOCK_FURN, //解锁家具 3
    UNLOCK_FOOD, //4.解锁菜品
    UNLOCK_DORM_BUILD, //5.解锁大通铺设施
    UNLOCK_SHOWER_BUILD, //6.解锁温泉设施
    UNLOCK_DINING_BUILD, //7.解锁餐厅设施
    READ_LETTER, //8.阅读
    TOTAL_BISCUITS, //累计获取小饼干 9
    SOLICIT_ROLE_NUM, //招揽客人数量 10
    UNLOCK_KEFANG_FURN, //解锁指定客房的家具 11
    UNLOCK_ROLE, //解锁指定客人 12
    CHANGE_MAIN, //解锁/修复大楼 13
    COMP_FINISH_COUNT, //合成完成次数 14
    GET_BISCUITS, //获取小饼干 15
    FOOD_MINING_COUNT, //厨艺点挖掘次数 16
    GET_CANDIES, //获取糖果 17
    USE_SOUPBASE_COUNT, //使用特殊汤料次数 18
    GET_FOODMINING_COUNT, //获取厨艺挖掘指定属性多少点 19
    UNLOCK_BUILD_COUNT, //累计解锁的所有家具数量 20
    TOTAL_PLAY_AD_COUNT, //累计看广告次数 21
    EXPEND_PROP, //消耗道具 22
    PLAY_AD_COUNT, //看广告次数 23
    TOTAL_CANDIES, //累计获得xx糖果 24
    SOLICIT_RAND_ROLE_COUNT, //招揽随机客人次数 25
    DINING_REPLENISH_COUNT, //餐厅补货次数 26
    SHOWER_REPAIR_COUNT, //温泉修理次数 27
    HOTSPR_TECHNOLOGY_UNLOCK, //温泉科技树学习xxx 28
    TOTAL_EXPEL_FUGUI, //累计赶走偷饼干的富国xxx 29
    SHARE_GAME, //分享游戏 30
    TASK_SHARE_GAME, //任务分享游戏 31
    GET_PROP, //获取道具 32
    SOLICIT_TRANSPORT_COUNT, //招揽指定交通工具 33
    GET_DROP_BISCUIT_COUNT, //拾取小饼干次数 34
    GET_TIP_COUNT, //获取小费罐次数 35
    OPEN_DOUBLE_EARN_COUNT, //开启广告宣传次数 36
    BUY_COMP_PROP_COUNT, //购买合成道具次数 37
    GALI_OUTING_COUNT, //咖喱出游次数 38
    ENTRUST_FINISH_COUNT, //委托完成次数 39
    SERVE_FINISH_COUNT, //服务完成次数 40
    TOTAL_SOLICIT_TRANSPORT_COUNT, //累计招揽指定交通工具 41
    TOTAL_INVITE_GRANDMA_COUNT, //累计邀请老奶奶次数 42
    UNLOCK_FOOD_COUNT, //解锁菜品数量 43
    LV_FOOD_COUNT, //拥有x级菜品数量 44
    TOTAL_USE_SOUPBASE_COUNT, //累计使用汤料次数 45
    TOTAL_GALI_OUTING_COUNT, //咖喱累计出游多少次 46
    TOTAL_SHOWER_REPAIR_COUNT, //累计修理温泉设施次数 47
    TOTAL_DINING_REPLENISH_COUNT, //累计餐厅补货次数 48
    TOTAL_SEEOFF_MAMMON_COUNT, //累计欢送财神次数 49
    TOTAL_GET_ELFIN_COUNT, //累计和饼干小精灵玩耍次数 50
    TOTAL_SOLICIT_GECHANG_COUNT, //累计招揽歌唱精灵次数 51
    TOTAL_GET_JIANBAO_STAMINA_COUNT, //累计获得煎包多少体力 52
    TOTAL_BUY_WASAI_PROP_COUNT, //累计从哇塞那购买道具次数 53
    TOTAL_SOLICIT_ROLE_NUM, //累计招揽客人 54
    TOTAL_COMP_FINISH_COUNT, //累计合成完成次数 55
    TOTAL_FOOD_MINING_COUNT, //累厨艺点挖掘次数 56
    CONCERN, //关注 57
    UNLOCK_CINEMA_BUILD=73,
    UP_FOOD_N=105,
    UNLOCK_SOUPBASE=76,
    UNLOCK_FILM=98
}

// 每日任务类型
enum EverydayTaskType {
    CONCERN, //关注次数 0
    SHARE_REWARD, //分享 1
    SOLICIT_RAND_ROLE_COUNT, //招揽随机客人次数 2
    COMP_FINISH_COUNT, //合成完成次数 3
    SHOWER_REPAIR_COUNT, //温泉修理次数 4
    FOOD_MINING_COUNT, //厨艺点挖掘次数 5
    DINING_REPLENISH_COUNT, //餐厅补货次数 6
    SOLICIT_ROLE_NUM, //招揽客人数量 7
    PLAY_AD_COUNT, //看广告次数 8
    GET_BISCUITS, //获取xxx小饼干 9
    GET_DROP_BISCUIT_COUNT, //拾取小饼干次数 10
    GET_TIP_COUNT, //获取小费罐次数 11
    OPEN_DOUBLE_EARN_COUNT, //开启广告宣传次数 12
    BUY_COMP_PROP_COUNT, //购买合成道具次数 13
    GALI_OUTING_COUNT, //咖喱出游次数 14
    ENTRUST_FINISH_COUNT, //委托完成次数 15
    SERVE_FINISH_COUNT, //服务完成次数 16
}
enum PlantStatus {
    NONE = 0,
    WATERING = 1,// 浇水
    FERTILIZE,// 施肥
    DEWORMING,// 除虫
    SING,// 唱歌
    FRUIT,// 结果
    UPDATE = 6,// 可升级
}
enum PlantStage {
    SEED = 0,//萌芽期
    INFANCY, // 幼苗
    GROWTHPERIOD,//成长期
    MATURITY//  成熟期
}
// 餐厅设施类型
enum DiningBuildType {
    HEIBAN, //黑板 0
    WALL, //墙 1 x
    WAYFLOOR, //入口地板 2 x
    DINFLOOR, //餐厅地板 3 x
    KITFLOOR, //厨房地板 4 x
    TABLE1, //餐桌1 5
    TABLE2, //餐桌2 6
    TABLE3, //餐桌3 7
    COUNTER1, //吧台餐桌1 8 x
    COUNTER2, //吧台餐桌2 9 x
    DESSERT1, //甜品区1 10
    DESSERT2, //甜品区2 11
    TAKEOUT, //外带区 12 x
    KITCHEN, //厨房 13
    TABLE4, //餐桌4 14
    TABLE5, //餐桌5 15
    TABLE6, //餐桌6 16
    MAX,
}

// 浴室设施类型
enum ShowerBuildType {
    STEWPOT, //烧锅 0
    INWALL, //室内墙 1 x
    OUTWALL, //室外墙 2 x
    WAYFLOOR, //入口地板 3 x
    INFLOOR, //室内地板 4 x
    OUTFLOOR, //室外地板 5 x
    INDOORSPA, //室内温泉 6 x
    OUTDOORMAXSPA, //室外大温泉 7 x
    OUTDOORMINSPA, //室外小温泉 8
    STANDBATH, //站浴 9
    SITBATH, //坐浴 10
    TOWEL, //毛巾柜 11
    MAKEUP, //梳妆台 12
    RESTAREA, //休息区 13
    AFFOREST, //绿化 14
    MAX,
}

// 大通铺设施类型
enum DormBuildType {
    RESTFLOOR = 1, //休息区地板 1  x
    RECRFLOOR, //娱乐区地板 2  x
    TASTFLOOR, //品茶区地板 3  x
    WALL, //墙 4  x
    CASHIER, //收银台 5
    ENTERCABINET, //入户柜子 6
    BUNK1, //一号床铺 7
    BUNK2, //二号床铺 8
    BUNK3, //三号床铺 9
    BUNK4, //四号床铺 10
    VENDING, //自动贩卖机 11
    SOFA, //沙发 12
    ARMCHAIR, //按摩椅 13
    RECREATION, //娱乐区 14
    WASHER, //洗衣机 15
    TEATASTING, //品茶区 16
}

// 电影院设施类型
enum CinemaBuildType {
    FIXED, //固定设施 0 围栏
    WALL, //墙 1
    PRAISEBOX, //口碑箱 2
    PROJECTOR, //影音设备 3
    SEAT1, //1排坐席 4
    SEAT2, //2排坐席 5
    SEAT3, //3排坐席 6
    SEAT4, //4排坐席 7
    WAITSEAT, //候影区座椅 8
    WAITSNACK, //候影区小吃柜 9
    SELLARK, //周边售卖柜 10
    AFFOREST, //室内绿植 11
    VIEWFLOOR, //观影区地板 12
    WAITFLOOR, //候影区地板 13
    WAYFLOOR, //入口地板 (依赖=影院排片栏) 14
}

// buff类型
enum BuffType {
    SPEED_R = 1, // 1.影响客人移动速度(百分比）
    TIP_V, // 2.影响客人小费的绝对值收益
    TIP_R, // 3.影响客人小费的百分比收益
    DROP, // 4.客人移动过程中随机掉落钱的总数
    BISCUIT_EARN, // 5.影响客人所有金币收益（百分比）
    ATTR_EARN // 6.影响客人属性的收益（百分比）
}

// 任务状态 0.未接取 1.未完成 2.可领奖 3.完成
enum TaskState {
    NONE, //未接取
    UNDONE, //未完成
    CANGET, //可领奖
    FINISH, //完成
}

// 咕咕鸡地图类型
enum GaliMapType {
    GRASS = 1, // 草地
    SAND, // 沙漠
    SNOW, // 雪地
    BEACH, // 沙滩
}

// 咕咕鸡探索状态
enum GaliExploreState {
    NONE,   // 默认
    ING,    // 探索中
    SPECIAL,    // 特殊事件
    PAUSE,      // 探索中止
    END,    // 探索完成
}

// 咕咕鸡探索特殊事件类型
enum GaliSpecialType {
    WALK = 1,   // 无特殊事件
    JUMP_GAME,  // 跳一跳小游戏
    SHOOT_GAME, // 射击小游戏
    DOG,        // 被狗撵
    CUSTOMER,   // 遇到客人
    BOX,    // 捡到宝箱
    MAZE_GAME,   // 迷宫小游戏
}

// 咕咕鸡特效类型
enum GaliEffectType {
    NONE = 0,   // 无
    RUN,       // 跑
    LAND,       // 着陆
    POWER,      // 蓄力
}

// 咕咕鸡棋盘格子类型
enum GaliChessType {
    BISCUITS = 1, // 小饼干
    CANDIES, // 糖果
    EXPLORE_PROP_A, // 冒险道具A
    SERVICE_PROP, // 服务道具
    SHOPPING, // 商店
    MOVE, // 移动
    SOLICIT, // 招客
    GOD, // 神
    EVENT_STAGE, // 事件舞台
    NONE, // 空
    EXPLORE, // 探索
    EXPLORE_PROP_B, // 冒险道具B
    MINI_GAME, // 小游戏
    NONE_PASS, // 踩过的格子
}

// 咖喱棋盘状态
enum GaliStatus {
    CHESS, // 棋盘
    MOVING, // 移动中
    CARDING, // 卡片中
    SHOPPING, // 哇塞交易
    STAGE, // 事件舞台
    EXPLORE, // 探险挂机
    GAMING, // 游戏中
}

// 咖喱循环地图类型
enum GaliRecycleMapType {
    ROAD = 1, // 路
    DI2, // 近景2
    DI3, // 远景3
    CLOUD, // 云
}

// 咖喱跳一跳游戏格子难度类型
enum GaliJumpItemType {
    PLAT_A = 'A',
    PLAT_B = 'B',
    PLAT_C = 'C',
}

//教程步骤类型
enum GuideStepType {
    CALL_FUNC = 0, //调用某个函数
    SEND_EVENT, //发送事件
    SESSION,//开启某个可操作区域
    STORY, //对话/剧情，本质是SESSION的语法糖
    CLICK_NEXT_STEP, // 点击后再发送事件
    WAIT_NEXT_STEP, // 等下一步
}

enum StoryType {
    TEXT = 0, //纯文本
    OPTION, //选项，确定/取消
}

//存档状态
enum RecordState {
    SAME = 0, //和线上一样
    DOWNLOAD = 1, //需要下载
    UPLOAD = 2, //需要上传
    CD = 3, //上传时间冷却中
    RECHECK = 4, //上传整个存档校验
}

//广告状态
enum AdState {
    WAIT = -3,
    PLAY_FAIL = -2,
    LOAD_FAIL = -1,
    LOADING = 0,
    LOAD_SUCCESS = 1,
    PLAY_SUCCESS = 2,
}

// 菜谱挖掘元素状态
enum FoodMiningStatus {
    NONE = 0, // 空（已使用，不可翻开，也不可使用）
    FRONT, // 正面（可点击使用）
    BACK, // 背面（可点击翻开）
    OPACITY, // 半透明（可点击翻开）
}

// 菜谱挖掘元素类型
enum FoodMiningType {
    NONE = 0,   // 空
    ROMANTIC,   // 肉食 1
    COMPREHEND, // 素食 2
    CREATIVITY, // 蛋白质 3
    TECH,       // 厨艺 4
    RANDOM,     // 随机
    STAMINA,    // 体力
    BOOM,       // 炸弹
    H_BOOM,     // 横向炸弹
    V_BOOM,     // 竖向炸弹
    EYE,        // 眼睛
    RESET,      // 重置
}

// 消息通知类型
enum MessageType {
    NONE, //自动消失 白色
    // 黄色
    SHOWER_BUILD, //浴池设施坏了
    DINING_BUILD, //餐厅设施坏了
    ROLE_NEED_HELP, //客人需要帮助
    MOUSE_APPEAR, //老鼠出没
    MAMMON_APPEAR, //财神出没
    WIAT_ROW_PIECE, //等待排片
    // 白色
    ROLE_NEED_SERVE, //客人需要服务
    ELFIN_APPEAR, //精灵出没
    GRANDMA_APPEAR, //老奶奶出没
    WASAI_APPEAR, //哇塞出没
    CINEMA_HAS_GIFT, //放映室有礼物可领
}

// 新手引导模块名称
enum GuideModType {
    OPENING = 1, // 开场
    DINING, // 餐厅
    SHOWER, // 温泉
    ATTIC, // 阁楼
    TIP_JAR, // 小费罐解锁
    ROOM, // 客房
    PAOPAO_SHOP, // 泡泡商店
    EVERYDAY_TASK, // 每日任务
    RANK, // 排行榜
    NEW_WUDONG_STAMINA, // 乌冬新体力系统(已废弃)
    GALI, // 咖喱
    SERVICE_TASK, // 特殊服务
    GALI_EXPLORE, // 咖喱探险
    DELAGATION_TASK, // 委托任务
    DOUBLE_INCOME, // 双倍收益教程
    SHOWER_SOUP, // 特殊汤料教程
    GALI_MINI_GAME, // 咖喱游戏教程
    MAGAZINE, // 杂志教程
    CINEMA, // 放映室教程
}

// 新手引导乌冬
enum GuideWudongPosType {
    NONE = 1, // 许愿池附近
    SHOWER, // 浴室附近
}

// 新手引导 特定步骤
enum GuideSpecialStep {
    NONE = '', // 空
    DORM_BUNK = 'DORM_BUNK', // 直接创建客人在休息室的床铺附近等待
    DORM_DROP_MONEY = 'DORM_DROP_MONEY', // 直接创建客人在休息室丢钱
    DORM_SECOND_PUCHASE = 'DORM_SECOND_PUCHASE', // 休息室二次购买设施
    DINING_MINING = 'DINING_MINING', // 直接打开菜谱挖掘界面
    DINING_GO_ORDER = 'DINING_GO_ORDER', // 创建客人走去桌子旁点餐
    DINING_SET_ORDER = 'DINING_SET_ORDER', // 直接创建客人在餐厅桌子旁等待、
    SHOWER_STEWPOT_PNL = 'SHOWER_STEWPOT_PNL', // 直接打开泡泡合成界面
    SHOWER_HOTSPR_PNL = 'SHOWER_HOTSPR_PNL', // 直接打开温泉强化界面
    ROOM_SET_DOOR = 'ROOM_SET_DOOR', // 直接生成客人在客房门口
    CINEMA_ROWPIECE = 'CINEMA_ROWPIECE', // 直接打开排片界面
    CINEMA_END = 'CINEMA_END', // 放映室最后一步创建观影客人
}

// 看广告增加的道具类型
enum AdPropType {
    // 注： 禁止改变已有顺序 主要用来统计
    NONE = 0, // 默认空
    MAIN_CARD, // 主楼揽客体力卡片（被动）1
    WUDONG_STAMINA, // 主动增加乌冬体力 2
    PAOPAO_COMP, // 泡泡合成材料 3
    TAITAN_STAMINA, // 被动增加泰坦体力 4
    EXTRA_TIP,//额外小费 5
    TAITAN_STAMINA_2, // 主动增加泰坦体力 6
    PAOPAO_SPEEDUP, // 合成加速 7
    DOUBLE_CLAIM, //双倍领取奖励 8
    GRANDMA_TIP,//老奶奶乐队 9
    OFFLINE_EXTRA,//离线额外奖励 10

    DOUBLE_INCOME = 100, // 双倍收益
}

// 乌冬闲话状态
enum WudongChitchatState {
    NONE,
    LEISURE, //闲暇
    BUSY, //忙碌
    FULLUP //客满
}

// 客人特殊属性类型
enum RoleSpeType {
    NONE,
    WEALTHY, //1.大富豪：在任意场景消费时有几率给予额外的钱(扔地上) x
    EAT_KING, //2.大胃王：吃饭完有几率多给钱(扔地上) x
    SLEEP_HOBBY, //3.睡觉爱好者：睡觉后有概率付更多的钱(扔地上) x
    BATH_HOBBY, //4.泡澡爱好者：泡澡后有概率付更多的钱(扔地上) x
    DORM_HOBBY, //5.休息室爱好者：消费时有几率给予额外的钱(扔地上) x
    KEFANG_HOBBY, //6.房间爱好者：消费时有几率给予额外的钱(扔地上) x
    SHOWER_HOBBY, //7.温泉爱好者：消费时有几率给予额外的钱(扔地上) x
    DINING_HOBBY, //8.餐厅爱好者：消费时有几率给予额外的钱(扔地上) x
    CINEMA_HOBBY, //9.电影院爱好者：消费时有几率给予额外的钱(扔地上) x
    LOVE_DORM, //10.钟爱休息室：离开时直接给予饼干(头顶冒) x
    LOVE_SHOWER, //11.钟爱温泉：离开时直接给予饼干(头顶冒) x
    LOVE_DINING, //12.钟爱餐厅：离开时直接给予饼干(头顶冒) x
    LOVE_CINEMA, //13.钟爱电影院：离开时直接给予饼干(头顶冒) x
    SOLICIT_WEALTHY, //14.土豪：招揽时有几率给予饼干(头顶冒) x
    TIP_WEALTHY, //15.富豪：给予小费时有几率给予额外倍的钱(小费罐冒) x
    VIEWING_HOBBY, //16.观影爱好者：看完电影后有概率付更多的钱(扔地上) x
    HUI_STAMINA, //17.回体力：招揽时有几率给乌冬体力
}

// 招客的客人类型
enum SolicitGuestType {
    NORMAL = 0, // 正常客人
    BLIND_BOX, // 盲盒客人 1
    SAOBA_BOX, // 扫把 （带一个盲盒客人） 2
    TRANS_FEITAN, // 飞毯（带两个正常客人） 3
    TRANS_CAR, // 小汽车 4
    TRANS_BUS, // 大巴车 5
    JIAN_BAO, // 煎包 6
    GE_CHANG, // 歌唱 7
    TRANS_DEVIL, // 大魔王 8
    TRANS_PHOTOGRAPH, // 照相（记者）
}

// 乌冬状态
enum WudongState {
    NONE, //不知道干啥
    IDLE, //闲置
    SOLICIT, //招客
    TOPUP, //充电了
    DECLINE, //没有体力了
    STROLL, //闲逛
    CLICK, //被点击了
}

// 招客客人状态
enum SolicitGuestStatus {
    WALKING, // 闲逛，行走
    PAUSE, // 暂停
    DROPING, // 卸客中（交通工具特有）
    DROPING2, // 卸客和星星的中间状态（交通工具特有）
    DROP_END, // 卸客完毕（交通工具特有）
    EATED, // 被吃
    SUCC, // 招揽成功
}

// 排行榜类型
enum RankType {
    NPC, // npc排行
    FRIEND, // 好友排行
    WORLD, // 世界排行
}

// 授权类型
enum AuthorizeType {
    RECORD, // 上传存档
    WORLD_RANK, // 世界排行
}

// 引导异常数据检测类型
enum CheckGuideDataType {
    SCENE = 1, // 场景解锁
    DORM_BUILD, // 休息室设施解锁
    DINING_BUILD, // 餐厅设施解锁
    SHOWER_BUILD, // 浴室设施解锁
    ROOM_BUILD, // 201房间绿植装饰
    CINEMA_BUILD, // 放映室设施解锁
}

// 广告状态
enum AdShowState {
    NONE, //不可以播放广告
    READY, //准备好了
    NOT_READY //没有准备好
}

// npc类型
enum NpcType {
    GOD = 500, //神的开始id
    MAP_WUDONG = 1000001, //场景中的乌冬
    WUDONG = 100000, //乌冬
    HUAMAO, //花猫 100001
    TAITAN, //泰坦 100002
    PAOPAO, //泡泡 100003
    GALI, //咖喱 100004
    BINGGAN = 110001, //饼干人
    ELFIN = 200001, //精灵
    MOUSE, //老鼠 200002
    MAMMON, //财神 200003
    GRANDMA, //老奶奶 200004
    WASHAI = 300001, //哇塞
    GECHANG = 400001, //歌唱精灵
    JIANBAO, //煎包精灵 400002
    DEVIL, // 大魔王
    PHOTO, // 照相精灵
    GRANDMA_BAND = 2000041, //老奶奶乐队 2000041
}

// 心愿类型
enum WishType {
    WISH = 1, //心愿
    SERVE, //委托
    ORDER, //预约
    UNLOCK,
    EXCLUSIVE_1,
    EXCLUSIVE_2,
    EXCLUSIVE_3,
}
// 专属任务类型
enum ExcTaskType {
    UNLOCK = 1,
    ORDER,
    CHOICE, //抉择
    PHOTOGRAPH, //拍照
    SEEK, //寻物
    DECORATION, //装修
}

// 广告状态 (虽然之前只用于激励广告，不过现在已经用于所有广告，命名懒得改了)
enum RewardAdStatus {
    SUCCESS = 0,
    FAIL,
    LOAD_CACHE_FAIL,
    SHARE_FAIL,
}

enum LoginSyncState {
    SAME = 0,
    DEVICE_DIFF,
    VERSION_DIFF,
    SYNC,
}

enum AdType {
    REWARD = 1,
    INTERSTITIAL,
    SHARE,
}

enum ShareType {
    UNKNOWN = 0,
    UNLOCK_CUSTOMER,
    RANK,
    DAILY_TASK,
    AD,
    ON_SHARE_APP_MESSAGE, // 左上角菜单转发
    ON_SHARE_TIMELINE, // 分享朋友圈
    ROOM_SHOOT,// 客房拍照分享
    INVITE,//邀请分享
    MMJ,//麻麻节活动分享

    //以上为常规分享，活动分享加个前缀区分，如1001
}

enum WeakGuideType {
    NONE = 0, // 空
    BISCUITS, // 饼干
    GUIDE_TASK, // 新手任务
    GET_TIP, // 收取小费
    SOLICIT, // 招客
    REPLENISHMENT, // 补货
    REPAIR, // 修缮
    GALI_WHEEL, // 咖喱转盘
    SERVICE, // 客人额外服务
    BUILD_TIP = 101, // 建设小费罐
    FOOD_MINING, // 厨艺挖掘
}

enum WeakGuideStatus {
    CHECKING, // 检测中
    GUIDING, // 引导中
    CHECK_ONE, // 依次检测
}

enum ChannelType {
    OFFICIAL = 'official', //正式渠道
    HYKB = 'HYKB', //好游快爆
    DY = 'DY', //抖音
}

// 分享使用类型
enum ShareUseType {
    WD_SKIN_9, //乌冬皮肤 id=9的
    WD_SKIN_11
}

//订阅类型
enum SubscribeType {
    GALI_EXPLORE = "5hBIDXA6bwBiaXMfoLHpxPVtwtT1RDQ3yDhVlDFxFhw",
}

//特殊奖励类型
enum SpAwardType {
    WX_APP_LOGIN, // app小程序登录奖励
}

// 电影播放状态
enum FilmPlayState {
    NONE, //等待中 等客人入场
    READY, //准备播放
    PLAYING, //播放中
    STOP_ENTER, //停止入场
    END, //播放结束
    SWEEP, //打扫
}

// 排片状态
enum RowPieceState {
    RESET, //刚刚重置 还未打开排片UI
    NONE, //还未排片
    PLAYING, //已经排好
    END, //结束
}

// 影片类型
enum FilmType {
    NONE,
    MAX,
}

//反馈类型
enum FeedBackType {
    GENERAL = 0, // 一般反馈
    CHEAT = 1, //作弊
}

enum Bundle {
    COMMON = "commonSubpackage",
}
enum ShopItemType  {
    RECEIVE_DAILY_WINDMILL= 1, //免费风车
    EXCHANGE_SCISSOR= 2, //兑换剪刀
    EXCHANGE_BISCUITS= 3, //兑换饼干
    EXCHANGE_CANDIES= 4, //兑换糖果
    WINDMILL= 5, //风车
    PACKAGE= 6, //礼包
    MONTH_CARD= 7, //月卡
}

// 日常活动类型
enum ActivityType {
    NONE,
    DAILY_ONLINE, // 在线奖励
    FIRST_TOPUP, // 首充礼包
    SEVEN_DAYS, // 七天签到
    FIRST_AD, // 首看奖励
    INVITE, // 邀请奖励
    AD_FEEDBACK, // 广告反馈
    HOLIDAY, // 节日活动
    LIMITED_OFFER, // 限时特惠
    DECORATION, // 装修大赛
    HOLIDAY_COMMON, // 通用活动
    HBDLYS, // 红包对联预售
    HALLOWEEN, // 万圣节
    CHRISTMAS, // 圣诞节
    NEW_YEAR, // 春节
    NEW_YEAR_AD_LIMIT, // 财神密保
    AD_SKIP_CARD, // 广告跳过卷
    FAV_APP, // 收藏小游戏
    BQBZJ, // 表情包征集活动
    SURVEY, // 有奖问卷
    LABOR_DAY_ONLINE_TIME, // 五一在线时长
}

// 限时广告活动id
enum OnlineTimeActivityId {
    NONE,
    LABOR_DAY, // 五一活动
}

// 客房家具类型
enum FurnitureType {
    SIT = 11, //坐 11
    BED, //床 12
    RECREATION, //电视 13
    CABINET, //柜 14
    LAVATORY, //卫生 15
    XISHU, //洗漱 16
    OUTSIT, //户外座椅 17
    HOPSCOTCH, //跳房子 特殊家具 18
    SLIDINGBOARD, //滑梯 特殊家具 19
    HAMMOCK, //吊床 20
    AIRBED = 1, //充气床 特殊家具 1
    VR_TV, //VR机器 特殊家具 2
    DANCE_MAT, //动感跳舞毯 特殊家具 3
    YE_FAN, //年夜饭 特殊家具 4
    PI_YING_XI,// 皮影戏 特殊家具 5
    FLOOR = 21, //地砖 21
    CARPET, //地毯 22
    TABLE, //桌 23
    DECORATE, //摆件 24
    PENDANT, //挂件 25
    WALL, //隔断 26
    OUTPE, //户外摆件 27
    WALLPAPER = 31, //墙纸 31
    GROUND, //地板 32
    OUTGR, //户外地板 33
    WALLPAPER2, //二楼墙纸 34
    GROUND2, //二楼地板 35
    STAIRS, //楼梯 36
    SHOWERS, //淋浴房 37
    DOOR = 100, //门口

    STAIRS_HOME = 200, // 小家楼梯
    FLOOR_HOME, // 小家地面
    WALL_HOME, // 小家墙面
    HANDDRAIL_HOME, // 小家栏杆
    DOOR_HOME,  //小家门（任意门）
    DOORMAT_HOME, //小家门垫（入口）
    FURN_HOME, //小家家具（只是分类用，小家的实际家具还是用的其他类）

    WAllBRICK = 301,  //墙砖，预留坑

    G_FLOOR_1 = 501,// 花园空间地面1
    G_FLOOR_2,// 花园空间地面2
    G_FLOOR_3, //花园空间地面3
    G_L_FLOOR_1,// 花园左景地面1
    G_L_FLOOR_2,// 花园左景地面2
    G_R_FLOOR_1,// 花园右景地面1
    G_EDGE_1,	//507	花园空间围栏1
    G_EDGE_2,	//508	花园空间围栏2
    G_EDGE_3,//509	花园空间围栏3
    G_R_EDGE_1,	//510	花园右景围栏
    G_L_WALLPAPER_1,//	511	花园左景墙
    G_L_BRIDGE_1,	//512	花园左景桥
    G_EDGE_4,//花园空间围栏2.5

    PLANT = 600,// 植物类型
    GARDEN_SPE_FURN = 601// 花园胚子
}

export {
    ActivityType,
    OnlineTimeActivityId,
    MapType,
    MapSceneType,
    ComeRoleType,
    PropType,
    ConditionType,
    FurnitureType,
    DiningBuildType,
    ShowerBuildType,
    DormBuildType,
    CinemaBuildType,
    TaskConditionType,
    EverydayTaskType,
    BuffType,
    TaskState,
    GaliMapType,
    GaliExploreState,
    GaliSpecialType,
    GaliEffectType,
    GaliChessType,
    GaliStatus,
    GaliRecycleMapType,
    GaliJumpItemType,
    GuideStepType,
    StoryType,
    RecordState,
    AdState,
    FoodMiningStatus,
    FoodMiningType,
    MessageType,
    GuideModType,
    GuideWudongPosType,
    GuideSpecialStep,
    AdPropType,
    WudongChitchatState,
    RoleSpeType,
    SolicitGuestType,
    WudongState,
    SolicitGuestStatus,
    RankType,
    AuthorizeType,
    CheckGuideDataType,
    AdShowState,
    NpcType,
    RewardAdStatus,
    LoginSyncState,
    WishType,
    AdType,
    ShareType,
    WeakGuideType,
    WeakGuideStatus,
    ChannelType,
    ShareUseType,
    SubscribeType,
    SpAwardType,
    FilmPlayState,
    RowPieceState,
    FilmType,
    FeedBackType,
    Bundle,
    ExcTaskType,
    CurrencyAction,
    ShopItemType,
    PlantStage,
    PlantStatus
}
