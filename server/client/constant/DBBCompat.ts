
// 2.2.0版本 心愿id兼容
const WISH_ID_CHANGE = {
    '1_1': 1001,
    '2_1': 1002,
    '3_1': 1003,
    '4_1': 1004,
    '5_2': 2001,
    '6_2': 2002,
    '7_2': 2003,
    '8_2': 2004,
    '9_3': 3001,
    '10_3': 3002,
    '11_3': 3003,
    '12_3': 3004,
    '17_12': 12001,
    '18_12': 12002,
    '19_12': 12003,
    '20_12': 12004,
    '13_4': 4001,
    '14_4': 4002,
    '15_4': 4003,
    '16_4': 4004,
    '29_8': 8001,
    '30_8': 8002,
    '31_8': 8003,
    '32_8': 8004,
    '37_10': 10001,
    '38_10': 10002,
    '39_10': 10003,
    '40_10': 10004,
    '41_11': 11001,
    '42_11': 11002,
    '43_11': 11003,
    '44_11': 11004,
    '13_51': 51001,
    '14_51': 51002,
    '15_51': 51003,
    '16_51': 51004,
    '25_37': 37001,
    '26_37': 37002,
    '27_37': 37003,
    '28_37': 37004,
    '49_13': 13001,
    '50_13': 13002,
    '51_13': 13003,
    '52_13': 13004,
    '53_14': 14001,
    '54_14': 14002,
    '55_14': 14003,
    '56_14': 14004,
    '109_28': 28001,
    '110_28': 28002,
    '111_28': 28003,
    '112_28': 28004,
    '57_15': 15001,
    '58_15': 15002,
    '59_15': 15003,
    '60_15': 15004,
    '61_16': 16001,
    '62_16': 16002,
    '63_16': 16003,
    '64_16': 16004,
    '33_31': 31001,
    '34_31': 31002,
    '35_31': 31003,
    '36_31': 31004,
    '45_5': 5001,
    '46_5': 5002,
    '47_5': 5003,
    '48_5': 5004,
    '65_17': 17001,
    '66_17': 17002,
    '67_17': 17003,
    '68_17': 17004,
    '69_18': 18001,
    '70_18': 18002,
    '71_18': 18003,
    '72_18': 18004,
    '73_19': 19001,
    '74_19': 19002,
    '75_19': 19003,
    '76_19': 19004,
    '81_21': 21001,
    '82_21': 21002,
    '83_21': 21003,
    '84_21': 21004,
    '77_20': 20001,
    '78_20': 20002,
    '79_20': 20003,
    '80_20': 20004,
    '93_24': 24001,
    '94_24': 24002,
    '95_24': 24003,
    '96_24': 24004,
    '85_22': 22001,
    '86_22': 22002,
    '87_22': 22003,
    '88_22': 22004,
    '89_23': 23001,
    '90_23': 23002,
    '91_23': 23003,
    '92_23': 23004,
    '105_27': 27001,
    '106_27': 27002,
    '107_27': 27003,
    '108_27': 27004,
    '97_25': 25001,
    '98_25': 25002,
    '99_25': 25003,
    '100_25': 25004,
    '101_26': 26001,
    '102_26': 26002,
    '103_26': 26003,
    '104_26': 26004,
    '117_30': 30001,
    '118_30': 30002,
    '119_30': 30003,
    '120_30': 30004,
    '21_6': 6001,
    '22_6': 6002,
    '23_6': 6003,
    '24_6': 6004,
    '113_29': 29001,
    '114_29': 29002,
    '115_29': 29003,
    '116_29': 29004,
    '129_33': 33001,
    '130_33': 33002,
    '131_33': 33003,
    '132_33': 33004,
    '121_9': 9001,
    '122_9': 9002,
    '123_9': 9003,
    '124_9': 9004,
    '125_32': 32001,
    '126_32': 32002,
    '127_32': 32003,
    '128_32': 32004,
    '141_36': 36001,
    '142_36': 36002,
    '143_36': 36003,
    '144_36': 36004,
    '133_34': 34001,
    '134_34': 34002,
    '135_34': 34003,
    '136_34': 34004,
    '137_35': 35001,
    '138_35': 35002,
    '139_35': 35003,
    '140_35': 35004,
    '145_7': 7001,
    '146_7': 7002,
    '147_7': 7003,
    '148_7': 7004,
    '157_40': 40001,
    '158_40': 40002,
    '159_40': 40003,
    '160_40': 40004,
    '149_38': 38001,
    '150_38': 38002,
    '151_38': 38003,
    '152_38': 38004,
    '153_39': 39001,
    '154_39': 39002,
    '155_39': 39003,
    '156_39': 39004,
    '157_41': 41001,
    '158_41': 41002,
    '159_41': 41003,
    '160_41': 41004,
    '157_42': 42001,
    '158_42': 42002,
    '159_42': 42003,
    '160_42': 42004,
    '157_43': 43001,
    '158_43': 43002,
    '159_43': 43003,
    '160_43': 43004,
    '157_44': 44001,
    '158_44': 44002,
    '159_44': 44003,
    '160_44': 44004,
    '157_45': 45001,
    '158_45': 45002,
    '159_45': 45003,
    '160_45': 45004,
    '157_46': 46001,
    '158_46': 46002,
    '159_46': 46003,
    '160_46': 46004,
    '157_47': 47001,
    '158_47': 47002,
    '159_47': 47003,
    '160_47': 47004,
    '157_48': 48001,
    '158_48': 48002,
    '159_48': 48003,
    '160_48': 48004,
    '157_49': 49001,
    '158_49': 49002,
    '159_49': 49003,
    '160_49': 49004,
    '157_50': 50001,
    '158_50': 50002,
    '159_50': 50003,
    '160_50': 50004,
}

// 2.2.0版本 客人等级和经验兼容
const ROLE_OLD_MAX_EXP = {
    '1_1': { lv: 2, stage: 1, isMaxExp: false },
    '2_1': { lv: 3, stage: 1, isMaxExp: false },
    '3_1': { lv: 4, stage: 1, isMaxExp: false },
    '4_1': { lv: 5, stage: 1, isMaxExp: true },
    '4_2': { lv: 7, stage: 2, isMaxExp: false },
    '5_2': { lv: 8, stage: 2, isMaxExp: false },
    '6_2': { lv: 9, stage: 2, isMaxExp: false },
    '7_2': { lv: 10, stage: 2, isMaxExp: true },
    '7_3': { lv: 12, stage: 3, isMaxExp: false },
    '8_3': { lv: 13, stage: 3, isMaxExp: false },
    '9_3': { lv: 14, stage: 3, isMaxExp: false },
    '10_3': { lv: 15, stage: 3, isMaxExp: true },
    '10_4': { lv: 17, stage: 4, isMaxExp: false },
    '11_4': { lv: 18, stage: 4, isMaxExp: false },
    '12_4': { lv: 19, stage: 4, isMaxExp: false },
    '13_4': { lv: 20, stage: 4, isMaxExp: true },
    '13_5': { lv: 21, stage: 5, isMaxExp: false },
}

const SHOWER_ACHIEVE_TASK_REWARD = [
    { id: 16001, reward: '2,-1,5' },
    { id: 16002, reward: '2,-1,5' },
    { id: 16003, reward: '2,-1,5' },
    { id: 16004, reward: '2,-1,10' },
    { id: 16005, reward: '2,-1,10' },
    { id: 16006, reward: '2,-1,15' },
    { id: 16007, reward: '2,-1,30' },
    { id: 16008, reward: '2,-1,30' },
    { id: 16009, reward: '2,-1,50' },
    { id: 16010, reward: '2,-1,100' },
    { id: 6001, reward: '1,-1,560' },
    { id: 6002, reward: '1,-1,2400' },
    { id: 6003, reward: '1,-1,2800' },
    { id: 6004, reward: '1,-1,4000' },
    { id: 6005, reward: '1,-1,6000' },
    { id: 6006, reward: '1,-1,16000' },
    { id: 6007, reward: '1,-1,20000' },
    { id: 6008, reward: '1,-1,36000' },
    { id: 6009, reward: '1,-1,52000' },
    { id: 6010, reward: '1,-1,80000' }

]

// 窗户换成黑夜
const WIND_CHANGE_HEI_WIND = {
    'PENDANT_040': 'PENDANT_051',
    'PENDANT_041': 'PENDANT_052',
    'PENDANT_018': 'PENDANT_053',
    'PENDANT_042': 'PENDANT_054',
    'PENDANT_019': 'PENDANT_055',
    'PENDANT_020': 'PENDANT_056',
    'PENDANT_021': 'PENDANT_057',
    'PENDANT_022': 'PENDANT_058',
    'PENDANT_023': 'PENDANT_059',
    'PENDANT_024': 'PENDANT_060',
    'PENDANT_025': 'PENDANT_061',
    'PENDANT_043': 'PENDANT_062',
    'DECORATE_014': 'DECORATE_028',
    'DECORATE_015': 'DECORATE_029',
    'DECORATE_017': 'DECORATE_030',
}


// 窗户转换成不用选择 让之前解锁的窗户另外一个也解锁
const COMPAT_UNLOCK_WIND = {
    'PENDANT_019': 'PENDANT_040',
    'PENDANT_022': 'PENDANT_041',
    'PENDANT_020': 'PENDANT_018',
    'PENDANT_021': 'PENDANT_042',

    'PENDANT_055': 'PENDANT_051',
    'PENDANT_058': 'PENDANT_052',
    'PENDANT_056': 'PENDANT_053',
    'PENDANT_057': 'PENDANT_054',

    'PENDANT_023': 'PENDANT_019',
    'PENDANT_024': 'PENDANT_022',
    'PENDANT_025': 'PENDANT_020',
    'PENDANT_043': 'PENDANT_021',

    'PENDANT_059': 'PENDANT_055',
    'PENDANT_060': 'PENDANT_058',
    'PENDANT_061': 'PENDANT_056',
    'PENDANT_062': 'PENDANT_057',

}

// 402 窗户换成黑夜
const WIND_CHANGE_HEI_WIND_402 = {
    'PENDANT_105': 'PENDANT_108',
    'PENDANT_106': 'PENDANT_109',
    'PENDANT_107': 'PENDANT_110',
}


export {
    WISH_ID_CHANGE,
    ROLE_OLD_MAX_EXP,
    SHOWER_ACHIEVE_TASK_REWARD,
    WIND_CHANGE_HEI_WIND,
    COMPAT_UNLOCK_WIND,
    WIND_CHANGE_HEI_WIND_402,
}