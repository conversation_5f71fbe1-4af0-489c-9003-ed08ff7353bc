import { MapSceneType, RoleSpeType } from "../constant/Enums"
import BaseMapModel from "../map/BaseMapModel"
// import RoleModel from "../role/RoleModel"
// import ElevatorSysModel from "./ElevatorSysModel"
import MainModel from "../main/MainModel"
import ShowerModel from "../shower/ShowerModel"
import DiningModel from "../dining/DiningModel"
import KefangModel from "../kefang/KefangModel"
import RoleAttrObj from "./RoleAttrObj"
import { UNLOCK_ROLE_IDS } from "../constant/Constant"
import DormModel from "../dorm/DormModel"
import ShopModel from "../shop/ShopModel"
// import Elevator2Model from "../map/main/Elevator2Model"
// import Elevator3Model from "../map/main/Elevator3Model"
// import Elevator4Model from "../map/main/Elevator4Model"
import { assetsMgr } from "../AssetsMgr"
import ModelMgr from "../ModelMgr"
import CinemaModel from "../cinema/CinemaModel"
import { ROLE_OLD_MAX_EXP } from "../constant/DBBCompat"
import GardenModel from "../garden/GardenModel";

// 世界模型
export default class WorldModel {

    private maps: BaseMapModel[] = []// 所有地图
    // private elevatorSys: ElevatorSysModel = null

    private shop: ShopModel = null
    private main: MainModel = null
    private _dining: DiningModel = null
    private _dorm: DormModel = null
    private _shower: ShowerModel = null
    private _cinema: CinemaModel = null
    private modelMgr: ModelMgr = null
    private _garden: GardenModel = null

    // private gali: GaLiModel = null

    private roleAttrs: RoleAttrObj[] = [] //客人的属性
    private mapUnlocks: MapSceneType[] = [] //当前解锁的地图
    private kefangUnlocks: number[] = [] //当前已经解锁的客房

    public onCreate(modelMgr: ModelMgr) {
        this.main = null
        this._dining = null
        this._dorm = null
        this._shower = null
        this.shop = null
        this.modelMgr = modelMgr
    }

    // 初始化世界
    public init() {
        let dbHelper = this.modelMgr.getDB()
        const ver = 3
        const data = dbHelper.register('world') || {}

        while (data._ver < ver) {
            if (data._ver === 1) {
            } else if (data._ver === 2) {
                // 兼容客人等级和经验
                (data.roleAttrs || []).forEach(m => {
                    const d = ROLE_OLD_MAX_EXP[m.lv + '_' + m.stage]
                    if (d) {
                        m.lv = d.lv
                        m.stage = d.stage
                        m.favorability = d.isMaxExp ? (assetsMgr.getJsonData('customersAttr', m.id * 1000 + m.lv)?.lv_love || 0) : 0
                    }
                })
                // 兼容还有双倍客人时间
                const time = Math.min(5 * 60000, (data.doubleRoleElapsedDB || 0) * 1000)
                if (time > 0) {
                    data.doubleRoleEndTime = Date.now() + time
                }
            }
            data._ver += 1
        }

        this.roleAttrs = data.roleAttrs ? Array.from(data.roleAttrs).map(m => new RoleAttrObj().fromDB(m, this.modelMgr)) : UNLOCK_ROLE_IDS.map(id => new RoleAttrObj().init(id))
        this.mapUnlocks = data.mapUnlocks || []
        this.kefangUnlocks = data.kefangUnlocks || [] //默认解锁

        // 创建世界
        this.maps.length = 0

        // 主楼
        this.main = new MainModel(this.modelMgr)
        this.shop = new ShopModel(this.modelMgr)

        // 先检测一边解锁
        this.main.getMainAttr().changes.forEach(id => {
            const mapId = assetsMgr.getJsonData('hostelChange', id)?.unlock_map
            if (mapId) {
                if (mapId > MapSceneType.MAX) {
                    if (!this.kefangUnlocks.has(mapId)) {
                        this.kefangUnlocks.push(mapId)
                    }
                } else if (!this.mapUnlocks.has(mapId)) {
                    this.mapUnlocks.push(mapId)
                }
            }
        })

        // 浴池, 餐厅, 休闲中心
        for (let i = 0; i < this.mapUnlocks.length; i++) {
            const m = this.mapUnlocks[i]
            this.createMap(m, true)
        }

        // 客房
        for (let i = 0; i < this.kefangUnlocks.length; i++) {
            const m = this.kefangUnlocks[i]
            this.maps.push(new KefangModel(m, null, this.modelMgr))
        }

        this._garden = new GardenModel(this.modelMgr)
    }

    public getMaps() {
        return this.maps
    }

    public getMain() {
        return this.main
    }

    public getKefangs() {
        return this.getMapsBySceneType(MapSceneType.KEFANG) as KefangModel[]
    }

    public getRoleAttrs() {
        return this.roleAttrs
    }

    public getRoleAttr(id: number) {
        return this.roleAttrs.find(m => m.id === id)
    }

    public getUnlockKefangs() {
        return this.kefangUnlocks
    }

    public getUnlockKefangCount() {
        return this.kefangUnlocks.length
    }

    public isUnlockKefang(no: number) {
        return this.kefangUnlocks.indexOf(no) !== -1
    }

    public getShop() {
        return this.shop
    }

    public getDining() {
        return this._dining || (this._dining = this.getMapBySceneType(MapSceneType.DINING) as DiningModel)
    }

    public getShower() {
        return this._shower || (this._shower = this.getMapBySceneType(MapSceneType.SHOWER) as ShowerModel)
    }

    public getDorm() {
        return this._dorm || (this._dorm = this.getMapBySceneType(MapSceneType.DORM) as DormModel)
    }

    public getCinema() {
        return this._cinema || (this._cinema = this.getMapBySceneType(MapSceneType.CINEMA) as CinemaModel)
    }

    public getGarden() {
        return this._garden || (this._garden = this.getMapBySceneType(MapSceneType.GARDEN) as GardenModel)
    }

    public getMapBySceneType(type: MapSceneType) {
        return type === MapSceneType.MAIN ? this.main : this.maps.find(m => m.sceneType === type)
    }

    public getMapsBySceneType(type: MapSceneType) {
        return this.maps.filter(m => m.sceneType === type)
    }

    private createMap(type: MapSceneType, init?: boolean): BaseMapModel {
        let map = null
        if (type === MapSceneType.DINING) {
            map = this.maps.add(new DiningModel(this.modelMgr))
            // !init && gameHelper.npcSys.unlockNpc(NpcType.TAITAN)
        } else if (type === MapSceneType.SHOWER) {
            map = this.maps.add(new ShowerModel(this.modelMgr))
            // !init && gameHelper.npcSys.unlockNpc(NpcType.PAOPAO)
        } else if (type === MapSceneType.DORM) {
            map = this.maps.add(new DormModel(this.modelMgr))
        } else if (type === MapSceneType.CINEMA) {
            map = this.maps.add(new CinemaModel(this.modelMgr))
        }
        return map
    }

    // 是否解锁
    public isUnlockMap(type: MapSceneType) {
        if (type === MapSceneType.MAIN || (type >= MapSceneType.ELEVATOR2 && type <= MapSceneType.ELEVATOR4)) {
            return true //主楼默认解锁
        }
        return type < MapSceneType.MAX ? this.mapUnlocks.indexOf(type) !== -1 : this.kefangUnlocks.indexOf(type) !== -1
    }

    // 获取有对应场景技能的客人
    public getHasSceneSpeRoles(sceneType: MapSceneType): { incomeMul: number, useIncomeMul: number }[] {
        let arr = [], sumWeight = 0
        this.roleAttrs.forEach(m => {
            let incomeMul = 0, useIncomeMul = 0
            m.spes.forEach(json => {
                const odds = json.odds * 0.01, mul = odds * json.mul + (1 - odds)
                if (json.type === RoleSpeType.WEALTHY
                    || (json.type === RoleSpeType.DORM_HOBBY && sceneType === MapSceneType.DORM)
                    || (json.type === RoleSpeType.KEFANG_HOBBY && sceneType === MapSceneType.KEFANG)
                    || (json.type === RoleSpeType.SHOWER_HOBBY && sceneType === MapSceneType.SHOWER)
                    || (json.type === RoleSpeType.DINING_HOBBY && sceneType === MapSceneType.DINING)
                    || (json.type === RoleSpeType.CINEMA_HOBBY && sceneType === MapSceneType.CINEMA)
                ) {
                    incomeMul += mul
                    useIncomeMul += mul
                } else if ((sceneType === MapSceneType.DINING && (json.type === RoleSpeType.LOVE_DINING || json.type === RoleSpeType.EAT_KING))
                    || (sceneType === MapSceneType.DORM && (json.type === RoleSpeType.LOVE_DORM || json.type === RoleSpeType.SLEEP_HOBBY))
                    || (sceneType === MapSceneType.SHOWER && (json.type === RoleSpeType.LOVE_SHOWER || json.type === RoleSpeType.BATH_HOBBY))
                    || (sceneType === MapSceneType.CINEMA && (json.type === RoleSpeType.LOVE_CINEMA || json.type === RoleSpeType.VIEWING_HOBBY))
                    || (sceneType === MapSceneType.KEFANG && json.type === RoleSpeType.SLEEP_HOBBY)
                ) {
                    incomeMul += mul
                }
            })
            const weight = m.base ? m.base.visit_weight : 0
            sumWeight += weight
            if (incomeMul > 0 || useIncomeMul > 0) {
                arr.push({id: m.id, incomeMul, useIncomeMul, weight})
            }
        })
        arr.forEach(m => {
            const r = m.weight / sumWeight
            m.incomeMul *= r
            m.useIncomeMul *= r
        })
        return arr
    }

    // 获取地上所有掉落的饼干总数
    public getAllDropSumCount() {
        return this.maps.reduce((val, cur) => val + cur.dropMoneys.reduce((v, c) => c.money + v, 0), 0)
    }
}
