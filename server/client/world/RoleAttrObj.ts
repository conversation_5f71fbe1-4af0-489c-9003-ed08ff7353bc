import {gameHelper} from "../GameHelper"
import {RoleAttrJItem, RoleBaseJItem, RoleStageJItem, RoleUpRewardInfo, RoleSpAttrJItem} from "../constant/DataType"
import {assetsMgr} from "../AssetsMgr"
import ut from "../../common/util"
import ModelMgr from "../ModelMgr"
import RoleLvRewardObj from "./RoleLvRewardObj"
import TaskConditionObj from "../task/TaskConditionObj"
import {StateType} from "../constant/StateEnum"
import {ExclusiveBaseJItem} from "../constant/DataType"

type TriggerExcTask = {
    id: number;
    trigger: boolean;
    json: ExclusiveBaseJItem;
    preIds: number[]; //前置
    conds?: TaskConditionObj[];
    state?: StateType; //客人状态
    uid: string; //触发的客人uid
}

// 角色属性 成长
export default class RoleAttrObj {
    // 接受了专属任务
    public acceptExcTask(id: number) {
        const task = this.excTasks.remove('id', id)
        if (task && !this.finishExcTasks.has(id)) {
            this.finishExcTasks.push(id) //只要接受了 就视为做过了 因为任务不能放弃
        }
    }

    public id: number = 0
    public stage: number = 1 //阶段
    public lv: number = 0 //等级 实际等级
    public favorability: number = 0 //好感度
    public yetLikeFoods: number[] = [] //吃过喜欢的菜
    public yetLikeFurns: string[] = [] //喜欢的家具
    public rewards2: RoleLvRewardObj[] = [] //奖励列表 第2版
    public hasNewTalkState: number = 1 //新人对话状态 1.还没开始 2.准备去了 0.完成
    public newBoyState: number = 1 //新人状态 1.准备 2.状态持续中 0.完成
    public yetDSleep: number = 0 //大通铺睡过觉 0.还没有 1.准备了 2.睡过了
    public hasShareAward: number = 0 //分享奖励
    public isCanWishState: boolean = false //是否有心愿任务状态
    public triggerAttrTaskIds: number[] = [] //已经触发过的等级 属性id
    public finishExcTasks: number[] = [] //完成的专属任务列表
    private excTasks: TriggerExcTask[] = [] //还未完成的专属任务列表
    public base: RoleBaseJItem = null
    public json: RoleAttrJItem = null
    public stageJson: RoleStageJItem = null
    public minLv: number = 0 //小等级 用于界面显示
    public spes: RoleSpAttrJItem[] = [] //当前 特性列表
    public upRewards: RoleUpRewardInfo[] = [] //升级奖励

    private tip: number = 0 //每个小等级对应的小费

    public init(id: number) {
        this.id = id
        this.lv = 1
        this.stage = 1
        // this.favorability = 0
        this.base = assetsMgr.getJsonData('customersBase', this.id)
        this.initJson()
        // this.hasNewTalkState = this.base.unlock_story ? 1 : 0
        // this.newBoyState = UNLOCK_ROLE_IDS.has(id) ? 0 : 1
        // this.yetDSleep = 0
        this.hasShareAward = this.base.share_award ? 1 : -1
        return this
    }

    public fromDB(data: any, modelMgr: ModelMgr) {
        ut.setValue('id|lv|stage|favorability|yetLikeFoods|yetLikeFurns', data, this)
        // this.hasNewTalkState = !!data.hasNewTalkState ? 1 : 0
        // this.newBoyState = !!data.newBoyState ? 1 : 0
        // this.yetDSleep = data.yetDSleep !== 2 ? 0 : 2
        this.base = assetsMgr.getJsonData('customersBase', this.id)
        this.rewards2 = (data.rewards || []).map(m => new RoleLvRewardObj().fromDB(m, modelMgr))
        this.hasShareAward = data.hasShareAward || (this.base && Object.keys(this.base).indexOf('share_award') > -1 && this.base.share_award ? 1 : -1)
        this.triggerAttrTaskIds = data.triggerAttrTaskIds || []
        this.finishExcTasks = data.finishExcTasks || []
        try {
            this.initJson()
        } catch (e) {
            console.log(1);
        }
        return this
    }

    private initStage(init?: boolean) {
        this.stageJson = assetsMgr.getJsonData('customersStage', this.getAttrId(this.stage))
        this.spes = this.stageJson && Object.keys(this.stageJson).indexOf('spe_id') > -1 ? ut.stringToNumbers(this.stageJson.spe_id).map(id => assetsMgr.getJsonData('customersSpAttr', id)) : [];
        // 是否有剧情
        if (!init && this.stageJson.story) {
            this.upRewards.push({lv: this.lv, minLv: this.minLv, stage: this.stage, storyId: this.stageJson.story})
        }
    }

    private initAttr(init?: boolean) {
        this.json = assetsMgr.getJsonData('customersAttr', this.getAttrId(this.lv))
        this.tip = this.json.tip || 0
        // 添加奖励
        if (!init && this.json.reward) {
            const rewards = gameHelper.stringToConditions(this.json.reward)
            if (rewards.length > 0) {
                this.upRewards.push({lv: this.lv, minLv: this.minLv, stage: this.stage, rewards: rewards})
            }
        }
    }

    private initJson() {
        this.initStage(true)
        if (!this.stageJson && this.stage !== 1) {
            this.stage = Math.max(this.stage - 1, 1)
            return this.initJson()
        } else if (this.lv > this.maxLv) {
            this.lv = this.maxLv
        }
        this.initAttr(true)
        // 算出小等级
        if (this.stage === 1) {
            this.minLv = this.lv
        } else {
            const json = assetsMgr.getJsonData('customersStage', this.getAttrId(this.stage - 1))
            this.minLv = Math.max(1, this.lv - (json?.lv_limt || this.lv))
        }
        // 算出升级奖励
        let preStage = -1
        this.upRewards.length = 0
        for (let lv = 1; lv <= this.lv; lv++) {
            const {stage, minLv} = gameHelper.getRoleStageAndMinLv(this.id, lv)
            let json = assetsMgr.getJsonData('customersAttr', this.getAttrId(lv));
            let rewards = gameHelper.stringToConditions(json?.reward)
            // 奖励
            if (rewards.length > 0) {
                this.upRewards.push({lv: lv, minLv: minLv, stage: stage, rewards: rewards})
            }
            // 剧情一
            if (lv === 1) {
                json = assetsMgr.getJsonData('customersBase', this.id)
                if (!!json?.unlock_story) {
                    this.upRewards.push({lv: lv, minLv: minLv, stage: stage, storyId: json.unlock_story})
                }
            }
            // 剧情二
            if (preStage !== stage) {
                preStage = stage
                json = assetsMgr.getJsonData('customersStage', this.getAttrId(stage))
                if (!!json?.story) {
                    this.upRewards.push({lv: lv, minLv: minLv, stage: stage, storyId: json.story})
                }
            }
        }
        return this
    }

    public getAttrId(lv: number) {
        return this.id * 1000 + lv
    }

    public get visit_weight() {
        return this.base ? this.base.visit_weight : 0
    } //来店权重
    public get maxLv() {
        return this.stageJson ? this.stageJson.lv_limt : this.lv
    }

    public get wishId() {
        return this.stageJson ? this.stageJson.wish_id : 0
    }

    public get qua() {
        return this.base?.qua || 1
    }

    // 是否有小费
    public isHasTip() {
        return !!this.tip
    }

    public getTip() {
        return this.tip
    }
}