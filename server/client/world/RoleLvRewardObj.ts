import ut from "../../common/util"
import ConditionObj from "../common/ConditionObj"
import ModelMgr from "../ModelMgr"

// 角色奖励
export default class RoleLvRewardObj {

    public lv: number = 0
    public stage: number = 0 //阶段
    public rewardId: number = 0
    public isClaim: boolean = false //是否领取

    public list: ConditionObj[] = []

    public init(lv: number, stage: number, rewardId: number, modelMgr: ModelMgr) {
        this.lv = lv
        this.stage = stage
        this.rewardId = rewardId
        this.isClaim = false
        this.list = modelMgr.getRewardsById(this.rewardId)
        return this
    }

    public fromDB(data: any, modelMgr: ModelMgr) {
        ut.setValue('lv|stage|rewardId|isClaim', data, this)
        this.list = modelMgr.getRewardsById(this.rewardId)
        return this
    }
}