import { PropType } from "../constant/Enums";
import ModelMgr from "../ModelMgr";
import PropObj from "./PropObj";

// 背包模块
export default class BagModel {

    private props: PropObj[] = []

    private modelMgr: ModelMgr = null

    public onCreate(modelMgr: ModelMgr) {
        this.modelMgr = modelMgr
    }

    public init() {
        const ver = 2
        const data = this.modelMgr.getDB().register('bag', ver) || {}
        this.props = (data.props || []).map(m => new PropObj().fromDB(m)).filter(prop => prop != null)
        if (data._ver === 1) {
            let obj = {}
            for (let i = this.props.length - 1; i >= 0; i--) {
                const prop = this.props[i]
                const id = prop.id = Number(prop.id), o = obj[id]
                if (o) {
                    o.count += prop.count
                    this.props.splice(i, 1)
                } else {
                    obj[id] = prop
                }
            }
        }
       
    }
    public getProps() { return this.props }

    // 获取道具列表 根据类型
    public getPropsByType(type: PropType) {
        return this.props.filter(m => m.type === type)
    }

    // 获取道具列表 根据背包类型
    public getPropsByBagType(type: number) {
        return this.props.filter(m => m.bagType === type)
    }

    // 获取一个道具 根据id
    public getPropById(id: number) {
        return this.props.find(m => m.id === id)
    }
    public getPropsById(id: number) {
        return this.props.filter(m => m.id === id)
    }

    // 获取道具数量
    public getPropCount(id: number) {
        return this.getPropsById(id).reduce((val, cur) => cur.count + val, 0)
    }
}