import { PropBaseJitem } from "../constant/DataType"
import { PropType } from "../constant/Enums"
import ut from "../../common/util"
import { assetsMgr } from "../AssetsMgr"

// 一个背包道具
export default class PropObj {

    public uid: string = ''
    public id: number = 0
    public json: PropBaseJitem = null
    public attr: any = null
    public count: number = 0

    public init(id: number, count: number = 1) {
        this.uid = ut.uid()
        this.id = id
        this.count = count
        return this.initJson()
    }

    public fromDB(data: any) {
        this.uid = data.uid || ut.uid()
        this.id = Number(data.id)
        this.count = data.count ?? 1
        return this.initJson()
    }

    private initJson() {
         // base
         this.json = assetsMgr.getJsonData('itemBase', this.id)
         if (!this.json) {
             return
         }
         // attr
         if (this.type === PropType.SERVE) {
             this.attr = assetsMgr.getJsonData('serveItem', this.id)
         } else if (this.type === PropType.EXPLORE_A) {
             this.attr = assetsMgr.getJsonData('adventureAItem', this.id)
         } else if (this.type === PropType.EXPLORE_B) {
             this.attr = assetsMgr.getJsonData('adventureBItem', this.id)
         } else if (this.type === PropType.FURNITURE) {
             this.attr = assetsMgr.getJsonData('speFurnItem', this.id)
         } else if (this.type === PropType.SUMMON_PROP) {
             this.attr = assetsMgr.getJsonData('summonItem', this.id)
         }
         return this
    }

    public get type() { return this.json.type }
    public get bagType() { return this.json.bag_type }
    public get bagSType() { return this.json.bag_s_type }
    public get bagSort() { return this.json.bag_sort }
}