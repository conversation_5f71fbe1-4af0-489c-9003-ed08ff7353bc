import BaseMapModel from "../map/BaseMapModel"
import MainAttrObj from "./MainAttrObj"
import { MapSceneType, MessageType, NpcType, ShareUseType } from "../constant/Enums"
import ModelMgr from "../ModelMgr"
import WorldModel from "../world/WorldModel"
import { assetsMgr } from "../AssetsMgr"
import { gameHelper } from "../GameHelper"

/**
 * 主楼
 */
export default class MainModel extends BaseMapModel {

    private mainAttr: MainAttrObj = null

    private model: WorldModel = null

    private unlockMainSkins: number[] = [] //解锁的大楼皮肤
    private unlockWudongSkins: number[] = [] //解锁的乌冬皮肤

    private tipMoney: number = 0 //小费

    constructor(modelMgr: ModelMgr) {
        super('main', modelMgr)
        super.init(MapSceneType.MAIN)
        let dbHelper = modelMgr.getDB()
        this.model = modelMgr.get<WorldModel>('world')

        const data = dbHelper.register(this.uid) || {}
     
        this.mainAttr = data.mainAttr ? new MainAttrObj().fromDB(data.mainAttr) : new MainAttrObj().init()
        if (data._ver === 1) { // 版本兼容
            const changes = this.mainAttr.changes
            assetsMgr.getJson('hostelChange').datas.forEach(m => {
                if (m.unlock_map && this.model.isUnlockMap(m.unlock_map) && !changes.has(m.id)) {
                    changes.push(m.id)
                }
            })
        }

        this.tipMoney = Math.floor(data.tipMoney || 0)

        this.unlockMainSkins = data.unlockMainSkins || assetsMgr.getJson('hotelSkinBase').datas.filter(m => gameHelper.conditionIsNone(m.unlock_cost)).map(m => m.id)
        this.unlockWudongSkins = data.unlockWudongSkins || assetsMgr.getJson('wudongBase').datas.filter(m => gameHelper.conditionIsNone(m.unlock_cost)).map(m => m.id)
    }

    public getMainAttr() { return this.mainAttr }
    public hasWudongSkin(id: number) { return this.unlockWudongSkins.has(id) }
    public hasMainSkin(id: number) { return this.unlockMainSkins.has(id) }
    public getTipMoney() { return this.tipMoney }
}