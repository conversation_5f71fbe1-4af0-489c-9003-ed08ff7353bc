import {HostelAttrJItem, HostelChangeJItem} from "../constant/DataType"
import ConditionObj from "../common/ConditionObj"
import {assetsMgr} from "../AssetsMgr"
import ut from "../../common/util"
import {gameHelper} from "../GameHelper"

class MainAttrInfo {
    id: number
    lv: number
    json?: HostelAttrJItem
    cost: ConditionObj[]

    init(id: number, lv: number) {
        this.id = id
        this.lv = lv
        return this
    }

    fromDB(data: any) {
        return ut.setValue('id|lv', data, this)
    }

    // public isMaxLv() {
    //     return this.json && !this.json.cost
    // }

    // 是否可以升级
    // public isCanUp() {
    //     if (this.isMaxLv() || !this.cost) {
    //         return false
    //     }
    //     return gameHelper.conditionCheck(this.cost)
    // }

    public get attrId() {
        return this.id * 1000 + this.lv
    }

    public get nextAttrId() {
        return this.id * 1000 + (this.lv + 1)
    }
}

// 主楼属性
export default class MainAttrObj {

    // 双倍收益
    public comeRoleTimeAttr: MainAttrInfo = null
    // 离线收益时间
    public offlineTimeAttr: MainAttrInfo = null
    public offlineTime: number = 0 //单位小时
    // 小费上限
    public tipLimtAttr: MainAttrInfo = null

    // 当前维修情况
    public changes: number[] = []

    // private tempChangeNeeds: { id: number, needs: ConditionObj[] }[] = [] //临时记录需要修缮的条件

    public init() {
        this.comeRoleTimeAttr = new MainAttrInfo().init(1, 1)
        this.offlineTimeAttr = new MainAttrInfo().init(2, 1)
        this.tipLimtAttr = new MainAttrInfo().init(3, 0) //小费暂不解锁
        return this.initJson()
    }

    public fromDB(data: any) {
        const cr = data.comeRoleTimeAttr || data.doubleEarnAttr
        cr.lv = cr.lv || 1 //兼容
        this.comeRoleTimeAttr = new MainAttrInfo().fromDB(cr)
        this.offlineTimeAttr = new MainAttrInfo().fromDB(data.offlineTimeAttr)
        this.tipLimtAttr = new MainAttrInfo().fromDB(data.tipLimtAttr)
        this.changes = data.changes || []
        return this.initJson()
    }

    private initJson() {
        let json = this.comeRoleTimeAttr.json = assetsMgr.getJsonData('hostelAttr', this.comeRoleTimeAttr.attrId)
        this.comeRoleTimeAttr.cost = json ? gameHelper.stringToConditions(json.cost) : null
        json = this.offlineTimeAttr.json = assetsMgr.getJsonData('hostelAttr', this.offlineTimeAttr.attrId)
        if (json) {
            this.offlineTime = json.value
            this.offlineTimeAttr.cost = gameHelper.stringToConditions(json.cost)
        }
        json = this.tipLimtAttr.json = assetsMgr.getJsonData('hostelAttr', this.tipLimtAttr.attrId)
        if (json) {
            this.tipLimtAttr.cost = json ? gameHelper.stringToConditions(json.cost) : null
        }
        return this
    }
}