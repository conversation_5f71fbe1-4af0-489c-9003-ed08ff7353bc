import { assetsMgr } from "../AssetsMgr";
import { MapJsonItem, MoneyAreaInfo } from "../constant/DataType";
import { MapType, MapSceneType, ConditionType } from "../constant/Enums";
import { gameHelper } from "../GameHelper";
import ModelMgr from "../ModelMgr";
import DropMoneyObj from "../world/DropMoneyObj";

import MapAttrObj from "./MapAttrObj";


// 地图模型数据
export default class BaseMapModel {

    public uid: string = ''
    public mapType: MapType
    public sceneType: MapSceneType

    public attr: MapAttrObj = new MapAttrObj() //场景属性

    public modelMgr: ModelMgr = null

    public dropMoneys: DropMoneyObj[] = [] //地图中掉落的钱
    public dropLeaf: DropMoneyObj[] = [] //地图中掉落的树叶

    constructor(uid: string, modelMgr: ModelMgr) {
        this.uid = uid
        this.modelMgr = modelMgr
    }

    public init(sceneType: MapSceneType) {
        this.sceneType = sceneType
        this.initJson(assetsMgr.getJsonData('_map_grid', sceneType))
    }

    // 配置信息
    public initJson(json: MapJsonItem) {
        this.mapType = json.type
    }

    // 持久化数据
    public initDB() {
        const data = this.modelMgr.getDB().register(this.uid + '_map') || {}
        this.dropMoneys = (data.dropMoneys || []).map(m => new DropMoneyObj().fromDB(m))
        this.dropLeaf = (data.dropLeaf || []).map(m => new DropMoneyObj().fromDB(m))
    }

    // 获取所有设施的 消费
    public getAllBuildCost(type: ConditionType) {
        const builds = this['builds'] || []
        return builds.reduce((val, cur) => {
            cur.json && gameHelper.stringToConditions(cur.json.unlock_cost).forEach(m => (m.type === type) && (val += m.count))
            return val
        }, 0)
    }

    // 在线收入
    public getOnlineIncome() {
        return this.modelMgr.getMapAllIncome(this.attr.income, this['builds'], this.sceneType)
    }
}
