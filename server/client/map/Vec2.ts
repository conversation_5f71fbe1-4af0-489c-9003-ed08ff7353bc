export default class Vec2 {

    public x: number = 0
    public y: number = 0

    constructor(x?: number, y?: number) {
        this.x = x || 0
        this.y = y || 0
    }

    set (newValue: any): this {
        this.x = newValue.x;
        this.y = newValue.y;
        return this;
    }

    public set2(x: number, y: number): Vec2 {
        this.x = x;
        this.y = y;
        return this;
    }

    public equals2(x: number, y: number): boolean {
        return this.x === x && this.y === y;
    }

    public Join(separator: string = ','): string {

        return this.x + separator + this.y
    };

    public toVec3() {
        this['z'] = 0
        return this
    }

    public toJson() {
        return { x: this.x, y: this.y }
    }

    public FlipX() {
        let x = this.x
        this.x = this.y
        this.y = x
        return this
    }
}