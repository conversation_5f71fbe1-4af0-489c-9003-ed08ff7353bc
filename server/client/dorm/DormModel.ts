import BaseMapModel from "../map/BaseMapModel"
import WorldModel from "../world/WorldModel"
import { DormBuildType, MapSceneType, NpcType } from "../constant/Enums"
import { gameHelper } from "../GameHelper"
import LBuildObj from "./LBuildObj"
import { DORM_LEAVE_ODDS } from "../constant/Constant"
import ModelMgr from "../ModelMgr"
import { assetsMgr } from "../AssetsMgr"

/**
 * 休闲中心
 */
export default class DormModel extends BaseMapModel {

    private builds: LBuildObj[] = []

    constructor(model: ModelMgr) {
        super('dorm', model)
        super.init(MapSceneType.DORM)
        // 注册持久化数据
        let dbHelper = model.getDB()
        const data = dbHelper.register(this.uid) || {}
        // 获取所有设施
        if (data.builds) {
            this.builds = data.builds.map(m => new LBuildObj().fromDB(m))
        } else {
            this.builds = assetsMgr.getJson('leisureUnlock').datas.filter(m => gameHelper.conditionIsNone(m.unlock_cost)).map(m => new LBuildObj().init(m.id, true))
        }
        this.updateAttr()
    }

    public getBuilds() { return this.builds }
    public getUseBuilds() { return this.builds.filter(m => m.useing) }

    public getSumIncome() { return this.attr.income }
    public getSumFav() { return this.attr.fav }

    // 获取所有的收入 包括使用设施的
    public getOfflineIncome() { return this.builds.reduce((val, cur) => cur.output + val, 0) }
    // 获取所有的收入 包括使用设施的
    public getOfflineLove() { return gameHelper.getMapAllLove(this.attr.fav, this.builds, DORM_LEAVE_ODDS) }

    private updateAttr() {
        this.attr.reset()
        this.builds.forEach(m => {
            this.attr.add(m)
        })
    }

}