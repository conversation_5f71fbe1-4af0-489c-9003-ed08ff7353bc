import { assetsMgr } from "../AssetsMgr"
import { FurnitureBaseJItem, FurnitureUnlockJItem } from "../constant/DataType"
const logger = require("../../common/log").getLogger("UnlockFurnObj");

// 解锁的家具
export default class UnlockFurnObj {
    public id :string = ""
    public base: FurnitureBaseJItem = null
    public json: FurnitureUnlockJItem = null
    public select: string = '' //当前的选择

    public _output: number = 0
    public _homeOutput: number = 0
    public isGardenDiy: boolean = false
    public gardenSpeUid: string = ''

    public init(json: FurnitureUnlockJItem) {
        this.base = assetsMgr.getJsonData('roomFurnitureAttr', json.id)
        this.json = json
        this._output = this.json ? (this.json.output || 0) : 0
        this.select = this.id
        return this
    }

    // 特殊家具
    public initSpe(id: string) {
        this.base = assetsMgr.getJsonData('roomFurnitureAttr', id)
        this.select = this.id
        return this
    }
    protected initBase(id: string) {
        this.id = id
        this.base = assetsMgr.getJsonData('roomFurnitureAttr', id) || assetsMgr.getJsonData('gardenFurnitureAttr', id)
            || assetsMgr.getJsonData('flowerContainerBase', id)
    }
    protected initGardenBase(id: number) {
        this.id = 'SPE_' + id
        this.base = assetsMgr.getJsonData('flowerContainerBase', id)
    }

    public fromDB(data: any, roomId: number) {
        this.base = assetsMgr.getJsonData('roomFurnitureAttr', data.id)
        if (!this.base) {
            return null
        }
        this.json = assetsMgr.getJson('roomFurnitureUnlock_' + roomId).datas.find(m => m.id === data.id && (data.unlockType === 0 || m.type === data.unlockType))
        if (!this.json) {
            return null
        }
        this._output = this.json ? (this.json.output || 0) : 0
        this.select = (this.isHasSelect() && data.select) || this.id
        return this
    }

    public get type() { return this.base.type }
    public get output() { return this._output }

    public get unlockType() { return this.json?.type || 0 }
    public get income() { return this.json?.income || 0 }
    public get use_income() { return this.json?.use_income || 0 }
    public get heart() { return this.json?.heart || 0 }
    public get fav() { return this.json?.fav || 0 }
    public get use_fav() { return this.json?.use_fav || 0 }
    public get output_online() { return this.json ? this.json.output_online : '' }
    public get sleep_eff() { return this.json ? this.json.sleep_eff : 0 }

    public isHasSelect() {
        return !!this.json?.select
    }
}
