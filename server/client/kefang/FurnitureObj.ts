import IMapObj from "../map/IMapObj"
import {FurnitureBaseJItem} from "../constant/DataType"
import {FurnitureType, MapType} from "../constant/Enums"
import UnlockFurnObj from "./UnlockFurnObj"
import KefangMapObj from "./KefangMapObj"
import {assetsMgr} from "../AssetsMgr"
import ut from "../../common/util"

// 一个家具
export default class FurnitureObj extends IMapObj {

    public id: string = ''
    public pid: string = '' //实际的id
    public type: FurnitureType = null
    public unlockType: number = 0
    public area: KefangMapObj = null //所在区域
    public actArea: KefangMapObj = null //实际的
    public flipX: number = 1
    public place: number = 0 //摆放的位置 0.任意 1.室内 2.室外
    public standAnims: string[] = [] //待机动画
    public clickAnims: string[] = [] //点击动画
    public inteAnims: string[] = [] //交互动画
    public placeAnims: string[] = [] //摆放动画
    public clickSounds: string[] = [] //点击音效
    public placeSounds: string[] = [] //摆放音效
    public dir: number = 0 //当前方向

    public size: cc.Vec2 //家具大小
    public triggers: cc.Vec2[] = [] //触发列表
    public triggerEnds: cc.Vec2[] = [] //触发结束列表
    public isCollision: boolean = false //是否可以碰撞
    public canTrigger: boolean = false //是否可以触发

    public specialItemId: number = 0 //特殊家具道具id
    public useRoles: any = {} //使用的客人列表
    public useRoleLimit: number = 1 //使用客人上限
    public swihState: number = 0 //当前的开关状态

    public actPoints: cc.Vec2[] = []
    private tempTriggers: cc.Vec2[] = []
    private tempTriggerEnds: cc.Vec2[] = []
    private tempFlipX: number = 1
    private isUpdatePoints: boolean = false

    public init(obj: UnlockFurnObj) {
        this.id = obj.select || obj.id
        this.specialItemId = this.id.startsWith('SPE_') ? Number(this.id.replace('SPE_', '')) : 0
        this.pid = obj.id
        this.uid = ut.uid()
        this.unlockType = obj.unlockType
        this.initJson(this.id === this.pid ? obj.base : undefined)
        if (this.isCanTouch()) {
            this.point.set2(-1, -1)
        }
        return this
    }

    public initDoor(area: KefangMapObj) {
        this.id = this.pid = area.door.id
        this.uid = ut.uid()
        this.unlockType = -1
        this.initJson()
        this.setArea(area)
        this.setPoint(area.door.point)
        return this
    }

    public clone() {
        const obj = new FurnitureObj()
        obj.uid = ut.uid()
        obj.id = this.id
        obj.specialItemId = this.specialItemId
        obj.pid = this.pid
        obj.unlockType = this.unlockType
        obj.initJson()
        obj.point.set(this.point)
        obj.setArea(this.area)
        obj.flipX = this.flipX
        obj.dir = this.dir
        obj.tempFlipX = this.tempFlipX
        obj.useRoles = ut.deepClone(this.useRoles)
        obj.useRoleLimit = this.useRoleLimit
        obj.swihState = this.swihState
        return obj
    }

    public fromDB(data: any, area?: KefangMapObj) {
        if (!data) {
            return null
        }
        this.id = data.id
        this.specialItemId = this.id.startsWith('SPE_') ? Number(this.id.replace('SPE_', '')) : 0
        this.pid = data.pid || this.id
        this.uid = data.uid
        this.unlockType = data.unlockType
        if (!this.initJson()) {
            return null
        }
        this.setArea(area)
        this.setFlipX(data.flipX)
        this.dir = data.dir ?? (data.flipX === 1 ? 0 : 1)
        this.swihState = this.swihState === -1 ? -1 : (data.swihState ?? this.swihState)
        this.point.set(data.point)
        // 如果是挂件 检测一下方向
        if (this.isPendant()) {
            let x;
            if (!area) x = 1;
            else if (!area.mapType) x = 1;
            else if (area.mapType === MapType.SKEW_L) x = -1
            else x = 1;
            if (x !== this.flipX) {
                this.initFlipXAndDir(x)
            }
        }
        return this
    }

    public toDB(isOb?: boolean) {
        if (!isOb && this.point.equals2(-1, -1)) {
            return undefined
        }
        return {
            id: this.id,
            pid: this.pid,
            uid: this.uid,
            unlockType: this.unlockType,
            point: this.point.toJson(),
            flipX: this.flipX,
            dir: this.dir,
            areaId: this.actArea.id,
            swihState: this.swihState === -1 ? undefined : this.swihState,
        }
    }

    public getObMap() {
        return {areaId: 'actArea'}
    }

    private initJson(base?: FurnitureBaseJItem) {
        base = base || assetsMgr.getJsonData('roomFurnitureAttr', this.id)
        if (!base) {
            return false
        }
        this.type = base.type
        this.isCollision = !!base.collision
        this.useRoleLimit = base.use_count
        this.place = base.place
        this.swihState = typeof (base.swih) === 'number' ? base.swih : -1
        //
        // 初始化zindex
        if (this.isFloor()) {
            this.zIndex = -32000
        } else if (this.isDoor()) {
            this.zIndex = -31100
        } else if (this.isCarpet()) {
            this.zIndex = -31000
        } else {
            this.zIndex = -30000
        }
        this.isUpdatePoints = false
        return true
    }

    // 比较id
    public equalsId(id: string) {
        return this.pid === id || this.id === id
    }

    // 是否地面 地板或者地毯
    public isGround() {
        return this.isFloor() || this.isCarpet()
    }

    // 是否门口
    public isDoor() {
        return this.type === FurnitureType.DOOR
    }

    // 是否楼梯
    public isStairs() {
        return this.type === FurnitureType.STAIRS
    }

    // 是否地板
    public isFloor() {
        return this.type === FurnitureType.FLOOR
    }

    // 是否地毯
    public isCarpet() {
        return this.type === FurnitureType.CARPET
    }

    // 是否摆件
    public isDecorate() {
        return this.type === FurnitureType.DECORATE
    }

    // 是否挂件
    public isPendant() {
        return this.type === FurnitureType.PENDANT
    }

    // 是否桌子
    public isTable() {
        return this.type === FurnitureType.TABLE
    }

    // 是否特殊地毯 上面可以走 但是不是地毯类型
    public isSpeCarpet() {
        return this.type === FurnitureType.HOPSCOTCH || this.type === FurnitureType.DANCE_MAT
    }

    // 是否可以交互
    public isInteraction() {
        return this.triggers.length > 0
    }

    // 是否有交互结束点
    public isHasInteractionEndPoint() {
        return this.tempTriggerEnds.length > 0
    }

    // 是否站位
    public isHasPoints() {
        return this.points.length > 0
    }

    // 是否可以触摸
    public isCanTouch() {
        return this.type < FurnitureType.WALLPAPER
    }

    // 是否特殊家具
    public isSpecialItem() {
        return !!this.specialItemId
    }

    // 是否有开关
    public isHasSwih() {
        return this.swihState !== -1
    }

    // 切换开关
    public changeSwih() {
        if (this.swihState === -1) {
            return false
        } else if (this.swihState === 0) {
            this.swihState = 1
        } else {
            this.swihState = 0
        }
        return !!this.swihState
    }

    public setArea(area: KefangMapObj) {
        this.area = this.actArea = area
        return this
    }

    public setPoint(point: cc.Vec2) {
        this.point.set(point)
        return this
    }

    public setFlipX(flipX: number) {
        this.flipX = flipX
        if (this.flipX !== this.tempFlipX) {
            this.flipAllPoints()
        }
        return this
    }

    public initFlipXAndDir(flipX: number) {
        this.setFlipX(flipX)
        this.dir = flipX === 1 ? 0 : 1
        return this
    }

    // 根据翻转 刷新点
    public flipAllPoints() {
        this.tempFlipX *= -1
        if (!this.isPendant()) { //挂件不需要翻转
            this.points.forEach(m => m.FlipX())
            this.triggers.forEach(m => m.FlipX())
            this.triggerEnds.forEach(m => m.FlipX())
        }
        this.isUpdatePoints = false //重新刷新
    }


    // 有客人使用中
    public get roleUseing() {
        for (let key in this.useRoles) {
            if (this.useRoles[key] === 2) {
                return true
            }
        }
        return false
    }

    // 是否可以使用
    public isCanUse() {
        return Object.keys(this.useRoles).length < this.useRoleLimit
    }

    // 添加使用客人
    public addUseRole(uid: string) {
        this.useRoles[uid] = 1
    }

    // 开始使用
    public startUse(uid: string) {
        this.useRoles[uid] = 2
    }

    // 删除使用客人
    public removeUseRole(uid: string) {
        delete this.useRoles[uid]
    }
}