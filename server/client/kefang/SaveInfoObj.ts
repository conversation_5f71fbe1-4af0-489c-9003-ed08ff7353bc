import FurnitureObj from "./FurnitureObj"

// 一个保存方案
export default class SaveInfoObj {

    public id: number = 0
    public name: string = '' //方案名字
    public furnitures: FurnitureObj[] = [] //摆放的家具列表

    public no: number = 0 //房号

    public init(id: number, no: number) {
        this.id = id
        this.no = no
        return this
    }

    public fromDB(data: any, no: number) {
        this.id = data.id
        this.name = data.name || ''
        this.no = no
        // this.furnitures //在外部单独赋值
        return this
    }

    public toDB() {
        return { id: this.id, name: this.name, furnitures: this.furnitures.map(m => m.toDB()) }
    }
}