import { MapType } from "../constant/Enums"
import ut from "../../common/util"
import { MapJsonItem } from "../constant/DataType"
import Vec2 from "../map/Vec2"

// 客房地图信息s
export default class KefangMapObj {

    public uid: string = ''
    public id: number = 0
    public mapType: MapType
    public mapOrigin: cc.Vec2 = new Vec2() //地图原点
    // public gridSize: cc.Size = cc.size(0, 0) //格子大小
    // public mapSize: cc.Size = cc.size(0, 0) //实际大小
    public mapPoints: cc.Vec2[] = [] //地图所有点
    public points: cc.Vec2[] = [] //多边形点
    public walls: any = {} //障碍列表
    public spawn: cc.Vec2[] = []
    public door: { id: string, point: cc.Vec2 } = null
    public ingrPoints: cc.Vec2[] = [] //室内点
    public outgrPoints: cc.Vec2[] = [] //室外点
    public stairs: cc.Vec2[] = [] //楼梯位置
    public maxGridSize: number = 0

    public groundPoints: cc.Vec2[] = [] //临时地面点
    public canMoveGroundPoints: any = {} //可移动的地面点
    // private tempVec: cc.Vec2 = cc.v2()

    public init(json: MapJsonItem) {
        this.uid = ut.uid()
        this.id = json.id
        this.mapType = json.type
        // this.mapOrigin.set2(json.origin.x, json.origin.y)
        // this.gridSize.width = json.w
        // this.gridSize.height = json.h
        // this.mapSize = mapHelper.convertMapSize(this.mapType, this.gridSize)
        // this.points = mapHelper.getMapPoints(this.mapType, this.gridSize, this.mapSize)
        // // 墙和障碍
        // json.walls?.forEach(m => this.walls[m.x + '_' + m.y] = true)
        // json.obstacle?.forEach(m => mapHelper.genPointsBySize(ut.stringToVec2(m.size, 'x'), ut.stringToVec2(m.point)).forEach(p => this.walls[p.x + '_' + p.y] = true))
        // // 出生点
        // this.spawn = json.spawn ? json.spawn.map(m => cc.v2(m)) : []
        // this.spawn.forEach(p => p['areaId'] = this.id)
        // // 门口
        // this.door = json.door ? { id: json.door.id, point: ut.stringToVec2(json.door.point) } : null
        // // 户外
        // this.outgrPoints = json.outgr ? mapHelper.genPointsBySize(ut.stringToVec2(json.outgr.size, 'x'), ut.stringToVec2(json.outgr.point)) : []
        // // 楼梯
        // this.stairs = json.stairs ? json.stairs.map(m => cc.v2(m)) : []
        // // 
        // this.maxGridSize = this.gridSize.width + this.gridSize.height
        // this.mapPoints = mapHelper.genPointsBySize(cc.v2(json.w, json.h))
        // this.ingrPoints.length = 0
        // this.mapPoints.forEach(p => {
        //     p['areaId'] = this.id
        //     if (!this.outgrPoints.some(m => m.equals(p))) {
        //         this.ingrPoints.push(p)
        //     }
        // })
        return this
    }

    public randomSpawn() {
        return this.spawn.random()
    }

    public randomSpawnByUid(uid: number) {
        return this.spawn[uid % this.spawn.length]
    }

    public getGroundPoints() {
        return this.groundPoints
    }
}