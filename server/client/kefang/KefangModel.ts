import BaseMapModel from "../map/BaseMapModel"
import WorldModel from "../world/WorldModel"
import {ConditionType, FurnitureType, MapSceneType, MapType, NpcType} from "../constant/Enums"
import {
    FurnitureUnlockJItem,
    FurnTypeConfigJItem,
    KefangJsonItem,
    MapJsonItem,
    RoomAttrJItem
} from "../constant/DataType"
import UnlockFurnObj from "./UnlockFurnObj"
import {gameHelper} from "../GameHelper"
import {ROOM_LEAVE_ODDS} from "../constant/Constant"
import ModelMgr from "../ModelMgr"
import {assetsMgr} from "../AssetsMgr"
import {COMPAT_UNLOCK_WIND, WIND_CHANGE_HEI_WIND, WIND_CHANGE_HEI_WIND_402} from "../constant/DBBCompat"
import SaveInfoObj from "./SaveInfoObj"
import FurnitureObj from "./FurnitureObj"
import ut from "../../common/util"
import KefangMapObj from "./KefangMapObj"

/**
 * 客房
 */
export default class KefangModel extends BaseMapModel {

    private roomId: number = 0
    private no: number = 0 //房号
    private unlockFurns: UnlockFurnObj[] = [] //当前解锁的家具
    private roomAttr: RoomAttrJItem = null //当前房间属性配置
    private furnTypes: FurnTypeConfigJItem[] = [] //家具类型列表
    private furnSaves: SaveInfoObj[] = [] //家具保存方案
    private areas: KefangMapObj[] = [] //区域列表
    private currFurnitureInfoId: number = 0 //当前方案
    private currFurnitureInfo: SaveInfoObj = null //当前方案

    private tempCollisionPoints: any = {} //参与碰撞的点 除开墙以外的 用于astar检测
    private mainArea: KefangMapObj = null//主区域

    constructor(no: number, roomId?: number, modelMgr?: ModelMgr) {
        super('kefang_' + no, modelMgr)
        this.no = no
        this.sceneType = MapSceneType.KEFANG
        this.roomAttr = assetsMgr.getJsonData('roomAttr', this.no)
        // 持久化数据
        this.initDB()
        const ver = 4
        let dbHelper = modelMgr.getDB()
        const data = dbHelper.register(this.uid) || {}
        this.roomId = data.roomId || roomId || this.roomAttr.cap_lv
        this.furnTypes = assetsMgr.getJson('roomTypeConfig').get('room_id', this.roomId)

        this.initMaps()

        // 初始化家具
        const hasUnlockFurn = data.unlockFurns && data.unlockFurns.length > 0,
            hasFurnitures = data.furnitures && data.furnitures.length > 0
        while (data._ver < ver) {
            // 这里将202和302的窗户换成黑夜
            if (data._ver === 2 && (this.no === 202 || this.no === 302)) {
                const c = WIND_CHANGE_HEI_WIND
                hasUnlockFurn && data.unlockFurns.forEach(m => {
                    m.id = c[m.id] || m.id
                    m.select = c[m.select] || m.select
                })
                hasFurnitures && data.furnitures.forEach(m => {
                    if (m) {
                        m.pid = c[m.pid] || m.pid
                        m.id = c[m.id] || m.id
                    }
                })
            } else if (data._ver === 3 && data.unlockFurns) {
                const arr = [], ids = []
                data.unlockFurns.forEach(m => {
                    const id = COMPAT_UNLOCK_WIND[m.id]
                    if (id) {
                        arr.push({id: id, unlockType: m.unlockType})
                        ids.push(id, m.id)
                    }
                })
                data.unlockFurns.pushArr(arr)
                // 取下对应的窗户
                data.furnSaves?.forEach(info => {
                    for (let i = info.furnitures.length - 1; i >= 0; i--) {
                        if (info.furnitures[i] && ids.has(info.furnitures[i].id)) {
                            info.furnitures.splice(i, 1)
                        }
                    }
                })
            } else if (data._ver === 4 && data.furnSaves) { //兼容没有墙面的
                const info = data.furnSaves.find(m => m.id === data.currFurnitureInfoId) || data.furnSaves[0]
                const furnitures: any[] = info?.furnitures || []
                if (!furnitures.some(m => m.id?.startsWith('WALLPAPER_'))) { //没有墙面
                    const furnitures1 = []
                    data.unlockFurns.forEach(uf => {
                        if (!furnitures.has('id', uf.id)) {
                            const m = new UnlockFurnObj().fromDB(uf, this.roomId)
                            m && this.addfurnituresByDB(furnitures1, m)
                        }
                    })
                    furnitures.pushArr(furnitures1.map(m => m.toDB()))
                    info.furnitures = furnitures
                }
            } else if (data._ver === 5) {
                if (this.no === 402) { //兼容402 窗户配置问题 白天版换成黑夜版
                    const c = WIND_CHANGE_HEI_WIND_402
                    hasUnlockFurn && data.unlockFurns.forEach(m => {
                        m.id = c[m.id] || m.id
                        m.select = c[m.select] || m.select
                    })
                    data.furnSaves?.forEach(m => m.furnitures?.forEach(m => {
                        if (m) {
                            m.pid = c[m.pid] || m.pid
                            m.id = c[m.id] || m.id
                        }
                    }))
                }
                data.furnSaves?.forEach(m => m.furnitures?.delete(m => m?.id === 'SPE_8201')) //理想节日 家具从2x2改成4x4 所以删掉 在后面world里面会补到背包
            }
            data._ver += 1
        }
        const unlocks: FurnitureUnlockJItem[] = !hasUnlockFurn ? assetsMgr.getJson('roomFurnitureUnlock_' + this.roomId).datas.filter(m => gameHelper.conditionIsNone(m.unlock_cost)) : undefined
        this.unlockFurns.length = 0
        if (hasUnlockFurn) {
            data.unlockFurns.map(m => {
                const obj = new UnlockFurnObj().fromDB(m, this.roomId)
                obj && this.unlockFurns.push(obj)
            })
        } else {
            this.unlockFurns = unlocks.map(m => new UnlockFurnObj().init(m))
        }

        // 保存方案
        if (data.furnSaves) {
            this.furnSaves = data.furnSaves.map(info => {
                const obj = new SaveInfoObj().fromDB(info, this.no)
                obj.furnitures = info.furnitures.map(m => new FurnitureObj().fromDB(m, this.getAreaById(m?.areaId))).filter(m => !!m)
                this.checkHasDoor(obj.furnitures)
                return obj
            })
        } else {
            const json = assetsMgr.getJson('kefangFurnSave').get('no', this.no).filter(m => gameHelper.conditionIsNone(m.unlock_cost))[0] || {id: this.no * 1000 + 1}
            const obj = new SaveInfoObj().init(json.id, this.no)
            if (hasFurnitures) { //如果上次存档还有 就用这套设置为默认方案
                obj.furnitures = data.furnitures.map(m => new FurnitureObj().fromDB(m, this.getAreaById(m?.areaId))).filter(m => !!m)
            } else { //还是没有就创建一个默认的
                const furnitures = []
                const unlockTypes = {} //同一个解锁类型只显示一个
                this.unlockFurns.forEach(m => {
                    if (!unlockTypes[m.unlockType]) {
                        unlockTypes[m.unlockType] = true
                        this.addfurnituresByDB(furnitures, m)
                    }
                })
                obj.furnitures = furnitures
            }
            this.checkHasDoor(obj.furnitures)
            this.furnSaves = [obj]
        }
        this.currFurnitureInfoId = data.currFurnitureInfoId || this.furnSaves[0]?.id
        this.currFurnitureInfo = this.furnSaves.find(m => m.id === this.currFurnitureInfoId) || this.furnSaves[0]
        // 刷新属性
        this.updateAttr()
    }

    private initMaps() {
        this.tempCollisionPoints = {}
        const conf: KefangJsonItem = assetsMgr.getJsonData('_kefang_cap', this.getRoomId())
        conf && conf.maps.split(',').forEach((m) => {
            const json: MapJsonItem = assetsMgr.getJsonData('_map_grid', Number(m))
            const area = this.areas.add(new KefangMapObj().init(json))
            if (json.spawn) {
                this.mainArea = area
                this.initJson(json)
            }
            this.tempCollisionPoints[area.uid] = {}
        })
    }

    // 获取收入
    public getSumIncome() {
        return this.attr.income
    }

    // 获取总的好感度
    public getSumFav() {
        return this.attr.fav
    }

    // 获取所有的收入 包括使用设施的
    public getOfflineIncome() {
        return this.unlockFurns.reduce((val, cur) => cur.output + val, 0)
    }

    // 获取所有的收入 包括使用设施的
    public getOfflineLove() {
        return gameHelper.getMapAllLove(this.attr.fav, this.unlockFurns, ROOM_LEAVE_ODDS)
    }

    public getRoomId() {
        return this.roomId
    }

    // 刷新家具属性
    public updateAttr() {
        this.attr.reset()
        this.attr.add(this.roomAttr)
        this.unlockFurns.forEach(m => {
            this.attr.add(m)
        })
    }

    // 获取所有设施的 消费
    public getAllBuildCost(type: ConditionType) {
        return this.unlockFurns.reduce((val, cur) => {
            cur.json && gameHelper.stringToConditions(cur.json.unlock_cost).forEach(m => (m.type === type) && (val += m.count))
            return val
        }, 0)
    }

    public get builds() {
        return this.unlockFurns
    }

    public getSpecFurnitures() {
        let specFurnitures = []
        for (let info of this.furnSaves) {
            let furnitures = info.furnitures
            specFurnitures.pushArr(furnitures.filter(f => f.isSpecialItem()))
        }
        return specFurnitures
    }

    private addfurnituresByDB(furnitures: any[], m: UnlockFurnObj) {
        const conf = this.getFurnTypeData(m.unlockType)
        if (!conf) {
            return
        }
        const has = furnitures.some(x => x.unlockType === m.unlockType)
        if (conf.placemen === 2) {
            !has && furnitures.push(this.newFurnitureByConf(m, conf)) //直接放入
        } else if (conf.pos && (conf.placemen === 1 || !has)) {
            furnitures.push(this.newFurnitureByConf(m, conf))
        }
    }

    // 获取类型数据
    public getFurnTypeData(type: number) {
        const id = this.roomId * 10000 + type
        return this.furnTypes.find(m => m.id === id)
    }

    public newFurnitureByConf(obj: UnlockFurnObj, conf: FurnTypeConfigJItem) {
        const data = new FurnitureObj().init(obj).initFlipXAndDir(1)
        const area = conf.area ? this.areas.find(m => conf.area === m.id) : this.getAreaByFurn(data)
        data.setArea(area)
        conf.pos && data.setPoint(ut.stringToVec2(conf.pos))
        return data
    }

    // 检测是否有门口
    private checkHasDoor(furnitures: FurnitureObj[]) {
        if (this.mainArea && this.mainArea.door && !furnitures.some(m => m.isDoor())) {
            furnitures.push(new FurnitureObj().initDoor(this.mainArea)) //门口
        }
    }

    public getAreaById(id: number) {
        return this.areas.find(m => m.id === id)
    }

    // 获取一个区域
    private getAreaByFurn(data: FurnitureObj) {
        let type = MapType.SKEW
        if (data.isPendant()) {
            type = data.flipX === -1 ? MapType.SKEW_L : MapType.SKEW_R
        }
        return this.areas.filter(m => m.mapType === type).random()
    }
}

