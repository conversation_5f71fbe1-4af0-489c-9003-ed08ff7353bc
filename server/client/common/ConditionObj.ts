import ut from "../../common/util";
import { ConditionType } from "../constant/Enums";

// 通用条件信息
// 这个只能通过配置表 创建出来
export default class ConditionObj {

    public type: ConditionType
    public id: any
    private _count: number = 0
    private _randomCount: number[] = []
    public tag: string = '' //标记 不会存档 临时使用

    public fromString(str: string) {
        if (!str) {
            return null
        }
        const [type, id, count, tag] = str.split(',')
        this.type = Number(type)
        const num = Number(id)
        this.id = isNaN(num) ? id : num
        this.tag = tag ?? ''
        // 是否随机数量
        if (count.indexOf('_') !== -1) {
            this._randomCount = ut.stringToNumbers(count, '_')
        } else {
            this._randomCount.length = 0
            this._count = Number(count)
        }
        return this
    }

    public init(type: ConditionType, id?: any, count?: number) {
        this.type = type
        this.id = id || -1
        this._count = Math.floor(count || 0)
        return this
    }

    public clone() {
        const obj = new ConditionObj()
        obj.type = this.type
        obj.id = this.id
        obj.count = this.count
        obj.tag = this.tag
        obj._randomCount = this._randomCount.slice()
        return obj
    }

    public fromDB(data: any) {
        this.type = data.type
        this.id = data.id || -1
        this.count = data.count || 0
        return this
    }

    public toDB() {
        return { type: this.type, id: this.id === -1 ? undefined : this.id, count: this.count }
    }

    public getObMap() {
        return { count: '_count' }
    }

    public get count() {
        if (this._randomCount.length === 2) {
            return ut.random(this._randomCount[0], this._randomCount[1])
        }
        return this._count
    }

    public set count(val: number) {
        this._count = val
    }

    // 随机数量
    public randomCount(clean: boolean = true) {
        if (this._randomCount.length === 2) {
            this._count = ut.random(this._randomCount[0], this._randomCount[1])
        }
        if (clean) {
            this._randomCount.length = 0
        }
    }
}