/**
 * 全局事件（全大写单词间用下划线隔开）
 */
export default {
    MAP_TOUCH_START: 'MAP_TOUCH_START', //地图触摸开始
    MAP_TOUCH_END: 'MAP_TOUCH_END', //地图触摸结束

    APP_LOAINDG_ALL: 'APP_LOAINDG_ALL', //开场剧情基本完成，可以加载会卡顿的资源了
    GUIDE_OPENING_COMPLETE: 'GUIDE_OPENING_COMPLETE', //开场剧情完成
    EVENT_NOT_DELAY_INIT: 'EVENT_NOT_DELAY_INIT', //完成其他初始化
    CERTIFICATION_COMPLETE: 'CERTIFICATION_COMPLETE', //实名认证完成

    UPDATE_GM_OFFSET_TIME: 'UPDATE_GM_OFFSET_TIME', //刷新gm偏移时间
    UPDATE_LAYOUT_HEIGHT: 'UPDATE_LAYOUT_HEIGHT', //刷新手动布局高度
    UPDATE_USER_BASE_INFO: 'UPDATE_USER_BASE_INFO', //刷新玩家昵称和头像信息
    UPDATE_INVITE_COUNT: 'UPDATE_INVITE_COUNT', //刷新邀请积分数量

    UPDATE_BISCUITS: 'UPDATE_BISCUITS', //刷新饼干
    UPDATE_CANDIES: 'UPDATE_CANDIES', //刷新糖果
    UPDATE_HEART: 'UPDATE_HEART', //刷新心心 用于UI显示
    EVENT_HEART_CHANGE: 'EVENT_HEART_CHANGE', //蜡烛发生变化 实际发生变化就会通知
    UPDATE_WINDMILL: 'UPDATE_WINDMILL', //刷新风车
    UPDATE_SCISSOR: 'UPDATE_SCISSOR', //刷新剪刀
    UPDATE_AD_SKIP_CARD: 'UPDATE_AD_SKIP_CARD', //刷新广告跳过卡
    UPDATE_STAMINA: 'UPDATE_STAMINA', // 刷新体力
    GET_UI_TOP_LAYER: 'GET_UI_TOP_LAYER', //获取顶层
    GET_UI_MONEY_ICON: 'GET_UI_MONEY_ICON', //获取饼干节点
    GET_NOT_TOP_LAYER: 'GET_NOT_TOP_LAYER', //获取NOT顶层节点
    SHOW_ACHIEVE_TIP: 'SHOW_ACHIEVE_TIP', //显示成就提示
    SHOW_CAN_UNLOCK_ROLE_TIP: 'SHOW_CAN_UNLOCK_ROLE_TIP', //显示客人可以解锁的提示
    SHOW_ROLE_AD_TIP: 'SHOW_ROLE_AD_TIP', //显示双倍客人提示
    UPDATE_PROP_COUNT: 'UPDATE_PROP_COUNT', //刷新道具数量
    BUY_PORP: 'BUY_PORP', //购买道具
    UPDATE_SUM_PEOPLE: 'UPDATE_SUM_PEOPLE', //刷新人数
    SHOW_OFFLINE_PNL: 'SHOW_OFFLINE_PNL', //显示离线UI
    ADD_MESSAGE: 'ADD_MESSAGE', //添加消息
    DEL_MESSAGE: 'DEL_MESSAGE', //删除消息
    PLAY_LIHUA: 'PLAY_LIHUA', //播放礼花
    PLAY_GALI_AIRCRAFT: 'PLAY_GALI_AIRCRAFT', //播放咖喱飞机飞过效果
    UNLOCK_SCENE: 'UNLOCK_SCENE', //解锁场景
    SCENE_OUTPUT: 'SCENE_OUTPUT', //场景产出
    UPDATE_GUIDE_TASK: 'UPDATE_GUIDE_TASK', //刷新新手任务
    SHARE_NPC_COMPLETE: 'SHARE_NPC_COMPLETE', //分享npc完成
    SHARE_ROLE_COMPLETE: 'SHARE_ROLE_COMPLETE', //分享role完成
    UPDATE_AD_SHOW_BUTTON: 'UPDATE_AD_SHOW_BUTTON', //刷新显示广告按钮
    UPDATE_DOUBLE_ROLE_INFO: 'UPDATE_DOUBLE_ROLE_INFO', //更新双倍收入显示
    BUY_HEAD_COMPLETE: 'BUY_HEAD_COMPLETE', //购买头像完成
    UPDATE_REDDOT_NEW: 'UPDATE_REDDOT_NEW',

    ROLE_ENTER_SCENE: 'ROLE_ENTER_SCENE', //角色进入场景
    ROLE_LEAVE_SCENE: 'ROLE_LEAVE_SCENE', //角色离开场景
    NPC_ENTER_SCENE: 'NPC_ENTER_SCENE', //npc进入场景
    NPC_LEAVE_SCENE: 'NPC_LEAVE_SCENE', //npc离开场景
    ROLE_ENTER_ELEVATOR: 'ROLE_ENTER_ELEVATOR', //角色进入电梯
    ROLE_LEAVE_ELEVATOR: 'ROLE_LEAVE_ELEVATOR', //角色离开电梯
    CHANGE_ELEVATOR_DOOR: 'CHANGE_ELEVATOR_DOOR', //切换电梯门的开关
    ROLE_THROW_MONEY: 'ROLE_THROW_MONEY', //角色扔钱
    ROLE_DROP_MONEY: 'ROLE_DROP_MONEY', //角色掉落钱
    STEAL_DROP_MONEY: 'STEAL_DROP_MONEY', //偷掉掉落钱
    ROLE_COLLECT_DROP_MONEY: 'ROLE_COLLECT_DROP_MONEY', //拾取掉落钱
    ADD_ROLE_FAVORABILITY: 'ADD_ROLE_FAVORABILITY', //客人添加好感度
    ROLE_FLUTTER_BISCUITS: 'ROLE_FLUTTER_BISCUITS', //客人添加饼干
    ROLE_FLUTTER_STAMINA: 'ROLE_FLUTTER_STAMINA', //添加体力
    SHOW_FLUTTER_MONEY: 'SHOW_FLUTTER_MONEY', //显示飘的钱数
    CLEAN_SCENE_EFFECT: 'CLEAN_SCENE_EFFECT', //清理场景特效
    REQ_SIT_POSITION: 'REQ_SIT_POSITION', //请求获取坐的位置
    REQ_SIT_NODE: 'REQ_SIT_NODE', //请求获取坐的节点
    ROLE_USE_BUILD: 'ROLE_USE_BUILD', //客人使用设施
    REQ_HOPSCOTCH_INFO: 'REQ_HOPSCOTCH_INFO', //请求跳房子的信息
    REQ_HUATI_INFO: 'REQ_HUATI_INFO', //请求滑梯的信息
    REQ_HAMMOCK_INFO: 'REQ_HAMMOCK_INFO', //请求吊床信息
    REQ_FURNITURE_INFO: 'REQ_FURNITURE_INFO', //请求家具信息
    ADD_SWEEP_KEFANG_PAIZI: 'ADD_SWEEP_KEFANG_PAIZI', //添加打扫客房的牌子效果
    GET_EXC_STAFFS: 'GET_EXC_STAFFS', //获取专属员工

    UNLOCK_WUDONG_SKIN: 'UNLOCK_WUDONG_SKIN', //解锁乌冬皮肤
    CHANGE_WUDONG_SKIN: 'CHANGE_WUDONG_SKIN', //切换乌冬皮肤
    UNLOCK_MAIN_SKIN: 'UNLOCK_MAIN_SKIN', //解锁大楼皮肤
    CHANGE_MAIN_SKIN: 'CHANGE_MAIN_SKIN', //切换大楼皮肤
    UPDATE_MAIN_SKIN_INFO: 'UPDATE_MAIN_SKIN_INFO', //刷新大楼皮肤信息
    UPDATE_WUDONG_SKIN_INFO: 'UPDATE_WUDONG_SKIN_INFO', //刷新乌冬皮肤信息
    PLAY_WUDONG_TOPUP_EFF: 'PLAY_WUDONG_TOPUP_EFF', //播放乌冬增加体力反馈特效
    PLAY_NAXINGLI_EFFECT: 'PLAY_NAXINGLI_EFFECT', //播放拿行李特效
    PLAY_CHAT_DENG: 'PLAY_CHAT_DENG', //播放询问过后的灯

    CHANGE_MAP_EDIT: 'CHANGE_MAP_EDIT',// 显示和隐藏地图编辑器
    CHANGE_SCREEN_UI: 'CHANGE_SCREEN_UI', //显示和隐藏屏幕UI

    CHANGE_MAP_TOUCH: 'CHANGE_MAP_TOUCH', //切换地图触摸
    LONG_PRESS_FURNITURE: 'LONG_PRESS_FURNITURE', //长按 选中
    CLICK_FURNITURE: 'CLICK_FURNITURE', //点击家具
    MOVE_FURNITURE: 'MOVE_FURNITURE', //移动家具
    ADD_FURNITURE: 'ADD_FURNITURE', //直接添加家具到场景
    MAP_ADD_FURNITURE: 'MAP_ADD_FURNITURE', //添加家具
    ADD_FURNITURE_CANCEL: 'ADD_FURNITURE_CANCEL', //添加家具 取消
    ADD_FURNITURE_COMPLETE: 'ADD_FURNITURE_COMPLETE', //添加家具完成
    RESET_FURNITURE: 'RESET_FURNITURE', //重置家具
    CHANGE_FURN_SAVE: 'CHANGE_FURN_SAVE', //切换家具保存方案
    ITEM_TOUCH_MOVE_EVENT: 'ITEM_TOUCH_MOVE_EVENT', //添加家具 这里移动事件
    UNLOCK_FURN: 'UNLOCK_FURN', //解锁家具
    DEL_FURNITURE: 'DEL_FURNITURE', //从场景删除家具
    REQ_BED_NODE: 'REQ_BED_NODE', //获取床的节点
    SHOW_EDITFURN_JIANTOU: 'SHOW_EDITFURN_JIANTOU', //显示编辑家具的箭头
    RESET_PREVIEW_KEFANG: 'RESET_PREVIEW_KEFANG', //重置预览客房
    WAIT_PREVIEW_KEFANG_CLOSE: 'WAIT_PREVIEW_KEFANG_CLOSE', //等待预览客房关闭

    UNLOCK_FOOD: 'UNLOCK_FOOD', //解锁菜品
    UPGRADE_FOOD: 'UPGRADE_FOOD', //升级菜品
    START_COOK: 'START_COOK', //开始做菜
    UNLOCK_DINING_BUILD: 'UNLOCK_DINING_BUILD',
    CHANGE_DINING_BUILD: 'CHANGE_DINING_BUILD', //切换餐厅的设施
    UPDATE_DESSERT_STOCK: 'UPDATE_DESSERT_STOCK', //刷新甜点的库存
    DINING_ORDER_FOOD: 'DINING_ORDER_FOOD', //餐厅点菜
    DINING_SHOW_DISH: 'DINING_SHOW_DISH', //上菜
    UPDATE_FOODMINING_COUNT: 'UPDATE_FOODMINING_COUNT', //刷新挖掘数量
    UPDATE_FOOD_STAMINA: 'UPDATE_FOOD_STAMINA', // 刷新菜谱挖掘专用体力
    SHOW_TECH_ANI: 'SHOW_TECH_ANI', // 显示解锁升级消耗动画
    ADD_FOODMINING_PLAY_LIST: 'ADD_FOODMINING_PLAY_LIST', // 添加一个动画队列
    PLAY_MINING_GEM_ANI: 'PLAY_MINING_GEM_ANI', // 播放一个挖掘元素动画
    RESET_ALL_MINING_GEM: 'RESET_ALL_MINING_GEM', // 重置所有元素属性
    SHOW_FOOD_REDDOT: 'SHOW_FOOD_REDDOT', // 菜谱外部红点显示

    UNLOCK_DRINK: 'UNLOCK_DRINK', // 解锁饮品
    UPGRADE_DRINK: 'UPGRADE_DRINK', // 升级饮品

    UNLOCK_SHOWER_BUILD: 'UNLOCK_SHOWER_BUILD', //解锁浴池设施
    CHANGE_SHOWER_BUILD: 'CHANGE_SHOWER_BUILD', //切换浴室的设施
    UPDATE_BUILD_DUR: 'UPDATE_BUILD_DUR', //刷新设施耐久
    USE_SHOWER_BUILD_BEGIN: 'USE_SHOWER_BUILD_BEGIN', //使用设施开始
    REQ_BATH_NODE: 'REQ_BATH_NODE', //获取泡澡的节点
    UPDATE_HOTSPR_SOUPBASE: 'UPDATE_HOTSPR_SOUPBASE', //刷新温泉汤料信息
    UPDATE_SOUP_STAMINA: 'UPDATE_SOUP_STAMINA', // 更新泡泡体力
    CREATE_COMP_STUFF: 'CREATE_COMP_STUFF', // 生成合成材料
    UPDATE_COMP_STUFF_PROFICIENCY: 'UPDATE_COMP_STUFF_PROFICIENCY', // 刷新合成熟练度
    OPEN_TODAY_COMP: 'OPEN_TODAY_COMP', // 开放今日特典
    UPDATE_TODAY_COMP: 'UPDATE_TODAY_COMP', // 刷新今日特典
    UPGRADE_SOUP: 'UPGRADE_SOUP', // 升级一个汤料
    UNLOCK_SOUP: 'UNLOCK_SOUP', // 解锁一个汤料
    SHOW_SOUP_REDDOT: 'SHOW_SOUP_REDDOT', // 汤料外部红点显示
    SOUP_STUFF_COMP_END: 'SOUP_STUFF_COMP_END', // 汤料合成材料升级完成
    AUTO_STUFF: 'AUTO_STUFF', // 自动材料
    SHOW_STUFF_WEAK_GUIDE: 'SHOW_STUFF_WEAK_GUIDE', // 可合成材料若引导
    UPDATE_SOUP_STUFF_INFO: 'UPDATE_SOUP_STUFF_INFO', // 更新一个材料
    SHOW_SOUP_TMP_STUFF: 'SHOW_SOUP_TMP_STUFF', // 显示临时的图标
    SHOW_STUFF_LIGHT_EFFECT: 'SHOW_STUFF_LIGHT_EFFECT', // 显示材料匹配光效
    DRAG_SOUP_STUFF_END: 'DRAG_SOUP_STUFF_END', // 温泉材料合成
    USE_SOUP_STUFF_PROP: 'USE_SOUP_STUFF_PROP', // 使用温泉材料道具
    SHOW_SOUP_TREASURE_MAP: 'SHOW_SOUP_TREASURE_MAP', // 展开藏宝图
    SHOW_SOUP_STAMINA_ANI: 'SHOW_SOUP_STAMINA_ANI', // 加体力动画
    ADD_TREASURE_MAP_STUFF: 'ADD_TREASURE_MAP_STUFF', // 道具放入藏宝图动画

    UNLOCK_DORM_BUILD: 'UNLOCK_DORM_BUILD',
    CHANGE_DORM_BUILD: 'CHANGE_DORM_BUILD', //切换大通铺的设施
    REQ_BUNK_NODE: 'REQ_BUNK_NODE', //获取床铺节点

    UNLOCK_CINEMA_BUILD: 'UNLOCK_CINEMA_BUILD',
    CHANGE_CINEMA_BUILD: 'CHANGE_CINEMA_BUILD', //切换电影院的设施
    CHANGE_CINEMA_FENCE: 'CHANGE_CINEMA_FENCE', //切换电影院围栏
    CHANGE_CINEMA_DENG: 'CHANGE_CINEMA_DENG', //开光电影的灯
    CINEMA_PLAY_FILM: 'CINEMA_PLAY_FILM', //播放影片了
    UPDATE_PRAISE: 'UPDATE_PRAISE', //刷新口碑
    UPDATE_FILM_COUNT: 'UPDATE_FILM_COUNT', //刷新影片数量
    SELECT_FILM: 'SELECT_FILM', //选择影片
    ROW_PIECE_RESET: 'ROW_PIECE_RESET', //排片重置
    UPDATE_WISH_HIT_COUNT: 'UPDATE_WISH_HIT_COUNT', //刷新心愿击中次数
    ROW_PIECE_COMPLETE: 'ROW_PIECE_COMPLETE', //排片完成
    UPDATE_ROW_PIECE_LAN_TIP: 'UPDATE_ROW_PIECE_LAN_TIP', //刷新排片栏提示
    ROLE_FLUTTER_HIT_WISH_COUNT: 'ROLE_FLUTTER_HIT_WISH_COUNT', //客人飘击中心愿次数

    SHOW_IN_HOTEL_UI: 'SHOW_IN_HOTEL_UI', //显示角色入店UI
    SHOW_BUY_COFFEE_UI: 'SHOW_BUY_COFFEE_UI', //显示购买咖啡UI
    UPDATE_KEFANG_DENG: 'UPDATE_KEFANG_DENG', //刷新客房灯
    CHANGE_MAIN: 'CHANGE_MAIN', //修缮大楼
    UPDATE_TIP_LIMT: 'UPDATE_TIP_LIMT', //刷新小费台上限
    UP_MAIN_ATTR: 'UP_MAIN_ATTR', //升级主楼属性
    UPDATE_WISH_COUNT: 'UPDATE_WISH_COUNT', //刷新心心数量
    UPDATE_WISH_STATE: 'UPDATE_WISH_STATE', //刷新愿望状态
    CLAIM_ROLE_GIFT: 'CLAIM_ROLE_GIFT', //领取客人礼物
    LOOK_ROLE_STORY: 'LOOK_ROLE_STORY', //查看剧情的时候让图鉴下次打开的时候播放动画

    CHANGE_MINI_GAME: 'CHANGE_MINI_GAME', //切换小游戏
    MGAME_MAZE_START: 'MGAME_MAZE_START', //开始挖宝游戏
    MGAME_MAZE_CLICK_ITEM: 'MGAME_MAZE_CLICK_ITEM', //点击挖宝游戏的一格
    RESTART_EXPLORE: 'RESTART_EXPLORE', // 重新开始探索
    STOP_EXPLORE: 'STOP_EXPLORE',    // 停止探索
    ADD_GAME_MAP: 'ADD_GAME_MAP',   // 添加游戏地图 
    PASS_MINI_GAME: 'PASS_MINI_GAME',   // 通关特殊事件
    PLAY_GALI_EFFECT: 'JUMP_EFFECT',     // 播放特效
    SHOW_GALI_FIX_AWARD: 'SHOW_GALI_FIX_AWARD',    // 显示固定奖励
    UPDATE_EVENT_TIME: 'UPDATE_EVENT_TIME', // 更新事件时间
    GALI_GOGOGO: 'GALI_GOGOGO', // gali前进
    GALI_GOBACK: 'GALI_GOBACK', // gali后退
    GALI_UPDATE_MAP: 'GALI_UPDATE_MAP', // gali地图更新
    GALI_ADD_CARD: 'GALI_ADD_CARD', // gali新增卡片
    GALI_SHOW_HIDE: 'GALI_SHOW_HIDE', // gali显隐
    GALI_RANDOM_CHESS: 'GALI_RANDOM_CHESS', // 任意格子卡
    GALI_STOP_WHEEL: 'GALI_STOP_WHEEL', // 转盘停转
    GALI_RESET_WHEEL: 'GALI_RESET_WHEEL', // 转盘恢复
    GALI_SLOW_DOWN_WHEEL: 'GALI_SLOW_DOWN_WHEEL', // 转盘减速
    GALI_UPDATE_GOD_INFO: 'GALI_UPDATE_GOD_INFO', // 更新神的信息
    GALI_UPDATE_DOUBLE_CARD_INFO: 'GALI_UPDATE_DOUBLE_CARD_INFO', // 更新双倍卡的细细信息
    GALI_NOT_FIND_NEXT: 'GALI_NOT_FIND_NEXT', // 更新双倍卡的细细信息
    GALI_PASS_ITEM: 'GALI_PASS_ITEM', // 跳格经过的格子动画
    GALI_SHOW_UI_STATUS: 'GALI_SHOW_UI_STATUS', // 咖喱状态外显
    GALI_UPDATE_SHOOT_ARROW: 'GALI_UPDATE_SHOOT_ARROW', // 咖喱弓箭次数
    GALI_UPDATE_JUMP_LIVES: 'GALI_UPDATE_JUMP_LIVES', // 咖喱跳一跳命数
    GALI_MINI_GAME_FAIL: 'GALI_MINI_GAME_FAIL', // 咖喱小游戏结束

    SOLICIT_CREATE_GUEST: 'SOLICIT_CREATE_GUEST', // 生成招客客人
    SOLICIT_RECYCLE_GUEST: 'SOLICIT_RECYCLE_GUEST', // 回收招客客人
    SOLICIT_CREATE_DEVIL: 'SOLICIT_CREATE_DEVIL', // 生成大魔王
    SOLICIT_GUEST_AUTO: 'SOLICIT_GUEST_AUTO', // 自动招揽
    SOLICIT_TURN_AROUND: 'SOLICIT_TURN_AROUND', // 交通工具掉头
    SOLICIT_TURN_AROUND_DEVIL: 'SOLICIT_TURN_AROUND_DEVIL', // 大魔王掉头
    SOLICIT_EAT_BISCUIT: 'SOLICIT_EAT_BISCUIT', // 大魔王吃小饼干
    SOLICIT_PLAY_DEVIL: 'SOLICIT_PLAY_DEVIL', // 大魔王动画
    SOLICIT_CREATE_ROLE_CAR: 'SOLICIT_CREATE_ROLE_CAR', // 创建客人需要的车车
    SOLICIT_RECYCLE_ROLE_CAR: 'SOLICIT_RECYCLE_ROLE_CAR', // 回收客人需要的车车

    UPDATE_RANK_FIRST: 'UPDATE_RANK_FIRST', // 更新排行榜第一名奖励
    UPDATE_RANK_TASK: 'UPDATE_RANK_TASK', // 更新排行榜任务

    SHOOT_OPEN: 'SHOOT_OPEN',// 截屏相机打开
    SHOOT_CLOSE: 'SHOOT_CLOSE',// 截屏相机关闭

    START_GUIDE: 'START_GUIDE', //开始教程
    CHANGE_GUIDE_PROGRESS: 'CHANGE_GUIDE_PROGRESS', //开始教程中的一个步骤
    END_GUIDE: 'END_GUIDE', //完成教程
    CLEAN_GUIDE: 'CLEAN_GUIDE', // 清理教程界面
    SHOW_SKIP_GUIDE: 'SHOW_SKIP_GUIDE', //展示跳过教程按钮
    DELAY_SHOW_SKIP: 'DELAY_SHOW_SKIP', //延迟展示跳过按钮
    ADD_CLICK_EVENT: 'ADD_CLICK_EVENT', // 添加点击事件
    SHOW_GUIDE_NODE_DESC: 'SHOW_GUIDE_NODE_DESC', // 显示节点描述
    HIDE_GUIDE_FINGER: 'HIDE_GUIDE_FINGER', // 隐藏教程箭头及其他辅助显示
    CHANGE_GUIDE_FINGER_ANI: 'CHANGE_GUIDE_FINGER_ANI', // 切换教程箭头动画及其位置

    START_STORY: 'START_STORY', //剧情开始时的消息
    CHANGE_STORY: 'CHANGE_STORY', //剧情改变
    END_STORY: 'END_STORY', //剧情结束
    PROCEED_STORY: 'PROCEED_STORY',// 剧情继续（中间有其他步骤，但是剧情不能断）

    GUIDE_SHOW_GALI: 'GUIDE_SHOW_GALI', // 显示咕咕鸡
    GUIDE_MOVE_CAMERA: 'GUIDE_MOVE_CAMERA', // 移动相机
    GUIDE_ZOOM_CAMERA: 'GUIDE_ZOOM_CAMERA', // 缩放相机
    GUIDE_SHOW_FINGER: 'GUIDE_SHOW_FINGER', // 新手引导指向手指隐藏与否
    GUIDE_CHANGE_FINGER: 'GUIDE_CHANGE_FINGER', // 新手引导改变手指
    UPDATE_HUAMAO_STATE: 'UPDATE_HUAMAO_STATE', // 更新画猫状态
    UPDATE_QIANTAI_STATE: 'UPDATE_QIANTAI_STATE', // 更新前台状态
    GUIDE_DRAK_SCREEN: 'GUIDE_DRAK_SCREEN',  // 睡觉黑屏
    GUIDE_FLIP_FOOD_MINING: 'GUIDE_FLIP_FOOD_MINING', // 菜谱挖掘计数
    GUIDE_SHOW_PAOPAO: 'GUIDE_SHOW_PAOPAO', // 显示泡泡
    GUIDE_FURN_SWITCH: 'GUIDE_FURN_SWITCH', // 新手引导家具列表切换到装饰页面
    GUIDE_FURN_USE: 'GUIDE_FURN_USE', // 装饰摆放
    GUIDE_CLOSE_SIGNED_REWARD: 'GUIDE_CLOSE_SIGNED_REWARD', // 关闭签约奖励界面
    GUIDE_PLAY_MOVE_FURN_ANI: 'GUIDE_PLAY_MOVE_FURN_ANI', // 播放家具装饰可拖动示意动画
    GUIDE_HIDE_MOVE_FURN_ANI: 'GUIDE_HIDE_MOVE_FURN_ANI', // 隐藏家具装饰可拖动示意动画
    GUIDE_WAIT_CREATE_TAXI: 'GUIDE_WAIT_CREATE_TAXI', // 等待对话完成创建出粗车

    WEAK_GUIDE_SHOW_FINGER: 'WEAK_GUIDE_SHOW_FINGER', // 弱引导显示箭头指向某个节点
    WEAK_GUIDE_MOVE_CAMERA: 'WEAK_GUIDE_MOVE_CAMERA', // 弱引导移动相机

    OPENID_CHANGE: 'OPENID_CHANGE', // openid变化

    GAME_SHOW: 'GAME_SHOW', // 游戏进入前台
    GAME_HIDE: 'GAME_HIDE', // 游戏进入后台

    YOYO_LOTTERY_COMPLETE: 'YOYO_LOTTERY_COMPLETE',// 转转集市开奖
    UNLOCK_ROLE_CLOSE: 'UNLOCK_ROLE_CLOSE',// NPC解锁弹窗关闭

    FIREND_MAMMON_LEAVE: 'FIREND_MAMMON_LEAVE',// 好友财神 or 精灵离开

    ACTIVITY_FINISH: 'ACTIVITY_FINISH', // 活动结束
    HOLIDAY_UPDATE_LIST: 'HOLIDAY_UPDATE_LIST', // 更新节日活动ui列表
    HOLIDAY_UPDATE_REDEEM_PROP: 'HOLIDAY_UPDATE_REDEEM_PROP', // 更新节日活动兑换货币
    UPDATE_HOLIDAY_TAB: 'UPDATE_HOLIDAY_TAB', // 更新节日活动tab
    HOLIDAY_NEWYEAR_ADD_FLOW: 'HOLIDAY_NEWYEAR_ADD_FLOW', // 更新春节客流
    HOLIDAY_UPDATE_GAME_PROP: 'HOLIDAY_UPDATE_GAME_PROP', // 更新节日活动游戏道具
    START_NEWYEAR_FIREWORKS: 'START_NEWYEAR_FIREWORKS', // 开始播放烟花

    UPDATE_LIMITED_OFFER_FREE: 'UPDATE_LIMITED_OFFER_FREE', // 更新限时特惠免费信息
    UPDATE_LIMITED_OFFER_UI: 'UPDATE_LIMITED_OFFER_UI', // 更新限时特惠界面

    UPDATE_SHOP_TAB: 'UPDATE_SHOP_TAB', // 更新商店tab
    UPDATE_PNL_OPEN: 'UPDATE_PNL_OPEN', // 强行关闭子界面但是不显示父界面

    UPDATE_WEATHER: 'UPDATE_WEATHER', // 刷新天气ICON显示
    CREATE_WEATHER: 'CREATE_WEATHER', // 生成天气
    RECYCLE_WEATHER: 'RECYCLE_WEATHER', // 天气消失
    CREATE_WEATHER_THUNDER: 'CREATE_WEATHER_THUNDER', // 创建雷云
    PLAY_WEATHER_THUNDER_HACK: 'PLAY_WEATHER_THUNDER_HACK', // 雷云劈击
    THUNDER_GUEST_AUTO: 'THUNDER_GUEST_AUTO', // 雷云劈击自动招揽
    UPDATE_WEATHER_VIEW: 'UPDATE_WEATHER_VIEW', // 天气视图资源加载成功
    REVERT_WEATHER: 'REVERT_WEATHER', // 还原天气
}