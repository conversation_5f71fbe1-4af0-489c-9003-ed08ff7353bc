import { assetsMgr } from "../AssetsMgr"
import { BuildUnlockJItem } from "../constant/DataType"

// 建筑设施
export default class BuildObj {

    public id: number = 0
    public type: number = 0
    public json: BuildUnlockJItem = null //当前的json数据
    public useing: boolean = false //是否使用中
    private _output: number = 0

    public rootPosition: cc.Vec2 = null //临时记录位置

    public init(id: number, useing?: boolean) {
        this.id = id
        this.useing = !!useing
        this.initAttr()
        this.initJson()
        return this
    }

    public fromDB(data: any) {
        this.id = data.id
        this.useing = data.useing
        this.initAttr()
        this.initJson()
        return this
    }

    public initAttr() {
        this.type = Math.floor(this.id / 1000)
    }

    private initJson() {
        // 配置信息
        this.json = assetsMgr.getJsonData(this.getJsonName() + 'Unlock', this.id)
        this._output = this.json ? (this.json.output || 0) : 0
    }

    public get name() { return '' }
    public get desc() { return '' }
    public get unlock() { return !!this.json }
    public get fav() { return this.json ? this.json.fav : 0 }
    public get use_fav() { return this.json ? this.json.use_fav : 0 }
    public get income() { return this.json ? this.json.income : 0 }
    public get use_income() { return this.json ? this.json.use_income : 0 }
    public get heart() { return this.json ? this.json.heart : 0 }
    public get output() { return this._output }
    public get output_online() { return this.json ? this.json.output_online : '' }

    public getSceneType() { return '' } //设施是属于哪个场景的
    public getJsonName() { return '' } //配置表名字
}