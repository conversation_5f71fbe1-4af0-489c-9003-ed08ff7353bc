import BaseMapModel from "../map/BaseMapModel"
import WorldModel from "../world/WorldModel"
import DBuildObj from "./DBuildObj"
import { DiningBuildType, MapSceneType, MessageType, NpcType, TaskConditionType } from "../constant/Enums"
import { DINING_LEAVE_ODDS } from "../constant/Constant"
import UnlockFoodObj from "./UnlockFoodObj"
import { gameHelper } from "../GameHelper"
import ModelMgr from "../ModelMgr"
import { assetsMgr } from "../AssetsMgr"

/**
 * 餐厅
 */
export default class DiningModel extends BaseMapModel {

    private builds: DBuildObj[] = []
    private unlockFoods: UnlockFoodObj[] = [] //当前解锁的菜品

    constructor(modelMgr: ModelMgr) {
        super('dining', modelMgr)
        super.init(MapSceneType.DINING)

        // 注册持久化数据
        let dbHelper = modelMgr.getDB()
        const data = dbHelper.register(this.uid) || {}

        this.unlockFoods = data.unlockFoods ? data.unlockFoods.map(m => new UnlockFoodObj().fromDB(m)) : assetsMgr.getJson('foodBase').datas.filter(m => gameHelper.conditionIsNone(m.unlock_cost)).map(m => new UnlockFoodObj().init(m.id))
        // 获取所有设施
        if (data.builds) {
            this.builds = data.builds.map(m => new DBuildObj().fromDB(m))
        } else {
            this.builds = assetsMgr.getJson('restaurantUnlock').datas.filter(m => gameHelper.conditionIsNone(m.unlock_cost)).map(m => new DBuildObj().init(m.id, true))
            this.builds.push(new DBuildObj().init(0, true))
        }
        this.updateAttr()
    }

     // 获取所有的收入 包括使用设施的
     public getOnlineIncome() {
        let income = 0
        if (this.unlockFoods.length > 0) {
            income += this.unlockFoods.reduce((val, cur) => cur.price + val, 0) / this.unlockFoods.length
        }
        return this.modelMgr.getMapAllIncome(this.attr.income + income, this.builds, this.sceneType)
    }

    public getOfflineIncome() { return this.builds.reduce((val, cur) => cur.output + val, 0) }

    public getOfflineLove() {
        let fav = 0
        if (this.unlockFoods.length > 0) {
            fav += this.unlockFoods.reduce((val, cur) => cur.fav + val, 0) / this.unlockFoods.length
        }
        return fav + gameHelper.getMapAllLove(this.attr.fav, this.builds, DINING_LEAVE_ODDS)
    }

    public getUnlockFoods(){
        return this.unlockFoods;
    }

    // 刷新属性
    private updateAttr() {
        this.attr.reset()
        this.builds.forEach(m => {
            this.attr.add(m)
        })
    }
}