import { FoodAttrJItem } from "../constant/DataType"
import IItemObj from "../common/IItemObj"
import ut from "../../common/util"
import { assetsMgr } from "../AssetsMgr"

// 解锁的菜品
export default class UnlockFoodObj extends IItemObj {

    public attr: FoodAttrJItem = null

    public init(id: number) {
        this.id = id
        this.lv = 1
        return this.updateAttr()
    }

    public fromDB(data: any) {
        return ut.setValue('id|lv', data, this).updateAttr()
    }

    public get price() { return this.attr ? this.attr.price : 0 }
    public get fav() { return this.attr ? this.attr.fav : 0 }
    public get heart() { return this.attr ? this.attr.heart : 0 }

    private updateAttr() {
        this.attr = assetsMgr.getJsonData('foodAttr', this.attrId)
        return this
    }
}