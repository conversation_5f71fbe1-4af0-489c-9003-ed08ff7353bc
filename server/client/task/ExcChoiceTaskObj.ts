import { gameHelper } from "../GameHelper";
import BaseExcTaskObj from "./BaseExcTaskObj";
import { assetsMgr } from "../AssetsMgr"

// 抉择任务
export default class ExcChoiceTaskObj extends BaseExcTaskObj {

    protected updateJson(data?: any) {
        this.json = assetsMgr.getJsonData('exclusiveTask_' + this.excType, this.id)
        // this.rewards = gameHelper.getRoleTaskRewardsByConf(this.base, this.roleId)
        return this
    }

    public getViewUrl() { return 'common/ExcChoiceTaskInfo' }
}