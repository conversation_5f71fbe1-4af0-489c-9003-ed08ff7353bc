import ConditionObj from "../common/ConditionObj"
import TaskConditionObj from "./TaskConditionObj"
import { ConditionType, TaskConditionType } from "../constant/Enums"

// 任务帮助
class TaskHelper {

    // 是否需要进度
    public isNeedProgress(type: TaskConditionType) {
        return type === TaskConditionType.SOLICIT_ROLE_NUM
            || type === TaskConditionType.COMP_FINISH_COUNT
            || type === TaskConditionType.FOOD_MINING_COUNT
            || type === TaskConditionType.USE_SOUPBASE_COUNT
            || type === TaskConditionType.GET_FOODMINING_COUNT
            || type === TaskConditionType.SOLICIT_RAND_ROLE_COUNT
            || type === TaskConditionType.DINING_REPLENISH_COUNT
            || type === TaskConditionType.SHOWER_REPAIR_COUNT
            || type === TaskConditionType.SHARE_GAME
            || type === TaskConditionType.TASK_SHARE_GAME
            || type === TaskConditionType.GET_BISCUITS
            || type === TaskConditionType.GET_CANDIES
            || type === TaskConditionType.PLAY_AD_COUNT
            || type === TaskConditionType.GET_PROP
            || type === TaskConditionType.SOLICIT_TRANSPORT_COUNT
            || type === TaskConditionType.GET_DROP_BISCUIT_COUNT
            || type === TaskConditionType.GET_TIP_COUNT
            || type === TaskConditionType.OPEN_DOUBLE_EARN_COUNT
            || type === TaskConditionType.BUY_COMP_PROP_COUNT
            || type === TaskConditionType.GALI_OUTING_COUNT
            || type === TaskConditionType.ENTRUST_FINISH_COUNT
            || type === TaskConditionType.SERVE_FINISH_COUNT
            || type === TaskConditionType.CONCERN
    }

    // 将任务条件转成默认条件
    public convertToConditionObj(cond: TaskConditionObj) {
        if (cond.type === TaskConditionType.TOTAL_HEART) {
            return new ConditionObj().init(ConditionType.HEART)
        } else if (cond.type === TaskConditionType.UNLOCK_FURN || cond.type === TaskConditionType.UNLOCK_KEFANG_FURN) {
            return new ConditionObj().init(ConditionType.FURNITURE, cond.tid)
        } else if (cond.type === TaskConditionType.UNLOCK_FOOD) {
            return new ConditionObj().init(ConditionType.FOOD, cond.tid)
        } else if (cond.type === TaskConditionType.UNLOCK_DORM_BUILD) {
            return new ConditionObj().init(ConditionType.DORM_BUILD, cond.tid)
        } else if (cond.type === TaskConditionType.UNLOCK_SHOWER_BUILD) {
            return new ConditionObj().init(ConditionType.SHOWER_BUILD, cond.tid)
        } else if (cond.type === TaskConditionType.UNLOCK_DINING_BUILD) {
            return new ConditionObj().init(ConditionType.DINING_BUILD, cond.tid)
        } else if (cond.type === TaskConditionType.TOTAL_BISCUITS || cond.type === TaskConditionType.GET_BISCUITS) {
            return new ConditionObj().init(ConditionType.BISCUITS)
        } else if (cond.type === TaskConditionType.EXPEND_PROP || cond.type === TaskConditionType.GET_PROP) {
            return new ConditionObj().init(ConditionType.PROP, cond.tid)
        }
        return null
    }
}

export const taskHelper = new TaskHelper()