import { ExclusiveBaseJItem } from "../constant/DataType"
import { WishType } from "../constant/Enums"
import BaseWishTaskObj from "./BaseWishTaskObj"
import { assetsMgr } from "../AssetsMgr"

// 基础专属任务
export default class BaseExcTaskObj extends BaseWishTaskObj {

    public base: ExclusiveBaseJItem = null
    public json: any = null

    public updateRoleInfo(id: number) {
        this.base = assetsMgr.getJsonData('exclusiveBase', this.id)
        return super.updateRoleInfo(id)
    }

    public get excType() { return this.base?.type }

    public getNameType() { return WishType.SERVE }

    // 获取结束时的对话
    public getEndTalkId() { return this.json?.end_talk || '' }
}