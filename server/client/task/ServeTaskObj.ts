import {GUIDE_SERVE_TASK_ID, SERVE_COND_ID} from "../constant/Constant";
import {ServeTaskJItem} from "../constant/DataType";
import {PropType, TaskState, WishType} from "../constant/Enums";
import {gameHelper} from "../GameHelper";
import {taskHelper} from "./TaskHelper";
import ConditionObj from "../common/ConditionObj";
import BaseWishTaskObj from "./BaseWishTaskObj";
import TaskConditionObj from "./TaskConditionObj";
import {assetsMgr} from "../AssetsMgr"
import ut from "../../common/util"

// 服务任务
export default class ServeTaskObj extends BaseWishTaskObj {

    public json: ServeTaskJItem = null
    public descId: number = 0 //说明id

    constructor() {
        super(WishType.SERVE)
    }

    public fromDB(data: any) {
        this.uid = data.uid
        this.id = data.id
        this.roleId = data.roleId
        this.state = data.state
        this.descId = data.descId || 0
        return this.updateRoleInfo(this.roleId).updateJson(data)
    }

    public toDB() {
        return {
            uid: this.uid,
            id: this.id,
            type: this.type,
            roleId: this.roleId,
            state: this.state,
            descId: this.descId,
            condInfos: this.toCondInfos(),
            rewards: this.rewards.map(m => m.toDB())
        }
    }

    protected updateJson(data?: any) {
        const json = this.json = assetsMgr.getJsonData('serveTask', this.id) as ServeTaskJItem
        if (json.desc) {
            this.desc = json.desc
        } else { //随机一个说明
            if (!this.descId) {
                let o = assetsMgr.getJson('serveTaskComDesc');
                this.descId = o && o.datas ? o.datas.random()?.id : 0
            }
            this.desc = 'serveTaskComDesc.' + this.descId
        }
        // 条件
        if (data?.condInfos) {
            this.condInfos = data.condInfos.map(m => new TaskConditionObj().init(m.id).fromDB(m))
        } else if (this.id === GUIDE_SERVE_TASK_ID) { //新手引导的特定任务
            this.condInfos = [new TaskConditionObj().init(SERVE_COND_ID).set(5004, Math.ceil(json.task_item_num))]
        } else {
            // 50%的概率会选背包， 如果选择背包，会根据背包的数量作为权重，进行类型挑选
            const isBagFilter = ut.chance(50)
            const items = gameHelper.getCanServeItems(), bagItems = gameHelper.bag.getPropsByType(PropType.SERVE)
            let cnt = this.json.task_item_count, list = [], sumWeight = 0
            for (let i = 0; i < cnt; i++) {
                let id = isBagFilter && gameHelper.randomBagItem(bagItems)?.id
                if (!id) {
                    id = gameHelper.randomCanItem(items)?.id
                } else {
                    items.remove('id', id)
                }
                const json = id && assetsMgr.getJsonData('serveItem', id)
                if (json) {
                    list.push(json)
                    sumWeight += json.adv_weight
                }
            }
            this.condInfos = []
            const sumNum = Math.ceil(gameHelper.getGaliOutputPropCount() * this.json.task_item_num) //总数量
            list.forEach(json => {
                const count = Math.ceil((json.adv_weight / sumWeight * sumNum) * ut.random(80, 120) * 0.01)
                this.condInfos.push(new TaskConditionObj().init(SERVE_COND_ID).set(json.id, count))
            })
        }
        // 奖励
        if (data?.rewards) {
            this.rewards = data.rewards.map(m => new ConditionObj().fromDB(m))
        } else {
            this.rewards = gameHelper.getRoleTaskRewardsByConf(json, this.roleId)
        }
        return this
    }

    public getViewUrl() {
        return 'common/ServeTaskInfo'
    }

    public getCondType() {
        return 2
    } //道具消耗类

    // 获取结束时的对话
    public getEndTalkId() {
        return this.json?.end_talk || ''
    }

    // 检测刷新完成
    public checkUpdateComplete() {
        if (this.state === TaskState.NONE || this.state === TaskState.FINISH) {
            return this.state
        }
        this.state = this.isComplete() ? TaskState.CANGET : TaskState.UNDONE
        return this.state
    }

    // 是否完成
    public isComplete() {
        return this.condInfos?.every(m => taskHelper.checkTaskCondition(m))
    }
}