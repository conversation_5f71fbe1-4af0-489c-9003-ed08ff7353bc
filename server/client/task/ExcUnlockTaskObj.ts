import {TaskConditionType, TaskState, WishType} from "../constant/Enums";
import {gameHelper} from "../GameHelper";
import {taskHelper} from "./TaskHelper";
import ConditionObj from "../common/ConditionObj";
import BaseExcTaskObj from "./BaseExcTaskObj";
import {assetsMgr} from "../AssetsMgr"

// 解锁任务
export default class ExcUnlockTaskObj extends BaseExcTaskObj {

    public fromDB(data: any) {
        this.uid = data.uid
        this.id = data.id
        this.roleId = data.roleId
        this.state = data.state
        return this.updateRoleInfo(this.roleId).updateJson(data)
    }

    public toDB(): any {
        return {
            uid: this.uid,
            id: this.id,
            type: this.type,
            roleId: this.roleId,
            state: this.state,
            rewards: this.rewards.map(m => m.toDB())
        }
    }

    protected updateJson(data?: any) {
        const json = this.json = assetsMgr.getJsonData('exclusiveTask_' + this.excType, this.id)
        this.desc = json.desc
        // 条件
        // this.condInfos = taskHelper.stringToConditions(json.condition)
        // 奖励
        if (data?.rewards) {
            this.rewards = data.rewards.map(m => new ConditionObj().fromDB(m))
        } else {
            // this.rewards = gameHelper.getRoleTaskRewardsByConf(this.base, this.roleId, this.condInfo.type === TaskConditionType.HAVE_PROP)
        }
        return this
    }

    public getViewUrl() {
        return 'common/ExcUnlockTaskInfo'
    }

    public getNameType() {
        return WishType.SERVE
    }

    public getDescType() {
        const cond = this.condInfo
        if (!cond) {
            return 4
        } else if (cond.type === TaskConditionType.UNLOCK_FOOD) {
            return 5
        } else if (cond.type === TaskConditionType.UNLOCK_SOUPBASE) {
            return 6
        } else if (cond.type === TaskConditionType.UNLOCK_FILM) {
            return 7
        } else if (cond.type === TaskConditionType.UP_FOOD_N) {
            return 8
        }
        return 4
    }

    public getCondType() {
        return 1
    } //道具消耗类

    // 检测刷新完成
    public checkUpdateComplete() {
        if (this.state === TaskState.NONE || this.state === TaskState.FINISH) {
            return this.state
        }
        this.state = this.isComplete() ? TaskState.CANGET : TaskState.UNDONE
        return this.state
    }

    // 是否完成
    public isComplete() {
        return this.condInfos?.every(m => taskHelper.checkTaskCondition(m))
    }
}