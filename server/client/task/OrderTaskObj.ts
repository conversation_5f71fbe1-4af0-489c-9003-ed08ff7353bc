import {OrderTaskJItem} from "../constant/DataType";
import {WishType} from "../constant/Enums";
import {gameHelper} from "../GameHelper";
import ConditionObj from "../common/ConditionObj";
import BaseWishTaskObj from "./BaseWishTaskObj";
import {assetsMgr} from "../AssetsMgr"
import ModelMgr from "../ModelMgr";
import ut from "../../common/util"

// 预约任务
export default class OrderTaskObj extends BaseWishTaskObj {

    public json: OrderTaskJItem = null
    public descId: number = 0 //说明id
    public startTime: number = 0 //开始时间
    public durationTime: number = 0 //持续时间
    public checkState: number = 0

    constructor() {
        super(WishType.ORDER)
    }

    public fromDB(data: any) {
        this.uid = data.uid
        this.id = data.id
        this.roleId = data.roleId
        this.state = data.state
        this.startTime = data.startTime
        this.descId = data.descId || 0
        this.updateRoleInfo(this.roleId)
        return this.updateJson(data)
    }

    public toDB() {
        return {
            uid: this.uid,
            id: this.id,
            type: this.type,
            roleId: this.roleId,
            state: this.state,
            startTime: this.startTime,
            descId: this.descId,
            rewards: this.rewards.map(m => m.toDB())
        } as any
    }

    protected updateJson(data?: any) {
        const json = this.json = assetsMgr.getJsonData('orderTask', this.id) as OrderTaskJItem
        if (json && json.desc) {
            this.desc = json.desc
        } else { //随机一个说明
            if (!this.descId) {
                let o = assetsMgr.getJson('orderTaskComDesc');

                this.descId = o && o.datas ? o.datas.random()?.id || 0 : 0
            }
            this.desc = 'orderTaskComDesc.' + this.descId
        }
        // 时间
        this.durationTime = json.time * ut.Time.Second
        // 奖励
        if (data?.rewards) {
            this.rewards = data.rewards.map(m => new ConditionObj().fromDB(m))
        } else {
            this.rewards = gameHelper.getRoleTaskRewardsByConf(json, this.roleId)
        }
        return this
    }

    public getViewUrl() {
        return 'common/OrderTaskInfo'
    }

    public getCondType() {
        return 3
    } //预约类

    public getNameType() {
        return WishType.ORDER
    }

    // 接受任务
    public accept() {
        this.setStartTime().then(() => super.accept())
    }

    // 设置开始时间
    private async setStartTime() {

    }

    // 获取进度比例
    public getProgressRatio() {
        return this.startTime > 0 ? Math.min((gameHelper.now() - this.startTime) / this.durationTime, 1) : 0
    }

    // 获取剩余时间
    public getProgressSurplusTime() {
        return this.startTime > 0 ? Math.max(this.durationTime - (gameHelper.now() - this.startTime), 0) : this.durationTime
    }

    // 是否完成
    public isComplete() {
        return this.startTime > 0 && (gameHelper.now() - this.startTime) >= this.durationTime
    }

    // 检测时间
    public async checkTime() {
        this.checkState = 1
        const serverTime = await gameHelper.getServerTime()
        if (this.startTime > serverTime) {//改时间才会这样
            this.startTime = serverTime
        }
        this.checkState = this.isComplete() ? 2 : 0
    }
}