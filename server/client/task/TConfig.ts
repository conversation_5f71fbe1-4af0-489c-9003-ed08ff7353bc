import { ExcTaskType, WishType } from "../constant/Enums";
import ExcChoiceTaskObj from "./ExcChoiceTaskObj";
import ExcOrderTaskObj from "./ExcOrderTaskObj";
import ExcPhotographTaskObj from "../task/ExcPhotographTaskObj";
import ExcUnlockTaskObj from "./ExcUnlockTaskObj";

// 任务配置
export const tconfig = {

    WISH_MAX_SUM_COUNT: 6, //任务最大数量
    SERVE_TASK_MAX_COUNT: 2, //委托任务最多个数
    ORDER_TASK_MAX_COUNT: 1, //预约任务最多个数
    UNLOCK_TASK_MAX_COUNT: 3, //解锁任务最多个数
    EXC_TASK_MAX_COUNT: { //专属任务最多个数
        [WishType.EXCLUSIVE_1]: 99, //揽客驱动
        [WishType.EXCLUSIVE_2]: 99, //玩家行为驱动
        [WishType.EXCLUSIVE_3]: 99, //客人行为驱动
    },
    EXC_TYPE_TASK_MAX_COUNT: {
        [ExcTaskType.UNLOCK]: 99, //带剧情的解锁任务
        [ExcTaskType.ORDER]: 99, //带剧情的预约任务
        [ExcTaskType.PHOTOGRAPH]: 1, //拍照
    },
    EXC_TRIGGER_INTERVAL: 3 * 60 * 1000, //专属任务触发间隔 单位毫秒

    // 委托任务的体力配置
    SERVE_STAMINA_CONF: {
        MAX_STAMINA: 4, //体力上限
        RECOVER_STAMINA_MAX_TIME: 30 * 60000, //恢复体力时间 单位毫秒
        TRIGGER_SERVE_INTERVAL: 240, //触发任务的间隔 单位秒
    },

    // 预约任务的体力配置
    ORDER_STAMINA_CONF: {
        MAX_STAMINA: 3, //体力上限
        RECOVER_STAMINA_MAX_TIME: 8 * 60 * 60000, //恢复体力时间 单位毫秒
        TRIGGER_SERVE_INTERVAL: 360, //触发任务的间隔 单位秒
    },

    ORDER_TRIGGER_DONE_CD: [120, 180], //触发预约任务完成得cd 单位秒

    EXC_TASK_CLASS: {
        [ExcTaskType.UNLOCK]: ExcUnlockTaskObj,
        [ExcTaskType.ORDER]: ExcOrderTaskObj,
        [ExcTaskType.CHOICE]: ExcChoiceTaskObj,
        [ExcTaskType.PHOTOGRAPH]: ExcPhotographTaskObj,
    }
}