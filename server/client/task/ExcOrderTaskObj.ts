import {OrderTaskJItem} from "../constant/DataType";
import {WishType} from "../constant/Enums";
import {gameHelper} from "../GameHelper";
import ConditionObj from "../common/ConditionObj";
import BaseExcTaskObj from "./BaseExcTaskObj";
import {assetsMgr} from "../AssetsMgr"
import ut from "../../common/util"
// 预约任务
export default class ExcOrderTaskObj extends BaseExcTaskObj {

    public descId: number = 0 //说明id
    public startTime: number = 0 //开始时间
    public durationTime: number = 0 //持续时间
    public checkState: number = 0

    public fromDB(data: any) {
        this.uid = data.uid
        this.id = data.id
        this.roleId = data.roleId
        this.state = data.state
        this.startTime = data.startTime
        this.descId = data.descId || 0
        this.updateRoleInfo(this.roleId)
        return this.updateJson(data)
    }

    public toDB(): any {
        return {
            uid: this.uid,
            id: this.id,
            type: this.type,
            roleId: this.roleId,
            state: this.state,
            startTime: this.startTime,
            descId: this.descId,
            rewards: this.rewards.map(m => m.toDB())
        }
    }

    protected updateJson(data?: any) {
        const json = this.json = assetsMgr.getJsonData('exclusiveTask_' + this.excType, this.id) as OrderTaskJItem
        if (json.desc) {
            this.desc = json.desc
        } else { //随机一个说明
            if (!this.descId) {
                this.descId = assetsMgr.getJson('orderTaskComDesc').datas.random()?.id || 0
            }
            this.desc = 'orderTaskComDesc.' + this.descId
        }
        // 时间
        this.durationTime = json.time * ut.Time.Second
        // 奖励
        if (data?.rewards) {
            this.rewards = data.rewards.map(m => new ConditionObj().fromDB(m))
        } else {
            // this.rewards = gameHelper.getRoleTaskRewardsByConf(this.base, this.roleId)
        }
        return this
    }

    public getViewUrl() {
        return 'common/ExcOrderTaskInfo'
    }

    public getCondType() {
        return 3
    } //预约类

    public getNameType() {
        return WishType.ORDER
    }

    public getDescType() {
        return WishType.ORDER
    }

    // 接受任务
    public accept() {
        this.setStartTime().then(() => super.accept())
    }

    // 设置开始时间
    private async setStartTime() {
        if (gameHelper.isTimeGoBack()) {
            this.startTime = await gameHelper.getServerTime()
        } else {
            this.startTime = gameHelper.now()
        }
    }

}