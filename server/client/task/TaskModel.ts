import { assetsMgr } from "../AssetsMgr";
import ConditionObj from "../common/ConditionObj";
import { SHOWER_ACHIEVE_TASK_REWARD } from "../constant/DBBCompat";
import { TaskState } from "../constant/Enums";
import ModelMgr from "../ModelMgr";
import AchieveTaskObj from "./AchieveTaskObj";
import BaseTaskObj from "./BaseTaskObj";

// 任务
export default class TaskModel {

    private achieves: AchieveTaskObj[] = [] //所有成就任务列表

    private modelMgr: ModelMgr = null;

    public onCreate(modelMgr: ModelMgr) {
        this.modelMgr = modelMgr
    }

    public init() {
        const ver = 5

        let dbHelper = this.modelMgr.getDB()
        const data = dbHelper.register('task') || {}
       
        this.achieves = [];
        (data.achieves || []).forEach(m => {
            const task = new AchieveTaskObj().fromDB(m, this.modelMgr)
            if (task) {
                this.achieves.push(task)
            }
        })

        while (data._ver < ver) {
            if (data._ver === 1) {
            } else if (data._ver === 2) {
            } else if (data._ver === 3) { //恢复一下
            } else if (data._ver === 4) { //温泉改变
            }
            data._ver += 1
        }
    }
    // 获取成就
    public getAchieves() { return this.achieves }
}