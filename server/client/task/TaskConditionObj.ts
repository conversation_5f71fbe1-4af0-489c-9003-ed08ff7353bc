import {TaskConditionType} from "../constant/Enums"
import {gameHelper} from "../GameHelper"
import {taskHelper} from "./TaskHelper"
import ut from "../../common/util"
import {assetsMgr} from "../AssetsMgr"
import ModelMgr from "../ModelMgr"

// 任务条件
export default class TaskConditionObj {

    public id: number = 0
    public type: TaskConditionType
    public tid: any //目标id
    private count: number = 0 //目标数量
    private progress: number = 0 //进度

    private svaeTid: boolean = false //是否保存tid
    private svaeCount: boolean = false //是否保存count
    private saveProgress: boolean = false //是否保存progress

    public init(id: number, modelMgr: ModelMgr) {
        let json = assetsMgr.getJsonData('taskCondition', id)
        if (json) {
            this.id = id
            this.type = json.type || null
            this.tid = json.tid
            this.count = json.count
            this.saveProgress = taskHelper.isNeedProgress(json.type)
        }
        // 这里如果是随机招揽客人 要特殊处理
        // if (this.type === TaskConditionType.SOLICIT_RAND_ROLE_COUNT) {
        //     const world = modelMgr.world
        //     let list = world.getRoleAttrs().filter(m => m.visit_weight > 3000)
        //     if (list.length === 0) {
        //         list = world.getRoleAttrs()
        //     }
        //     let role = world.getRandomUnlockRoleByWeight(list, modelMgr.task.ignoreSolicitRoleIds), sum = list.reduce((w, curr) => w + curr.visit_weight, 0)
        //     if (!role) {
        //         role = list[0]
        //     }
        //     this.tid = role.id
        //     this.count = Math.max(1, Math.floor(this.count * Math.min((role.visit_weight / sum), 0.15) * (ut.random(8, 12) * 0.1)))
        //     this.svaeTid = true
        //     this.svaeCount = true
        // } else if (json.heart_add_factor) {
        //     this.count = ut.toFloor(this.count + modelMgr.getHeart() * json.heart_add_factor)
        //     this.svaeCount = true
        // }
        this.progress = 0
        return this
    }

    public set(tid: number, count: number) {
        this.tid = tid
        this.count = count
        this.svaeTid = true
        this.svaeCount = true
        return this
    }

    public fromDB(data: any) {
        if (data && data.id === this.id) {
            this.progress = data.progress || 0
            this.tid = data.tid || this.tid
            this.count = data.count || this.count
            this.svaeTid = !!data.tid
            this.svaeCount = !!data.count
        }
        return this
    }

    // 只要有一个 就代表可以保存
    public isCanSvae() {
        return this.saveProgress || this.svaeTid || this.svaeCount
    }

    // 记录进度
    public recordProgress(type: TaskConditionType, count: number, data?: any) {
        if (this.type !== type) {
            return
        }
        if (type === TaskConditionType.GET_FOODMINING_COUNT && this.tid !== data) {
            return //特殊判断 获取指定挖掘材料
        } else if (type === TaskConditionType.SOLICIT_RAND_ROLE_COUNT && this.tid !== data) {
            return //特殊判断 招揽指定客人
        } else if (type === TaskConditionType.SOLICIT_TRANSPORT_COUNT && this.tid !== data) {
            return //特殊判断 招揽指定交通工具
        } else if (type === TaskConditionType.GET_PROP && this.tid !== data) {
            return //特殊判断 获取指定道具
        }
        this.progress += count
    }
}