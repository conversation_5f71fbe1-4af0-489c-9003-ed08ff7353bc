import { TaskState, WishType } from "../constant/Enums";
import EventType from "../common/EventType";
import { gameHelper } from "../GameHelper";
import BaseTaskObj from "./BaseTaskObj";
import ut from "../../common/util"
import { assetsMgr } from "../AssetsMgr"

// 心愿任务基础类
export default class BaseWishTaskObj extends BaseTaskObj {

    public type: WishType //类型 1.心愿 2.委托
    public roleId: number = 0 //客人ID
    public roleBody: number = 1
    public roleSkin: string = ''
    public desc: string = ''

    constructor(type: WishType) {
        super()
        this.type = type
    }

    public init(id: number, roleId: number) {
        this.uid = ut.uid()
        this.id = id
        this.state = TaskState.NONE
        return this.updateRoleInfo(roleId).updateJson()
    }

    public fromDB(data: any) {
        this.uid = data.uid
        this.id = data.id
        this.roleId = data.roleId
        this.state = data.state
        return this.updateRoleInfo(this.roleId).updateJson(data.condInfos)
    }

    public toDB() {
        return {
            uid: this.uid,
            id: this.id,
            type: this.type,
            roleId: this.roleId,
            state: this.state,
            condInfos: this.toCondInfos()
        }
    }

    public updateRoleInfo(id: number) {
        const json = assetsMgr.getJsonData('customersBase', id)
        this.roleId = id
        this.roleBody = json.body
        this.roleSkin = json.skin
        return this
    }

    protected updateJson(condInfo?: any) { return this }

    public getViewUrl() { return '' }
    public getNameType() { return this.type }
    public getDescType() { return this.type }
    public getCondType() { return 0 }

    // 获取结束时的对话
    public getEndTalkId() { return '' }

    // 接受任务
    public accept() {
        if (this.state === TaskState.NONE) {
            this.state = TaskState.UNDONE
            this.checkUpdateComplete()
            if (gameHelper.wish.isExcTaskByType(this.type)) {
                gameHelper.world?.getRoleAttr(this.roleId)?.acceptExcTask(this.id)
            } else {
                gameHelper.world?.getRoleAttr(this.roleId)?.acceptAttrTask()
            }
            eventCenter.emit(EventType.UPDATE_WISH_STATE, this)
        }
    }

    // 完成任务
    public finish() {
        this.state = TaskState.FINISH
        eventCenter.emit(EventType.UPDATE_WISH_STATE, this)
    }

    // 直接设置完成 可领取
    public complete() {
        this.state = TaskState.CANGET
        eventCenter.emit(EventType.UPDATE_WISH_STATE, this)
    }
}