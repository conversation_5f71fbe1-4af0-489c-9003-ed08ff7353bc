import { TaskState } from "../constant/Enums";
import ut from "../../common/util";
import { assetsMgr } from "../AssetsMgr";
import BaseTaskObj from "./BaseTaskObj";
import TaskConditionObj from "./TaskConditionObj";
import ModelMgr from "../ModelMgr";

// 成就任务
export default class AchieveTaskObj extends BaseTaskObj {

    public finishTime: number = 0 //完成时间

    public type: number = 0
    public sort: number = 0 //排序

    // public init(json: any) {
    //     this.id = json.id
    //     this.state = TaskState.UNDONE
    //     this.initJson(json)
    //     this.condInfo = new TaskConditionObj().init(json.condition)
    //     return this
    // }

    public fromDB(data: any, modelMgr: ModelMgr) {
        this.id = data.id
        this.finishTime = data.finishTime || 0
        this.state = data.finishTime > 0 ? TaskState.FINISH : (data.state || TaskState.UNDONE)
        const json = assetsMgr.getJsonData('achieveTaskBase', this.id)
        if (!json) {
            return
        }
        this.initJson(json, modelMgr)
        // this.condInfo = new TaskConditionObj().init(json.condition).fromDB(data.condInfo)
        return this
    }

    private initJson(json: any, modelMgr: ModelMgr) {
        // this.uid = ut.uid()
        this.type = json.type
        this.sort = json.sort
        this.rewards = modelMgr.getRewardsById(json.reward)
    }
}