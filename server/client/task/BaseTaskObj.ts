import { GUIDE_SERVE_TASK_ID } from "../constant/Constant"
import { TaskConditionType, TaskState } from "../constant/Enums"
import ConditionObj from "../common/ConditionObj"
import TaskConditionObj from "./TaskConditionObj"

// 任务基础类
export default class BaseTaskObj {

    public uid: string = ''
    public id: number = 0
    public state: TaskState = TaskState.NONE //状态 0.未接取 1.未完成 2.可领奖 3.完成
    public rewards: ConditionObj[] = null //奖励信息
    // public condInfos: TaskConditionObj[] = null //条件信息

    // // 记录任务进度
    // public recordProgress(type: TaskConditionType, count: number, data?: any) {
    //     if (!this.id || !this.condInfos || this.state >= TaskState.CANGET) {
    //         return
    //     }
    //     this.condInfos.forEach(m => m.recordProgress(type, count, data))
    // }

    // public get condInfo() { return this.condInfos && this.condInfos[0] }
    // public set condInfo(val: TaskConditionObj) {
    //     this.condInfos = val && [val]
    // }

    // 是否完成
    public isFinish() { return this.state === TaskState.FINISH }

    // // 是否可领奖
    // public isCanClaim() { return this.state === TaskState.CANGET }

    // // 检测刷新完成
    // public checkUpdateComplete() {
    //     if (this.state >= TaskState.CANGET) {
    //         return this.state
    //     } else if (this.condInfos && this.condInfos.every(m => taskHelper.checkTaskCondition(m))) {
    //         this.state = TaskState.CANGET
    //     }
    //     return this.state
    // }

    // public toCondInfos() {
    //     return this.condInfos && this.condInfos.length > 0 ? this.condInfos.map(m => m.toDB()) : undefined
    // }

    // public toCondInfo() {
    //     return this.condInfos && this.condInfos.length > 0 ? this.condInfos[0].toDB() : undefined
    // }

    // // 获取消耗道具数量 根据id
    // public getExpendPropCountById(id: number) {
    //     if (!this.condInfos) {
    //         return 0
    //     }
    //     let count = 0
    //     this.condInfos.forEach(m => {
    //         if (m.type === TaskConditionType.EXPEND_PROP && m.tid === id) {
    //             count += m.count
    //         }
    //     })
    //     return count
    // }

    // public getSortVal() {
    //     if (this.isCanClaim()) {
    //         return this.id === GUIDE_SERVE_TASK_ID ? 0 : 1
    //     }
    //     return 2
    // }
}