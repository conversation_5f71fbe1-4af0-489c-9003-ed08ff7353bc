import { WishType } from "../constant/Enums";
import { gameHelper } from "../GameHelper";
import ConditionObj from "../common/ConditionObj";
import BaseExcTaskObj from "./BaseExcTaskObj";
import { assetsMgr } from "../AssetsMgr"
import ut from "../../common/util"

// 拍照任务
export default class ExcPhotographTaskObj extends BaseExcTaskObj {

    public roles: { id: number, type: number, value: string }[] = [] //包含的客人列表

    public toDB(): any {
        return {
            uid: this.uid,
            id: this.id,
            type: this.type,
            roleId: this.roleId,
            state: this.state,
            rewards: this.rewards.map(m => m.toDB())
        }
    }

    protected updateJson(data?: any) {
        const json = this.json = assetsMgr.getJsonData('exclusiveTask_' + this.excType, this.id)
        this.desc = json.desc
        // 角色列表
        const types = ut.stringToNumbers(json.cond_type, ',')
        const values: string[] = json.cond_value.split(',')
        this.roles = ut.stringToNumbers(json.role, ',').map((m, i) => {
            return { id: m === -1 ? this.roleId : m, type: types[i], value: values[i] }
        })
        if (this.roles.length === 0) {
            this.roles.push({ id: this.roleId, type: types[0], value: values[0] }) //如果没有就包含自己
        }
        // 奖励
        if (data?.rewards) {
            this.rewards = data.rewards.map(m => new ConditionObj().fromDB(m))
        } else {
            //this.rewards = gameHelper.getRoleTaskRewardsByConf(this.base, this.roleId)
        }
        return this
    }

    public getViewUrl() { return 'common/ExcPhotographTaskInfo' }

    public getCondType() { return 4 } //纯内容

    public getNameType() { return WishType.SERVE }

    public getDescType() { return 9 } //需求

}