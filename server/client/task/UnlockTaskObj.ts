import { UnlockTaskJItem } from "../constant/DataType";
import { TaskConditionType, TaskState, WishType } from "../constant/Enums";
import { gameHelper } from "../GameHelper";
import { taskHelper } from "./TaskHelper";
import ConditionObj from "../common/ConditionObj";
import BaseWishTaskObj from "./BaseWishTaskObj";
import TaskConditionObj from "./TaskConditionObj";
import { assetsMgr } from "../AssetsMgr"

// 解锁任务
export default class UnlockTaskObj extends BaseWishTaskObj {

    public json: UnlockTaskJItem = null
    public descId: number = 0 //说明id

    constructor() {
        super(WishType.UNLOCK)
    }

    public init(id: number, roleId: number, cond?: TaskConditionObj) {
        this.condInfo = cond
        return super.init(id, roleId)
    }

    public fromDB(data: any) {
        this.uid = data.uid
        this.id = data.id
        this.roleId = data.roleId
        this.state = data.state
        this.descId = data.descId || 0
        return this.updateRoleInfo(this.roleId).updateJson(data)
    }

    public toDB() {
        return {
            uid: this.uid,
            id: this.id,
            type: this.type,
            roleId: this.roleId,
            state: this.state,
            descId: this.descId,
            condInfos: this.toCondInfos(),
            rewards: this.rewards.map(m => m.toDB())
        }
    }

    protected updateJson(data?: any) {
        const json = this.json = assetsMgr.getJsonData('unlockTask', this.id) as UnlockTaskJItem
        if (json.desc) {
            this.desc = json.desc
        } else { //随机一个说明
            if (!this.descId) {
                const datas = assetsMgr.getJson('unlockTaskComDesc').datas, cond = this.condInfo
                this.descId = datas.filter(m => m.type === cond?.type).random()?.id || datas.random()?.id || 0
            }
            this.desc = 'unlockTaskComDesc.' + this.descId
        }
        // 条件
        if (data?.condInfos) {
            this.condInfos = data.condInfos.map(m => new TaskConditionObj().init(m.id).fromDB(m))
        }
        // 奖励
        if (data?.rewards) {
            this.rewards = data.rewards.map(m => new ConditionObj().fromDB(m))
        } else {
            this.rewards = gameHelper.getRoleTaskRewardsByConf(json, this.roleId, this.condInfo.type === TaskConditionType.HAVE_PROP)
        }
        return this
    }

    public getViewUrl() { return 'common/UnlockTaskInfo' }

    public getNameType() { return WishType.SERVE }

    public getDescType() {
        const cond = this.condInfo
        if (!cond) {
            return 4
        } else if (cond.type === TaskConditionType.UNLOCK_FOOD) {
            return 5
        } else if (cond.type === TaskConditionType.UNLOCK_SOUPBASE) {
            return 6
        } else if (cond.type === TaskConditionType.UNLOCK_FILM) {
            return 7
        } else if (cond.type === TaskConditionType.UP_FOOD_N) {
            return 8
        }
        return 4
    }

    public getCondType() { return 1 } //道具消耗类

    // 获取结束时的对话
    public getEndTalkId() { return this.json?.end_talk || '' }

    // 检测刷新完成
    public checkUpdateComplete() {
        if (this.state === TaskState.NONE || this.state === TaskState.FINISH) {
            return this.state
        }
        this.state = this.isComplete() ? TaskState.CANGET : TaskState.UNDONE
        return this.state
    }

    // 是否完成
    public isComplete() {
        return this.condInfos?.every(m => taskHelper.checkTaskCondition(m))
    }
}