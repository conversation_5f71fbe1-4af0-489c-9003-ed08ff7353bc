import {WishBaseJItem} from "../constant/DataType"
import {TaskState, WishType} from "../constant/Enums"
import {gameHelper} from "../GameHelper"
import {taskHelper} from "./TaskHelper"
import BaseWishTaskObj from "./BaseWishTaskObj"
import TaskConditionObj from "./TaskConditionObj"
import {assetsMgr} from "../AssetsMgr"
import ut from "../../common/util"

// 一个愿望
export default class WishObj extends BaseWishTaskObj {

    public json: WishBaseJItem = null

    constructor() {
        super(WishType.WISH)
    }

    public get condType() {
        return this.json.cond_type
    }

    public get condDesc() {
        return this.json.cond_desc
    }

    protected updateJson(condInfo?: any) {
        const condInfos = Array.isArray(condInfo) ? condInfo : (condInfo ? [condInfo] : [])
        this.json = assetsMgr.getJsonData('wishBase', this.id)
        if (this.json) {
            this.desc = this.json.desc
            this.rewards = gameHelper.stringToConditions(this.json.reward)
            this.condInfos = ut.stringToNumbers(this.json.condition).map((id, i) => new TaskConditionObj().init(id)).map(m => m.fromDB(condInfos.find(x => x.id === m.id)))
        }
        return this
    }

    public getViewUrl() {
        return 'common/WishInfo'
    }

    public getCondType() {
        return this.condType
    }

    // 检测刷新完成
    public checkUpdateComplete() {
        if (this.state === TaskState.NONE || this.state === TaskState.FINISH) {
            return this.state
        }
        this.state = this.isComplete() ? TaskState.CANGET : TaskState.UNDONE
        return this.state
    }

    // 是否完成
    public isComplete() {
        return this.condInfos?.every(m => taskHelper.checkTaskCondition(m))
    }
}