import {GUIDE_ORDER_TASK_ID, GUIDE_SERVE_TASK_ID, XINMEI_ROLE_ID, YAYA_ROLE_ID} from "../constant/Constant";
import {MapSceneType, TaskConditionType, TaskState, WishType} from "../constant/Enums";
import {gameHelper} from "../GameHelper";
import WorldModel from "../world/WorldModel";
import BaseWishTaskObj from "./BaseWishTaskObj";
import OrderTaskObj from "./OrderTaskObj";
import ServeTaskObj from "./ServeTaskObj";
import {tconfig} from "./TConfig";
import UnlockTaskObj from "./UnlockTaskObj";
import TaskConditionObj from "./TaskConditionObj";
import WishObj from "./WishObj";
import {assetsMgr} from "../AssetsMgr"
import ModelMgr from "../ModelMgr";
import {WISH_ID_CHANGE} from "../constant/DBBCompat";

// 愿望
export default class WishModel {

    private world: WorldModel = null

    private wishs: BaseWishTaskObj[] = [] //愿望列表

    private serveTriggerRoles: any[] = [] //当前委托触发数量
    private serveTaskCount: number = 0 //当前委托任务个数 + serveTriggerRoles
    private orderTriggerRoles: any[] = [] //当前预约触发数量
    private orderTaskCount: number = 0 //当前预约任务个数 + orderTriggerRoles
    private orderDoneInterval: number = 0
    private unlockTriggerRoles: any[] = [] //当前解锁触发的客人
    private unlockTaskCount: number = 0 //当前解锁任务个数 + unlockTriggerRoles
    private excTriggerRoles: any = {[WishType.EXCLUSIVE_1]: [], [WishType.EXCLUSIVE_2]: [], [WishType.EXCLUSIVE_3]: []} //当前解锁触发的客人
    private excTaskTypeTriggerRoles: any = {}
    private excTaskCount: any = {[WishType.EXCLUSIVE_1]: 0, [WishType.EXCLUSIVE_2]: 0, [WishType.EXCLUSIVE_3]: 0} //当前解锁任务个数 + unlockTriggerRoles
    private lastTriggerExtTaskTime: number = 0 //最后一次触发专属任务的时间

    private reddotRecord: any = {} //红点记录

    private notUseUnlockBuilds: any = {} //记录不能作为条件解锁类
    private notUseUnlockFoods: any = {} //记录不能作为条件解锁类
    private notUseUnlockSoups: any = {} //记录不能作为条件解锁类
    private notUseUnlockFilms: any = {} //记录不能作为条件解锁类

    private tempRecordCanTriggerCount: any = {} //临时记录 客人触发任务的数量

    private modelMgr: ModelMgr = null;


    public onCreate(modelMgr: ModelMgr) {
        this.modelMgr = modelMgr
        this.world = modelMgr.get('world');
        this.excTaskTypeTriggerRoles = {}
    }

    public init() {
        this.initNotUseUnlock()
        let dbHelper = this.modelMgr.getDB();
        // 持久化数据
        const ver = 3
        const data = dbHelper.register('wish', ver, this.toDB, this, Object.keys(this.toDB())) || {}
        while (data._ver < ver) {
            if (data._ver === 1) { //兼容id
                data.wishs = data.wishs || []
                for (let i = data.wishs.length - 1; i >= 0; i--) {
                    const m = data.wishs[i]
                    if (m.type === undefined || m.type === WishType.WISH) { //很早的版本还没有type
                        const id = WISH_ID_CHANGE[m.id + '_' + m.roleId]
                        if (id) {
                            m.id = id
                        } else {
                            data.wishs.splice(i, 1)
                        }
                    }
                }
            } else if (data._ver === 2) { //兼容新的事件触发机制
                // 2.老数据已经接取了某个心愿升阶挑战, 新数据下，直接移除掉
                if (data.wishs) {
                    data.wishs.delete(m => m.type === undefined || m.type === WishType.WISH)
                }
            }
            data._ver += 1
        }
        this.wishs = (data.wishs || []).map(m => this.createTask(m.type, m.id)?.fromDB(m)).filter(m => !!m)
        const world = this.world
        // 兼容星妹预约任务
        // if (gameHelper.guide.isCanEnter201Room() && !world.getRoleAttr(XINMEI_ROLE_ID)) {
        //     this.acceptOrder(this.createGuideOrderTask(XINMEI_ROLE_ID))
        // }
        // 兼容呀呀委托任务
        // if (gameHelper.guide.isGaliFinish() && !world.getRoleAttr(YAYA_ROLE_ID) && !this.wishs.some(m => m.type === WishType.SERVE && m.id === GUIDE_SERVE_TASK_ID && m.roleId === YAYA_ROLE_ID)) {
        //     this.acceptServe(this.createGuideServeTask(YAYA_ROLE_ID))
        // }
        // 兼容客人已经接取了专属任务
        this.wishs.forEach(m => this.isExcTaskByType(m.type) && world.getRoleAttr(m.roleId) && world.getRoleAttr(m.roleId).acceptExcTask(m.id))
        // 刷新数量
        this.updateServeTaskCount()
        this.updateOrderTaskCount()
        this.updateUnlockTaskCount()
        this.updateExcTaskCount()
    }

    private toDB() {
        return {
            wishs: this.wishs.map(m => m.toDB()),
        }
    }

    private initNotUseUnlock() {
        this.notUseUnlockBuilds = {}
        this.notUseUnlockFoods = {}
        this.notUseUnlockSoups = {}
        this.notUseUnlockFilms = {}
        assetsMgr.getJson('taskCondition').datas.forEach(m => this.addNotUseUnlockCond(m, this.notUseUnlockBuilds, this.notUseUnlockFoods, this.notUseUnlockSoups, this.notUseUnlockFilms))
        // cc.log(this.notUseUnlockBuilds)
    }

    private addNotUseUnlockCond(m: TaskConditionObj, builds: any, foods: any, soups: any, films: any) {
        if (m.type === TaskConditionType.UNLOCK_KEFANG_FURN) {
            this.addNotUseBuild(builds, m.tid, m.count)
        } else if (m.type === TaskConditionType.UNLOCK_FURN) {
            this.addNotUseBuild(builds, m.tid, 201, 202, 301, 302, 401, 402)
        } else if (m.type === TaskConditionType.UNLOCK_DORM_BUILD) {
            this.addNotUseBuild(builds, m.tid, MapSceneType.DORM)
        } else if (m.type === TaskConditionType.UNLOCK_SHOWER_BUILD) {
            this.addNotUseBuild(builds, m.tid, MapSceneType.SHOWER)
        } else if (m.type === TaskConditionType.UNLOCK_DINING_BUILD) {
            this.addNotUseBuild(builds, m.tid, MapSceneType.DINING)
        } else if (m.type === TaskConditionType.UNLOCK_CINEMA_BUILD) {
            this.addNotUseBuild(builds, m.tid, MapSceneType.CINEMA)
        } else if (m.type === TaskConditionType.UNLOCK_FOOD && !gameHelper.world?.getDining()?.getUnlockFood(m.tid)) { //解锁菜品
            foods[m.tid] = true
        } else if (m.type === TaskConditionType.UP_FOOD_N) { //升级菜品
            foods[m.tid] = true
        } else if (m.type === TaskConditionType.UNLOCK_SOUPBASE) {
            soups[m.tid] = true
        } else if (m.type === TaskConditionType.UNLOCK_FILM) {
            films[m.tid] = true
        }
    }

    private addNotUseBuild(builds: any, id: any, ...mapIds: any[]) {
        let arr: any[] = builds[id]
        if (!arr) {
            arr = builds[id] = []
        }
        mapIds.forEach(m => !arr.has(m) && arr.push(m))
    }

    private createTask(type: WishType, id: number) {
        if (type === WishType.SERVE) { //委托任务
            return new ServeTaskObj()
        } else if (type === WishType.ORDER) {
            return new OrderTaskObj()
        } else if (type === WishType.UNLOCK) {
            return new UnlockTaskObj()
        } else if (this.isExcTaskByType(type)) { //专属任务
            const json = assetsMgr.getJsonData('exclusiveBase', id)
            if (json) {
                let clazz = tconfig.EXC_TASK_CLASS[json.type];
                return clazz ? new clazz(type) : null
            } else {
                return null
            }
        }
        return new WishObj() //兼容老版本没有type的时候
    }


    public getWishs() {
        return this.wishs
    }

    // 获取任务
    public getTaskByUid(uid: string): any {
        return this.wishs.find(m => m.uid === uid)
    }

    // 当前实际总任务数量
    private getActSumTaskCount() {
        return this.wishs.length + this.getSumTriggerCount()
    }

    // 当前的触发任务数量
    private getSumTriggerCount() {
        return this.serveTriggerRoles.length + this.orderTriggerRoles.length + this.unlockTriggerRoles.length + this.excTriggerRoles[WishType.EXCLUSIVE_1].length + this.excTriggerRoles[WishType.EXCLUSIVE_2].length + this.excTriggerRoles[WishType.EXCLUSIVE_3].length
    }

    // 是否有某个心愿
    public hasWish(id: number, roleId: number) {
        return this.wishs.some(m => m.type === WishType.WISH && m.id === id && m.roleId === roleId)
    }

    // 刷新委托任务数量
    public updateServeTaskCount() {
        this.serveTaskCount = this.wishs.filter(m => m.type === WishType.SERVE && !m.isFinish()).length
    }


    // 刷新委托任务数量
    public updateOrderTaskCount() {
        this.orderTaskCount = this.wishs.filter(m => m.type === WishType.ORDER && !m.isFinish()).length
    }

    public updateUnlockTaskCount() {
        this.unlockTaskCount = this.wishs.filter(m => m.type === WishType.UNLOCK && !m.isFinish()).length
    }

    // --------------------------------------------专属任务--------------------------------------------------
    public isExcTaskByType(type: WishType) {
        return type >= WishType.EXCLUSIVE_1 && type <= WishType.EXCLUSIVE_3
    }

    // 刷新解锁任务数量
    public updateExcTaskCount() {
        this.excTaskCount[WishType.EXCLUSIVE_1] = this.wishs.filter(m => m.type === WishType.EXCLUSIVE_1 && !m.isFinish()).length
        this.excTaskCount[WishType.EXCLUSIVE_2] = this.wishs.filter(m => m.type === WishType.EXCLUSIVE_2 && !m.isFinish()).length
        this.excTaskCount[WishType.EXCLUSIVE_3] = this.wishs.filter(m => m.type === WishType.EXCLUSIVE_3 && !m.isFinish()).length
    }

}