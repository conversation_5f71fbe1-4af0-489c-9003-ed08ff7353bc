import WishModel from "./task/WishModel";

require("../extend/ExtendArray")
import {assetsMgr} from "./AssetsMgr"

assetsMgr.init()
import DBModel from "./DBModel"
import {gameHelper} from "./GameHelper"
import MainModel from "./main/MainModel"
import OfflineSysModel from "./OfflineSysModel"
import WorldModel from "./world/WorldModel"
import {ConditionType, MapSceneType, PropType} from "./constant/Enums"
import ConditionObj from "./common/ConditionObj"
import GaLiModel from "./gali/GaLiModel"
import ut from "../common/util"
import GlobalModel from "./Global"
import TaskModel from "./task/TaskModel"
import BagModel from "./bag/BagModel"
import SoupModel from "./shower/SoupModel"
import Labour from "./activities/LaborDayOnlineTimeModel"
import GardenModel from "./garden/GardenModel";


// 数据模型管理
export default class ModelMgr {

    private models: Map<string, any> = new Map<string, any>()

    static __default_models: { [key: string]: any } = {
        'db': DBModel,
        'offline': OfflineSysModel,
        'world': WorldModel,
        'gali': GaLiModel,
        'global': GlobalModel,
        'task': TaskModel,
        'bag': BagModel,
        'soup': SoupModel,
        'wish': WishModel,
        'labour':Labour,

    }

    private _world: WorldModel = null
    private _gali: GaLiModel = null
    private _task: TaskModel = null
    private _global: GlobalModel = null
    private _offline: OfflineSysModel = null
    private _bag: BagModel = null
    private _wish: WishModel = null
    private _labour: Labour = null


    get world() {
        if (!this._world) {
            this._world = this.get<WorldModel>('world')
        }
        return this._world
    }

    public get gali() {
        return this._gali || (this._gali = this.get('gali'))
    }

    public get task() {
        return this._task || (this._task = this.get('task'))
    }

    public get global() {
        return this._global || (this._global = this.get('global'))
    }

    public get offline() {
        return this._offline || (this._offline = this.get('offline'))
    }

    public get bag() {
        return this._bag || (this._bag = this.get('bag'))
    }

    public get wish() {
        return this._wish || (this._wish = this.get('wish'))
    }
    public get labour() {
        return this._labour || (this._labour = this.get('labour'))
    }

    // 静态添加模型
    public init(record?: any, map?: any) {
        if (!map) {
            map = ModelMgr.__default_models
        }
        for (let key in map) {
            this.add(new map[key](), key)
        }
        this.create()

        if (record) {
            let db = this.getDB()
            db && db.init(record)
        }
        this.models.forEach((m, k) => {
            if (m.init && k != 'db') {
                m.init()
            }
        })
        return this
    }

    public create() {
        this.models.forEach(m => m.onCreate && m.onCreate(this))
    }

    private add(model: any, type: string) {
        model.type = type
        this.models.set(type, model)
    }

    // 获取模型
    public get<T>(key: string): T {
        let model: any = this.models.get(key)
        return model
    }

    public getDB() {
        return this.get<DBModel>('db')
    }

    public getMain() {
        return this.get<WorldModel>('world').getMain()
    }

    public getHeart() {
        return this.global.getHeart()
    }

    // 获取乌冬皮肤所有消耗
    public getAllWudongSkinCost(type: ConditionType) {
        let main = this.getMain(), val = 0
        assetsMgr.getJson('wudongBase').datas.forEach(data => {
            if (main.hasWudongSkin(data.id)) {
                gameHelper.stringToConditions(data.unlock_cost).forEach(m => {
                    if (m.type === type) {
                        let count = m.count
                        if (data.discount) {
                            count *= data.discount * 0.01
                        }
                        val += count
                    }
                })
            }
        })
        return val
    }

    // 获取大楼皮肤所有消耗
    public getAllMainSkinCost(type: ConditionType) {
        let main = this.getMain(), val = 0
        assetsMgr.getJson('hotelSkinBase').datas.forEach(m => {
            if (main.hasMainSkin(m.id)) {
                gameHelper.stringToConditions(m.unlock_cost).forEach(m => {
                    if (m.type === type) {
                        val += m.count
                    }
                })
            }
        })
        return val
    }

    public getAllBuildCost(type: ConditionType) {
        let world = this.get<WorldModel>('world')
        let maps = world.getMaps()
        let sum = 0
        for (let map of maps) {
            if (map.sceneType == MapSceneType.MAIN) continue
            sum += map.getAllBuildCost(type)
        }
        return sum
    }

    public getMainAttrCos() {
        let attr = this.getMain().getMainAttr()
        let biscuits = 0, candies = 0
        let json = assetsMgr.getJson('hostelAttr')
        let calCost = (attr) => {
            if (!attr) return
            let {id, lv} = attr
            lv = lv - 1
            while (lv >= 0) {
                let attrId = id * 1000 + lv
                let data = json.getById(attrId)
                if (data) {
                    let costs = gameHelper.stringToConditions(data.cost)
                    costs.forEach((cost) => {
                        if (cost.type == ConditionType.BISCUITS) {
                            biscuits += cost.count
                        } else if (cost.type == ConditionType.CANDIES) {
                            candies += cost.count
                        }
                    })
                }
                lv--
            }
        }
        calCost(attr.doubleEarnAttr)
        calCost(attr.offlineTimeAttr)
        calCost(attr.tipLimtAttr)
        return {biscuits, candies}
    }

    // 获取已领成就奖励总和
    public getAchieveAwardSum() {
        let biscuits = 0, candies = 0
        this.task.getAchieves().forEach(x => x.isFinish() && x.rewards.forEach(m => {
            if (m.type === ConditionType.BISCUITS) {
                biscuits += m.count
            } else if (m.type === ConditionType.CANDIES) {
                candies += m.count
            }
        }))
        return {biscuits, candies}
    }

    // 获取客人的奖励总和
    public getRoleAwardSum() {
        let biscuits = 0, candies = 0
        this.world.getRoleAttrs().forEach(x => {
            // 分享奖励
            if (x.hasShareAward === -1 && x.base && x.base.share_award) {
                const cond = new ConditionObj().fromString(x.base.share_award)
                if (cond.type === ConditionType.BISCUITS) {
                    biscuits += cond.count
                } else if (cond.type === ConditionType.CANDIES) {
                    candies += cond.count
                }
            }
            // 升级奖励
            x.upRewards && x.upRewards.forEach(r => r.rewards?.forEach(m => {
                if (m.type === ConditionType.BISCUITS) {
                    biscuits += m.count
                } else if (m.type === ConditionType.CANDIES) {
                    candies += m.count
                }
            }))
        })
        return {biscuits, candies}
    }

    // 获取咖喱信件奖励总和
    public getGaliPostcardAwardSum() {
        let biscuits = 0, candies = 0
        this.gali.getPostcards().forEach(m => {
            if (!m.reward) {
            } else if (m.reward.type === ConditionType.BISCUITS) {
                biscuits += m.reward.count
            } else if (m.reward.type === ConditionType.CANDIES) {
                candies += m.reward.count
            }
        })
        return {biscuits, candies}
    }

    // 获取奖励列表 感觉奖励id
    public getRewardsById(id: number): ConditionObj[] {
        const json = assetsMgr.getJsonData('reward', id)
        if (!json) {
            return []
        }
        const list = gameHelper.stringToConditions(json.reward)
        // 是否有成长系数 这个只能在创建的时候就计算了
        if (json.heart_add_factor) {
            const heartFactors = ut.stringToNumbers(json.heart_add_factor), heart = this.getHeart()
            const offlineFactors = ut.stringToNumbers(json.offline_add_factor),
                offline = this.offline.getOfflineExpectedIncome()
            const onlineFactors = ut.stringToNumbers(json.online_add_factor),
                online = this.offline.getOnlineExpectedIncome()
            list.forEach((m, i) => {
                m.randomCount()
                const count = m.count
                m.count = Math.floor(Math.floor((count + (heartFactors[i] || 0) * heart) * 0.01) * 100)
                m.count += Math.floor((offlineFactors[i] || 0) * offline)
                m.count += Math.floor((onlineFactors[i] || 0) * online)
            })
        }
        return list
    }

    // 计算场景所有收入
    public getMapAllIncome(income: number, builds: any[], sceneType: MapSceneType) {
        let use_income = 0, n = 0, m = gameHelper.getLeaveOdds(sceneType) * 0.01
        builds.forEach(m => {
            if (!!m.use_income) {
                use_income += m.use_income
                n += 1
            }
        })
        // 与某个设施的互动概率:(1-m)(1-(1-m)n)/mn
        if (n > 0 && m > 0) {
            use_income *= (1 - m) * (1 - Math.pow(1 - m, n)) / (m * n)
        }
        // 计算客人技能
        let income_r = 1, use_income_r = 1
        this.world.getHasSceneSpeRoles(sceneType).forEach(m => {
            income_r += m.incomeMul
            use_income_r += m.useIncomeMul
        })
        // 收入之和+互动概率x互动收入之和
        return Math.floor(income * income_r + use_income * use_income_r)
    }


    public getUsingFurn(map = {}) {
        // 用来保证不重复统计家具 每次进来只计数一次
        let array = [];
        let rooms = this.world.getKefangs()
        for (let room of rooms) {
            let info = room.currFurnitureInfo || {};
            let furnitures = info.furnitures || [];
            furnitures.forEach(item => {
                if (array.indexOf(item.id) === -1) {
                    map[item.id] = map[item.id] + 1 || 1;
                    array.push(item.id);
                }
            })
        }
        return map;
    }

    public getSpecFurnCount() {
        let rooms = this.world.getKefangs()
        let count = 0
        for (let room of rooms) {
            count += room.getSpecFurnitures().length
        }
        let speProps = this.bag.getProps().filter(p => p.type == PropType.FURNITURE)
        for (let prop of speProps) {
            count += prop.count
        }
        return count
    }

    public getRoleFinishComTaskCount() {
        return this.world.getRoleAttrs().reduce((val, cur) => cur.triggerAttrTaskIds.length + val, 0)
    }

    // 获取所有客人已经完成的专属任务总数
    public getRoleFinishExcTaskCount() {
        return this.world.getRoleAttrs().reduce((val, cur) => cur.finishExcTasks.length + val, 0)
    }

    // 是否完成指定专属任务
    public hasRoleFinishExcTask(id = 0) {
        for (let cur of this.world.getRoleAttrs()) {
            if (cur.finishExcTasks.length > 0) {
                if (cur.finishExcTasks.indexOf(id) > -1) {
                    return true;
                }
            }
        }
        return false;
    }

    // 是否接取指定专属任务
    public hasWishExcTask(id = 0) {
        if (this.wish.getWishs().length > 0) {
            for (let w of this.wish.getWishs()) {
                if (w && w.id && w.id === id) {
                    return true;
                }
            }
        }
        return false;
    }

    public getRoleLvInfo() {
        let data = [];
        let roleAttrs = this.world.getRoleAttrs();
        if (!roleAttrs) return data;
        roleAttrs.forEach(role => {
            let _i = {id: role.id, lv: role.lv};
            data.push(_i);
        })
        return data;
    }

    public getCinemaLvInfo() {
        let data = [];
        let cinema = this.world.getCinema();
        if (!cinema) return data;
        let films = cinema.getFilms();
        if (!films) return data;
        films.forEach(film => {
            let lv = film.id % 10;
            let _i = {id: film.id, lv};
            data.push(_i);
        })
        return data;
    }

    public getDiningLvInfo() {
        let data = [];
        let dining = this.world.getDining();
        if (!dining) return data;
        let unlockFoods = dining.getUnlockFoods();
        unlockFoods.forEach(food => {
            let _i = {id: food.id, lv: food.lv};
            data.push(_i);
        })
        return data;
    }

    public getSoupLvInfo() {
        let data = [];
        let shower = this.world.getShower();
        if (!shower) return data;
        let soup = shower.getSoup();
        if (!soup) return data;
        let soupList = soup.getSoupList();
        if (!soupList || !soupList.length) return data;
        soupList.forEach(s => {
            let _i = {id: s.id, lv: s.level}
            data.push(_i);
        });
        return data;
    }

    // 移除存档特权 退款用
    public removePrivilegeByAction(action: string) {
        let shop = this.world.getShop();
        let items = shop.getPurchasedItems() || [];
        items = items.filter(item => {
            return item.action !== action;
        })
        shop.setPurchasedItems(items);
    }

}
