import { SoupbaseItemJTtem } from "../constant/DataType"
import { assetsMgr } from "../AssetsMgr"


// 汤料
export default class SoupObj {

    public id: number = 0 // id
    public level: number = 0 // 等级

    public json: SoupbaseItemJTtem = null // 配置信息
    public attr: any = null // 汤料每级属性


    public init(id: number) {
        this.id = id
        this.level = 1
        this.initJson()
        return this
    }

    public fromDB(data: any) {
        this.id = data.id
        this.level = data.level
        this.initJson()
        return this
    }

    private initJson() {
        this.json = assetsMgr.getJsonData('soupBase', this.id)
        this.initAttr()
    }

    private initAttr() {
        this.attr = assetsMgr.getJsonData('soupAttr', this.id * 100 + this.level)
    }

    public get income() { return this.attr ? this.attr.income : 0 }
    public get heart() { return this.attr ? this.attr.heart : 0 }
}