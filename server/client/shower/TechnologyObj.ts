import { assetsMgr } from "../AssetsMgr"
import { TechnologyConfJItem } from "../constant/DataType"

// 科技
export default class TechnologyObj {

    private unlockAttrs: number[] = [] //已经解锁的属性

    public income: number = 0//收入 a
    // public materials: number[] = [] //解锁材料id a
    // public cut_off: number = 0 //降低材料价格 (%) a
    // public retime_off: number = 0 //缩短材料刷新时间 (分钟) a
    // public mix_eff: number = 0 //提升合成效率 (%) a
    // public hotspring_eff: number = 0 //提升泡温泉的效率 (%) a
    // public sptime: number = 0 //延长汤料时间 (秒) a
    // public compPorps: number[] = [] //可以合成出来的道具 a

    public init() {
        // this.materials = [1001, 1002] //默认添加前面2个道具
        // this.compPorps = [2001, 4001] //默认添加升级材料
        return this
    }

    public fromDB(data: any) {
        this.unlockAttrs = data.unlockAttrs || []
        this.init()
        this.unlockAttrs.forEach(id => this.updateAttr(assetsMgr.getJsonData('technologyConfig', id)))
        return this
    }

    public getUnlockAttrs() { return this.unlockAttrs }
    public hasAttr(id: number) { return this.unlockAttrs.has(id) }

    private updateAttr(json: TechnologyConfJItem) {
        this.income += json.income || 0
        // this.cut_off += json.cut_off || 0
        // this.retime_off += json.retime_off || 0
        // this.mix_eff += json.mix_eff || 0
        // this.hotspring_eff += json.hotspring_eff || 0
        // this.sptime += json.sptime || 0
        // json.material && this.materials.push(json.material)
        // json.comp_prop && this.compPorps.push(json.comp_prop)
    }
}