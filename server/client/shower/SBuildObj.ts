import BuildObj from "../common/BuildObj"
import { ShowerBuildType } from "../constant/Enums"

// 浴室 建筑设施
export default class SBuildObj extends BuildObj {

    public get durability() { return this.json ? this.json.durability : 0 }
    public get hotspring_eff() { return this.json ? this.json.hotspring_eff : 0 }

    public getSceneType() { return 'shower' }

    public getJsonName() { return 'bathroom' }

    // 是否有耐久
    public isHasDur() {
        return this.durability > 0
    }

    // 是否温泉
    public isHotspring() {
        return this.type >= ShowerBuildType.INDOORSPA && this.type <= ShowerBuildType.OUTDOORMINSPA
    }
}