import { assetsMgr } from "../AssetsMgr"
import ModelMgr from "../ModelMgr"
import SoupObj from "./SoupObj"

// 温泉材料合成
export default class SoupModel {

    /* -----------------------持久化数据--------------------- */
    private curProficiencyLv: 0 // 当前熟练度等级
    private curProficiency: 0 // 当前等级的合成熟练度
    private todayCompCnt: 0 // 今日可用的特典合成次数
    private curStamina: 0 // 当前体力
    private recoverStaminaStartTime: number = 0 // 开始恢复体力时间
    private lastUpdateTodayCompTime: number = 0 // 上次刷新今日特典的时间
    private adFreeCount: number = 0 // 泡泡广告的免费可使用次数

    private soupList: SoupObj[] = [] // 汤料列表
    private compRecordList: number[] = [1101] // 合成记录

    private todayCompCfgId: number = 0 // 今日特典配置id
    private todayCompMaxCnt: number = 0 // 今日特典最高合成次数
    private todayCompRewardLimit: { id: number, count: number }[] = [] // 今日特典特殊合成奖励限制


    /* -----------------------暂存数据--------------------- */
    private readonly STUFF_MAX_COUNT = 30 // 材料仓库数量上限(5*6布局)
    private sortOffsetTime: number = 5 // 排序间隔
    private soupChance: number = 0.22 // 客人选择汤料的概率
    private add_stamina_limit: number = 75 // 大于等于不能观看广告
    private addStamina: 25
    private maxStamina: number = 50 // 最大体力
    private recoverStaminaMaxTime: number = 180  // 体力恢复时间
    private adFreeMaxCount: number = 1 // 广告的免费总次数
    private isSyncStaminaTime: boolean = false  // 是否正在同步时间
    private fullMessageInterval: number = 0 // 体力满消息通知间隔

    private _isOpenTodayComp: boolean = false // 今日特典是否开启
    private nextProficiencyLimit: number = 0 // 当前熟练度升级限制
    private curMaxLvCompStuffId: number = null // 当前最高级可合成材料id
    private todayCompLimitLv: number = 0 // 今日特典限制等级
    private todayCompTargetType: number = 0 // 今日特典目标产物类型
    private todayCompTargetId: number = 0 // 今日特典目标产物id
    private todayCompStuffIds: number[] = [] // 今日特典合成所需材料id
    private proficiencyIsMax: boolean = false // 熟练度等级是否已满

    private sortTime: number = 0 // 排序倒计时
    public soupRedList: { id: number, show: boolean }[] = [] // 显示一次后就不再显示

    private modelMgr: ModelMgr = null

    public onCreate(modelMgr: ModelMgr) { this.modelMgr = modelMgr}

    public onClean() { }

    public init() {
        this.initBaseConfig()

        let dbHelper = this.modelMgr.getDB()
        const ver = 1
        const data = dbHelper.register('soup') || {}

        this.soupList = data.soupList ? data.soupList.map(m => new SoupObj().fromDB(m)) : []
    }

    // 基础信息配置
    private initBaseConfig() {
        let cfg = assetsMgr.getJson('soupConfig')
        this.maxStamina = cfg.getById(1).value
        this.addStamina = cfg.getById(2).value
        this.recoverStaminaMaxTime = cfg.getById(3).value
        this.add_stamina_limit = cfg.getById(4).value
        this.soupChance = cfg.getById(6).value
        this.sortOffsetTime = cfg.getById(7).value
    }

    public getCurMaxLvCompStuffId() { return this.curMaxLvCompStuffId }

    public getCurProficiencyLv() { return this.curProficiencyLv }
    public getProficiencyCirclePercent() { return this.curProficiency / this.nextProficiencyLimit * 0.84 }
    public getProficiencyPercent() { return this.curProficiency/ this.nextProficiencyLimit }
    public getProficiencyText() { return this.curProficiency + '/' + this.nextProficiencyLimit }
    public isProficiencyMax() { return this.proficiencyIsMax }

    public getTodayCompTargetType() { return this.todayCompTargetType }
    public getTodayCompTargetId() { return this.todayCompTargetId }
    public getTodayCompStuffIds() { return this.todayCompStuffIds }
    public getTodayCompCnt() { return this.todayCompCnt }
    public getTodayCompCntText() { return this.todayCompCnt + '/' + this.todayCompMaxCnt }

    public getAddStamina() { return this.addStamina }
    public getCurStamina() { return this.curStamina }
    public getStaminaPercent() { return this.curStamina / this.maxStamina }
    public getStaminaText() { return this.curStamina + '/' + this.maxStamina }
    public isStaminaMaxLimt() { return this.curStamina >= this.add_stamina_limit } // 体力是否达到广告上限

    public getAdFreeText() { return (this.adFreeMaxCount - this.adFreeCount) + '/' + this.adFreeMaxCount } // 广告免费次数文字显示
    public hasAdFreeCnt() { return this.adFreeCount < this.adFreeMaxCount } // 是否有广告免费次数
    public useAdFreeCnt(cnt: number) { this.adFreeCount = Math.max(this.adFreeCount + cnt, 0) } // 使用广告的免费次数

    public getUseSoupChance() { return this.soupChance }

    // 获取已有的汤料
    public getSoupList() {
        return this.soupList
    }

    // 通过id获取汤料
    public getSoup(id: number) {
        return this.soupList.find(m => m.id === id)
    }
}