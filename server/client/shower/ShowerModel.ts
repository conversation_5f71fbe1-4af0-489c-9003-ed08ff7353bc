import BaseMapModel from "../map/BaseMapModel"
import WorldModel from "../world/WorldModel"
import SBuildObj from "./SBuildObj"
import { ShowerBuildType, MapSceneType, MessageType, TaskConditionType, NpcType } from "../constant/Enums"
import { gameHelper } from "../GameHelper"
import TechnologyObj from "./TechnologyObj"
import ModelMgr from "../ModelMgr"
import { assetsMgr } from "../AssetsMgr"
import SoupModel from "./SoupModel"

/**
 * 浴室
 */
export default class ShowerModel extends BaseMapModel {

    private builds: SBuildObj[] = []
    // private buildDurabilitys: BuildDurabilityObj[] = [] //当前建筑耐久
    // private hotsprs: HotsprObj[] = [] //温泉
    // private stewpot: StewpotObj = null //烧锅
    private technology: TechnologyObj = null //科技

    private soup: SoupModel = null //汤料模块

    constructor(modelMgr: ModelMgr) {
        super('shower', modelMgr)
        super.init(MapSceneType.SHOWER)
        let dbHelper = modelMgr.getDB()

        this.soup = modelMgr.get('soup')
        this.soup.init()

        // 注册持久化数据
        const ver = 5
        const data = dbHelper.register(this.uid) || { _ver: ver }
      
        // 科技 这个必须在烧锅之前 因为骚锅需要他
        this.technology = data.technology ? new TechnologyObj().fromDB(data.technology) : new TechnologyObj().init()
       
        // 获取所有设施
        if (data.builds) {
            this.builds = data.builds.map(m => new SBuildObj().fromDB(m))
        } else {
            this.builds = assetsMgr.getJson('bathroomUnlock').datas.filter(m => gameHelper.conditionIsNone(m.unlock_cost)).map(m => new SBuildObj().init(m.id, true))
            this.builds.push(new SBuildObj().init(0, true))
        }

        this.updateAttr()
    }

    public getSumIncome() { return this.attr.income + this.technology.income }

     // 获取所有的收入 包括使用设施的
     public getOnlineIncome() {
        let list = this.soup.getSoupList(), len = list.length, income = 0
        if (len > 0) {
            income = list.reduce((val, cur) => val + cur.income, 0) / len * this.soup.getUseSoupChance()
        }
        return this.modelMgr.getMapAllIncome(this.attr.income + income, this.builds, this.sceneType)
    }

    public getOfflineIncome() { return this.builds.reduce((val, cur) => cur.output + val, 0) }

    // 获取所有的love 包括使用设施的
    public getOfflineLove() { return this.attr.fav }

    public getSoup(){
        return this.soup;
    }

    private updateAttr() {
        this.attr.reset()
        this.builds.forEach(m => {
            this.attr.add(m)
        })
    }
}