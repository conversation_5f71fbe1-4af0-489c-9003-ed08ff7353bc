import fs from "fs"
import path from "path"
import util from "../common/util"

class AssetsMgr {

    private jsons = new Map<string, JsonConfData>()// 公共配置文件
    private inited: boolean = false

    public init() {
        if (this.inited) return
        this.loadJsons()
        this.inited = true
    }

    // 初始json文件配置
    private loadJsons() {
        let dir = path.join(__dirname, "json")
        let fileNames = fs.readdirSync(dir)
        for (let fileName of fileNames) {
            let dataIdMap = {}
            let extName = path.extname(fileName)
            if (extName == '.json') {
                let jsonStr = fs.readFileSync(path.join(dir, fileName)).toString()
                let json
                try {
                    json = JSON.parse(jsonStr)
                } catch (error) {
                    console.error("loadJsons fail", fileName)
                    continue
                }
                if (json.length > 0 && json[0]['id'] !== undefined) {
                    json.forEach(m => dataIdMap[m.id] = m)
                }
                let [name] = fileName.split(extName)

                this.jsons.set(name, {
                    datas: json,
                    // @ts-ignore
                    dataIdMap: dataIdMap,
                    getById(id: string | number) {
                        return this.dataIdMap[id]
                    },
                    get(key: string, value: any) {
                        return this.datas.filter(m => m[key] === value)
                    }
                })

            }
        }
    }

    public getJson(key: string): JsonConfData {
        return this.jsons.get(key)
    }

    public getJsonData(key: string, id: string | number): any {
        const json = this.jsons.get(key)
        return json && json.getById(id)
    }

    // 根据key获取文本
    public lang(key: string, lang = "en", ...params: any[]) {
        if (!key) {
            return ''
        }
        const [name, id] = key.split('.')
        const json = this.getJsonData(name, id) || {}
        const val = json[lang]
        if (val !== undefined) {
            const _params = []
            params.forEach(m => Array.isArray(m) ? _params.pushArr(m) : _params.push(m))
            return util.stringFormat(val, this.updateParams(_params, lang))
        }
    }

    // 刷新参数
    private updateParams(params: any[], lang: string) {
        return params.map(m => {
            if (typeof (m) === 'string' && m.indexOf('.') !== -1) {
                const [name, id] = m.split('.')
                const json = this.getJsonData(name, id) || {}
                const val = json[lang]
                return val !== undefined ? val : m
            }
            return m
        })
    }
}

export const assetsMgr = new AssetsMgr()