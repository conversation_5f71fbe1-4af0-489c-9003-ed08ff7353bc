import { assetsMgr } from "../AssetsMgr"
import ConditionObj from "../common/ConditionObj"

// 明信片
export default class PostcardObj {

    public id: number = 0 //明信片id
    public debris: number[] = [] //碎片id
    public isClaim: boolean = false //是否领取

    public json: any = null
    public reward: ConditionObj = null
    public debrisJsons: { id: number, pos: number }[] = [] //碎片json信息列表

    public init(id: number) {
        this.id = id
        this.json = assetsMgr.getJsonData('adventurePostcard', id)
        this.json && (this.reward = new ConditionObj().fromString(this.json.reward))
        // this.initDebrisJson()
        return this
    }

    // private initDebrisJson() {
    //     this.debrisJsons = assetsMgr.getJson('adventureDebris').get('cardid', this.id).map(m => {
    //         return { id: m.id, pos: m.pos }
    //     })
    // }

    public fromDB(data: any) {
        this.id = data.id
        this.debris = data.debris || []
        this.isClaim = !!data.isClaim
        this.init(this.id)
        return this
    }

    public get mapId() { return this.json?.map }

    // 是否有碎片
    public hasDebris(id: number) {
        return this.debris.has(id)
    }
}
