import ModelMgr from "../ModelMgr"
import PostcardObj from "./PostcardObj"

// 挂机系统-咖喱
export default class GaLiModel{

    private postcards: PostcardObj[] = [] //明信片列表

    private modelMgr: ModelMgr = null

    public onCreate(modelMgr: ModelMgr) {
        this.modelMgr = modelMgr
    }

    // 初始化咖喱挂机 
    public init() {
        let dbHelper = this.modelMgr.getDB()
        const data = dbHelper.register('gali') || {}
        this.postcards = (data.postcards || []).map(m => new PostcardObj().fromDB(m))
    }

    public getPostcards() { return this.postcards }
    public getPostcardsByMapId(id: number) { return this.postcards.filter(m => m.mapId === id) }
}