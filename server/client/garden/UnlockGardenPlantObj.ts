import UnlockFurnObj from "../kefang/UnlockFurnObj";
import {PlantStage, PlantStatus} from "../constant/Enums";
import {assetsMgr} from "../AssetsMgr";

type PlantBaseJItem = {
    id: number;
    qua: number;// 品质
    init_stage: number;// 初始阶段
    max_count: number;// 全局限量
    name: string;
    desc: string;
    leaves_reward: string;//成熟给的奖励
    fruit: number;// 果实id
    fruit_count: number;// 果实限量
    fruit_cd: number;//结果周期(h)
}

export default class UnlockGardenPlantObj extends UnlockFurnObj {
    public currentStage: number = PlantStage.SEED// 植物当前的状态，0，1，2，3四个阶段
    private currentExp: number = 0// 当前植物经验值
    private currentStageExp: number = 0// 当前植物在该等级的经验值
    public startFruitTime: number = 0// 当前植物开始结果的时间
    public fruitSubCDTime: number = 0// 当前减少的cd时间
    public status: number = 0// 植物状态 ，可以浇水，施肥，伤心了，虫害
    public plantBase: PlantBaseJItem = null

    public plantUID: string = ''// 植物的id
    public appearID: number = 0
    public waterTime: number = 0// 上一次浇水的时间
    public fertilizeTime: number = 0// 上一次施肥的时间
    public waterCount: number = 0// 累计浇水次数
    public fertilizeCount: number = 0// 累计施肥次数
    public singCount: number = 0// 累计唱歌次数
    public dewormingCount: number = 0// 累计除虫次数
    public fruitCount: number = 0// 植物结果的次数
    public canGetFruit: boolean = false// 植物当前没有被采摘的果实
    public dropFruitcount: number = 0

    public fromDB(data: any) {
        this.currentExp = data.currentExp || 0
        this.currentStageExp = data.currentStageExp || 0
        this.currentStage = data.currentStage || PlantStage.SEED
        this.plantUID = data.plantUID || ""
        this.appearID = data.appearID || 0
        this.waterTime = data.waterTime || 0
        this.waterCount = data.waterCount || 0
        this.fertilizeTime = data.fertilizeTime || 0
        this.fertilizeCount = data.fertilizeCount || 0
        this.singCount = data.singCount || 0
        this.dewormingCount = data.dewormingCount || 0
        this.fruitCount = data.fruitCount | 0
        this.canGetFruit = data.canGetFruit || false
        this.fruitSubCDTime = data.fruitSubCDTime || 0
        this.startFruitTime = data.startFruitTime || 0
        this.status = data.status || PlantStatus.NONE
        this.dropFruitcount = data.dropFruitcount || 0
        this.updateBase(data.id)
        if (!this.base) {
            return null
        }
        this._output = this.json ? (this.json.output || 0) : 0
        this._homeOutput = this.json ? (this.json.home_output || 0) : 0
        this.select = (this.isHasSelect() && data.select) || this.id

        return this
    }

    public updateBase(id: string) {
        this.id = id
        this.plantBase = assetsMgr.getJsonData('plantBase', Number(id))
        let attrId = Number(id) * 100 + this.currentStage + 1
        this.base = assetsMgr.getJsonData('plantAttr', attrId)

        this.updateStageExp()
    }

    private updateStageExp() {
        if (this.currentStage > PlantStage.SEED) {
            let stage = this.currentStage
            let subtotal = 0
            for (let i = stage; i > 0; i--) {
                let attrId = Number(this.id) * 100 + i
                subtotal += assetsMgr.getJsonData('plantAttr', attrId)?.exp || 0
            }
            this.currentStageExp = this.currentExp - subtotal
        } else {
            this.currentStageExp = this.currentExp
        }
    }

    public get qua() { return this.plantBase?.qua || 0 }
    public get name() { return this.plantBase?.name || '' }
    public get desc() { return this.plantBase?.desc || '' }
    public get leaves_reward() { return this.plantBase?.leaves_reward || '' }
    public get fruit() { return this.plantBase?.fruit || 0 }
    public get fruit_count() { return this.plantBase?.fruit_count || 0 }
    public get fruit_cd() { return this.plantBase?.fruit_cd || 0 }
    public get exp() { return this.base?.exp || 0 }
    public get leaf_output() { return this.base?.leaf_output || 0 }
    public get heart() { return this.base?.heart || 0 }
}
