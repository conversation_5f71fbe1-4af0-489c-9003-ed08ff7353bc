import ModelMgr from "../ModelMgr";
import {FurnitureType} from "../constant/Enums";
import UnlockGardenBuildObj from "./UnlockGardenBuildObj";
import UnlockGardenPlantObj from "./UnlockGardenPlantObj";
import BaseMapModel from "../map/BaseMapModel";
import WorldModel from "../world/WorldModel";
import DropMoneyObj from "../world/DropMoneyObj";

export const GARDEN_LEVEL = {
    ONE: 1,
    TWO: 2,
    THREE: 3,
}

export default class GardenModel extends BaseMapModel {
    private unlockBuilds: UnlockGardenBuildObj[] = []// 已经解锁的家具
    private unlockPlants: UnlockGardenPlantObj[] = []//  玩家拥有的植物
    private level: number = 1
    private model: WorldModel = null
    private gardenTotalLeaves: number = 0// 累积树叶量

    constructor(modelMgr: ModelMgr) {
        super('garden', modelMgr)
        let dbHelper = modelMgr.getDB()
        const ver = 2
        const data = dbHelper.register('garden', ver, this.toDB, this) || {}
        this.level = data.level || 1
        this.unlockPlants = (data.unlockPlants || []).map(m => new UnlockGardenPlantObj().fromDB(m))
        this.unlockBuilds = (data.unlockBuilds || []).map(m => new UnlockGardenBuildObj().fromDB(m))
        this.gardenTotalLeaves = data.gardenTotalLeaves || 0
        this.initDB()
    }

    public toDB() {
    }

    public getTotalLeaves() {
        return this.gardenTotalLeaves
    }

    public getTotalDropLeaves() {
        let total = 0
        this.dropLeaf.forEach((e: DropMoneyObj) => {
            total += e.money
        })
        return total
    }

    public getCurrentLeavesOutPut() {
        let buildIncome = 0
        this.unlockBuilds.forEach(b => {
            if (b.base.type >= FurnitureType.G_FLOOR_1 && b.base.type <= FurnitureType.G_EDGE_4) {
                // 地面的分等级计算
                // 建筑
                //501  507一级
                // 501，502 ，507二级
                // 501，502，503 ，504，505，506，507，509，510，511，512，513三级
                if (this.level === GARDEN_LEVEL.ONE && (b.type === 501 || b.type === 507)) {
                    buildIncome += b.json?.leaf_output || 0
                } else if (this.level === GARDEN_LEVEL.TWO && (b.type === 501 || b.type === 502 || b.type === 507)) {
                    buildIncome += b.json?.leaf_output || 0
                } else if (this.level === GARDEN_LEVEL.THREE && (b.type !== 508)) {
                    buildIncome += b.json?.leaf_output || 0
                }
            } else {
                buildIncome += b.json?.leaf_output || 0
            }

        })
        let plantIncome = 0
        this.unlockPlants.forEach(p => {
            plantIncome += p.leaf_output
        })
        return (buildIncome + plantIncome)
    }

}
