import Unlock<PERSON>urnObj from "../kefang/UnlockFurnObj";
import {FurnitureType} from "../constant/Enums";
import {assetsMgr} from "../AssetsMgr";

export default class UnlockGardenBuildObj extends UnlockFurnObj {

    public fromDB(data: any) {
        this.isGardenDiy = data.isGardenDiy
        if (this.isGardenDiy) {
            data.id = this.id.startsWith('SPE_') ? Number(this.id.replace('SPE_', '')) : 0
            this.initGardenBase(data.id)
        } else {
            this.initBase(data.id)
        }
        if (!this.base) {
            return null
        }
        this.json = assetsMgr.getJson('gardenUnlock').datas.find(m => m.id === data.id && (data.unlockType === 0 || m.type === data.unlockType))
        if (!this.json) {
            console.error('读取配置失败 ', data.id, data.unlockType)
            return null
        }
        this._output = this.json ? (this.json.output || 0) : 0
        this._homeOutput = this.json ? (this.json.home_output || 0) : 0
        this.select = (this.isHasSelect() && data.select) || this.id
        this.gardenSpeUid = data.gardenSpeUid || ''
        return this
    }

    public toDB() {
        return {
            id: this.id,
            unlockType: this.unlockType,
            select: this.isHasSelect() ? this.select : undefined,
            gardenSpeUid: this.gardenSpeUid,
            isGardenDiy: this.isGardenDiy
        }
    }
    public isDefaultGardenStaticFurn() {
        if ((this.isGardenStaticBuild() && this.getStaticBuildLevel() === 3)) {
            return true
        }
        return false
    }
    public getStaticBuildLevel() {
        if (this.id.lastIndexOf('00') > 0) {
            return Number(this.id.slice(this.id.lastIndexOf('00') + 2))
        }
    }
    public isGardenStaticBuild() {
        return this.type >= FurnitureType.G_FLOOR_1
    }
}
