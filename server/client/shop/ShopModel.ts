import ut from "../../common/util"
import {PurchasedShopItemInfo} from "../constant/DataType"
import {CurrencyAction, ShopItemType} from "../constant/Enums"
import {gameHelper} from "../GameHelper"
// import LimitedOfferModel from "../activities/LimitedOfferModel"
// import ShopMonthCard from "./ShopMonthCard"
import ModelMgr from "../ModelMgr"
import {assetsMgr} from "../AssetsMgr"

// 商城模块
export default class ShopModel {

    private xszlTimestamp = 0// 下次购买新手助力礼包时间戳
    private cxyxTimestamp = 0// 下次购买畅享游戏礼包时间戳
    private jxthTimestamp = 0// 下次购买精选特惠礼包时间戳
    private freeWindmillTimestamp = 0// 下次免费领取风车时间戳

    private hasTopup = false// 是否有过充值
    private fristCxyx = true// 是否首次购买畅享游戏礼包
    private fristJxth = true// 是否首次购买精选特惠礼包

    private purchasedItems: PurchasedShopItemInfo[] = [] //已购买过的商品，用于处理首充和只能购买一次的物品

    private gaszSurplusTime: number = 0 //关爱手指剩余时间，实际值会有延迟，不用作逻辑
    private gaszRefreshTime: number = 0 //关爱手指下次刷新时间
    private _gaszSurplusTime: number = 0 //为了不那么频繁存储，实际逻辑用这个字段

    private receiveWindmillTimes: number = 0 //领取风车次数

    // private monthCards: ShopMonthCard[] = []

    // 临时数据
    private exposures: string[] = []

    // 限时特惠模块
    // private limitOffer: LimitedOfferModel = null
    constructor(modelMgr: ModelMgr) {
        this.init(modelMgr)
    }

    public init(modelMgr: ModelMgr) {
        // 持久化数据
        let dbHelper = modelMgr.getDB()
        const ver = 2
        const data = dbHelper.register('shop', ver, this.toDB, this) || {}
        this.xszlTimestamp = data.xszlTimestamp ?? gameHelper.now()
        this.cxyxTimestamp = data.cxyxTimestamp ?? gameHelper.now()
        this.jxthTimestamp = data.jxthTimestamp ?? gameHelper.now()
        this.freeWindmillTimestamp = data.freeWindmillTimestamp ?? gameHelper.now()
        this.hasTopup = data.hasTopup ?? false
        this.fristCxyx = data.fristCxyx ?? true
        this.fristJxth = data.fristJxth ?? true
        this.purchasedItems = data.purchasedItems ?? []
        this.gaszSurplusTime = data.gaszSurplusTime ?? 0
        this._gaszSurplusTime = this.gaszSurplusTime
        this.gaszRefreshTime = data.gaszRefreshTime ?? 0
        this.receiveWindmillTimes = data.receiveWindmillTimes ?? 0

        // this.initMonthCard(data)

        while (data._ver < ver) {
            if (data._ver === 1) {
                if (!this.fristJxth) {
                }
            }
            data._ver += 1
        }
        //this.limitOffer = this.getModel('limitedOffer')
    }

    public update(dt) {
        this.checkGaszRefresh()
        this.updateGaszSurplusTime(dt)
    }

    // private initMonthCard(_data) {
    //     let monthCardDatas = _data.monthCards || []
    //     let monthCardCfgs = assetsMgr.getJson("shop").datas.filter(m => m.type == ShopItemType.MONTH_CARD)
    //     for (let cfg of monthCardCfgs) {
    //         let data = monthCardDatas.find(m => m.action == cfg.action)
    //         let obj = new ShopMonthCard()
    //         if (data) {
    //             obj.fromDB(data)
    //         }
    //         else {
    //             obj.init(cfg.action)
    //         }
    //         this.monthCards.push(obj)
    //     }
    // }

    private checkGaszRefresh() {
        if (!this.hasGasz()) return
        this.gaszSurplusTime = this._gaszSurplusTime = 20 * ut.Time.Minute
        this.gaszRefreshTime = 1000
    }

    private updateGaszSurplusTime(dt) {
        if (!this.hasGasz()) return
        if (this._gaszSurplusTime <= 0) return
        dt = Math.floor(dt * ut.Time.Second)
        if (this._gaszSurplusTime <= dt) { //减到0的时候存一次
            this.gaszSurplusTime = this._gaszSurplusTime = 0
        } else {
            this._gaszSurplusTime -= dt
        }
        let interval = ut.Time.Second * 3 //x秒存一次
        if (this.gaszSurplusTime > this._gaszSurplusTime + interval) {
            this.gaszSurplusTime = this._gaszSurplusTime
        }
    }

    private toDB() {
        return {
            xszlTimestamp: this.xszlTimestamp,
            cxyxTimestamp: this.cxyxTimestamp,
            jxthTimestamp: this.jxthTimestamp,
            freeWindmillTimestamp: this.freeWindmillTimestamp,
            hasTopup: this.hasTopup,
            fristCxyx: this.fristCxyx,
            fristJxth: this.fristJxth,
            purchasedItems: this.purchasedItems,
            gaszSurplusTime: this.gaszSurplusTime,
            gaszRefreshTime: this.gaszRefreshTime,
            receiveWindmillTimes: this.receiveWindmillTimes,
            // monthCards: this.monthCards.map(m => m.toDB())
        }
    }

    public hasItem(action: CurrencyAction) {
        return this.purchasedItems.has("action", action)
    }

    public hasGasz() {
        return this.hasItem(CurrencyAction.IAP_GASZ)
    }
    public getPurchasedItems(){return this.purchasedItems}
    public setPurchasedItems(purchasedItems: PurchasedShopItemInfo[]){
        this.purchasedItems = purchasedItems;
    }

}