
const { VError } = require("verror");
const CryptoJS = require("./CryptoJS");
const crypto = require('crypto')
const config = require("../config")
const logger = require("../common/log").getLogger('crypto')

let privateKey = `
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

let privateKey2 = `
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

let cryptoHelper = {

    keyList: [privateKey2, privateKey],

    init() {
        if (config.privateKeys) {
            this.keyList = config.privateKeys.concat(this.keyList)
        }
    },

    /**
     * @param {string} encryptStr 
     * @param {string} encryptKey 如果这个参数有值，说明是加密过后的aes密钥
     * @description 用私钥解密公钥
     */
    async pkDecrypt(encryptStr, encryptKey, userCryptoServer = true) {
        if (encryptKey) {
            let info = await this.rsaDecrypt(encryptKey, userCryptoServer) //rsa先解密密钥
            if (info) {
                let str
                try {
                    str = CryptoJS.AES.decrypt(encryptStr, info.decStr).toString(CryptoJS.enc.Utf8); //aes解密加密串
                } catch (error) {
                    throw VError({name: "ase error", info: {info}, cause: error})
                }
                if (str == "") {
                    return false;
                }
                return {decStr: str, index: info.index};
            }
            return false
        }
        else {
            return this.rsaDecrypt(encryptStr, userCryptoServer)
        }
    },

    async rsaDecrypt(encryptStr, userCryptoServer) {
        if (userCryptoServer) {
            let res = await this.rsaDecryptByCryptoSever(encryptStr)
            if (res) return res
            return this.rsaDecrypt(encryptStr, false)
        }
        else {
            for (let i = 0; i < this.keyList.length; i++) {
                let str = this._rsaDecrypt(encryptStr, i)
                if (str) {
                    return {decStr: str, index: i}
                }
            }
            return false
        }
    },

    _rsaDecrypt(encryptStr, idx) {
        try {
            let privateKey = this.keyList[idx]
            let res = crypto.privateDecrypt({
                key: privateKey,
                padding: crypto.constants.RSA_PKCS1_PADDING
            }, Buffer.from(encryptStr.toString('base64'), 'base64')).toString();
            if (res && util.isAsciiOnly(res)) {
                return res
            }
            // logger.error("_rsaDecrypt2", encryptStr, res, idx)
            return false
        } catch (error) {
            
        }
    },

    async rsaDecryptByCryptoSever(encryptStr) {
        let res = await global.requestCryptoSever("rsaDecrypt", {encryptStr})
        if (res && res.decStr) {
            return res
        }
        return false
    },

    /**
     * @param {string} data 
     * @description  用私钥加签
     */
    async pkSign(data, index = 0, userCryptoServer = true) {
        if (userCryptoServer) {
            let sign = await this.pkSignByCryptoSever(data, index)
            if (sign) return sign
            return this.pkSign(data, index, false)
        }
        else {
            let privateKey = this.keyList[index]
            if (!privateKey) {
                throw VError({name: "pkSign error", info: {index}})
            }
            return crypto.sign("md5", Buffer.from(data), privateKey).toString('base64')
        }
    },

    async pkSignByCryptoSever(data, idx) {
        let res = await global.requestCryptoSever("pkSign", {data, idx})
        if (res) {
            return res.sign
        }
        else {
            return false
        }
    },

    md5(data) {
        return CryptoJS.MD5(data).toString();
    },

    ase(str, key) {
        let encryptStr = CryptoJS.AES.encrypt(str, key).toString()
        return encryptStr
    },

    decryptASE(encryptStr, aesKey) {
        return CryptoJS.AES.decrypt(encryptStr, aesKey).toString(CryptoJS.enc.Utf8);
    }
}

cryptoHelper.init();

module.exports = cryptoHelper;

const global = require("../common/global");
const { default: util } = require("../common/util");
