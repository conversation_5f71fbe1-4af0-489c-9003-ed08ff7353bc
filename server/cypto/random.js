class Random {
    static RANDOM_SEED_MASTER = BigInt(25214903917)
    static ADDEND = BigInt(11)
    static RANDOM_SEED_MASK = BigInt((1n << 48n) - 1n)
    static MOD = BigInt(1n << 64n)
    seed = BigInt(0)

    setSeed(t) {
        this.seed = (BigInt(t) ^ Random.RANDOM_SEED_MASTER) & Random.RANDOM_SEED_MASK;
        this.seed = this.seed % Random.MOD;
    }

    next(t) {
        this.seed = (this.seed * Random.RANDOM_SEED_MASTER + Random.ADDEND) & Random.RANDOM_SEED_MASK;
        this.seed = this.seed % Random.MOD;
        let i = Number(this.seed >> BigInt(48 - t));
        return this.unsignedInt2int(i);
    }

    nextInt() {
        return this.next(32);
    }

    unsignedInt2int(e) {
        let t = 4294967295 & e;
        if (t > 2147483647) {
            return -((4294967295 ^ t) + 1);
        }
        return t;
    }
}

module.exports = Random