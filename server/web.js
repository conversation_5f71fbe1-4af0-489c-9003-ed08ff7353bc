require('ts-node').register();

const logger = require("./common/log").getLogger("web");
const db = require("./db/db");
const model = require("./mod/model");
const app = require("./common/app");
const {errorHandler, decodeUid, requestLogger} = require("./common/middleware");
const warpApp = require("./common/warpApp");
let config = require("./config");
const jwt = require("jsonwebtoken");
const serverConfig = require("./config");

if (config.debug) {
    config.port = 8040;
}

// ts 就是请求id，I和O的输出是一对儿
function beforeHttpRequest(ts, ip, userName, url, params) {
    logger.info(`I(${ts}) - [${ip}] [${url}], user: ${userName}`, "Params", params);
}

function afterHttpResponse(ts, ip, userName, url, res) {
    url !== '/doClsQuery' && logger.info(`O(${ts}) - [${ip}] [${url}], user: ${userName}`, "Data: ", res.__rspData);
}

warpApp.init((func) => {
    return async (req, res, next) => {
        try {

            let params = req.__data || (req.method == "GET" ? req.query : req.body);
            let data = await permission.permissionCheck(req.url, req.headers.authorization);
            let userName = 'None';
            let ts = Date.now();
            if (!data) {
                if (!await permission.checkExcludeUrl(req.url) && req.headers.authorization !== 'null' && req.headers.authorization !== 'undefined') {
                    try {
                        let _o = jwt.verify(req.headers.authorization, serverConfig.token.SECRET);
                        if (!params.permission) {
                            params.permission = _o.permission ? _o.permission=JSON.parse(_o.permission)['wd']  : await permission.getAllPermission()
                        }
                        userName = _o.userName;
                    } catch (e) {
                        res.json({status: 5, desc: '会话过期,请重新登录'});
                    }
                }
                beforeHttpRequest(ts, util.getClientIp(req), userName, util.getReqPath(req), params);
                data = await func(params);
            }
            res.__rspData = JSON.stringify(data);
            res.json(data);
            afterHttpResponse(ts, util.getClientIp(req), userName, util.getReqPath(req), res)
        } catch (error) {
            next(error);
        }
    }
})

db.init();
model.init();

warpApp.use(decodeUid)
warpApp.use(requestLogger());

require("./web/account");
require("./web/version");
require("./web/user")
require("./web/errorLog")
require("./web/reddemCode")
require('./web/verify')
require('./web/rank')
require("./web/feedback")
require("./web/share")
require("./web/motherMsg")
require("./web/statistics")
require("./web/performance")
require("./web/Broadcast")
require("./web/Refunds")
require("./web/Common")
require("./web/Analysis")

const permission = require('./web/Permission')
const {default: util} = require("./common/util");

warpApp.use(errorHandler);
