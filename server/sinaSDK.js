require('ts-node').register();
let router = require('express').Router();
let app = require('./common/app');
app.__init();

router.get('/share_call', async (req, res, next) => {

    if (!req.url || req.url == '') {
        res.send(JSON.stringify({
            errcode: "-1",
            msg: "param url is null"
        }))
        return
    }

    let url = decodeURIComponent(req.url)
    let searchUrl = url.replace('/share_call?url=', '')
    if (searchUrl == '' || !searchUrl.startsWith('http')) {
        res.send(JSON.stringify({
            errcode: "-1",
            msg: "param url is null"
        }))
    } else {
        let data = JSON.stringify({
            "display_name": "乌冬的旅店",
            "summary": "打造你的梦幻旅店！",
            "image": {
                "url": "https://twgame-hotel-1257666530.file.myqcloud.com/share/share_icon.png",
                "width": 120,
                "height": 120
            },
            "url": searchUrl,
            "object_type": "webpage"
        });
        res.send(data)
    }
    next();
})

app.use('/sina', router);