require('ts-node').register();
const util = require("./common/util").default;
const fs = require("fs")
const path = require('path')
const db = require("./db/db");
const model = require("./mod/model");
const rankMod = require("./mod/rankMod");
const userMod = require("./mod/userMod");
const recordMod = require("./mod/recordMod");
const idMod = require("./mod/idMod");
const statisticsMod = require("./mod/statisticsMod");
const got = require("got")
const textModerationMod = require("./mod/textModerationMod");
const txCosMod = require("./mod/txCosMod");
const roomMod = require("./mod/roomMod");
const { isNumber } = require("./common/util").default;
const verfiyRecordMod = require("./mod/verfiyRecordMod").verfiyRecordMod
const cyptoHelper = require("./cypto/cryptoHelper")
const redisKey = require("./db/redisKey")
const recommendMod = require("./mod/recommendMod")
const wxMod = require("./mod/wxMod")
const cryptoHelper = require("./cypto/cryptoHelper")
const { payMod } = require("./payment/PayModel")
const { hmsPay } = require("./payment/HuaWeiPay")
const router = require("./router/router");
const facebookLoginMod = require("./mod/facebookLoginMod")
const request = require("request")
const logger = require("./common/log").getLocal("fix")
const currencyModel = require("./mod/currencyModel")
const { CURRENCY, CURRENCY_ACTION, CURRENCY_ORDER_STATUS } = require("./common/constant");
const googleMod = require("./mod/googleMod")
const appleLoginMod = require("./mod/appleLoginMod")
const ModelMgr = require("./client/ModelMgr").default;
const xlsx = require('node-xlsx');
const { gameHelper } = require("./client/GameHelper")
const { assetsMgr } = require('./client/AssetsMgr')
const subscribeMod = require("./mod/subscribeMod");
const storeMod = require("./mod/storeMod")
const Excel = require("./common/excel");


const room_201 = require('./client/json/roomFurnitureUnlock_1.json')
const room_202 = require('./client/json/roomFurnitureUnlock_2.json')
const room_301 = require('./client/json/roomFurnitureUnlock_3.json')
const room_302 = require('./client/json/roomFurnitureUnlock_4.json')
const room_401 = require('./client/json/roomFurnitureUnlock_5.json')
const room_402 = require('./client/json/roomFurnitureUnlock_6.json')
const { ObjectId } = require("mongoose/lib/types");
const newsMod = require("./mod/NewsMod");
const { googlePay } = require("./payment/GooglePay");
const { googleVerify } = require("./payment/GoogleVerify");
const curMod = require("./mod/curLogMod");
const partyMod = require("./mod/partyMod");
const friendMod = require("./mod/friendMod");
const hmsLoginMod = require("./mod/hmsLoginMod");
const gardenMod = require("./mod/gardenMod");
const partyPlusMod = require("./mod/partyPlusMod");
const activityMod = require("./mod/activityMod");
const robotMod = require("./mod/robotMod");
const jwt = require("jsonwebtoken");
const serverConfig = require("./config");
const { PARTY_REWARD_GET } = require("./db/redisKey");
const { handleLaunchInfo } = require("./mod/loginMod");
const shopRewardConfig = require("./client/json/tutuShopReward.json");
const shopExchangeConfig = require("./client/json/tutuShopExchange.json");
const Random = require('./cypto/random');

const NAMESPACE_LEN = '__data_len'
const NAMESPACE_VER = '__data_ver'
const NAMESPACE_DATE = '__data_date'
const DATA_CLEAR = '__@clearAll__'
const DATA_VERSION = '__@version__'
const KEY_COUNT = 5

assetsMgr.init()
db.init();
model.init();

const redis = db.redis
let userCol = db.createUserModel()
let gameRecordCol = db.createGameRecordModel()
let blackListCol = db.createBlackListModel()
let blackRecordCol = db.createBlackRecordModel()
let statUserHeartCol = db.createStatUserHeartModel()
let payOrderCol = db.createPayOrderModel()
let currencyOrderCol = db.createCurrencyOrderModel()
let deviceTokenCol = db.createDeviceToken()
let deviceTokenOldCol = db.createDeviceTokenOld()

let staFix = async () => {
    let hashMap = {}
    let day = new Date().getTime() - 30 * util.Time.Day
    day -= (day % util.Time.Day)
    while (day < Date.now()) {
        let nextDay = day + util.Time.Day
        let docs = await userCol.find({ signupTime: { $gte: day, $lt: nextDay } })
        console.log(new Date(day), new Date(nextDay), docs.length)
        // if (docs.length <= 0) {
        // break;
        // }
        for (let doc of docs) {
            if (!doc.openid) continue
            if (!hashMap[doc.openid]) {
                hashMap[doc.openid] = 0
            }
            hashMap[doc.openid]++
        }
        day = nextDay
    }
    let result = []
    for (let key in hashMap) {
        if (hashMap[key] > 1) {
            result.push(key)
        }
    }
    console.log(result.length)
    fs.writeFileSync("./fixData", JSON.stringify(result))
}

let fixDup = async () => {

    let reBind = async (uid, docA, docB) => {
        let openid = docA.openid || docB.openid
        let unionid = docA.unionid || docB.unionid
        let uid1 = await idMod.getID(openid)
        let uid2 = await idMod.getID(unionid)
        if (uid1 && uid1 != uid) {
            console.log("reBind1", openid, uid)
            await idMod.reBindID(openid, uid)
        }
        if (uid2 && uid2 != uid) {
            console.log("reBind2", unionid, uid)
            await idMod.reBindID(unionid, uid)
        }
    }

    let getUid = async (docA, docB) => {
        let recordA = await recordMod.getRecord(docA.uid)
        let starA = 0
        if (recordA) {
            starA = recordMod.getStarSum(recordA) || 0
        }
        let starB = 0
        let recordB = await recordMod.getRecord(docB.uid)
        if (recordB) {
            starB = recordMod.getStarSum(recordB) || 0
        }
        let uid = null
        if (docA.loginTime > docB.loginTime && starA >= starB) {
            if (starA >= starB) {
                uid = docA.uid
            } else if (starA < 20 && starB - starA > 50) {
                uid = docB.uid
            } else {
                uid = docA.uid
            }
        } else if (docB.loginTime > docA.loginTime) {
            if (starB >= starA) {
                uid = docB.uid
            } else if (starB < 20 && starA - starB > 50) {
                uid = docA.uid
            } else {
                uid = docB.uid
            }
        } else {
            if (starB >= starA) {
                uid = docB.uid
            } else {
                uid = docA.uid
            }
        }
        // else if (){
        //     console.log("##########", starA, starB, docA.loginTime, docB.loginTime)
        //     return
        // }

        let flag = "A"
        if (uid == docB.uid) {
            flag = "B"
        }
        console.log(uid, flag, starA, starB, docA.loginTime, docB.loginTime)
        return uid
    }
    let fixList = fs.readFileSync("./fixData").toString()
    fixList = JSON.parse(fixList)
    let count = 0;
    for (let openid of fixList) {
        let docs = await userCol.find({ openid })
        if (docs.length < 2) continue;
        if (docs.length > 2) {
            console.log("warn", openid)
            continue
        }
        let [docA, docB] = docs;
        let uid = await getUid(docA, docB)
        if (!uid) continue;
        await reBind(uid, docA, docB)
        count++
    }
    console.log("over ", count)
}

let fixRecord = async (record) => {
    let prefix = "hotel_"
    let isOldKey = (dataKey) => {
        if (dataKey.startsWith("__")) return false
        if (dataKey.startsWith(prefix)) {
            let [_, suffix] = dataKey.split(prefix)
            return isNumber(suffix)
        }
        return false
    }

    let isNewKey = (dataKey, data) => {
        if (dataKey.startsWith(prefix)) {
            let [_, suffix] = dataKey.split(prefix)
            if (isNaN(Number(suffix))) {
                if (typeof data == "object") return true
            }
        }
        return false
    }
    let oldGlobal = null
    for (let dataKey in record) {
        if (isOldKey(dataKey)) {
            let data = record[dataKey]
            if (data.global) {
                if (typeof data.global == 'string') {
                    oldGlobal = JSON.parse(data.global)
                } else {
                    oldGlobal = data.global
                }
            }
        }
    }
    let newGlobal = null
    if (record[prefix + "global"]) {
        newGlobal = record[prefix + "global"].global
    }
    if (oldGlobal && newGlobal) {
        if (oldGlobal.heart <= newGlobal.heart) { //删除自动桶里的key
            for (let dataKey in record) {
                if (isOldKey(dataKey)) {
                    let data = record[dataKey]
                    for (let key in data) {
                        if (record[prefix + key]) {
                            delete data[key]
                        }
                    }
                    record[dataKey] = data
                }
            }
        } else { //删除手动桶的key
            for (let dataKey in record) {
                if (isNewKey(dataKey, record[dataKey])) {
                    delete record[dataKey]
                }
            }
        }
    }

    for (let dataKey in record) {
        if (isOldKey(dataKey)) { //如果是自动桶
            let data = record[dataKey]
            let [_, suffix] = dataKey.split(prefix)
            for (let key in data) {
                let subData = data[key]
                let type = typeof subData
                if (type == "string") {
                    try {
                        let val = JSON.parse(subData)
                        if (val && typeof val == "object") {
                            subData = data[key] = val
                            type = "object"
                        }
                    } catch (error) {
                    }
                }
                if (type == 'object') {
                    let info = {}
                    info[NAMESPACE_VER] = data[NAMESPACE_VER] || 0
                    info[NAMESPACE_DATE] = data[NAMESPACE_DATE] || new Date().getTime()
                    info[key] = subData
                    record[prefix + key] = info

                    delete record[dataKey][key]
                }
            }
            if (Number(suffix) >= KEY_COUNT) {
                delete record[dataKey]
            } else {
                record[dataKey][NAMESPACE_LEN] = 0
            }
        }
    }
    if (!record[DATA_VERSION] || Number(record[DATA_VERSION]) < 2) {
        record[DATA_VERSION] = 2
    }
}

let fixRecordByUid = async () => {
    let uid = "83ba51f6-7155-4eb9-8af8-9c6d77dfab01"
    // await recordMod.debugCopy(uid)
    let record = await recordMod.getRecord(uid)
    recordMod.fixRecordOnDownload(record)
    // console.log(record)
    await recordMod.setRecord(uid, record)
}

let fixIdMap = async () => {
    let unionid = "ocONN1O0OL1KlwRDFfZpDGaatbk0"
    let uid = "4a01837a-88b4-476a-9608-9dfb49166464"
    // let orgUid = ""
    if (orgUid) {
        await userMod.mergeUser(uid, orgUid)
    }
    let userDoc = await userMod.findOne(uid)
    if (unionid) {
        await idMod.reBindID(unionid, uid, true)
        await userMod.updateOne(uid, { unionid })
    }
    if (userDoc.openid) {
        await idMod.reBindID(userDoc.openid, uid, true)
    }
    if (userDoc.wxAppOpenid) {
        await idMod.reBindID(userDoc.wxAppOpenid, uid, true)
    }
}

let fixDebug = async () => {
    let uid = '*************-4d81-9f53-a89bd40cde2b'
    let record = await recordMod.getRecord(uid)
    for (let key in record) {
        if (key.startsWith("hotel_debug")) {
            delete record[key]
        }
    }
    await recordMod.setRecord(uid, record)
}


//修复误删用户
let fixMissUser = async () => {
    // let unionid = "ocONN1HXpcDTBOOIYoGVC-LyAE4w"
    // let openid = "o1LE346ysaKMf_9Q8ER0ZJyhbKis"
    // let uid = "e1c2be04-b59d-4136-9792-91197f38b84e"
    let record = {}

    if (openid) {
        await idMod.reBindID(openid, uid, true)
    }
    if (unionid) {
        await idMod.reBindID(unionid, uid, true)
    }

    await userCol.findOneAndUpdate({ uid: uid }, {
        unionid, openid,
        "signupTime": 1612531152782,
        "updateTime": 1614355054592,
        "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKfPE6WurZ0QfDZCSPqhF0Ocq3MlqIsSwCpLsJgY2QFhXCSIKBs4d092tzCdEPLknUc9GFAzcLhUw/132",
        "nickName": "neversaynever-",
        "userType": "app_wx",
        "wxAppOpenid": "ohsnC5s2S85yQMSuiw3B1dTG-5KM",
        "loginTime": 1620623428785,
        "playTime": 8283544,
        "serverId": 3,
    }, { upsert: true, new: true });

    fixRecord(record)
    await recordMod.setRecord(uid, record)
}

let testVerfiyRecord = async () => {
    await recordMod.debugCopy('dc1d1d0b-77a0-46d7-afc0-780dbc9626c7')
    let docs = await gameRecordCol.find({ uid: "dc1d1d0b-77a0-46d7-afc0-780dbc9626c7" }, { checkInfo: 0 }).limit(2000)
    let startTime1 = Date.now()
    docs = await util.promiseMap(docs, async (doc) => {
        let record = await recordMod.unzipRecord(doc.recordBuff, true)
        return { uid: doc.uid, record }
    })
    console.log(Date.now() - startTime1)
    console.log(docs.length)
    let startTime = Date.now()
    await util.promiseMap(docs, async ({ uid, record }) => {
        await verfiyRecordMod.check(uid, record, true)
    })
    console.log(Date.now() - startTime)
}

let randomCopy = async () => {
    let count = 100
    while (count > 0) {
        let uid = await userMod.getRandomUser()
        if (uid) {
            await recordMod.debugCopy(uid, false)
            console.log('randomCopy: ', uid)
            count--
        }
    }
}


let copyToBlackList = async (_id) => {
    let where = {}
    if (_id) {
        where = { _id: { $gt: _id } }
    }
    let docs = await blackRecordCol.find(where).sort({ _id: 1 }).limit(100)
    for (let doc of docs) {
        let types = doc.types
        let level = 2
        if (types.length == 1 && types[0] == 5) {
            level = 1
        }
        doc = doc.toObject()
        doc.level = level
        await blackListCol.updateOne({ uid: doc.uid }, doc, { upsert: true })
        await rankMod.remove(uid)
    }
    console.log("done", docs.length)
    if (docs.length <= 0) return
    return copyToBlackList(docs[docs.length - 1]._id)
}

let verfiyTop = async () => {
    for (let i = 0; i < 100; i++) {
        let key = rankMod.getRankName(i)
        let users = await rankMod.getTop(key, 100)
        for (let { id } of users) {
            let record = await recordMod.getRecord(id)
            await recordMod.verfityRecord(id, record)
        }
        console.log('verfiyTop -- ', i)
    }
}

let reCheck = async (_id) => {
    let where = {}
    if (_id) {
        where = { _id: { $gt: _id } }
    }
    let docs = await blackListCol.find(where).sort({ _id: 1 }).limit(300)
    for (let { uid } of docs) {
        let record = await recordMod.getRecord(uid)
        let level = await verfiyRecordMod.check(uid, record, true)
        if (level == 0) {
            console.log("del", uid)
            await blackListCol.deleteOne({ uid })
        }
    }
    if (docs.length <= 0) return
    console.log("reCheck --", docs.length)
    return reCheck(docs[docs.length - 1]._id)
}

let autoDeblock = async (_id) => {
    let where = {}
    if (_id) {
        where = { _id: { $gt: _id } }
    }
    // await verfiyRecordMod.deblock()
    let docs = await blackListCol.find(where).sort({ _id: 1 }).limit(300)
    for (let { uid } of docs) {
        await verfiyRecordMod.deblock(uid)
    }
    if (docs.length <= 0) return
    console.log("autoDeblock --", docs.length)
    return autoDeblock(docs[docs.length - 1]._id)
}

let reCheck2 = async () => {
    let uid = "68567363-a89c-48eb-85d0-7e86c526fb98"
    await recordMod.debugCopy(uid)
    let record = await recordMod.getRecord(uid)
    let level = await verfiyRecordMod.check(uid, record, true)
    console.log(level)
}

let skipRecordCheck = async () => {
    let uid = "1c329cf6-8455-4907-bb45-34017850724f"
    await recordMod.changeRecord(uid, (record) => {
        for (let dataKey in record) {
            let data = record[dataKey]
            if (typeof data == 'object') {
                data[NAMESPACE_VER] = 0
            }
            record[dataKey] = data
        }
    })
}

let compensationSuperValuePack = async () => {
    let date = new Date("2021-10-26").getTime()
    // let uids = ["a3bfbd98-**************-608c08c2f16f", "8b5fe441-3bed-44fc-8075-8a11a085c999"]
    let uid = "a3bfbd98-**************-608c08c2f16f"
    let docs = await payOrderCol.find({
        uid,
        product: "hotel_global_super_value_pack",
        stat: 100,
        timestamp: { $lte: date }
    })
    for (let doc of docs) {
        let { uid, order_id } = doc
        if (uid) {
            await currencyModel.changeCurrencyBalance(uid, CURRENCY.SCISSOR, 150)
            logger.info("compensationSuperValuePack", uid, order_id)
        }
    }
}

let fixDeviceToken = async () => {
    let len = 0
    let work = async (_id) => {
        let where = {}
        if (_id) {
            where = { _id: { $lt: _id } }
        }
        let docs = await deviceTokenOldCol.find(where).sort("-_id").limit(20000)
        await util.promiseMap(docs, async ({ uid, token, platform }) => {
            let doc = await deviceTokenCol.findOne({ uid, platform: token })
            if (!doc) {
                await deviceTokenCol.updateOne({ uid, platform: token }, { token: platform }, { upsert: true })
            }
        })

        len += docs.length
        console.log("完成: ", docs.length, len)
        if (docs.length > 0) {
            return work(docs[docs.length - 1]._id)
        }
    }
    await work()
}

let diffManifest = async () => {
    let path1 = "./etc/project_old.manifest"
    let path2 = "./etc/project_new.manifest"
    let json1 = JSON.parse(fs.readFileSync(path1).toString())
    let json2 = JSON.parse(fs.readFileSync(path2).toString())
    let assets1 = json1.assets
    let assets2 = json2.assets
    let list = []
    for (let key in assets2) {
        let item2 = assets2[key]
        let item1 = assets1[key]
        if (!item1 || item1.md5 != item2.md5) {
            let arr = key.split("/")
            arr.splice(arr.length - 1, 0, item2.md5)
            key = arr.join("/")
            list.push({ key: key, size: item2.size })
        }
    }
    list.sort((a, b) => {
        return b.size - a.size
    })
    console.log(json2.remoteManifestUrl)
    for (let { key } of list) {
        console.log(json2.packageUrl + key)
    }
}

let compensation = async () => {
    const type = 0
    var sheets = xlsx.parse('./etc/玩家掉档补偿.xlsx');
    let extraReward = "4,9207,3|4,9100,200"
    let sheet = sheets[0]['data']
    let fixStr = (str) => {
        if (!str) return ""
        return str.replace(/\r/g, '').replace(/\n/g, '').replace(/ /g, '')
    }

    let skips = []
    for (var rowId in sheet) {
        if (rowId == 0) continue
        try {
            var [_uid, _, reward] = sheet[rowId];
            if (!_uid && !reward) {
                continue
            }
            _uid = fixStr(_uid)
            reward = fixStr(reward)
            let info = await cryptoHelper.pkDecrypt(_uid)
            if (!info) {
                skips.push([rowId, _uid])
                continue
            }
            let uid = info.decStr
            let doc = await userMod.compensationRewardHistoryCol.findOne({ uid, type })
            if (doc) continue
            reward += "|" + extraReward
            let conds = gameHelper.stringToConditions(reward)
            conds = conds.map((cond) => {
                let { type, id } = cond
                if (type == 3) {
                    console.log("heart", rowId, reward)
                }
                if (type == 10) {
                    let data = assetsMgr.getJsonData("wudongBase", id)
                    if (!data) return console.error("wudong", rowId, id)
                    if (data.unlock_cost) {
                        let cond2 = gameHelper.stringToConditions(data.unlock_cost)[0]
                        if (cond2.type == 1 || cond2.type == 2) {
                            return cond2
                        }
                    }
                } else if (type == 11) {
                    let data = assetsMgr.getJsonData("hotelSkinBase", id)
                    if (!data) return console.error("mainSkin", rowId, id)
                    if (data.unlock_cost) {
                        let cond2 = gameHelper.stringToConditions(data.unlock_cost)[0]
                        if (cond2.type == 1 || cond2.type == 2) {
                            return cond2
                        }
                    }
                }
                return cond
            })
            reward = gameHelper.conditionsToString(conds)
            await userMod.addCompensationReward(uid, reward, type)
            console.log(rowId, uid, reward)
        } catch (error) {
            console.error(rowId, error)
        }
    }
    for (let [rowId] of skips) {
        console.log("skip: ", Number(rowId) + 1)
    }
}

let exportAq = async () => {
    let key = "twomiles";
    let sheet = xlsx.parse('/Users/<USER>/Downloads/样本数据_4月12日/乌冬_韩语（回复）.xlsx');
    let data = sheet[0].data;
    for (let line of data) {
        let result = "Y";
        if (result === "Y") {
            let str = line[1] + line[2] + key;
            let md5 = line[3];
            cyptoHelper.md5(str) !== md5 && (result = "N");
        }
        line[4] = result;
    }
    let excel = new Excel({ data })
    excel.writeFile("韩语.xlsx")
    console.log(1)
}

let read_user_id = async () => {
    console.log("======> read <======")
    let data = fs.readFileSync('./send_demo_data.txt', 'utf-8');
    let arr = data.split("\r\n");
    let index_id_arr = [];
    for (let uid of arr) {
        let doc = await userMod.findOne({ uid });
        let id = doc && doc.index_id || 0;
        console.log(`uid:${uid},index_id:${id}`)
        index_id_arr.push(id);
        j++;
    }
    data = "";
    for (let id of index_id_arr) {
        data += id + "\r\n";
    }
    console.log("======> write <======")
    fs.writeFileSync('./output.txt', data);
    console.log("======> end <======")
}

// 装修方案 数据转移至 3.0
let transfer_data = async () => {
    let buildPlanCol = db.createBuildPlanModel();
    let recommendCol = db.createRecommendModel();
    // 指定用户id和房间数据移动
    let spePlayers = {
        //"e78c5864-d3ea-4956-b392-ad58d500bfdd": [201, 301, 401],
        //"2aa887df-2d02-4234-9d76-454a5f15a05c": [201, 202, 301, 302, 401, 402],
        //"eb03b30e-1cc7-47e3-b5b6-a381704873c6": [201, 202, 301, 302, 401, 402],
    }
    let furnMaxHeart = 0; //家具总最大蜡烛大于等于该值才恢赋值
    let furnMinHeart = 0; //家具总最大蜡烛小于于等于该值才恢赋值

    let rooms = [];//房间限制
    let minRoomLike = 5;// 点赞数大于该值的才复制过去
    let maxRoomLike = 0;// 点赞数小于该值的才复制过去

    let getRoomFurnData = async (roomId) => {
        roomId && (roomId = parseInt(roomId));
        let data = [];
        roomId === 201 && (data = room_201);
        roomId === 202 && (data = room_202);
        roomId === 301 && (data = room_301);
        roomId === 302 && (data = room_302);
        roomId === 401 && (data = room_401);
        roomId === 402 && (data = room_402);
        return data;
    }
    let park = async (uid, roomId, activation, furnitures, recommended, roomlike) => {
        let record = await recordMod.getRecord(uid);
        if (!record) return;
        let modelMgr = new ModelMgr();
        try {
            modelMgr.init(record);
            // 获取玩家的客房
            let keFangs = modelMgr.world.getKefangs()
            if (!keFangs) return;
            let _t = keFangs.filter(kefang => {
                return kefang.no == roomId;
            })
            _t && _t.length === 1 && (_t = _t[0]);
            let roomFurnData = await getRoomFurnData(roomId);
            let curData;
            let curId = _t.currFurnitureInfoId;
            if (!curId || !roomFurnData) return;
            curData = _t.furnSaves.filter(_f => {
                return _f.id === curId;
            })
            if (!curData || (curData && !curData.length)) return;
            curData.length >= 1 && (curData = curData[0]);
            // 计算方案蜡烛
            let heart = 0;
            for (let furniture of curData.furnitures) {
                !furniture.isSpecialItem() && (roomFurnData.filter(_dt => {
                    if (_dt.id === furniture.id) {
                        let _arr = _dt['unlock_limit'].split(',');
                        _arr.length === 3 && heart < parseInt(_arr[2]) && (heart = parseInt(_arr[2]));
                        return false;
                    }
                }));
            }
            if ((furnMaxHeart && heart < furnMaxHeart) || (furnMinHeart && heart > furnMinHeart)) return;
            // 提取存档里的方案信息
            let __kefang = record[`hotel_debug_v1_kefang_${roomId}`] || record[`hotel_kefang_${roomId}`]
            let __room = __kefang[`kefang_${roomId}`];
            let __index = 0;
            if (__room.furnSaves && __room.furnSaves.length > 1 && __room.currFurnitureInfoId) {
                __room.furnSaves.filter((item, idx) => {
                    if (item.id === __room.currFurnitureInfoId) {
                        __index = idx;
                    }
                })
            }
            let saveInfo = "";
            __room && __room.furnSaves && __room.furnSaves.length && (saveInfo = JSON.stringify(__room.furnSaves[__index]));
            let save = {
                uid,
                roomId,
                activation, furnitures,
                recommended, roomlike,
                heart,
                saveInfo: saveInfo,
            };
            await buildPlanCol.updateOne({ uid, roomId }, save, { upsert: true });
        } catch (e) {
            console.log(e);
            return false;
        }
        return true;
    }
    let logic = async (lastId) => {
        let where = {};
        if (minRoomLike || maxRoomLike) {
            where.roomlike = {}
        }
        minRoomLike && (where.roomlike.$gte = minRoomLike);
        maxRoomLike && (where.roomlike.$lte = maxRoomLike);
        rooms.length && (where.roomId = {}, where.roomId.$in = rooms);
        lastId && (where._id = { $gt: lastId });
        let docs = await recommendCol.find(where).sort({ _id: 1 }).limit(1000);
        await util.promiseMap(docs, async ({ uid, roomId, activation, furnitures, recommended, roomlike }) => {
            if (!uid) return;
            await park(uid, roomId, activation, furnitures, recommended, roomlike);
        })
        if (docs.length > 0) {
            return docs[docs.length - 1]._id;
        } else {
            return 0;
        }
    }
    let exec_ = true;
    let lastId = 0;
    let keys = Object.keys(spePlayers);
    while (exec_) {
        try {
            if (keys && keys.length) {
                for (let key of keys) {
                    let rooms = spePlayers[key];
                    for (let roomId of rooms) {
                        await park(key, roomId, [], 0, 0, 0);
                    }
                }
                break;
            }
            lastId = await logic(lastId);
        } catch (e) {
        }
        exec_ = !!lastId;
    }
}

const exportCode = async () => {
    const col = db.createRedeemCodeModel()
    const timeMap = {
        "1": 1755153311000,
        "2": 1755157052000,
        "3": 1755160669000,
        "4": 1755164275000,
        "5": 1755167884000,
    }

    for (const key in timeMap) {
        const date = timeMap[key]
        const docs = await col.find({ date })
        const out = []
        for (const doc of docs) {
            out.push(doc.code)
        }
        fs.writeFileSync(`./${key}.txt`, out.join("\n"))
    }
}

(async () => {
    try {
        // diffManifest()
        // let record = await recordMod.getRecord("d197bd4d-3769-4d57-adf3-8fa1cd27808c")

        // let modelMgr = new ModelMgr().init(record)
        // console.log(modelMgr.getSpecFurnCount())

        // console.log(res)
        // await statisticsMod.updateWorkDay()
        // await statisticsMod.heartDisNew()
        // await wxMod.TestToken()
        // await statisticsMod.guideDis()
        // await fixRecordByUid()
        // await recommendMod.updateRefreshUserAll()
        // await statisticsMod.heartDis()
        // await randomCopy()
        // await userMod.copyTop()
        // await fixDebug()
        // updateJson()
        // await copyToBlackList()
        // await reCheck()
        // await autoDeblock()
        // await testVerfiyRecord()
        // await verfiyTop()
        // cyptoHelper
        // await reCheck2()
        // await skipRecordCheck()
        // let count = await userMod.getOnlineUsersCount()
        // console.log(count)
        // await compensationSuperValuePack()
        // await googleMod.notify("fUEem7SvQAuB_C3REpkoD_:APA91bEqJ-3sD0bMG3uuJznZiBhyMjC804-ZGobs_iIDVmIESIHK9A5xwQtAG78oZ9qZCWrMkNDSaEHmWZuKtKvyWvEwMVffqO-zanlcBgfHbF_lJ7vC9m87G1fVtXpY1D74aCioqshE", {
        //     notification: {body: "test", title: "这个是标题"}
        // })
        // await appleLoginMod.notify("04dc84a17923031b0f320ce99a41dbecfeab6ff9bebec83509faa504673410e9", {alert: {body: "123", title: "title"}})
        // await appleLoginMod.notify("b9c5e85d2d4a0f82a6336acab8572033c2d5373cf603f082c26b071118543192", {alert: {body: "123", title: "title"}})
        // await userMod.online("519849e0-7257-447b-9669-dcd8690507e4")

        // await fixDeviceToken(o

        // await statisticsMod.statOrder()
        // await statisticsMod.commonDis()
        // updateWebJson();
        // await statisticsMod.staFurnitureAvgAndMed()

        // await userMod.addCompensationReward("02053198-9c88-4ac8-9eac-c66026848037", "2,-1,1111")
        // await userMod.addCompensationReward("6b4e22aa-8a40-415f-87e5-876ad6523d8c", "1,-1,300")
        // await userMod.addCompensationReward("741037e4-2780-4dee-b903-321f196076a6", "1,-1,500")
        // await userMod.addCompensationReward("9516dacc-c26b-4293-9c6e-b37f7091772a", "2,-1,1111")
        // await userMod.addCompensationReward("ff877f1d-7b93-4ade-abee-4f946445dad9", "4,9207,3|4,9100,200")

        // await compensation()
        // await statisticsMod.customerTask();
        // console.log(await recordMod.getRecord(''));

        //await userMod.backupUserData("41d15cd8-c191-4398-b07a-e00bf844448b","测试2");
        //  await userMod.deleteUser("41d15cd8-c191-4398-b07a-e00bf844448b");
        // await userMod.restoreUserData("6205ca1dda3fc4ba6a25d4fc");
        //await userMod.getCompensationRewards('584f3352-9d0d-49fb-be9b-643c6767dbc9', Date.now());
        //await userMod.clearUserBackupData();
        //await statisticsMod.specialTaskCompletion();
        // await statisticsMod.statisticsOnFurnitureUsage();
        // await statisticsMod.statUserLoginDaysAvg();
        // await statisticsMod.statCommonLevelAvg();
        //await statisticsMod.stat();
        // let record = await recordMod.findOne("a3ebe013-dfec-41a0-8b18-1740e6d0b610")
        // let modelMgr = new ModelMgr();
        // modelMgr.init(record);
        //let data = await currencyModel.getCfgByAction("iap_thyk", "2.12.0");
        //await currencyModel.changeCurrencyBalance("584f3352-9d0d-49fb-be9b-643c6767dbc9", "windmill", -11000, false);

        // let umengsCol = db.createUmengModel();
        // let docs = await umengsCol.find({"eventId": "report_share_platfrom"})
        // let ios = 0, android = 0, pc = 0;
        // docs.forEach(item => {
        //     let params = item.params
        //     params.forEach(p => {
        //         if (p.key == 'ios') {
        //             ios += p.val;
        //         }
        //         if (p.key == 'android') {
        //             android += p.val;
        //         }
        //         if (p.key == 'pc') {
        //             pc += p.val;
        //         }
        //     })
        // })
        // console.log(`ios:${ios},android:${android},pc:${pc}`);

        //await storeMod.deal_order_action("iap_meteor_skin", "1fd18dd5-58e7-4be6-96c3-d9f5b8cdec56")
        //await storeMod.deal_google_refund_order_info();
        // await statisticsMod.statCommonLevelAvg();
        // await read_user_id();
        //await statisticsMod.platformTourists();
        //await statisticsMod.statisticsActivityUsers();
        // let data = await roomMod.getRoomList3_0(['201', '202'], 0, {}, 1, 10);
        //let data = await roomMod.changePlanState3_0("e923ea9f-e626-4d08-a7ab-47019a99413f","302",1);
        //let doc = await actCol.findOneAndUpdate({uid: 'c5438814-ca40-4b7d-a735-63edc3bf2202'}, {serverId: 2}, {new: true})

        //let dr = await roomMod.tryGetBuildPlanData(1, [201], 4, 1);
        // let dr = await roomMod.getBuildPlanInfo(['627b1da1694295d7b881901f']);

        //await newsMod.tryGetTodayActionTypes();

        // let dr = await newsMod.doTodayRandom()
        // await activityMod.getTasks("yu")
        // const data = await activityMod.sendAward("yu", activityMod._mod.rewards[0], false)

        // const data = await activityMod.refreshTask("hu", 1)
        // -2147483648

        //await activityMod.setLastFreeTime("3hga")
        // const day = await activityMod.getTasks("3hga")

        // const record = await recordMod.getRecord("b1d9a217-25b6-440f-b9f3-c3dadfe45ba3")
        // console.log(record)
        // await recordMod.debugCopy('baf27577-0d66-439c-b800-297970e0b1e2')
        // let record = await recordMod.getRecord("13bd9d65-1aed-4e6a-8946-3ab3a391096e")
        // const usr = recordMod.getFromRecord('user', record)
        // usr.uid = "13bd9d65-1aed-4e6a-8946-3ab3a391096e"
        // let level = await verfiyRecordMod.check("13bd9d65-1aed-4e6a-8946-3ab3a391096e", record, true)
        //
        // console.error(1)

        // await exportCode()


        let cnt = 0
        let logic = async (lastId) => {
            let where = {};
            if (lastId) {
                where._id = { $gt: lastId }
            }
            let docs = await currencyOrderCol.find(where).limit(10000);
            const deleteDocs = []

            await util.promiseMap(docs, async ({ _id, uid, action, status, timestamp }) => {
                if (!uid) return;
                // if (timestamp > 1746028800000) return
                if (action.startsWith("exchange_")) {
                    deleteDocs.push(_id)
                }
                else if (action.endsWith("_ad")) {
                    deleteDocs.push(_id)
                }
                else if (action.endsWith("_ad_windmill")) {
                    deleteDocs.push(_id)
                }
                else if (action.startsWith("shopFreeExchange")) {
                    deleteDocs.push(_id)
                }
            })
            if (deleteDocs.length) {
                cnt += deleteDocs.length
                await currencyOrderCol.deleteMany({ _id: { $in: deleteDocs } })
                console.log("删除：", deleteDocs.length)
            }
            if (docs.length > 0) {
                return docs[docs.length - 1]._id;
            }
            return null;
        }
        let exec = true
        let lastId = null
        while (exec) {
            lastId = await logic(lastId)
            exec = !!lastId
        }

        console.log(cnt)
        console.log("end")
    } catch (error) {
        console.error(error)
    }
    console.log("done")
}
)
    ()


